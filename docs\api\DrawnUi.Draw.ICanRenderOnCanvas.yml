### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.ICanRenderOnCanvas
  commentId: T:DrawnUi.Draw.ICanRenderOnCanvas
  id: ICanRenderOnCanvas
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.ICanRenderOnCanvas.Render(DrawnUi.Draw.DrawingContext,DrawnUi.Draw.IDrawnBase)
  langs:
  - csharp
  - vb
  name: ICanRenderOnCanvas
  nameWithType: ICanRenderOnCanvas
  fullName: DrawnUi.Draw.ICanRenderOnCanvas
  type: Interface
  source:
    remote:
      path: src/Shared/Features/Animations/Interfaces/ICanRenderOnCanvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ICanRenderOnCanvas
    path: ../src/Shared/Features/Animations/Interfaces/ICanRenderOnCanvas.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public interface ICanRenderOnCanvas
    content.vb: Public Interface ICanRenderOnCanvas
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.ICanRenderOnCanvas.Render(DrawnUi.Draw.DrawingContext,DrawnUi.Draw.IDrawnBase)
  commentId: M:DrawnUi.Draw.ICanRenderOnCanvas.Render(DrawnUi.Draw.DrawingContext,DrawnUi.Draw.IDrawnBase)
  id: Render(DrawnUi.Draw.DrawingContext,DrawnUi.Draw.IDrawnBase)
  parent: DrawnUi.Draw.ICanRenderOnCanvas
  langs:
  - csharp
  - vb
  name: Render(DrawingContext, IDrawnBase)
  nameWithType: ICanRenderOnCanvas.Render(DrawingContext, IDrawnBase)
  fullName: DrawnUi.Draw.ICanRenderOnCanvas.Render(DrawnUi.Draw.DrawingContext, DrawnUi.Draw.IDrawnBase)
  type: Method
  source:
    remote:
      path: src/Shared/Features/Animations/Interfaces/ICanRenderOnCanvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Render
    path: ../src/Shared/Features/Animations/Interfaces/ICanRenderOnCanvas.cs
    startLine: 11
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Renders effect overlay to canvas, return true if has drawn something and rendering needs to be applied.
  example: []
  syntax:
    content: bool Render(DrawingContext context, IDrawnBase control)
    parameters:
    - id: context
      type: DrawnUi.Draw.DrawingContext
      description: ''
    - id: control
      type: DrawnUi.Draw.IDrawnBase
      description: ''
    return:
      type: System.Boolean
      description: ''
    content.vb: Function Render(context As DrawingContext, control As IDrawnBase) As Boolean
  overload: DrawnUi.Draw.ICanRenderOnCanvas.Render*
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.ICanRenderOnCanvas.Render*
  commentId: Overload:DrawnUi.Draw.ICanRenderOnCanvas.Render
  href: DrawnUi.Draw.ICanRenderOnCanvas.html#DrawnUi_Draw_ICanRenderOnCanvas_Render_DrawnUi_Draw_DrawingContext_DrawnUi_Draw_IDrawnBase_
  name: Render
  nameWithType: ICanRenderOnCanvas.Render
  fullName: DrawnUi.Draw.ICanRenderOnCanvas.Render
- uid: DrawnUi.Draw.DrawingContext
  commentId: T:DrawnUi.Draw.DrawingContext
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.DrawingContext.html
  name: DrawingContext
  nameWithType: DrawingContext
  fullName: DrawnUi.Draw.DrawingContext
- uid: DrawnUi.Draw.IDrawnBase
  commentId: T:DrawnUi.Draw.IDrawnBase
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.IDrawnBase.html
  name: IDrawnBase
  nameWithType: IDrawnBase
  fullName: DrawnUi.Draw.IDrawnBase
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
