### YamlMime:ManagedReference
items:
- uid: DrawnUi.Infrastructure.MeasuringConstraints
  commentId: T:DrawnUi.Infrastructure.MeasuringConstraints
  id: MeasuringConstraints
  parent: DrawnUi.Infrastructure
  children:
  - DrawnUi.Infrastructure.MeasuringConstraints.Content
  - DrawnUi.Infrastructure.MeasuringConstraints.Margins
  - DrawnUi.Infrastructure.MeasuringConstraints.Request
  - DrawnUi.Infrastructure.MeasuringConstraints.TotalMargins
  langs:
  - csharp
  - vb
  name: MeasuringConstraints
  nameWithType: MeasuringConstraints
  fullName: DrawnUi.Infrastructure.MeasuringConstraints
  type: Struct
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/MeasuringConstraints.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: MeasuringConstraints
    path: ../src/Shared/Draw/Internals/Models/MeasuringConstraints.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public struct MeasuringConstraints
    content.vb: Public Structure MeasuringConstraints
  inheritedMembers:
  - System.ValueType.Equals(System.Object)
  - System.ValueType.GetHashCode
  - System.ValueType.ToString
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetType
  - System.Object.ReferenceEquals(System.Object,System.Object)
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Infrastructure.MeasuringConstraints.Margins
  commentId: P:DrawnUi.Infrastructure.MeasuringConstraints.Margins
  id: Margins
  parent: DrawnUi.Infrastructure.MeasuringConstraints
  langs:
  - csharp
  - vb
  name: Margins
  nameWithType: MeasuringConstraints.Margins
  fullName: DrawnUi.Infrastructure.MeasuringConstraints.Margins
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/MeasuringConstraints.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Margins
    path: ../src/Shared/Draw/Internals/Models/MeasuringConstraints.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public Thickness Margins { readonly get; set; }
    parameters: []
    return:
      type: Microsoft.Maui.Thickness
    content.vb: Public Property Margins As Thickness
  overload: DrawnUi.Infrastructure.MeasuringConstraints.Margins*
- uid: DrawnUi.Infrastructure.MeasuringConstraints.TotalMargins
  commentId: P:DrawnUi.Infrastructure.MeasuringConstraints.TotalMargins
  id: TotalMargins
  parent: DrawnUi.Infrastructure.MeasuringConstraints
  langs:
  - csharp
  - vb
  name: TotalMargins
  nameWithType: MeasuringConstraints.TotalMargins
  fullName: DrawnUi.Infrastructure.MeasuringConstraints.TotalMargins
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/MeasuringConstraints.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: TotalMargins
    path: ../src/Shared/Draw/Internals/Models/MeasuringConstraints.cs
    startLine: 9
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  summary: Include padding
  example: []
  syntax:
    content: public Thickness TotalMargins { readonly get; set; }
    parameters: []
    return:
      type: Microsoft.Maui.Thickness
    content.vb: Public Property TotalMargins As Thickness
  overload: DrawnUi.Infrastructure.MeasuringConstraints.TotalMargins*
- uid: DrawnUi.Infrastructure.MeasuringConstraints.Request
  commentId: P:DrawnUi.Infrastructure.MeasuringConstraints.Request
  id: Request
  parent: DrawnUi.Infrastructure.MeasuringConstraints
  langs:
  - csharp
  - vb
  name: Request
  nameWithType: MeasuringConstraints.Request
  fullName: DrawnUi.Infrastructure.MeasuringConstraints.Request
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/MeasuringConstraints.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Request
    path: ../src/Shared/Draw/Internals/Models/MeasuringConstraints.cs
    startLine: 11
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public SKSize Request { readonly get; set; }
    parameters: []
    return:
      type: SkiaSharp.SKSize
    content.vb: Public Property Request As SKSize
  overload: DrawnUi.Infrastructure.MeasuringConstraints.Request*
- uid: DrawnUi.Infrastructure.MeasuringConstraints.Content
  commentId: P:DrawnUi.Infrastructure.MeasuringConstraints.Content
  id: Content
  parent: DrawnUi.Infrastructure.MeasuringConstraints
  langs:
  - csharp
  - vb
  name: Content
  nameWithType: MeasuringConstraints.Content
  fullName: DrawnUi.Infrastructure.MeasuringConstraints.Content
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/MeasuringConstraints.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Content
    path: ../src/Shared/Draw/Internals/Models/MeasuringConstraints.cs
    startLine: 12
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public SKRect Content { readonly get; set; }
    parameters: []
    return:
      type: SkiaSharp.SKRect
    content.vb: Public Property Content As SKRect
  overload: DrawnUi.Infrastructure.MeasuringConstraints.Content*
references:
- uid: DrawnUi.Infrastructure
  commentId: N:DrawnUi.Infrastructure
  href: DrawnUi.html
  name: DrawnUi.Infrastructure
  nameWithType: DrawnUi.Infrastructure
  fullName: DrawnUi.Infrastructure
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Infrastructure
    name: Infrastructure
    href: DrawnUi.Infrastructure.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Infrastructure
    name: Infrastructure
    href: DrawnUi.Infrastructure.html
- uid: System.ValueType.Equals(System.Object)
  commentId: M:System.ValueType.Equals(System.Object)
  parent: System.ValueType
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype.equals
  name: Equals(object)
  nameWithType: ValueType.Equals(object)
  fullName: System.ValueType.Equals(object)
  nameWithType.vb: ValueType.Equals(Object)
  fullName.vb: System.ValueType.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.ValueType.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.equals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.ValueType.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.equals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.ValueType.GetHashCode
  commentId: M:System.ValueType.GetHashCode
  parent: System.ValueType
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype.gethashcode
  name: GetHashCode()
  nameWithType: ValueType.GetHashCode()
  fullName: System.ValueType.GetHashCode()
  spec.csharp:
  - uid: System.ValueType.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.ValueType.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.gethashcode
  - name: (
  - name: )
- uid: System.ValueType.ToString
  commentId: M:System.ValueType.ToString
  parent: System.ValueType
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype.tostring
  name: ToString()
  nameWithType: ValueType.ToString()
  fullName: System.ValueType.ToString()
  spec.csharp:
  - uid: System.ValueType.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.ValueType.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.tostring
  - name: (
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.ValueType
  commentId: T:System.ValueType
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype
  name: ValueType
  nameWithType: ValueType
  fullName: System.ValueType
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Infrastructure.MeasuringConstraints.Margins*
  commentId: Overload:DrawnUi.Infrastructure.MeasuringConstraints.Margins
  href: DrawnUi.Infrastructure.MeasuringConstraints.html#DrawnUi_Infrastructure_MeasuringConstraints_Margins
  name: Margins
  nameWithType: MeasuringConstraints.Margins
  fullName: DrawnUi.Infrastructure.MeasuringConstraints.Margins
- uid: Microsoft.Maui.Thickness
  commentId: T:Microsoft.Maui.Thickness
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.thickness
  name: Thickness
  nameWithType: Thickness
  fullName: Microsoft.Maui.Thickness
- uid: Microsoft.Maui
  commentId: N:Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft
  name: Microsoft.Maui
  nameWithType: Microsoft.Maui
  fullName: Microsoft.Maui
  spec.csharp:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  spec.vb:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
- uid: DrawnUi.Infrastructure.MeasuringConstraints.TotalMargins*
  commentId: Overload:DrawnUi.Infrastructure.MeasuringConstraints.TotalMargins
  href: DrawnUi.Infrastructure.MeasuringConstraints.html#DrawnUi_Infrastructure_MeasuringConstraints_TotalMargins
  name: TotalMargins
  nameWithType: MeasuringConstraints.TotalMargins
  fullName: DrawnUi.Infrastructure.MeasuringConstraints.TotalMargins
- uid: DrawnUi.Infrastructure.MeasuringConstraints.Request*
  commentId: Overload:DrawnUi.Infrastructure.MeasuringConstraints.Request
  href: DrawnUi.Infrastructure.MeasuringConstraints.html#DrawnUi_Infrastructure_MeasuringConstraints_Request
  name: Request
  nameWithType: MeasuringConstraints.Request
  fullName: DrawnUi.Infrastructure.MeasuringConstraints.Request
- uid: SkiaSharp.SKSize
  commentId: T:SkiaSharp.SKSize
  parent: SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.sksize
  name: SKSize
  nameWithType: SKSize
  fullName: SkiaSharp.SKSize
- uid: SkiaSharp
  commentId: N:SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp
  name: SkiaSharp
  nameWithType: SkiaSharp
  fullName: SkiaSharp
- uid: DrawnUi.Infrastructure.MeasuringConstraints.Content*
  commentId: Overload:DrawnUi.Infrastructure.MeasuringConstraints.Content
  href: DrawnUi.Infrastructure.MeasuringConstraints.html#DrawnUi_Infrastructure_MeasuringConstraints_Content
  name: Content
  nameWithType: MeasuringConstraints.Content
  fullName: DrawnUi.Infrastructure.MeasuringConstraints.Content
- uid: SkiaSharp.SKRect
  commentId: T:SkiaSharp.SKRect
  parent: SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.skrect
  name: SKRect
  nameWithType: SKRect
  fullName: SkiaSharp.SKRect
