### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.BevelType
  commentId: T:DrawnUi.Draw.BevelType
  id: BevelType
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.BevelType.Bevel
  - DrawnUi.Draw.BevelType.Emboss
  - DrawnUi.Draw.BevelType.None
  langs:
  - csharp
  - vb
  name: BevelType
  nameWithType: BevelType
  fullName: DrawnUi.Draw.BevelType
  type: Enum
  source:
    remote:
      path: src/Shared/Draw/SkiaBevel.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: BevelType
    path: ../src/Shared/Draw/SkiaBevel.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public enum BevelType
    content.vb: Public Enum BevelType
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.BevelType.None
  commentId: F:DrawnUi.Draw.BevelType.None
  id: None
  parent: DrawnUi.Draw.BevelType
  langs:
  - csharp
  - vb
  name: None
  nameWithType: BevelType.None
  fullName: DrawnUi.Draw.BevelType.None
  type: Field
  source:
    remote:
      path: src/Shared/Draw/SkiaBevel.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: None
    path: ../src/Shared/Draw/SkiaBevel.cs
    startLine: 7
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: No bevel or emboss effect
  example: []
  syntax:
    content: None = 0
    return:
      type: DrawnUi.Draw.BevelType
- uid: DrawnUi.Draw.BevelType.Bevel
  commentId: F:DrawnUi.Draw.BevelType.Bevel
  id: Bevel
  parent: DrawnUi.Draw.BevelType
  langs:
  - csharp
  - vb
  name: Bevel
  nameWithType: BevelType.Bevel
  fullName: DrawnUi.Draw.BevelType.Bevel
  type: Field
  source:
    remote:
      path: src/Shared/Draw/SkiaBevel.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Bevel
    path: ../src/Shared/Draw/SkiaBevel.cs
    startLine: 12
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Raised effect (light on top/left, shadow on bottom/right)
  example: []
  syntax:
    content: Bevel = 1
    return:
      type: DrawnUi.Draw.BevelType
- uid: DrawnUi.Draw.BevelType.Emboss
  commentId: F:DrawnUi.Draw.BevelType.Emboss
  id: Emboss
  parent: DrawnUi.Draw.BevelType
  langs:
  - csharp
  - vb
  name: Emboss
  nameWithType: BevelType.Emboss
  fullName: DrawnUi.Draw.BevelType.Emboss
  type: Field
  source:
    remote:
      path: src/Shared/Draw/SkiaBevel.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Emboss
    path: ../src/Shared/Draw/SkiaBevel.cs
    startLine: 17
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Pressed in effect (shadow on top/left, light on bottom/right)
  example: []
  syntax:
    content: Emboss = 2
    return:
      type: DrawnUi.Draw.BevelType
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.BevelType
  commentId: T:DrawnUi.Draw.BevelType
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.BevelType.html
  name: BevelType
  nameWithType: BevelType
  fullName: DrawnUi.Draw.BevelType
