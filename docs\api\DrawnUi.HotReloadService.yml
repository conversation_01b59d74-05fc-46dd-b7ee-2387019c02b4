### YamlMime:ManagedReference
items:
- uid: DrawnUi.HotReloadService
  commentId: T:DrawnUi.HotReloadService
  id: HotReloadService
  parent: DrawnUi
  children:
  - DrawnUi.HotReloadService.ClearCache(System.Type[])
  - DrawnUi.HotReloadService.UpdateApplication(System.Type[])
  - DrawnUi.HotReloadService.UpdateApplicationEvent
  langs:
  - csharp
  - vb
  name: HotReloadService
  nameWithType: HotReloadService
  fullName: DrawnUi.HotReloadService
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/HotReload.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: HotReloadService
    path: ../src/Maui/DrawnUi/HotReload.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi
  syntax:
    content: public static class HotReloadService
    content.vb: Public Module HotReloadService
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
- uid: DrawnUi.HotReloadService.UpdateApplicationEvent
  commentId: E:DrawnUi.HotReloadService.UpdateApplicationEvent
  id: UpdateApplicationEvent
  parent: DrawnUi.HotReloadService
  langs:
  - csharp
  - vb
  name: UpdateApplicationEvent
  nameWithType: HotReloadService.UpdateApplicationEvent
  fullName: DrawnUi.HotReloadService.UpdateApplicationEvent
  type: Event
  source:
    remote:
      path: src/Maui/DrawnUi/HotReload.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: UpdateApplicationEvent
    path: ../src/Maui/DrawnUi/HotReload.cs
    startLine: 7
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi
  syntax:
    content: public static event Action<Type[]?>? UpdateApplicationEvent
    return:
      type: System.Action{System.Type[]}
    content.vb: Public Shared Event UpdateApplicationEvent As Action(Of Type())
- uid: DrawnUi.HotReloadService.ClearCache(System.Type[])
  commentId: M:DrawnUi.HotReloadService.ClearCache(System.Type[])
  id: ClearCache(System.Type[])
  parent: DrawnUi.HotReloadService
  langs:
  - csharp
  - vb
  name: ClearCache(Type[]?)
  nameWithType: HotReloadService.ClearCache(Type[]?)
  fullName: DrawnUi.HotReloadService.ClearCache(System.Type[]?)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/HotReload.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ClearCache
    path: ../src/Maui/DrawnUi/HotReload.cs
    startLine: 21
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi
  syntax:
    content: public static void ClearCache(Type[]? types)
    parameters:
    - id: types
      type: System.Type[]
    content.vb: Public Shared Sub ClearCache(types As Type())
  overload: DrawnUi.HotReloadService.ClearCache*
  nameWithType.vb: HotReloadService.ClearCache(Type())
  fullName.vb: DrawnUi.HotReloadService.ClearCache(System.Type())
  name.vb: ClearCache(Type())
- uid: DrawnUi.HotReloadService.UpdateApplication(System.Type[])
  commentId: M:DrawnUi.HotReloadService.UpdateApplication(System.Type[])
  id: UpdateApplication(System.Type[])
  parent: DrawnUi.HotReloadService
  langs:
  - csharp
  - vb
  name: UpdateApplication(Type[]?)
  nameWithType: HotReloadService.UpdateApplication(Type[]?)
  fullName: DrawnUi.HotReloadService.UpdateApplication(System.Type[]?)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/HotReload.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: UpdateApplication
    path: ../src/Maui/DrawnUi/HotReload.cs
    startLine: 29
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi
  syntax:
    content: public static void UpdateApplication(Type[]? types)
    parameters:
    - id: types
      type: System.Type[]
    content.vb: Public Shared Sub UpdateApplication(types As Type())
  overload: DrawnUi.HotReloadService.UpdateApplication*
  nameWithType.vb: HotReloadService.UpdateApplication(Type())
  fullName.vb: DrawnUi.HotReloadService.UpdateApplication(System.Type())
  name.vb: UpdateApplication(Type())
references:
- uid: DrawnUi
  commentId: N:DrawnUi
  href: DrawnUi.html
  name: DrawnUi
  nameWithType: DrawnUi
  fullName: DrawnUi
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: System.Action{System.Type[]}
  commentId: T:System.Action{System.Type[]}
  parent: System
  definition: System.Action`1
  href: https://learn.microsoft.com/dotnet/api/system.action-1
  name: Action<Type[]>
  nameWithType: Action<Type[]>
  fullName: System.Action<System.Type[]>
  nameWithType.vb: Action(Of Type())
  fullName.vb: System.Action(Of System.Type())
  name.vb: Action(Of Type())
  spec.csharp:
  - uid: System.Action`1
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-1
  - name: <
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: '['
  - name: ']'
  - name: '>'
  spec.vb:
  - uid: System.Action`1
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-1
  - name: (
  - name: Of
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: (
  - name: )
  - name: )
- uid: System.Action`1
  commentId: T:System.Action`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.action-1
  name: Action<T>
  nameWithType: Action<T>
  fullName: System.Action<T>
  nameWithType.vb: Action(Of T)
  fullName.vb: System.Action(Of T)
  name.vb: Action(Of T)
  spec.csharp:
  - uid: System.Action`1
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Action`1
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: DrawnUi.HotReloadService.ClearCache*
  commentId: Overload:DrawnUi.HotReloadService.ClearCache
  href: DrawnUi.HotReloadService.html#DrawnUi_HotReloadService_ClearCache_System_Type___
  name: ClearCache
  nameWithType: HotReloadService.ClearCache
  fullName: DrawnUi.HotReloadService.ClearCache
- uid: System.Type[]
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.type
  name: Type[]
  nameWithType: Type[]
  fullName: System.Type[]
  nameWithType.vb: Type()
  fullName.vb: System.Type()
  name.vb: Type()
  spec.csharp:
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: '['
  - name: ']'
  spec.vb:
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: (
  - name: )
- uid: DrawnUi.HotReloadService.UpdateApplication*
  commentId: Overload:DrawnUi.HotReloadService.UpdateApplication
  href: DrawnUi.HotReloadService.html#DrawnUi_HotReloadService_UpdateApplication_System_Type___
  name: UpdateApplication
  nameWithType: HotReloadService.UpdateApplication
  fullName: DrawnUi.HotReloadService.UpdateApplication
