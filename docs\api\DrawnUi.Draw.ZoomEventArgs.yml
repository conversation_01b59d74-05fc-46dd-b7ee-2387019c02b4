### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.ZoomEventArgs
  commentId: T:DrawnUi.Draw.ZoomEventArgs
  id: ZoomEventArgs
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.ZoomEventArgs.#ctor
  - DrawnUi.Draw.ZoomEventArgs.#ctor(Microsoft.Maui.Graphics.PointF,System.Double)
  - DrawnUi.Draw.ZoomEventArgs.Center
  - DrawnUi.Draw.ZoomEventArgs.Value
  langs:
  - csharp
  - vb
  name: ZoomEventArgs
  nameWithType: ZoomEventArgs
  fullName: DrawnUi.Draw.ZoomEventArgs
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Gestures/ZoomEventArgs.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ZoomEventArgs
    path: ../src/Maui/DrawnUi/Features/Gestures/ZoomEventArgs.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: 'public class ZoomEventArgs : EventArgs'
    content.vb: Public Class ZoomEventArgs Inherits EventArgs
  inheritance:
  - System.Object
  - System.EventArgs
  inheritedMembers:
  - System.EventArgs.Empty
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.ZoomEventArgs.#ctor
  commentId: M:DrawnUi.Draw.ZoomEventArgs.#ctor
  id: '#ctor'
  parent: DrawnUi.Draw.ZoomEventArgs
  langs:
  - csharp
  - vb
  name: ZoomEventArgs()
  nameWithType: ZoomEventArgs.ZoomEventArgs()
  fullName: DrawnUi.Draw.ZoomEventArgs.ZoomEventArgs()
  type: Constructor
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Gestures/ZoomEventArgs.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Maui/DrawnUi/Features/Gestures/ZoomEventArgs.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public ZoomEventArgs()
    content.vb: Public Sub New()
  overload: DrawnUi.Draw.ZoomEventArgs.#ctor*
  nameWithType.vb: ZoomEventArgs.New()
  fullName.vb: DrawnUi.Draw.ZoomEventArgs.New()
  name.vb: New()
- uid: DrawnUi.Draw.ZoomEventArgs.#ctor(Microsoft.Maui.Graphics.PointF,System.Double)
  commentId: M:DrawnUi.Draw.ZoomEventArgs.#ctor(Microsoft.Maui.Graphics.PointF,System.Double)
  id: '#ctor(Microsoft.Maui.Graphics.PointF,System.Double)'
  parent: DrawnUi.Draw.ZoomEventArgs
  langs:
  - csharp
  - vb
  name: ZoomEventArgs(PointF, double)
  nameWithType: ZoomEventArgs.ZoomEventArgs(PointF, double)
  fullName: DrawnUi.Draw.ZoomEventArgs.ZoomEventArgs(Microsoft.Maui.Graphics.PointF, double)
  type: Constructor
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Gestures/ZoomEventArgs.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Maui/DrawnUi/Features/Gestures/ZoomEventArgs.cs
    startLine: 7
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public ZoomEventArgs(PointF center, double value)
    parameters:
    - id: center
      type: Microsoft.Maui.Graphics.PointF
    - id: value
      type: System.Double
    content.vb: Public Sub New(center As PointF, value As Double)
  overload: DrawnUi.Draw.ZoomEventArgs.#ctor*
  nameWithType.vb: ZoomEventArgs.New(PointF, Double)
  fullName.vb: DrawnUi.Draw.ZoomEventArgs.New(Microsoft.Maui.Graphics.PointF, Double)
  name.vb: New(PointF, Double)
- uid: DrawnUi.Draw.ZoomEventArgs.Center
  commentId: P:DrawnUi.Draw.ZoomEventArgs.Center
  id: Center
  parent: DrawnUi.Draw.ZoomEventArgs
  langs:
  - csharp
  - vb
  name: Center
  nameWithType: ZoomEventArgs.Center
  fullName: DrawnUi.Draw.ZoomEventArgs.Center
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Gestures/ZoomEventArgs.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Center
    path: ../src/Maui/DrawnUi/Features/Gestures/ZoomEventArgs.cs
    startLine: 13
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public PointF Center { get; set; }
    parameters: []
    return:
      type: Microsoft.Maui.Graphics.PointF
    content.vb: Public Property Center As PointF
  overload: DrawnUi.Draw.ZoomEventArgs.Center*
- uid: DrawnUi.Draw.ZoomEventArgs.Value
  commentId: P:DrawnUi.Draw.ZoomEventArgs.Value
  id: Value
  parent: DrawnUi.Draw.ZoomEventArgs
  langs:
  - csharp
  - vb
  name: Value
  nameWithType: ZoomEventArgs.Value
  fullName: DrawnUi.Draw.ZoomEventArgs.Value
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Gestures/ZoomEventArgs.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Value
    path: ../src/Maui/DrawnUi/Features/Gestures/ZoomEventArgs.cs
    startLine: 14
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public double Value { get; set; }
    parameters: []
    return:
      type: System.Double
    content.vb: Public Property Value As Double
  overload: DrawnUi.Draw.ZoomEventArgs.Value*
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.EventArgs
  commentId: T:System.EventArgs
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.eventargs
  name: EventArgs
  nameWithType: EventArgs
  fullName: System.EventArgs
- uid: System.EventArgs.Empty
  commentId: F:System.EventArgs.Empty
  parent: System.EventArgs
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.eventargs.empty
  name: Empty
  nameWithType: EventArgs.Empty
  fullName: System.EventArgs.Empty
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.ZoomEventArgs.#ctor*
  commentId: Overload:DrawnUi.Draw.ZoomEventArgs.#ctor
  href: DrawnUi.Draw.ZoomEventArgs.html#DrawnUi_Draw_ZoomEventArgs__ctor
  name: ZoomEventArgs
  nameWithType: ZoomEventArgs.ZoomEventArgs
  fullName: DrawnUi.Draw.ZoomEventArgs.ZoomEventArgs
  nameWithType.vb: ZoomEventArgs.New
  fullName.vb: DrawnUi.Draw.ZoomEventArgs.New
  name.vb: New
- uid: Microsoft.Maui.Graphics.PointF
  commentId: T:Microsoft.Maui.Graphics.PointF
  parent: Microsoft.Maui.Graphics
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.pointf
  name: PointF
  nameWithType: PointF
  fullName: Microsoft.Maui.Graphics.PointF
- uid: System.Double
  commentId: T:System.Double
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.double
  name: double
  nameWithType: double
  fullName: double
  nameWithType.vb: Double
  fullName.vb: Double
  name.vb: Double
- uid: Microsoft.Maui.Graphics
  commentId: N:Microsoft.Maui.Graphics
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft
  name: Microsoft.Maui.Graphics
  nameWithType: Microsoft.Maui.Graphics
  fullName: Microsoft.Maui.Graphics
  spec.csharp:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Graphics
    name: Graphics
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics
  spec.vb:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Graphics
    name: Graphics
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics
- uid: DrawnUi.Draw.ZoomEventArgs.Center*
  commentId: Overload:DrawnUi.Draw.ZoomEventArgs.Center
  href: DrawnUi.Draw.ZoomEventArgs.html#DrawnUi_Draw_ZoomEventArgs_Center
  name: Center
  nameWithType: ZoomEventArgs.Center
  fullName: DrawnUi.Draw.ZoomEventArgs.Center
- uid: DrawnUi.Draw.ZoomEventArgs.Value*
  commentId: Overload:DrawnUi.Draw.ZoomEventArgs.Value
  href: DrawnUi.Draw.ZoomEventArgs.html#DrawnUi_Draw_ZoomEventArgs_Value
  name: Value
  nameWithType: ZoomEventArgs.Value
  fullName: DrawnUi.Draw.ZoomEventArgs.Value
