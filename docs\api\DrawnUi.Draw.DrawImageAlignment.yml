### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.DrawImageAlignment
  commentId: T:DrawnUi.Draw.DrawImageAlignment
  id: DrawImageAlignment
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.DrawImageAlignment.Center
  - DrawnUi.Draw.DrawImageAlignment.End
  - DrawnUi.Draw.DrawImageAlignment.Start
  langs:
  - csharp
  - vb
  name: DrawImageAlignment
  nameWithType: DrawImageAlignment
  fullName: DrawnUi.Draw.DrawImageAlignment
  type: Enum
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/DrawImageAlignment.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: DrawImageAlignment
    path: ../src/Shared/Draw/Internals/Enums/DrawImageAlignment.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public enum DrawImageAlignment
    content.vb: Public Enum DrawImageAlignment
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.DrawImageAlignment.Start
  commentId: F:DrawnUi.Draw.DrawImageAlignment.Start
  id: Start
  parent: DrawnUi.Draw.DrawImageAlignment
  langs:
  - csharp
  - vb
  name: Start
  nameWithType: DrawImageAlignment.Start
  fullName: DrawnUi.Draw.DrawImageAlignment.Start
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/DrawImageAlignment.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Start
    path: ../src/Shared/Draw/Internals/Enums/DrawImageAlignment.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: Start = 0
    return:
      type: DrawnUi.Draw.DrawImageAlignment
- uid: DrawnUi.Draw.DrawImageAlignment.Center
  commentId: F:DrawnUi.Draw.DrawImageAlignment.Center
  id: Center
  parent: DrawnUi.Draw.DrawImageAlignment
  langs:
  - csharp
  - vb
  name: Center
  nameWithType: DrawImageAlignment.Center
  fullName: DrawnUi.Draw.DrawImageAlignment.Center
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/DrawImageAlignment.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Center
    path: ../src/Shared/Draw/Internals/Enums/DrawImageAlignment.cs
    startLine: 5
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: Center = 1
    return:
      type: DrawnUi.Draw.DrawImageAlignment
- uid: DrawnUi.Draw.DrawImageAlignment.End
  commentId: F:DrawnUi.Draw.DrawImageAlignment.End
  id: End
  parent: DrawnUi.Draw.DrawImageAlignment
  langs:
  - csharp
  - vb
  name: End
  nameWithType: DrawImageAlignment.End
  fullName: DrawnUi.Draw.DrawImageAlignment.End
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/DrawImageAlignment.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: End
    path: ../src/Shared/Draw/Internals/Enums/DrawImageAlignment.cs
    startLine: 6
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: End = 2
    return:
      type: DrawnUi.Draw.DrawImageAlignment
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.DrawImageAlignment
  commentId: T:DrawnUi.Draw.DrawImageAlignment
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.DrawImageAlignment.html
  name: DrawImageAlignment
  nameWithType: DrawImageAlignment
  fullName: DrawnUi.Draw.DrawImageAlignment
