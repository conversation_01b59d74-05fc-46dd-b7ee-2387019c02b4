<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <title>Interface ISkiaGridLayout | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width">
      <meta name="title" content="Interface ISkiaGridLayout | DrawnUi Documentation ">
    
    
      <link rel="shortcut icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../styles/docfx.vendor.min.css">
      <link rel="stylesheet" href="../styles/docfx.css">
      <link rel="stylesheet" href="../styles/main.css">
      <meta property="docfx:navrel" content="../toc.html">
      <meta property="docfx:tocrel" content="toc.html">
    
    <meta property="docfx:rel" content="../">
    
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first=First data-prev=Previous data-next=Next data-last=Last></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="DrawnUi.Draw.ISkiaGridLayout">



  <h1 id="DrawnUi_Draw_ISkiaGridLayout" data-uid="DrawnUi.Draw.ISkiaGridLayout" class="text-break">Interface ISkiaGridLayout</h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritedMembers">
    <h5>Inherited Members</h5>
    <div>
      <a class="xref" href="DrawnUi.Draw.ISkiaLayout.html#DrawnUi_Draw_ISkiaLayout_NeedAutoSize">ISkiaLayout.NeedAutoSize</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.ISkiaLayout.html#DrawnUi_Draw_ISkiaLayout_NeedAutoHeight">ISkiaLayout.NeedAutoHeight</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.ISkiaLayout.html#DrawnUi_Draw_ISkiaLayout_NeedAutoWidth">ISkiaLayout.NeedAutoWidth</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.ISkiaControl.html#DrawnUi_Draw_ISkiaControl_VisualLayer">ISkiaControl.VisualLayer</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.ISkiaControl.html#DrawnUi_Draw_ISkiaControl_Parent">ISkiaControl.Parent</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.ISkiaControl.html#DrawnUi_Draw_ISkiaControl_Margin">ISkiaControl.Margin</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.ISkiaControl.html#DrawnUi_Draw_ISkiaControl_Padding">ISkiaControl.Padding</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.ISkiaControl.html#DrawnUi_Draw_ISkiaControl_SetParent_DrawnUi_Draw_IDrawnBase_">ISkiaControl.SetParent(IDrawnBase)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.ISkiaControl.html#DrawnUi_Draw_ISkiaControl_IsGhost">ISkiaControl.IsGhost</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.ISkiaControl.html#DrawnUi_Draw_ISkiaControl_Clipping">ISkiaControl.Clipping</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.ISkiaControl.html#DrawnUi_Draw_ISkiaControl_ZIndex">ISkiaControl.ZIndex</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.ISkiaControl.html#DrawnUi_Draw_ISkiaControl_OptionalOnBeforeDrawing">ISkiaControl.OptionalOnBeforeDrawing()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.ISkiaControl.html#DrawnUi_Draw_ISkiaControl_OnBeforeMeasure">ISkiaControl.OnBeforeMeasure()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.ISkiaControl.html#DrawnUi_Draw_ISkiaControl_Arrange_SkiaSharp_SKRect_System_Single_System_Single_System_Single_">ISkiaControl.Arrange(SKRect, float, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.ISkiaControl.html#DrawnUi_Draw_ISkiaControl_Render_DrawnUi_Draw_DrawingContext_">ISkiaControl.Render(DrawingContext)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.ISkiaControl.html#DrawnUi_Draw_ISkiaControl_RenderedAtDestination">ISkiaControl.RenderedAtDestination</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.ISkiaControl.html#DrawnUi_Draw_ISkiaControl_SetChildren_System_Collections_Generic_IEnumerable_DrawnUi_Draw_SkiaControl__">ISkiaControl.SetChildren(IEnumerable&lt;SkiaControl&gt;)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.ISkiaControl.html#DrawnUi_Draw_ISkiaControl_Measure_System_Single_System_Single_System_Single_">ISkiaControl.Measure(float, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.ISkiaControl.html#DrawnUi_Draw_ISkiaControl_HorizontalOptions">ISkiaControl.HorizontalOptions</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.ISkiaControl.html#DrawnUi_Draw_ISkiaControl_VerticalOptions">ISkiaControl.VerticalOptions</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.ISkiaControl.html#DrawnUi_Draw_ISkiaControl_CanDraw">ISkiaControl.CanDraw</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_DrawingRect">IDrawnBase.DrawingRect</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_Tag">IDrawnBase.Tag</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_IsVisible">IDrawnBase.IsVisible</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_IsDisposed">IDrawnBase.IsDisposed</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_IsDisposing">IDrawnBase.IsDisposing</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_IsVisibleInViewTree">IDrawnBase.IsVisibleInViewTree()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_GetOnScreenVisibleArea_DrawnUi_Draw_DrawingContext_System_Numerics_Vector2_">IDrawnBase.GetOnScreenVisibleArea(DrawingContext, Vector2)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_Invalidate">IDrawnBase.Invalidate()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_InvalidateParents">IDrawnBase.InvalidateParents()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_ClipSmart_SkiaSharp_SKCanvas_SkiaSharp_SKPath_SkiaSharp_SKClipOperation_">IDrawnBase.ClipSmart(SKCanvas, SKPath, SKClipOperation)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_CreateClip_System_Object_System_Boolean_SkiaSharp_SKPath_">IDrawnBase.CreateClip(object, bool, SKPath)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_RegisterAnimator_DrawnUi_Draw_ISkiaAnimator_">IDrawnBase.RegisterAnimator(ISkiaAnimator)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_UnregisterAnimator_System_Guid_">IDrawnBase.UnregisterAnimator(Guid)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_UnregisterAllAnimatorsByType_System_Type_">IDrawnBase.UnregisterAllAnimatorsByType(Type)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_RegisterGestureListener_DrawnUi_Draw_ISkiaGestureListener_">IDrawnBase.RegisterGestureListener(ISkiaGestureListener)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_UnregisterGestureListener_DrawnUi_Draw_ISkiaGestureListener_">IDrawnBase.UnregisterGestureListener(ISkiaGestureListener)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_PostAnimators">IDrawnBase.PostAnimators</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_Views">IDrawnBase.Views</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_AddSubView_DrawnUi_Draw_SkiaControl_">IDrawnBase.AddSubView(SkiaControl)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_RemoveSubView_DrawnUi_Draw_SkiaControl_">IDrawnBase.RemoveSubView(SkiaControl)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_MeasuredSize">IDrawnBase.MeasuredSize</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_Destination">IDrawnBase.Destination</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_HeightRequest">IDrawnBase.HeightRequest</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_WidthRequest">IDrawnBase.WidthRequest</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_Height">IDrawnBase.Height</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_Width">IDrawnBase.Width</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_TranslationX">IDrawnBase.TranslationX</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_TranslationY">IDrawnBase.TranslationY</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_InputTransparent">IDrawnBase.InputTransparent</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_IsClippedToBounds">IDrawnBase.IsClippedToBounds</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_ClipEffects">IDrawnBase.ClipEffects</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_UpdateLocks">IDrawnBase.UpdateLocks</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_InvalidateByChild_DrawnUi_Draw_SkiaControl_">IDrawnBase.InvalidateByChild(SkiaControl)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_UpdateByChild_DrawnUi_Draw_SkiaControl_">IDrawnBase.UpdateByChild(SkiaControl)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_ShouldInvalidateByChildren">IDrawnBase.ShouldInvalidateByChildren</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_RenderingScale">IDrawnBase.RenderingScale</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_X">IDrawnBase.X</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_Y">IDrawnBase.Y</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_InvalidateViewport">IDrawnBase.InvalidateViewport()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_Repaint">IDrawnBase.Repaint()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_InvalidateViewsList">IDrawnBase.InvalidateViewsList()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_DisposeObject_System_IDisposable_">IDrawnBase.DisposeObject(IDisposable)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.ICanBeUpdatedWithContext.html#DrawnUi_Draw_ICanBeUpdatedWithContext_BindingContext">ICanBeUpdatedWithContext.BindingContext</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.ICanBeUpdated.html#DrawnUi_Draw_ICanBeUpdated_Update">ICanBeUpdated.Update()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.ILayoutInsideViewport.html#DrawnUi_Draw_ILayoutInsideViewport_GetVisibleChildIndexAt_SkiaSharp_SKPoint_">ILayoutInsideViewport.GetVisibleChildIndexAt(SKPoint)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.ILayoutInsideViewport.html#DrawnUi_Draw_ILayoutInsideViewport_GetChildIndexAt_SkiaSharp_SKPoint_">ILayoutInsideViewport.GetChildIndexAt(SKPoint)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IInsideViewport.html#DrawnUi_Draw_IInsideViewport_OnViewportWasChanged_DrawnUi_Draw_ScaledRect_">IInsideViewport.OnViewportWasChanged(ScaledRect)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IInsideViewport.html#DrawnUi_Draw_IInsideViewport_OnLoaded">IInsideViewport.OnLoaded()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IVisibilityAware.html#DrawnUi_Draw_IVisibilityAware_OnAppearing">IVisibilityAware.OnAppearing()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IVisibilityAware.html#DrawnUi_Draw_IVisibilityAware_OnAppeared">IVisibilityAware.OnAppeared()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IVisibilityAware.html#DrawnUi_Draw_IVisibilityAware_OnDisappeared">IVisibilityAware.OnDisappeared()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IVisibilityAware.html#DrawnUi_Draw_IVisibilityAware_OnDisappearing">IVisibilityAware.OnDisappearing()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.idisposable.dispose">IDisposable.Dispose()</a>
    </div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="DrawnUi.html">DrawnUi</a>.<a class="xref" href="DrawnUi.Draw.html">Draw</a></h6>
  <h6><strong>Assembly</strong>: DrawnUi.Maui.dll</h6>
  <h5 id="DrawnUi_Draw_ISkiaGridLayout_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public interface ISkiaGridLayout : ISkiaLayout, ISkiaControl, IDrawnBase, ICanBeUpdatedWithContext, ICanBeUpdated, ILayoutInsideViewport, IInsideViewport, IVisibilityAware, IDisposable</code></pre>
  </div>
  <h3 id="properties">Properties
</h3>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_ISkiaGridLayout_ColumnDefinitions.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.ISkiaGridLayout.ColumnDefinitions%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Interfaces/ISkiaGridLayout.cs/#L18">View Source</a>
  </span>
  <a id="DrawnUi_Draw_ISkiaGridLayout_ColumnDefinitions_" data-uid="DrawnUi.Draw.ISkiaGridLayout.ColumnDefinitions*"></a>
  <h4 id="DrawnUi_Draw_ISkiaGridLayout_ColumnDefinitions" data-uid="DrawnUi.Draw.ISkiaGridLayout.ColumnDefinitions">ColumnDefinitions</h4>
  <div class="markdown level1 summary"><p>An IGridColumnDefinition collection for the GridLayout instance.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">IReadOnlyList&lt;IGridColumnDefinition&gt; ColumnDefinitions { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlylist-1">IReadOnlyList</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.igridcolumndefinition">IGridColumnDefinition</a>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_ISkiaGridLayout_ColumnSpacing.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.ISkiaGridLayout.ColumnSpacing%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Interfaces/ISkiaGridLayout.cs/#L28">View Source</a>
  </span>
  <a id="DrawnUi_Draw_ISkiaGridLayout_ColumnSpacing_" data-uid="DrawnUi.Draw.ISkiaGridLayout.ColumnSpacing*"></a>
  <h4 id="DrawnUi_Draw_ISkiaGridLayout_ColumnSpacing" data-uid="DrawnUi.Draw.ISkiaGridLayout.ColumnSpacing">ColumnSpacing</h4>
  <div class="markdown level1 summary"><p>Gets the amount of space left between columns in the GridLayout.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">double ColumnSpacing { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_ISkiaGridLayout_DefaultColumnDefinition.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.ISkiaGridLayout.DefaultColumnDefinition%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Interfaces/ISkiaGridLayout.cs/#L6">View Source</a>
  </span>
  <a id="DrawnUi_Draw_ISkiaGridLayout_DefaultColumnDefinition_" data-uid="DrawnUi.Draw.ISkiaGridLayout.DefaultColumnDefinition*"></a>
  <h4 id="DrawnUi_Draw_ISkiaGridLayout_DefaultColumnDefinition" data-uid="DrawnUi.Draw.ISkiaGridLayout.DefaultColumnDefinition">DefaultColumnDefinition</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">ColumnDefinition DefaultColumnDefinition { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.columndefinition">ColumnDefinition</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_ISkiaGridLayout_DefaultRowDefinition.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.ISkiaGridLayout.DefaultRowDefinition%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Interfaces/ISkiaGridLayout.cs/#L8">View Source</a>
  </span>
  <a id="DrawnUi_Draw_ISkiaGridLayout_DefaultRowDefinition_" data-uid="DrawnUi.Draw.ISkiaGridLayout.DefaultRowDefinition*"></a>
  <h4 id="DrawnUi_Draw_ISkiaGridLayout_DefaultRowDefinition" data-uid="DrawnUi.Draw.ISkiaGridLayout.DefaultRowDefinition">DefaultRowDefinition</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">RowDefinition DefaultRowDefinition { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.rowdefinition">RowDefinition</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_ISkiaGridLayout_RowDefinitions.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.ISkiaGridLayout.RowDefinitions%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Interfaces/ISkiaGridLayout.cs/#L13">View Source</a>
  </span>
  <a id="DrawnUi_Draw_ISkiaGridLayout_RowDefinitions_" data-uid="DrawnUi.Draw.ISkiaGridLayout.RowDefinitions*"></a>
  <h4 id="DrawnUi_Draw_ISkiaGridLayout_RowDefinitions" data-uid="DrawnUi.Draw.ISkiaGridLayout.RowDefinitions">RowDefinitions</h4>
  <div class="markdown level1 summary"><p>An IGridRowDefinition collection for the GridLayout instance.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">IReadOnlyList&lt;IGridRowDefinition&gt; RowDefinitions { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlylist-1">IReadOnlyList</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.igridrowdefinition">IGridRowDefinition</a>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_ISkiaGridLayout_RowSpacing.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.ISkiaGridLayout.RowSpacing%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Interfaces/ISkiaGridLayout.cs/#L23">View Source</a>
  </span>
  <a id="DrawnUi_Draw_ISkiaGridLayout_RowSpacing_" data-uid="DrawnUi.Draw.ISkiaGridLayout.RowSpacing*"></a>
  <h4 id="DrawnUi_Draw_ISkiaGridLayout_RowSpacing" data-uid="DrawnUi.Draw.ISkiaGridLayout.RowSpacing">RowSpacing</h4>
  <div class="markdown level1 summary"><p>Gets the amount of space left between rows in the GridLayout.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">double RowSpacing { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
</h3>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_ISkiaGridLayout_GetColumn_Microsoft_Maui_Controls_BindableObject_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.ISkiaGridLayout.GetColumn(Microsoft.Maui.Controls.BindableObject)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Interfaces/ISkiaGridLayout.cs/#L49">View Source</a>
  </span>
  <a id="DrawnUi_Draw_ISkiaGridLayout_GetColumn_" data-uid="DrawnUi.Draw.ISkiaGridLayout.GetColumn*"></a>
  <h4 id="DrawnUi_Draw_ISkiaGridLayout_GetColumn_Microsoft_Maui_Controls_BindableObject_" data-uid="DrawnUi.Draw.ISkiaGridLayout.GetColumn(Microsoft.Maui.Controls.BindableObject)">GetColumn(BindableObject)</h4>
  <div class="markdown level1 summary"><p>Gets the column of the child element.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">int GetColumn(BindableObject view)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a></td>
        <td><span class="parametername">view</span></td>
        <td><p>A view that belongs to the Grid layout.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></td>
        <td><p>The column that the child element is in.</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_ISkiaGridLayout_GetColumnSpan_Microsoft_Maui_Controls_BindableObject_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.ISkiaGridLayout.GetColumnSpan(Microsoft.Maui.Controls.BindableObject)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Interfaces/ISkiaGridLayout.cs/#L56">View Source</a>
  </span>
  <a id="DrawnUi_Draw_ISkiaGridLayout_GetColumnSpan_" data-uid="DrawnUi.Draw.ISkiaGridLayout.GetColumnSpan*"></a>
  <h4 id="DrawnUi_Draw_ISkiaGridLayout_GetColumnSpan_Microsoft_Maui_Controls_BindableObject_" data-uid="DrawnUi.Draw.ISkiaGridLayout.GetColumnSpan(Microsoft.Maui.Controls.BindableObject)">GetColumnSpan(BindableObject)</h4>
  <div class="markdown level1 summary"><p>Gets the row span of the child element.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">int GetColumnSpan(BindableObject view)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a></td>
        <td><span class="parametername">view</span></td>
        <td><p>A view that belongs to the Grid layout.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></td>
        <td><p>The row that the child element is in.</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_ISkiaGridLayout_GetRow_Microsoft_Maui_Controls_BindableObject_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.ISkiaGridLayout.GetRow(Microsoft.Maui.Controls.BindableObject)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Interfaces/ISkiaGridLayout.cs/#L35">View Source</a>
  </span>
  <a id="DrawnUi_Draw_ISkiaGridLayout_GetRow_" data-uid="DrawnUi.Draw.ISkiaGridLayout.GetRow*"></a>
  <h4 id="DrawnUi_Draw_ISkiaGridLayout_GetRow_Microsoft_Maui_Controls_BindableObject_" data-uid="DrawnUi.Draw.ISkiaGridLayout.GetRow(Microsoft.Maui.Controls.BindableObject)">GetRow(BindableObject)</h4>
  <div class="markdown level1 summary"><p>Gets the row of the child element.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">int GetRow(BindableObject view)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a></td>
        <td><span class="parametername">view</span></td>
        <td><p>A view that belongs to the Grid layout.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></td>
        <td><p>An integer that represents the row in which the item will appear.</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_ISkiaGridLayout_GetRowSpan_Microsoft_Maui_Controls_BindableObject_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.ISkiaGridLayout.GetRowSpan(Microsoft.Maui.Controls.BindableObject)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Interfaces/ISkiaGridLayout.cs/#L42">View Source</a>
  </span>
  <a id="DrawnUi_Draw_ISkiaGridLayout_GetRowSpan_" data-uid="DrawnUi.Draw.ISkiaGridLayout.GetRowSpan*"></a>
  <h4 id="DrawnUi_Draw_ISkiaGridLayout_GetRowSpan_Microsoft_Maui_Controls_BindableObject_" data-uid="DrawnUi.Draw.ISkiaGridLayout.GetRowSpan(Microsoft.Maui.Controls.BindableObject)">GetRowSpan(BindableObject)</h4>
  <div class="markdown level1 summary"><p>Gets the row span of the child element.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">int GetRowSpan(BindableObject view)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a></td>
        <td><span class="parametername">view</span></td>
        <td><p>A view that belongs to the Grid layout.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></td>
        <td><p>The row that the child element is in.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h3 id="extensionmethods">Extension Methods</h3>
  <div>
      <a class="xref" href="DrawnUi.Draw.DrawnExtensions.html#DrawnUi_Draw_DrawnExtensions_GetVelocityRatioForChild_DrawnUi_Draw_IDrawnBase_DrawnUi_Draw_ISkiaControl_">DrawnExtensions.GetVelocityRatioForChild(IDrawnBase, ISkiaControl)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_">InternalExtensions.FromPlatform(object)</a>
  </div>

</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_ISkiaGridLayout.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.ISkiaGridLayout%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A" class="contribution-link">Edit this page</a>
                  </li>
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Interfaces/ISkiaGridLayout.cs/#L3" class="contribution-link">View Source</a>
                  </li>
                </ul>
              </div>
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In this article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      
      <span>Generated by <strong>DocFX</strong></span>
          </div>
        </div>
      </footer>
    </div>

    <script type="text/javascript" src="../styles/docfx.vendor.min.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
