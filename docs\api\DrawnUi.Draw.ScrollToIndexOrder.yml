### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.ScrollToIndexOrder
  commentId: T:DrawnUi.Draw.ScrollToIndexOrder
  id: ScrollToIndexOrder
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.ScrollToIndexOrder.Animated
  - DrawnUi.Draw.ScrollToIndexOrder.Default
  - DrawnUi.Draw.ScrollToIndexOrder.Index
  - DrawnUi.Draw.ScrollToIndexOrder.IsSet
  - DrawnUi.Draw.ScrollToIndexOrder.MaxTimeSecs
  - DrawnUi.Draw.ScrollToIndexOrder.RelativePosition
  langs:
  - csharp
  - vb
  name: ScrollToIndexOrder
  nameWithType: ScrollToIndexOrder
  fullName: DrawnUi.Draw.ScrollToIndexOrder
  type: Struct
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Scroll/ScrollToIndexOrder.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ScrollToIndexOrder
    path: ../src/Maui/DrawnUi/Draw/Scroll/ScrollToIndexOrder.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public struct ScrollToIndexOrder
    content.vb: Public Structure ScrollToIndexOrder
  inheritedMembers:
  - System.ValueType.Equals(System.Object)
  - System.ValueType.GetHashCode
  - System.ValueType.ToString
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetType
  - System.Object.ReferenceEquals(System.Object,System.Object)
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.ScrollToIndexOrder.Default
  commentId: P:DrawnUi.Draw.ScrollToIndexOrder.Default
  id: Default
  parent: DrawnUi.Draw.ScrollToIndexOrder
  langs:
  - csharp
  - vb
  name: Default
  nameWithType: ScrollToIndexOrder.Default
  fullName: DrawnUi.Draw.ScrollToIndexOrder.Default
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Scroll/ScrollToIndexOrder.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Default
    path: ../src/Maui/DrawnUi/Draw/Scroll/ScrollToIndexOrder.cs
    startLine: 6
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static ScrollToIndexOrder Default { get; }
    parameters: []
    return:
      type: DrawnUi.Draw.ScrollToIndexOrder
    content.vb: Public Shared ReadOnly Property [Default] As ScrollToIndexOrder
  overload: DrawnUi.Draw.ScrollToIndexOrder.Default*
- uid: DrawnUi.Draw.ScrollToIndexOrder.IsSet
  commentId: P:DrawnUi.Draw.ScrollToIndexOrder.IsSet
  id: IsSet
  parent: DrawnUi.Draw.ScrollToIndexOrder
  langs:
  - csharp
  - vb
  name: IsSet
  nameWithType: ScrollToIndexOrder.IsSet
  fullName: DrawnUi.Draw.ScrollToIndexOrder.IsSet
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Scroll/ScrollToIndexOrder.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IsSet
    path: ../src/Maui/DrawnUi/Draw/Scroll/ScrollToIndexOrder.cs
    startLine: 10
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public bool IsSet { get; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public ReadOnly Property IsSet As Boolean
  overload: DrawnUi.Draw.ScrollToIndexOrder.IsSet*
- uid: DrawnUi.Draw.ScrollToIndexOrder.Animated
  commentId: P:DrawnUi.Draw.ScrollToIndexOrder.Animated
  id: Animated
  parent: DrawnUi.Draw.ScrollToIndexOrder
  langs:
  - csharp
  - vb
  name: Animated
  nameWithType: ScrollToIndexOrder.Animated
  fullName: DrawnUi.Draw.ScrollToIndexOrder.Animated
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Scroll/ScrollToIndexOrder.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Animated
    path: ../src/Maui/DrawnUi/Draw/Scroll/ScrollToIndexOrder.cs
    startLine: 17
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public bool Animated { readonly get; set; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property Animated As Boolean
  overload: DrawnUi.Draw.ScrollToIndexOrder.Animated*
- uid: DrawnUi.Draw.ScrollToIndexOrder.MaxTimeSecs
  commentId: P:DrawnUi.Draw.ScrollToIndexOrder.MaxTimeSecs
  id: MaxTimeSecs
  parent: DrawnUi.Draw.ScrollToIndexOrder
  langs:
  - csharp
  - vb
  name: MaxTimeSecs
  nameWithType: ScrollToIndexOrder.MaxTimeSecs
  fullName: DrawnUi.Draw.ScrollToIndexOrder.MaxTimeSecs
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Scroll/ScrollToIndexOrder.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: MaxTimeSecs
    path: ../src/Maui/DrawnUi/Draw/Scroll/ScrollToIndexOrder.cs
    startLine: 18
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public float MaxTimeSecs { readonly get; set; }
    parameters: []
    return:
      type: System.Single
    content.vb: Public Property MaxTimeSecs As Single
  overload: DrawnUi.Draw.ScrollToIndexOrder.MaxTimeSecs*
- uid: DrawnUi.Draw.ScrollToIndexOrder.RelativePosition
  commentId: P:DrawnUi.Draw.ScrollToIndexOrder.RelativePosition
  id: RelativePosition
  parent: DrawnUi.Draw.ScrollToIndexOrder
  langs:
  - csharp
  - vb
  name: RelativePosition
  nameWithType: ScrollToIndexOrder.RelativePosition
  fullName: DrawnUi.Draw.ScrollToIndexOrder.RelativePosition
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Scroll/ScrollToIndexOrder.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: RelativePosition
    path: ../src/Maui/DrawnUi/Draw/Scroll/ScrollToIndexOrder.cs
    startLine: 19
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public RelativePositionType RelativePosition { readonly get; set; }
    parameters: []
    return:
      type: DrawnUi.Draw.RelativePositionType
    content.vb: Public Property RelativePosition As RelativePositionType
  overload: DrawnUi.Draw.ScrollToIndexOrder.RelativePosition*
- uid: DrawnUi.Draw.ScrollToIndexOrder.Index
  commentId: P:DrawnUi.Draw.ScrollToIndexOrder.Index
  id: Index
  parent: DrawnUi.Draw.ScrollToIndexOrder
  langs:
  - csharp
  - vb
  name: Index
  nameWithType: ScrollToIndexOrder.Index
  fullName: DrawnUi.Draw.ScrollToIndexOrder.Index
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Scroll/ScrollToIndexOrder.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Index
    path: ../src/Maui/DrawnUi/Draw/Scroll/ScrollToIndexOrder.cs
    startLine: 20
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public int Index { readonly get; set; }
    parameters: []
    return:
      type: System.Int32
    content.vb: Public Property Index As Integer
  overload: DrawnUi.Draw.ScrollToIndexOrder.Index*
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.ValueType.Equals(System.Object)
  commentId: M:System.ValueType.Equals(System.Object)
  parent: System.ValueType
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype.equals
  name: Equals(object)
  nameWithType: ValueType.Equals(object)
  fullName: System.ValueType.Equals(object)
  nameWithType.vb: ValueType.Equals(Object)
  fullName.vb: System.ValueType.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.ValueType.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.equals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.ValueType.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.equals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.ValueType.GetHashCode
  commentId: M:System.ValueType.GetHashCode
  parent: System.ValueType
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype.gethashcode
  name: GetHashCode()
  nameWithType: ValueType.GetHashCode()
  fullName: System.ValueType.GetHashCode()
  spec.csharp:
  - uid: System.ValueType.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.ValueType.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.gethashcode
  - name: (
  - name: )
- uid: System.ValueType.ToString
  commentId: M:System.ValueType.ToString
  parent: System.ValueType
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype.tostring
  name: ToString()
  nameWithType: ValueType.ToString()
  fullName: System.ValueType.ToString()
  spec.csharp:
  - uid: System.ValueType.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.ValueType.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.tostring
  - name: (
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.ValueType
  commentId: T:System.ValueType
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype
  name: ValueType
  nameWithType: ValueType
  fullName: System.ValueType
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.ScrollToIndexOrder.Default*
  commentId: Overload:DrawnUi.Draw.ScrollToIndexOrder.Default
  href: DrawnUi.Draw.ScrollToIndexOrder.html#DrawnUi_Draw_ScrollToIndexOrder_Default
  name: Default
  nameWithType: ScrollToIndexOrder.Default
  fullName: DrawnUi.Draw.ScrollToIndexOrder.Default
- uid: DrawnUi.Draw.ScrollToIndexOrder
  commentId: T:DrawnUi.Draw.ScrollToIndexOrder
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ScrollToIndexOrder.html
  name: ScrollToIndexOrder
  nameWithType: ScrollToIndexOrder
  fullName: DrawnUi.Draw.ScrollToIndexOrder
- uid: DrawnUi.Draw.ScrollToIndexOrder.IsSet*
  commentId: Overload:DrawnUi.Draw.ScrollToIndexOrder.IsSet
  href: DrawnUi.Draw.ScrollToIndexOrder.html#DrawnUi_Draw_ScrollToIndexOrder_IsSet
  name: IsSet
  nameWithType: ScrollToIndexOrder.IsSet
  fullName: DrawnUi.Draw.ScrollToIndexOrder.IsSet
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: DrawnUi.Draw.ScrollToIndexOrder.Animated*
  commentId: Overload:DrawnUi.Draw.ScrollToIndexOrder.Animated
  href: DrawnUi.Draw.ScrollToIndexOrder.html#DrawnUi_Draw_ScrollToIndexOrder_Animated
  name: Animated
  nameWithType: ScrollToIndexOrder.Animated
  fullName: DrawnUi.Draw.ScrollToIndexOrder.Animated
- uid: DrawnUi.Draw.ScrollToIndexOrder.MaxTimeSecs*
  commentId: Overload:DrawnUi.Draw.ScrollToIndexOrder.MaxTimeSecs
  href: DrawnUi.Draw.ScrollToIndexOrder.html#DrawnUi_Draw_ScrollToIndexOrder_MaxTimeSecs
  name: MaxTimeSecs
  nameWithType: ScrollToIndexOrder.MaxTimeSecs
  fullName: DrawnUi.Draw.ScrollToIndexOrder.MaxTimeSecs
- uid: System.Single
  commentId: T:System.Single
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.single
  name: float
  nameWithType: float
  fullName: float
  nameWithType.vb: Single
  fullName.vb: Single
  name.vb: Single
- uid: DrawnUi.Draw.ScrollToIndexOrder.RelativePosition*
  commentId: Overload:DrawnUi.Draw.ScrollToIndexOrder.RelativePosition
  href: DrawnUi.Draw.ScrollToIndexOrder.html#DrawnUi_Draw_ScrollToIndexOrder_RelativePosition
  name: RelativePosition
  nameWithType: ScrollToIndexOrder.RelativePosition
  fullName: DrawnUi.Draw.ScrollToIndexOrder.RelativePosition
- uid: DrawnUi.Draw.RelativePositionType
  commentId: T:DrawnUi.Draw.RelativePositionType
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.RelativePositionType.html
  name: RelativePositionType
  nameWithType: RelativePositionType
  fullName: DrawnUi.Draw.RelativePositionType
- uid: DrawnUi.Draw.ScrollToIndexOrder.Index*
  commentId: Overload:DrawnUi.Draw.ScrollToIndexOrder.Index
  href: DrawnUi.Draw.ScrollToIndexOrder.html#DrawnUi_Draw_ScrollToIndexOrder_Index
  name: Index
  nameWithType: ScrollToIndexOrder.Index
  fullName: DrawnUi.Draw.ScrollToIndexOrder.Index
- uid: System.Int32
  commentId: T:System.Int32
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.int32
  name: int
  nameWithType: int
  fullName: int
  nameWithType.vb: Integer
  fullName.vb: Integer
  name.vb: Integer
