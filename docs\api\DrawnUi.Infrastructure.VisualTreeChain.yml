### YamlMime:ManagedReference
items:
- uid: DrawnUi.Infrastructure.VisualTreeChain
  commentId: T:DrawnUi.Infrastructure.VisualTreeChain
  id: VisualTreeChain
  parent: DrawnUi.Infrastructure
  children:
  - DrawnUi.Infrastructure.VisualTreeChain.#ctor(DrawnUi.Draw.SkiaControl)
  - DrawnUi.Infrastructure.VisualTreeChain.AddNode(DrawnUi.Draw.SkiaControl)
  - DrawnUi.Infrastructure.VisualTreeChain.Child
  - DrawnUi.Infrastructure.VisualTreeChain.NodeIndices
  - DrawnUi.Infrastructure.VisualTreeChain.Nodes
  - DrawnUi.Infrastructure.VisualTreeChain.Transform
  langs:
  - csharp
  - vb
  name: VisualTreeChain
  nameWithType: VisualTreeChain
  fullName: DrawnUi.Infrastructure.VisualTreeChain
  type: Class
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/VisualTreeChain.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: VisualTreeChain
    path: ../src/Shared/Draw/Internals/Models/VisualTreeChain.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public class VisualTreeChain
    content.vb: Public Class VisualTreeChain
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Infrastructure.VisualTreeChain.#ctor(DrawnUi.Draw.SkiaControl)
  commentId: M:DrawnUi.Infrastructure.VisualTreeChain.#ctor(DrawnUi.Draw.SkiaControl)
  id: '#ctor(DrawnUi.Draw.SkiaControl)'
  parent: DrawnUi.Infrastructure.VisualTreeChain
  langs:
  - csharp
  - vb
  name: VisualTreeChain(SkiaControl)
  nameWithType: VisualTreeChain.VisualTreeChain(SkiaControl)
  fullName: DrawnUi.Infrastructure.VisualTreeChain.VisualTreeChain(DrawnUi.Draw.SkiaControl)
  type: Constructor
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/VisualTreeChain.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Shared/Draw/Internals/Models/VisualTreeChain.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public VisualTreeChain(SkiaControl child)
    parameters:
    - id: child
      type: DrawnUi.Draw.SkiaControl
    content.vb: Public Sub New(child As SkiaControl)
  overload: DrawnUi.Infrastructure.VisualTreeChain.#ctor*
  nameWithType.vb: VisualTreeChain.New(SkiaControl)
  fullName.vb: DrawnUi.Infrastructure.VisualTreeChain.New(DrawnUi.Draw.SkiaControl)
  name.vb: New(SkiaControl)
- uid: DrawnUi.Infrastructure.VisualTreeChain.AddNode(DrawnUi.Draw.SkiaControl)
  commentId: M:DrawnUi.Infrastructure.VisualTreeChain.AddNode(DrawnUi.Draw.SkiaControl)
  id: AddNode(DrawnUi.Draw.SkiaControl)
  parent: DrawnUi.Infrastructure.VisualTreeChain
  langs:
  - csharp
  - vb
  name: AddNode(SkiaControl)
  nameWithType: VisualTreeChain.AddNode(SkiaControl)
  fullName: DrawnUi.Infrastructure.VisualTreeChain.AddNode(DrawnUi.Draw.SkiaControl)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/VisualTreeChain.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: AddNode
    path: ../src/Shared/Draw/Internals/Models/VisualTreeChain.cs
    startLine: 12
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public void AddNode(SkiaControl node)
    parameters:
    - id: node
      type: DrawnUi.Draw.SkiaControl
    content.vb: Public Sub AddNode(node As SkiaControl)
  overload: DrawnUi.Infrastructure.VisualTreeChain.AddNode*
- uid: DrawnUi.Infrastructure.VisualTreeChain.Child
  commentId: P:DrawnUi.Infrastructure.VisualTreeChain.Child
  id: Child
  parent: DrawnUi.Infrastructure.VisualTreeChain
  langs:
  - csharp
  - vb
  name: Child
  nameWithType: VisualTreeChain.Child
  fullName: DrawnUi.Infrastructure.VisualTreeChain.Child
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/VisualTreeChain.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Child
    path: ../src/Shared/Draw/Internals/Models/VisualTreeChain.cs
    startLine: 21
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  summary: The final node the tree leads to
  example: []
  syntax:
    content: public SkiaControl Child { get; set; }
    parameters: []
    return:
      type: DrawnUi.Draw.SkiaControl
    content.vb: Public Property Child As SkiaControl
  overload: DrawnUi.Infrastructure.VisualTreeChain.Child*
- uid: DrawnUi.Infrastructure.VisualTreeChain.Nodes
  commentId: P:DrawnUi.Infrastructure.VisualTreeChain.Nodes
  id: Nodes
  parent: DrawnUi.Infrastructure.VisualTreeChain
  langs:
  - csharp
  - vb
  name: Nodes
  nameWithType: VisualTreeChain.Nodes
  fullName: DrawnUi.Infrastructure.VisualTreeChain.Nodes
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/VisualTreeChain.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Nodes
    path: ../src/Shared/Draw/Internals/Models/VisualTreeChain.cs
    startLine: 26
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  summary: Parents leading to the final node
  example: []
  syntax:
    content: public List<SkiaControl> Nodes { get; set; }
    parameters: []
    return:
      type: System.Collections.Generic.List{DrawnUi.Draw.SkiaControl}
    content.vb: Public Property Nodes As List(Of SkiaControl)
  overload: DrawnUi.Infrastructure.VisualTreeChain.Nodes*
- uid: DrawnUi.Infrastructure.VisualTreeChain.NodeIndices
  commentId: P:DrawnUi.Infrastructure.VisualTreeChain.NodeIndices
  id: NodeIndices
  parent: DrawnUi.Infrastructure.VisualTreeChain
  langs:
  - csharp
  - vb
  name: NodeIndices
  nameWithType: VisualTreeChain.NodeIndices
  fullName: DrawnUi.Infrastructure.VisualTreeChain.NodeIndices
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/VisualTreeChain.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: NodeIndices
    path: ../src/Shared/Draw/Internals/Models/VisualTreeChain.cs
    startLine: 30
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  summary: Perf cache for node indices
  example: []
  syntax:
    content: public Dictionary<SkiaControl, int> NodeIndices { get; set; }
    parameters: []
    return:
      type: System.Collections.Generic.Dictionary{DrawnUi.Draw.SkiaControl,System.Int32}
    content.vb: Public Property NodeIndices As Dictionary(Of SkiaControl, Integer)
  overload: DrawnUi.Infrastructure.VisualTreeChain.NodeIndices*
- uid: DrawnUi.Infrastructure.VisualTreeChain.Transform
  commentId: P:DrawnUi.Infrastructure.VisualTreeChain.Transform
  id: Transform
  parent: DrawnUi.Infrastructure.VisualTreeChain
  langs:
  - csharp
  - vb
  name: Transform
  nameWithType: VisualTreeChain.Transform
  fullName: DrawnUi.Infrastructure.VisualTreeChain.Transform
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/VisualTreeChain.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Transform
    path: ../src/Shared/Draw/Internals/Models/VisualTreeChain.cs
    startLine: 35
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  summary: Final transform of the chain
  example: []
  syntax:
    content: public VisualTransform Transform { get; set; }
    parameters: []
    return:
      type: DrawnUi.Infrastructure.VisualTransform
    content.vb: Public Property Transform As VisualTransform
  overload: DrawnUi.Infrastructure.VisualTreeChain.Transform*
references:
- uid: DrawnUi.Infrastructure
  commentId: N:DrawnUi.Infrastructure
  href: DrawnUi.html
  name: DrawnUi.Infrastructure
  nameWithType: DrawnUi.Infrastructure
  fullName: DrawnUi.Infrastructure
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Infrastructure
    name: Infrastructure
    href: DrawnUi.Infrastructure.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Infrastructure
    name: Infrastructure
    href: DrawnUi.Infrastructure.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Infrastructure.VisualTreeChain.#ctor*
  commentId: Overload:DrawnUi.Infrastructure.VisualTreeChain.#ctor
  href: DrawnUi.Infrastructure.VisualTreeChain.html#DrawnUi_Infrastructure_VisualTreeChain__ctor_DrawnUi_Draw_SkiaControl_
  name: VisualTreeChain
  nameWithType: VisualTreeChain.VisualTreeChain
  fullName: DrawnUi.Infrastructure.VisualTreeChain.VisualTreeChain
  nameWithType.vb: VisualTreeChain.New
  fullName.vb: DrawnUi.Infrastructure.VisualTreeChain.New
  name.vb: New
- uid: DrawnUi.Draw.SkiaControl
  commentId: T:DrawnUi.Draw.SkiaControl
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaControl.html
  name: SkiaControl
  nameWithType: SkiaControl
  fullName: DrawnUi.Draw.SkiaControl
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: DrawnUi.Infrastructure.VisualTreeChain.AddNode*
  commentId: Overload:DrawnUi.Infrastructure.VisualTreeChain.AddNode
  href: DrawnUi.Infrastructure.VisualTreeChain.html#DrawnUi_Infrastructure_VisualTreeChain_AddNode_DrawnUi_Draw_SkiaControl_
  name: AddNode
  nameWithType: VisualTreeChain.AddNode
  fullName: DrawnUi.Infrastructure.VisualTreeChain.AddNode
- uid: DrawnUi.Infrastructure.VisualTreeChain.Child*
  commentId: Overload:DrawnUi.Infrastructure.VisualTreeChain.Child
  href: DrawnUi.Infrastructure.VisualTreeChain.html#DrawnUi_Infrastructure_VisualTreeChain_Child
  name: Child
  nameWithType: VisualTreeChain.Child
  fullName: DrawnUi.Infrastructure.VisualTreeChain.Child
- uid: DrawnUi.Infrastructure.VisualTreeChain.Nodes*
  commentId: Overload:DrawnUi.Infrastructure.VisualTreeChain.Nodes
  href: DrawnUi.Infrastructure.VisualTreeChain.html#DrawnUi_Infrastructure_VisualTreeChain_Nodes
  name: Nodes
  nameWithType: VisualTreeChain.Nodes
  fullName: DrawnUi.Infrastructure.VisualTreeChain.Nodes
- uid: System.Collections.Generic.List{DrawnUi.Draw.SkiaControl}
  commentId: T:System.Collections.Generic.List{DrawnUi.Draw.SkiaControl}
  parent: System.Collections.Generic
  definition: System.Collections.Generic.List`1
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  name: List<SkiaControl>
  nameWithType: List<SkiaControl>
  fullName: System.Collections.Generic.List<DrawnUi.Draw.SkiaControl>
  nameWithType.vb: List(Of SkiaControl)
  fullName.vb: System.Collections.Generic.List(Of DrawnUi.Draw.SkiaControl)
  name.vb: List(Of SkiaControl)
  spec.csharp:
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: <
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
- uid: System.Collections.Generic.List`1
  commentId: T:System.Collections.Generic.List`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  name: List<T>
  nameWithType: List<T>
  fullName: System.Collections.Generic.List<T>
  nameWithType.vb: List(Of T)
  fullName.vb: System.Collections.Generic.List(Of T)
  name.vb: List(Of T)
  spec.csharp:
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: System.Collections.Generic
  commentId: N:System.Collections.Generic
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Collections.Generic
  nameWithType: System.Collections.Generic
  fullName: System.Collections.Generic
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Generic
    name: Generic
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Generic
    name: Generic
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic
- uid: DrawnUi.Infrastructure.VisualTreeChain.NodeIndices*
  commentId: Overload:DrawnUi.Infrastructure.VisualTreeChain.NodeIndices
  href: DrawnUi.Infrastructure.VisualTreeChain.html#DrawnUi_Infrastructure_VisualTreeChain_NodeIndices
  name: NodeIndices
  nameWithType: VisualTreeChain.NodeIndices
  fullName: DrawnUi.Infrastructure.VisualTreeChain.NodeIndices
- uid: System.Collections.Generic.Dictionary{DrawnUi.Draw.SkiaControl,System.Int32}
  commentId: T:System.Collections.Generic.Dictionary{DrawnUi.Draw.SkiaControl,System.Int32}
  parent: System.Collections.Generic
  definition: System.Collections.Generic.Dictionary`2
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2
  name: Dictionary<SkiaControl, int>
  nameWithType: Dictionary<SkiaControl, int>
  fullName: System.Collections.Generic.Dictionary<DrawnUi.Draw.SkiaControl, int>
  nameWithType.vb: Dictionary(Of SkiaControl, Integer)
  fullName.vb: System.Collections.Generic.Dictionary(Of DrawnUi.Draw.SkiaControl, Integer)
  name.vb: Dictionary(Of SkiaControl, Integer)
  spec.csharp:
  - uid: System.Collections.Generic.Dictionary`2
    name: Dictionary
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2
  - name: <
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: ','
  - name: " "
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.Dictionary`2
    name: Dictionary
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: ','
  - name: " "
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: System.Collections.Generic.Dictionary`2
  commentId: T:System.Collections.Generic.Dictionary`2
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2
  name: Dictionary<TKey, TValue>
  nameWithType: Dictionary<TKey, TValue>
  fullName: System.Collections.Generic.Dictionary<TKey, TValue>
  nameWithType.vb: Dictionary(Of TKey, TValue)
  fullName.vb: System.Collections.Generic.Dictionary(Of TKey, TValue)
  name.vb: Dictionary(Of TKey, TValue)
  spec.csharp:
  - uid: System.Collections.Generic.Dictionary`2
    name: Dictionary
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2
  - name: <
  - name: TKey
  - name: ','
  - name: " "
  - name: TValue
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.Dictionary`2
    name: Dictionary
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2
  - name: (
  - name: Of
  - name: " "
  - name: TKey
  - name: ','
  - name: " "
  - name: TValue
  - name: )
- uid: DrawnUi.Infrastructure.VisualTreeChain.Transform*
  commentId: Overload:DrawnUi.Infrastructure.VisualTreeChain.Transform
  href: DrawnUi.Infrastructure.VisualTreeChain.html#DrawnUi_Infrastructure_VisualTreeChain_Transform
  name: Transform
  nameWithType: VisualTreeChain.Transform
  fullName: DrawnUi.Infrastructure.VisualTreeChain.Transform
- uid: DrawnUi.Infrastructure.VisualTransform
  commentId: T:DrawnUi.Infrastructure.VisualTransform
  parent: DrawnUi.Infrastructure
  href: DrawnUi.Infrastructure.VisualTransform.html
  name: VisualTransform
  nameWithType: VisualTransform
  fullName: DrawnUi.Infrastructure.VisualTransform
