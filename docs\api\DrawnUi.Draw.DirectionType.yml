### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.DirectionType
  commentId: T:DrawnUi.Draw.DirectionType
  id: DirectionType
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.DirectionType.Horizontal
  - DrawnUi.Draw.DirectionType.None
  - DrawnUi.Draw.DirectionType.Vertical
  langs:
  - csharp
  - vb
  name: DirectionType
  nameWithType: DirectionType
  fullName: DrawnUi.Draw.DirectionType
  type: Enum
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/DirectionType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: DirectionType
    path: ../src/Shared/Draw/Internals/Enums/DirectionType.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public enum DirectionType
    content.vb: Public Enum DirectionType
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.DirectionType.Horizontal
  commentId: F:DrawnUi.Draw.DirectionType.Horizontal
  id: Horizontal
  parent: DrawnUi.Draw.DirectionType
  langs:
  - csharp
  - vb
  name: Horizontal
  nameWithType: DirectionType.Horizontal
  fullName: DrawnUi.Draw.DirectionType.Horizontal
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/DirectionType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Horizontal
    path: ../src/Shared/Draw/Internals/Enums/DirectionType.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: Horizontal = 0
    return:
      type: DrawnUi.Draw.DirectionType
- uid: DrawnUi.Draw.DirectionType.Vertical
  commentId: F:DrawnUi.Draw.DirectionType.Vertical
  id: Vertical
  parent: DrawnUi.Draw.DirectionType
  langs:
  - csharp
  - vb
  name: Vertical
  nameWithType: DirectionType.Vertical
  fullName: DrawnUi.Draw.DirectionType.Vertical
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/DirectionType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Vertical
    path: ../src/Shared/Draw/Internals/Enums/DirectionType.cs
    startLine: 5
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: Vertical = 1
    return:
      type: DrawnUi.Draw.DirectionType
- uid: DrawnUi.Draw.DirectionType.None
  commentId: F:DrawnUi.Draw.DirectionType.None
  id: None
  parent: DrawnUi.Draw.DirectionType
  langs:
  - csharp
  - vb
  name: None
  nameWithType: DirectionType.None
  fullName: DrawnUi.Draw.DirectionType.None
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/DirectionType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: None
    path: ../src/Shared/Draw/Internals/Enums/DirectionType.cs
    startLine: 6
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: None = 2
    return:
      type: DrawnUi.Draw.DirectionType
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.DirectionType
  commentId: T:DrawnUi.Draw.DirectionType
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.DirectionType.html
  name: DirectionType
  nameWithType: DirectionType
  fullName: DrawnUi.Draw.DirectionType
