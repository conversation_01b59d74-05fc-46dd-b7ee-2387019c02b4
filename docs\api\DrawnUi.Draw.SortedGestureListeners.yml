### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.SortedGestureListeners
  commentId: T:DrawnUi.Draw.SortedGestureListeners
  id: SortedGestureListeners
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.SortedGestureListeners.Add(DrawnUi.Draw.ISkiaGestureListener)
  - DrawnUi.Draw.SortedGestureListeners.Clear
  - DrawnUi.Draw.SortedGestureListeners.GetListeners
  - DrawnUi.Draw.SortedGestureListeners.Invalidate
  - DrawnUi.Draw.SortedGestureListeners.Remove(DrawnUi.Draw.ISkiaGestureListener)
  - DrawnUi.Draw.SortedGestureListeners.Sorted
  - DrawnUi.Draw.SortedGestureListeners._dic
  - DrawnUi.Draw.SortedGestureListeners._isDirty
  langs:
  - csharp
  - vb
  name: SortedGestureListeners
  nameWithType: SortedGestureListeners
  fullName: DrawnUi.Draw.SortedGestureListeners
  type: Class
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/SortedGestureListeners.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: SortedGestureListeners
    path: ../src/Shared/Draw/Internals/Models/SortedGestureListeners.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public class SortedGestureListeners
    content.vb: Public Class SortedGestureListeners
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.SortedGestureListeners._dic
  commentId: F:DrawnUi.Draw.SortedGestureListeners._dic
  id: _dic
  parent: DrawnUi.Draw.SortedGestureListeners
  langs:
  - csharp
  - vb
  name: _dic
  nameWithType: SortedGestureListeners._dic
  fullName: DrawnUi.Draw.SortedGestureListeners._dic
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/SortedGestureListeners.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: _dic
    path: ../src/Shared/Draw/Internals/Models/SortedGestureListeners.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: protected readonly Dictionary<Guid, ISkiaGestureListener> _dic
    return:
      type: System.Collections.Generic.Dictionary{System.Guid,DrawnUi.Draw.ISkiaGestureListener}
    content.vb: Protected ReadOnly _dic As Dictionary(Of Guid, ISkiaGestureListener)
- uid: DrawnUi.Draw.SortedGestureListeners.Sorted
  commentId: P:DrawnUi.Draw.SortedGestureListeners.Sorted
  id: Sorted
  parent: DrawnUi.Draw.SortedGestureListeners
  langs:
  - csharp
  - vb
  name: Sorted
  nameWithType: SortedGestureListeners.Sorted
  fullName: DrawnUi.Draw.SortedGestureListeners.Sorted
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/SortedGestureListeners.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Sorted
    path: ../src/Shared/Draw/Internals/Models/SortedGestureListeners.cs
    startLine: 5
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: protected List<ISkiaGestureListener> Sorted { get; set; }
    parameters: []
    return:
      type: System.Collections.Generic.List{DrawnUi.Draw.ISkiaGestureListener}
    content.vb: Protected Property Sorted As List(Of ISkiaGestureListener)
  overload: DrawnUi.Draw.SortedGestureListeners.Sorted*
- uid: DrawnUi.Draw.SortedGestureListeners._isDirty
  commentId: F:DrawnUi.Draw.SortedGestureListeners._isDirty
  id: _isDirty
  parent: DrawnUi.Draw.SortedGestureListeners
  langs:
  - csharp
  - vb
  name: _isDirty
  nameWithType: SortedGestureListeners._isDirty
  fullName: DrawnUi.Draw.SortedGestureListeners._isDirty
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/SortedGestureListeners.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: _isDirty
    path: ../src/Shared/Draw/Internals/Models/SortedGestureListeners.cs
    startLine: 6
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: protected bool _isDirty
    return:
      type: System.Boolean
    content.vb: Protected _isDirty As Boolean
- uid: DrawnUi.Draw.SortedGestureListeners.GetListeners
  commentId: M:DrawnUi.Draw.SortedGestureListeners.GetListeners
  id: GetListeners
  parent: DrawnUi.Draw.SortedGestureListeners
  langs:
  - csharp
  - vb
  name: GetListeners()
  nameWithType: SortedGestureListeners.GetListeners()
  fullName: DrawnUi.Draw.SortedGestureListeners.GetListeners()
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/SortedGestureListeners.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: GetListeners
    path: ../src/Shared/Draw/Internals/Models/SortedGestureListeners.cs
    startLine: 8
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public List<ISkiaGestureListener> GetListeners()
    return:
      type: System.Collections.Generic.List{DrawnUi.Draw.ISkiaGestureListener}
    content.vb: Public Function GetListeners() As List(Of ISkiaGestureListener)
  overload: DrawnUi.Draw.SortedGestureListeners.GetListeners*
- uid: DrawnUi.Draw.SortedGestureListeners.Add(DrawnUi.Draw.ISkiaGestureListener)
  commentId: M:DrawnUi.Draw.SortedGestureListeners.Add(DrawnUi.Draw.ISkiaGestureListener)
  id: Add(DrawnUi.Draw.ISkiaGestureListener)
  parent: DrawnUi.Draw.SortedGestureListeners
  langs:
  - csharp
  - vb
  name: Add(ISkiaGestureListener)
  nameWithType: SortedGestureListeners.Add(ISkiaGestureListener)
  fullName: DrawnUi.Draw.SortedGestureListeners.Add(DrawnUi.Draw.ISkiaGestureListener)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/SortedGestureListeners.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Add
    path: ../src/Shared/Draw/Internals/Models/SortedGestureListeners.cs
    startLine: 24
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public void Add(ISkiaGestureListener item)
    parameters:
    - id: item
      type: DrawnUi.Draw.ISkiaGestureListener
    content.vb: Public Sub Add(item As ISkiaGestureListener)
  overload: DrawnUi.Draw.SortedGestureListeners.Add*
- uid: DrawnUi.Draw.SortedGestureListeners.Remove(DrawnUi.Draw.ISkiaGestureListener)
  commentId: M:DrawnUi.Draw.SortedGestureListeners.Remove(DrawnUi.Draw.ISkiaGestureListener)
  id: Remove(DrawnUi.Draw.ISkiaGestureListener)
  parent: DrawnUi.Draw.SortedGestureListeners
  langs:
  - csharp
  - vb
  name: Remove(ISkiaGestureListener)
  nameWithType: SortedGestureListeners.Remove(ISkiaGestureListener)
  fullName: DrawnUi.Draw.SortedGestureListeners.Remove(DrawnUi.Draw.ISkiaGestureListener)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/SortedGestureListeners.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Remove
    path: ../src/Shared/Draw/Internals/Models/SortedGestureListeners.cs
    startLine: 33
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public void Remove(ISkiaGestureListener item)
    parameters:
    - id: item
      type: DrawnUi.Draw.ISkiaGestureListener
    content.vb: Public Sub Remove(item As ISkiaGestureListener)
  overload: DrawnUi.Draw.SortedGestureListeners.Remove*
- uid: DrawnUi.Draw.SortedGestureListeners.Clear
  commentId: M:DrawnUi.Draw.SortedGestureListeners.Clear
  id: Clear
  parent: DrawnUi.Draw.SortedGestureListeners
  langs:
  - csharp
  - vb
  name: Clear()
  nameWithType: SortedGestureListeners.Clear()
  fullName: DrawnUi.Draw.SortedGestureListeners.Clear()
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/SortedGestureListeners.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Clear
    path: ../src/Shared/Draw/Internals/Models/SortedGestureListeners.cs
    startLine: 44
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public void Clear()
    content.vb: Public Sub Clear()
  overload: DrawnUi.Draw.SortedGestureListeners.Clear*
- uid: DrawnUi.Draw.SortedGestureListeners.Invalidate
  commentId: M:DrawnUi.Draw.SortedGestureListeners.Invalidate
  id: Invalidate
  parent: DrawnUi.Draw.SortedGestureListeners
  langs:
  - csharp
  - vb
  name: Invalidate()
  nameWithType: SortedGestureListeners.Invalidate()
  fullName: DrawnUi.Draw.SortedGestureListeners.Invalidate()
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/SortedGestureListeners.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Invalidate
    path: ../src/Shared/Draw/Internals/Models/SortedGestureListeners.cs
    startLine: 53
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public void Invalidate()
    content.vb: Public Sub Invalidate()
  overload: DrawnUi.Draw.SortedGestureListeners.Invalidate*
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: System.Collections.Generic.Dictionary{System.Guid,DrawnUi.Draw.ISkiaGestureListener}
  commentId: T:System.Collections.Generic.Dictionary{System.Guid,DrawnUi.Draw.ISkiaGestureListener}
  parent: System.Collections.Generic
  definition: System.Collections.Generic.Dictionary`2
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2
  name: Dictionary<Guid, ISkiaGestureListener>
  nameWithType: Dictionary<Guid, ISkiaGestureListener>
  fullName: System.Collections.Generic.Dictionary<System.Guid, DrawnUi.Draw.ISkiaGestureListener>
  nameWithType.vb: Dictionary(Of Guid, ISkiaGestureListener)
  fullName.vb: System.Collections.Generic.Dictionary(Of System.Guid, DrawnUi.Draw.ISkiaGestureListener)
  name.vb: Dictionary(Of Guid, ISkiaGestureListener)
  spec.csharp:
  - uid: System.Collections.Generic.Dictionary`2
    name: Dictionary
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2
  - name: <
  - uid: System.Guid
    name: Guid
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.guid
  - name: ','
  - name: " "
  - uid: DrawnUi.Draw.ISkiaGestureListener
    name: ISkiaGestureListener
    href: DrawnUi.Draw.ISkiaGestureListener.html
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.Dictionary`2
    name: Dictionary
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2
  - name: (
  - name: Of
  - name: " "
  - uid: System.Guid
    name: Guid
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.guid
  - name: ','
  - name: " "
  - uid: DrawnUi.Draw.ISkiaGestureListener
    name: ISkiaGestureListener
    href: DrawnUi.Draw.ISkiaGestureListener.html
  - name: )
- uid: System.Collections.Generic.Dictionary`2
  commentId: T:System.Collections.Generic.Dictionary`2
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2
  name: Dictionary<TKey, TValue>
  nameWithType: Dictionary<TKey, TValue>
  fullName: System.Collections.Generic.Dictionary<TKey, TValue>
  nameWithType.vb: Dictionary(Of TKey, TValue)
  fullName.vb: System.Collections.Generic.Dictionary(Of TKey, TValue)
  name.vb: Dictionary(Of TKey, TValue)
  spec.csharp:
  - uid: System.Collections.Generic.Dictionary`2
    name: Dictionary
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2
  - name: <
  - name: TKey
  - name: ','
  - name: " "
  - name: TValue
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.Dictionary`2
    name: Dictionary
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2
  - name: (
  - name: Of
  - name: " "
  - name: TKey
  - name: ','
  - name: " "
  - name: TValue
  - name: )
- uid: System.Collections.Generic
  commentId: N:System.Collections.Generic
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Collections.Generic
  nameWithType: System.Collections.Generic
  fullName: System.Collections.Generic
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Generic
    name: Generic
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Generic
    name: Generic
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic
- uid: DrawnUi.Draw.SortedGestureListeners.Sorted*
  commentId: Overload:DrawnUi.Draw.SortedGestureListeners.Sorted
  href: DrawnUi.Draw.SortedGestureListeners.html#DrawnUi_Draw_SortedGestureListeners_Sorted
  name: Sorted
  nameWithType: SortedGestureListeners.Sorted
  fullName: DrawnUi.Draw.SortedGestureListeners.Sorted
- uid: System.Collections.Generic.List{DrawnUi.Draw.ISkiaGestureListener}
  commentId: T:System.Collections.Generic.List{DrawnUi.Draw.ISkiaGestureListener}
  parent: System.Collections.Generic
  definition: System.Collections.Generic.List`1
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  name: List<ISkiaGestureListener>
  nameWithType: List<ISkiaGestureListener>
  fullName: System.Collections.Generic.List<DrawnUi.Draw.ISkiaGestureListener>
  nameWithType.vb: List(Of ISkiaGestureListener)
  fullName.vb: System.Collections.Generic.List(Of DrawnUi.Draw.ISkiaGestureListener)
  name.vb: List(Of ISkiaGestureListener)
  spec.csharp:
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: <
  - uid: DrawnUi.Draw.ISkiaGestureListener
    name: ISkiaGestureListener
    href: DrawnUi.Draw.ISkiaGestureListener.html
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Draw.ISkiaGestureListener
    name: ISkiaGestureListener
    href: DrawnUi.Draw.ISkiaGestureListener.html
  - name: )
- uid: System.Collections.Generic.List`1
  commentId: T:System.Collections.Generic.List`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  name: List<T>
  nameWithType: List<T>
  fullName: System.Collections.Generic.List<T>
  nameWithType.vb: List(Of T)
  fullName.vb: System.Collections.Generic.List(Of T)
  name.vb: List(Of T)
  spec.csharp:
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: DrawnUi.Draw.SortedGestureListeners.GetListeners*
  commentId: Overload:DrawnUi.Draw.SortedGestureListeners.GetListeners
  href: DrawnUi.Draw.SortedGestureListeners.html#DrawnUi_Draw_SortedGestureListeners_GetListeners
  name: GetListeners
  nameWithType: SortedGestureListeners.GetListeners
  fullName: DrawnUi.Draw.SortedGestureListeners.GetListeners
- uid: DrawnUi.Draw.SortedGestureListeners.Add*
  commentId: Overload:DrawnUi.Draw.SortedGestureListeners.Add
  href: DrawnUi.Draw.SortedGestureListeners.html#DrawnUi_Draw_SortedGestureListeners_Add_DrawnUi_Draw_ISkiaGestureListener_
  name: Add
  nameWithType: SortedGestureListeners.Add
  fullName: DrawnUi.Draw.SortedGestureListeners.Add
- uid: DrawnUi.Draw.ISkiaGestureListener
  commentId: T:DrawnUi.Draw.ISkiaGestureListener
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ISkiaGestureListener.html
  name: ISkiaGestureListener
  nameWithType: ISkiaGestureListener
  fullName: DrawnUi.Draw.ISkiaGestureListener
- uid: DrawnUi.Draw.SortedGestureListeners.Remove*
  commentId: Overload:DrawnUi.Draw.SortedGestureListeners.Remove
  href: DrawnUi.Draw.SortedGestureListeners.html#DrawnUi_Draw_SortedGestureListeners_Remove_DrawnUi_Draw_ISkiaGestureListener_
  name: Remove
  nameWithType: SortedGestureListeners.Remove
  fullName: DrawnUi.Draw.SortedGestureListeners.Remove
- uid: DrawnUi.Draw.SortedGestureListeners.Clear*
  commentId: Overload:DrawnUi.Draw.SortedGestureListeners.Clear
  href: DrawnUi.Draw.SortedGestureListeners.html#DrawnUi_Draw_SortedGestureListeners_Clear
  name: Clear
  nameWithType: SortedGestureListeners.Clear
  fullName: DrawnUi.Draw.SortedGestureListeners.Clear
- uid: DrawnUi.Draw.SortedGestureListeners.Invalidate*
  commentId: Overload:DrawnUi.Draw.SortedGestureListeners.Invalidate
  href: DrawnUi.Draw.SortedGestureListeners.html#DrawnUi_Draw_SortedGestureListeners_Invalidate
  name: Invalidate
  nameWithType: SortedGestureListeners.Invalidate
  fullName: DrawnUi.Draw.SortedGestureListeners.Invalidate
