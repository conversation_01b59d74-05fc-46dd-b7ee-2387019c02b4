### YamlMime:ManagedReference
items:
- uid: DrawnUi.Infrastructure.Models.LimitedStack`1
  commentId: T:DrawnUi.Infrastructure.Models.LimitedStack`1
  id: LimitedStack`1
  parent: DrawnUi.Infrastructure.Models
  children:
  - DrawnUi.Infrastructure.Models.LimitedStack`1.#ctor
  - DrawnUi.Infrastructure.Models.LimitedStack`1.#ctor(System.Int32)
  - DrawnUi.Infrastructure.Models.LimitedStack`1.Clear
  - DrawnUi.Infrastructure.Models.LimitedStack`1.Count
  - DrawnUi.Infrastructure.Models.LimitedStack`1.IsLocked
  - DrawnUi.Infrastructure.Models.LimitedStack`1.Lock
  - DrawnUi.Infrastructure.Models.LimitedStack`1.OnAutoRemovingItem(`0)
  - DrawnUi.Infrastructure.Models.LimitedStack`1.Pop
  - DrawnUi.Infrastructure.Models.LimitedStack`1.Push(`0)
  - DrawnUi.Infrastructure.Models.LimitedStack`1.ToArray
  - DrawnUi.Infrastructure.Models.LimitedStack`1.ToList
  - DrawnUi.Infrastructure.Models.LimitedStack`1.Unlock
  langs:
  - csharp
  - vb
  name: LimitedStack<T>
  nameWithType: LimitedStack<T>
  fullName: DrawnUi.Infrastructure.Models.LimitedStack<T>
  type: Class
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/LimitedStack.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: LimitedStack
    path: ../src/Shared/Draw/Internals/Models/LimitedStack.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure.Models
  syntax:
    content: public class LimitedStack<T>
    typeParameters:
    - id: T
    content.vb: Public Class LimitedStack(Of T)
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  nameWithType.vb: LimitedStack(Of T)
  fullName.vb: DrawnUi.Infrastructure.Models.LimitedStack(Of T)
  name.vb: LimitedStack(Of T)
- uid: DrawnUi.Infrastructure.Models.LimitedStack`1.Count
  commentId: P:DrawnUi.Infrastructure.Models.LimitedStack`1.Count
  id: Count
  parent: DrawnUi.Infrastructure.Models.LimitedStack`1
  langs:
  - csharp
  - vb
  name: Count
  nameWithType: LimitedStack<T>.Count
  fullName: DrawnUi.Infrastructure.Models.LimitedStack<T>.Count
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/LimitedStack.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Count
    path: ../src/Shared/Draw/Internals/Models/LimitedStack.cs
    startLine: 8
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure.Models
  syntax:
    content: public int Count { get; }
    parameters: []
    return:
      type: System.Int32
    content.vb: Public ReadOnly Property Count As Integer
  overload: DrawnUi.Infrastructure.Models.LimitedStack`1.Count*
  nameWithType.vb: LimitedStack(Of T).Count
  fullName.vb: DrawnUi.Infrastructure.Models.LimitedStack(Of T).Count
- uid: DrawnUi.Infrastructure.Models.LimitedStack`1.ToArray
  commentId: M:DrawnUi.Infrastructure.Models.LimitedStack`1.ToArray
  id: ToArray
  parent: DrawnUi.Infrastructure.Models.LimitedStack`1
  langs:
  - csharp
  - vb
  name: ToArray()
  nameWithType: LimitedStack<T>.ToArray()
  fullName: DrawnUi.Infrastructure.Models.LimitedStack<T>.ToArray()
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/LimitedStack.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ToArray
    path: ../src/Shared/Draw/Internals/Models/LimitedStack.cs
    startLine: 10
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure.Models
  syntax:
    content: public T[] ToArray()
    return:
      type: '{T}[]'
    content.vb: Public Function ToArray() As T()
  overload: DrawnUi.Infrastructure.Models.LimitedStack`1.ToArray*
  nameWithType.vb: LimitedStack(Of T).ToArray()
  fullName.vb: DrawnUi.Infrastructure.Models.LimitedStack(Of T).ToArray()
- uid: DrawnUi.Infrastructure.Models.LimitedStack`1.ToList
  commentId: M:DrawnUi.Infrastructure.Models.LimitedStack`1.ToList
  id: ToList
  parent: DrawnUi.Infrastructure.Models.LimitedStack`1
  langs:
  - csharp
  - vb
  name: ToList()
  nameWithType: LimitedStack<T>.ToList()
  fullName: DrawnUi.Infrastructure.Models.LimitedStack<T>.ToList()
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/LimitedStack.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ToList
    path: ../src/Shared/Draw/Internals/Models/LimitedStack.cs
    startLine: 15
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure.Models
  syntax:
    content: public List<T> ToList()
    return:
      type: System.Collections.Generic.List{{T}}
    content.vb: Public Function ToList() As List(Of T)
  overload: DrawnUi.Infrastructure.Models.LimitedStack`1.ToList*
  nameWithType.vb: LimitedStack(Of T).ToList()
  fullName.vb: DrawnUi.Infrastructure.Models.LimitedStack(Of T).ToList()
- uid: DrawnUi.Infrastructure.Models.LimitedStack`1.#ctor
  commentId: M:DrawnUi.Infrastructure.Models.LimitedStack`1.#ctor
  id: '#ctor'
  parent: DrawnUi.Infrastructure.Models.LimitedStack`1
  langs:
  - csharp
  - vb
  name: LimitedStack()
  nameWithType: LimitedStack<T>.LimitedStack()
  fullName: DrawnUi.Infrastructure.Models.LimitedStack<T>.LimitedStack()
  type: Constructor
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/LimitedStack.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Shared/Draw/Internals/Models/LimitedStack.cs
    startLine: 20
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure.Models
  syntax:
    content: public LimitedStack()
    content.vb: Public Sub New()
  overload: DrawnUi.Infrastructure.Models.LimitedStack`1.#ctor*
  nameWithType.vb: LimitedStack(Of T).New()
  fullName.vb: DrawnUi.Infrastructure.Models.LimitedStack(Of T).New()
  name.vb: New()
- uid: DrawnUi.Infrastructure.Models.LimitedStack`1.OnAutoRemovingItem(`0)
  commentId: M:DrawnUi.Infrastructure.Models.LimitedStack`1.OnAutoRemovingItem(`0)
  id: OnAutoRemovingItem(`0)
  parent: DrawnUi.Infrastructure.Models.LimitedStack`1
  langs:
  - csharp
  - vb
  name: OnAutoRemovingItem(T)
  nameWithType: LimitedStack<T>.OnAutoRemovingItem(T)
  fullName: DrawnUi.Infrastructure.Models.LimitedStack<T>.OnAutoRemovingItem(T)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/LimitedStack.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: OnAutoRemovingItem
    path: ../src/Shared/Draw/Internals/Models/LimitedStack.cs
    startLine: 24
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure.Models
  syntax:
    content: protected virtual void OnAutoRemovingItem(T item)
    parameters:
    - id: item
      type: '{T}'
    content.vb: Protected Overridable Sub OnAutoRemovingItem(item As T)
  overload: DrawnUi.Infrastructure.Models.LimitedStack`1.OnAutoRemovingItem*
  nameWithType.vb: LimitedStack(Of T).OnAutoRemovingItem(T)
  fullName.vb: DrawnUi.Infrastructure.Models.LimitedStack(Of T).OnAutoRemovingItem(T)
- uid: DrawnUi.Infrastructure.Models.LimitedStack`1.#ctor(System.Int32)
  commentId: M:DrawnUi.Infrastructure.Models.LimitedStack`1.#ctor(System.Int32)
  id: '#ctor(System.Int32)'
  parent: DrawnUi.Infrastructure.Models.LimitedStack`1
  langs:
  - csharp
  - vb
  name: LimitedStack(int)
  nameWithType: LimitedStack<T>.LimitedStack(int)
  fullName: DrawnUi.Infrastructure.Models.LimitedStack<T>.LimitedStack(int)
  type: Constructor
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/LimitedStack.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Shared/Draw/Internals/Models/LimitedStack.cs
    startLine: 29
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure.Models
  syntax:
    content: public LimitedStack(int max)
    parameters:
    - id: max
      type: System.Int32
    content.vb: Public Sub New(max As Integer)
  overload: DrawnUi.Infrastructure.Models.LimitedStack`1.#ctor*
  nameWithType.vb: LimitedStack(Of T).New(Integer)
  fullName.vb: DrawnUi.Infrastructure.Models.LimitedStack(Of T).New(Integer)
  name.vb: New(Integer)
- uid: DrawnUi.Infrastructure.Models.LimitedStack`1.Push(`0)
  commentId: M:DrawnUi.Infrastructure.Models.LimitedStack`1.Push(`0)
  id: Push(`0)
  parent: DrawnUi.Infrastructure.Models.LimitedStack`1
  langs:
  - csharp
  - vb
  name: Push(T)
  nameWithType: LimitedStack<T>.Push(T)
  fullName: DrawnUi.Infrastructure.Models.LimitedStack<T>.Push(T)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/LimitedStack.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Push
    path: ../src/Shared/Draw/Internals/Models/LimitedStack.cs
    startLine: 34
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure.Models
  syntax:
    content: public void Push(T item)
    parameters:
    - id: item
      type: '{T}'
    content.vb: Public Sub Push(item As T)
  overload: DrawnUi.Infrastructure.Models.LimitedStack`1.Push*
  nameWithType.vb: LimitedStack(Of T).Push(T)
  fullName.vb: DrawnUi.Infrastructure.Models.LimitedStack(Of T).Push(T)
- uid: DrawnUi.Infrastructure.Models.LimitedStack`1.IsLocked
  commentId: P:DrawnUi.Infrastructure.Models.LimitedStack`1.IsLocked
  id: IsLocked
  parent: DrawnUi.Infrastructure.Models.LimitedStack`1
  langs:
  - csharp
  - vb
  name: IsLocked
  nameWithType: LimitedStack<T>.IsLocked
  fullName: DrawnUi.Infrastructure.Models.LimitedStack<T>.IsLocked
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/LimitedStack.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IsLocked
    path: ../src/Shared/Draw/Internals/Models/LimitedStack.cs
    startLine: 47
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure.Models
  syntax:
    content: public bool IsLocked { get; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public ReadOnly Property IsLocked As Boolean
  overload: DrawnUi.Infrastructure.Models.LimitedStack`1.IsLocked*
  nameWithType.vb: LimitedStack(Of T).IsLocked
  fullName.vb: DrawnUi.Infrastructure.Models.LimitedStack(Of T).IsLocked
- uid: DrawnUi.Infrastructure.Models.LimitedStack`1.Lock
  commentId: M:DrawnUi.Infrastructure.Models.LimitedStack`1.Lock
  id: Lock
  parent: DrawnUi.Infrastructure.Models.LimitedStack`1
  langs:
  - csharp
  - vb
  name: Lock()
  nameWithType: LimitedStack<T>.Lock()
  fullName: DrawnUi.Infrastructure.Models.LimitedStack<T>.Lock()
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/LimitedStack.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Lock
    path: ../src/Shared/Draw/Internals/Models/LimitedStack.cs
    startLine: 55
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure.Models
  syntax:
    content: public void Lock()
    content.vb: Public Sub Lock()
  overload: DrawnUi.Infrastructure.Models.LimitedStack`1.Lock*
  nameWithType.vb: LimitedStack(Of T).Lock()
  fullName.vb: DrawnUi.Infrastructure.Models.LimitedStack(Of T).Lock()
- uid: DrawnUi.Infrastructure.Models.LimitedStack`1.Unlock
  commentId: M:DrawnUi.Infrastructure.Models.LimitedStack`1.Unlock
  id: Unlock
  parent: DrawnUi.Infrastructure.Models.LimitedStack`1
  langs:
  - csharp
  - vb
  name: Unlock()
  nameWithType: LimitedStack<T>.Unlock()
  fullName: DrawnUi.Infrastructure.Models.LimitedStack<T>.Unlock()
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/LimitedStack.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Unlock
    path: ../src/Shared/Draw/Internals/Models/LimitedStack.cs
    startLine: 60
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure.Models
  syntax:
    content: public void Unlock()
    content.vb: Public Sub Unlock()
  overload: DrawnUi.Infrastructure.Models.LimitedStack`1.Unlock*
  nameWithType.vb: LimitedStack(Of T).Unlock()
  fullName.vb: DrawnUi.Infrastructure.Models.LimitedStack(Of T).Unlock()
- uid: DrawnUi.Infrastructure.Models.LimitedStack`1.Pop
  commentId: M:DrawnUi.Infrastructure.Models.LimitedStack`1.Pop
  id: Pop
  parent: DrawnUi.Infrastructure.Models.LimitedStack`1
  langs:
  - csharp
  - vb
  name: Pop()
  nameWithType: LimitedStack<T>.Pop()
  fullName: DrawnUi.Infrastructure.Models.LimitedStack<T>.Pop()
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/LimitedStack.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Pop
    path: ../src/Shared/Draw/Internals/Models/LimitedStack.cs
    startLine: 65
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure.Models
  syntax:
    content: public T Pop()
    return:
      type: '{T}'
    content.vb: Public Function Pop() As T
  overload: DrawnUi.Infrastructure.Models.LimitedStack`1.Pop*
  nameWithType.vb: LimitedStack(Of T).Pop()
  fullName.vb: DrawnUi.Infrastructure.Models.LimitedStack(Of T).Pop()
- uid: DrawnUi.Infrastructure.Models.LimitedStack`1.Clear
  commentId: M:DrawnUi.Infrastructure.Models.LimitedStack`1.Clear
  id: Clear
  parent: DrawnUi.Infrastructure.Models.LimitedStack`1
  langs:
  - csharp
  - vb
  name: Clear()
  nameWithType: LimitedStack<T>.Clear()
  fullName: DrawnUi.Infrastructure.Models.LimitedStack<T>.Clear()
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/LimitedStack.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Clear
    path: ../src/Shared/Draw/Internals/Models/LimitedStack.cs
    startLine: 72
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure.Models
  syntax:
    content: public void Clear()
    content.vb: Public Sub Clear()
  overload: DrawnUi.Infrastructure.Models.LimitedStack`1.Clear*
  nameWithType.vb: LimitedStack(Of T).Clear()
  fullName.vb: DrawnUi.Infrastructure.Models.LimitedStack(Of T).Clear()
references:
- uid: DrawnUi.Infrastructure.Models
  commentId: N:DrawnUi.Infrastructure.Models
  href: DrawnUi.html
  name: DrawnUi.Infrastructure.Models
  nameWithType: DrawnUi.Infrastructure.Models
  fullName: DrawnUi.Infrastructure.Models
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Infrastructure
    name: Infrastructure
    href: DrawnUi.Infrastructure.html
  - name: .
  - uid: DrawnUi.Infrastructure.Models
    name: Models
    href: DrawnUi.Infrastructure.Models.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Infrastructure
    name: Infrastructure
    href: DrawnUi.Infrastructure.html
  - name: .
  - uid: DrawnUi.Infrastructure.Models
    name: Models
    href: DrawnUi.Infrastructure.Models.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Infrastructure.Models.LimitedStack`1.Count*
  commentId: Overload:DrawnUi.Infrastructure.Models.LimitedStack`1.Count
  href: DrawnUi.Infrastructure.Models.LimitedStack-1.html#DrawnUi_Infrastructure_Models_LimitedStack_1_Count
  name: Count
  nameWithType: LimitedStack<T>.Count
  fullName: DrawnUi.Infrastructure.Models.LimitedStack<T>.Count
  nameWithType.vb: LimitedStack(Of T).Count
  fullName.vb: DrawnUi.Infrastructure.Models.LimitedStack(Of T).Count
- uid: System.Int32
  commentId: T:System.Int32
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.int32
  name: int
  nameWithType: int
  fullName: int
  nameWithType.vb: Integer
  fullName.vb: Integer
  name.vb: Integer
- uid: DrawnUi.Infrastructure.Models.LimitedStack`1.ToArray*
  commentId: Overload:DrawnUi.Infrastructure.Models.LimitedStack`1.ToArray
  href: DrawnUi.Infrastructure.Models.LimitedStack-1.html#DrawnUi_Infrastructure_Models_LimitedStack_1_ToArray
  name: ToArray
  nameWithType: LimitedStack<T>.ToArray
  fullName: DrawnUi.Infrastructure.Models.LimitedStack<T>.ToArray
  nameWithType.vb: LimitedStack(Of T).ToArray
  fullName.vb: DrawnUi.Infrastructure.Models.LimitedStack(Of T).ToArray
- uid: '{T}[]'
  isExternal: true
  name: T[]
  nameWithType: T[]
  fullName: T[]
  nameWithType.vb: T()
  fullName.vb: T()
  name.vb: T()
  spec.csharp:
  - name: T
  - name: '['
  - name: ']'
  spec.vb:
  - name: T
  - name: (
  - name: )
- uid: DrawnUi.Infrastructure.Models.LimitedStack`1.ToList*
  commentId: Overload:DrawnUi.Infrastructure.Models.LimitedStack`1.ToList
  href: DrawnUi.Infrastructure.Models.LimitedStack-1.html#DrawnUi_Infrastructure_Models_LimitedStack_1_ToList
  name: ToList
  nameWithType: LimitedStack<T>.ToList
  fullName: DrawnUi.Infrastructure.Models.LimitedStack<T>.ToList
  nameWithType.vb: LimitedStack(Of T).ToList
  fullName.vb: DrawnUi.Infrastructure.Models.LimitedStack(Of T).ToList
- uid: System.Collections.Generic.List{{T}}
  commentId: T:System.Collections.Generic.List{`0}
  parent: System.Collections.Generic
  definition: System.Collections.Generic.List`1
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  name: List<T>
  nameWithType: List<T>
  fullName: System.Collections.Generic.List<T>
  nameWithType.vb: List(Of T)
  fullName.vb: System.Collections.Generic.List(Of T)
  name.vb: List(Of T)
  spec.csharp:
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: System.Collections.Generic.List`1
  commentId: T:System.Collections.Generic.List`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  name: List<T>
  nameWithType: List<T>
  fullName: System.Collections.Generic.List<T>
  nameWithType.vb: List(Of T)
  fullName.vb: System.Collections.Generic.List(Of T)
  name.vb: List(Of T)
  spec.csharp:
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: System.Collections.Generic
  commentId: N:System.Collections.Generic
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Collections.Generic
  nameWithType: System.Collections.Generic
  fullName: System.Collections.Generic
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Generic
    name: Generic
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Generic
    name: Generic
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic
- uid: DrawnUi.Infrastructure.Models.LimitedStack`1.#ctor*
  commentId: Overload:DrawnUi.Infrastructure.Models.LimitedStack`1.#ctor
  href: DrawnUi.Infrastructure.Models.LimitedStack-1.html#DrawnUi_Infrastructure_Models_LimitedStack_1__ctor
  name: LimitedStack
  nameWithType: LimitedStack<T>.LimitedStack
  fullName: DrawnUi.Infrastructure.Models.LimitedStack<T>.LimitedStack
  nameWithType.vb: LimitedStack(Of T).New
  fullName.vb: DrawnUi.Infrastructure.Models.LimitedStack(Of T).New
  name.vb: New
- uid: DrawnUi.Infrastructure.Models.LimitedStack`1.OnAutoRemovingItem*
  commentId: Overload:DrawnUi.Infrastructure.Models.LimitedStack`1.OnAutoRemovingItem
  href: DrawnUi.Infrastructure.Models.LimitedStack-1.html#DrawnUi_Infrastructure_Models_LimitedStack_1_OnAutoRemovingItem__0_
  name: OnAutoRemovingItem
  nameWithType: LimitedStack<T>.OnAutoRemovingItem
  fullName: DrawnUi.Infrastructure.Models.LimitedStack<T>.OnAutoRemovingItem
  nameWithType.vb: LimitedStack(Of T).OnAutoRemovingItem
  fullName.vb: DrawnUi.Infrastructure.Models.LimitedStack(Of T).OnAutoRemovingItem
- uid: '{T}'
  commentId: '!:T'
  definition: T
  name: T
  nameWithType: T
  fullName: T
- uid: T
  name: T
  nameWithType: T
  fullName: T
- uid: DrawnUi.Infrastructure.Models.LimitedStack`1.Push*
  commentId: Overload:DrawnUi.Infrastructure.Models.LimitedStack`1.Push
  href: DrawnUi.Infrastructure.Models.LimitedStack-1.html#DrawnUi_Infrastructure_Models_LimitedStack_1_Push__0_
  name: Push
  nameWithType: LimitedStack<T>.Push
  fullName: DrawnUi.Infrastructure.Models.LimitedStack<T>.Push
  nameWithType.vb: LimitedStack(Of T).Push
  fullName.vb: DrawnUi.Infrastructure.Models.LimitedStack(Of T).Push
- uid: DrawnUi.Infrastructure.Models.LimitedStack`1.IsLocked*
  commentId: Overload:DrawnUi.Infrastructure.Models.LimitedStack`1.IsLocked
  href: DrawnUi.Infrastructure.Models.LimitedStack-1.html#DrawnUi_Infrastructure_Models_LimitedStack_1_IsLocked
  name: IsLocked
  nameWithType: LimitedStack<T>.IsLocked
  fullName: DrawnUi.Infrastructure.Models.LimitedStack<T>.IsLocked
  nameWithType.vb: LimitedStack(Of T).IsLocked
  fullName.vb: DrawnUi.Infrastructure.Models.LimitedStack(Of T).IsLocked
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: DrawnUi.Infrastructure.Models.LimitedStack`1.Lock*
  commentId: Overload:DrawnUi.Infrastructure.Models.LimitedStack`1.Lock
  href: DrawnUi.Infrastructure.Models.LimitedStack-1.html#DrawnUi_Infrastructure_Models_LimitedStack_1_Lock
  name: Lock
  nameWithType: LimitedStack<T>.Lock
  fullName: DrawnUi.Infrastructure.Models.LimitedStack<T>.Lock
  nameWithType.vb: LimitedStack(Of T).Lock
  fullName.vb: DrawnUi.Infrastructure.Models.LimitedStack(Of T).Lock
- uid: DrawnUi.Infrastructure.Models.LimitedStack`1.Unlock*
  commentId: Overload:DrawnUi.Infrastructure.Models.LimitedStack`1.Unlock
  href: DrawnUi.Infrastructure.Models.LimitedStack-1.html#DrawnUi_Infrastructure_Models_LimitedStack_1_Unlock
  name: Unlock
  nameWithType: LimitedStack<T>.Unlock
  fullName: DrawnUi.Infrastructure.Models.LimitedStack<T>.Unlock
  nameWithType.vb: LimitedStack(Of T).Unlock
  fullName.vb: DrawnUi.Infrastructure.Models.LimitedStack(Of T).Unlock
- uid: DrawnUi.Infrastructure.Models.LimitedStack`1.Pop*
  commentId: Overload:DrawnUi.Infrastructure.Models.LimitedStack`1.Pop
  href: DrawnUi.Infrastructure.Models.LimitedStack-1.html#DrawnUi_Infrastructure_Models_LimitedStack_1_Pop
  name: Pop
  nameWithType: LimitedStack<T>.Pop
  fullName: DrawnUi.Infrastructure.Models.LimitedStack<T>.Pop
  nameWithType.vb: LimitedStack(Of T).Pop
  fullName.vb: DrawnUi.Infrastructure.Models.LimitedStack(Of T).Pop
- uid: DrawnUi.Infrastructure.Models.LimitedStack`1.Clear*
  commentId: Overload:DrawnUi.Infrastructure.Models.LimitedStack`1.Clear
  href: DrawnUi.Infrastructure.Models.LimitedStack-1.html#DrawnUi_Infrastructure_Models_LimitedStack_1_Clear
  name: Clear
  nameWithType: LimitedStack<T>.Clear
  fullName: DrawnUi.Infrastructure.Models.LimitedStack<T>.Clear
  nameWithType.vb: LimitedStack(Of T).Clear
  fullName.vb: DrawnUi.Infrastructure.Models.LimitedStack(Of T).Clear
