<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <title>Interface ISkiaLayout | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width">
      <meta name="title" content="Interface ISkiaLayout | DrawnUi Documentation ">
    
    
      <link rel="shortcut icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../styles/docfx.vendor.min.css">
      <link rel="stylesheet" href="../styles/docfx.css">
      <link rel="stylesheet" href="../styles/main.css">
      <meta property="docfx:navrel" content="../toc.html">
      <meta property="docfx:tocrel" content="toc.html">
    
    <meta property="docfx:rel" content="../">
    
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first=First data-prev=Previous data-next=Next data-last=Last></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="DrawnUi.Draw.ISkiaLayout">



  <h1 id="DrawnUi_Draw_ISkiaLayout" data-uid="DrawnUi.Draw.ISkiaLayout" class="text-break">Interface ISkiaLayout</h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritedMembers">
    <h5>Inherited Members</h5>
    <div>
      <a class="xref" href="DrawnUi.Draw.ISkiaControl.html#DrawnUi_Draw_ISkiaControl_VisualLayer">ISkiaControl.VisualLayer</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.ISkiaControl.html#DrawnUi_Draw_ISkiaControl_Parent">ISkiaControl.Parent</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.ISkiaControl.html#DrawnUi_Draw_ISkiaControl_Margin">ISkiaControl.Margin</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.ISkiaControl.html#DrawnUi_Draw_ISkiaControl_Padding">ISkiaControl.Padding</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.ISkiaControl.html#DrawnUi_Draw_ISkiaControl_SetParent_DrawnUi_Draw_IDrawnBase_">ISkiaControl.SetParent(IDrawnBase)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.ISkiaControl.html#DrawnUi_Draw_ISkiaControl_IsGhost">ISkiaControl.IsGhost</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.ISkiaControl.html#DrawnUi_Draw_ISkiaControl_Clipping">ISkiaControl.Clipping</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.ISkiaControl.html#DrawnUi_Draw_ISkiaControl_ZIndex">ISkiaControl.ZIndex</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.ISkiaControl.html#DrawnUi_Draw_ISkiaControl_OptionalOnBeforeDrawing">ISkiaControl.OptionalOnBeforeDrawing()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.ISkiaControl.html#DrawnUi_Draw_ISkiaControl_OnBeforeMeasure">ISkiaControl.OnBeforeMeasure()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.ISkiaControl.html#DrawnUi_Draw_ISkiaControl_Arrange_SkiaSharp_SKRect_System_Single_System_Single_System_Single_">ISkiaControl.Arrange(SKRect, float, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.ISkiaControl.html#DrawnUi_Draw_ISkiaControl_Render_DrawnUi_Draw_DrawingContext_">ISkiaControl.Render(DrawingContext)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.ISkiaControl.html#DrawnUi_Draw_ISkiaControl_RenderedAtDestination">ISkiaControl.RenderedAtDestination</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.ISkiaControl.html#DrawnUi_Draw_ISkiaControl_SetChildren_System_Collections_Generic_IEnumerable_DrawnUi_Draw_SkiaControl__">ISkiaControl.SetChildren(IEnumerable&lt;SkiaControl&gt;)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.ISkiaControl.html#DrawnUi_Draw_ISkiaControl_Measure_System_Single_System_Single_System_Single_">ISkiaControl.Measure(float, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.ISkiaControl.html#DrawnUi_Draw_ISkiaControl_HorizontalOptions">ISkiaControl.HorizontalOptions</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.ISkiaControl.html#DrawnUi_Draw_ISkiaControl_VerticalOptions">ISkiaControl.VerticalOptions</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.ISkiaControl.html#DrawnUi_Draw_ISkiaControl_CanDraw">ISkiaControl.CanDraw</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_DrawingRect">IDrawnBase.DrawingRect</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_Tag">IDrawnBase.Tag</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_IsVisible">IDrawnBase.IsVisible</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_IsDisposed">IDrawnBase.IsDisposed</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_IsDisposing">IDrawnBase.IsDisposing</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_IsVisibleInViewTree">IDrawnBase.IsVisibleInViewTree()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_GetOnScreenVisibleArea_DrawnUi_Draw_DrawingContext_System_Numerics_Vector2_">IDrawnBase.GetOnScreenVisibleArea(DrawingContext, Vector2)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_Invalidate">IDrawnBase.Invalidate()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_InvalidateParents">IDrawnBase.InvalidateParents()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_ClipSmart_SkiaSharp_SKCanvas_SkiaSharp_SKPath_SkiaSharp_SKClipOperation_">IDrawnBase.ClipSmart(SKCanvas, SKPath, SKClipOperation)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_CreateClip_System_Object_System_Boolean_SkiaSharp_SKPath_">IDrawnBase.CreateClip(object, bool, SKPath)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_RegisterAnimator_DrawnUi_Draw_ISkiaAnimator_">IDrawnBase.RegisterAnimator(ISkiaAnimator)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_UnregisterAnimator_System_Guid_">IDrawnBase.UnregisterAnimator(Guid)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_UnregisterAllAnimatorsByType_System_Type_">IDrawnBase.UnregisterAllAnimatorsByType(Type)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_RegisterGestureListener_DrawnUi_Draw_ISkiaGestureListener_">IDrawnBase.RegisterGestureListener(ISkiaGestureListener)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_UnregisterGestureListener_DrawnUi_Draw_ISkiaGestureListener_">IDrawnBase.UnregisterGestureListener(ISkiaGestureListener)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_PostAnimators">IDrawnBase.PostAnimators</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_Views">IDrawnBase.Views</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_AddSubView_DrawnUi_Draw_SkiaControl_">IDrawnBase.AddSubView(SkiaControl)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_RemoveSubView_DrawnUi_Draw_SkiaControl_">IDrawnBase.RemoveSubView(SkiaControl)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_MeasuredSize">IDrawnBase.MeasuredSize</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_Destination">IDrawnBase.Destination</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_HeightRequest">IDrawnBase.HeightRequest</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_WidthRequest">IDrawnBase.WidthRequest</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_Height">IDrawnBase.Height</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_Width">IDrawnBase.Width</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_TranslationX">IDrawnBase.TranslationX</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_TranslationY">IDrawnBase.TranslationY</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_InputTransparent">IDrawnBase.InputTransparent</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_IsClippedToBounds">IDrawnBase.IsClippedToBounds</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_ClipEffects">IDrawnBase.ClipEffects</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_UpdateLocks">IDrawnBase.UpdateLocks</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_InvalidateByChild_DrawnUi_Draw_SkiaControl_">IDrawnBase.InvalidateByChild(SkiaControl)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_UpdateByChild_DrawnUi_Draw_SkiaControl_">IDrawnBase.UpdateByChild(SkiaControl)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_ShouldInvalidateByChildren">IDrawnBase.ShouldInvalidateByChildren</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_RenderingScale">IDrawnBase.RenderingScale</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_X">IDrawnBase.X</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_Y">IDrawnBase.Y</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_InvalidateViewport">IDrawnBase.InvalidateViewport()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_Repaint">IDrawnBase.Repaint()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_InvalidateViewsList">IDrawnBase.InvalidateViewsList()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_DisposeObject_System_IDisposable_">IDrawnBase.DisposeObject(IDisposable)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.ICanBeUpdatedWithContext.html#DrawnUi_Draw_ICanBeUpdatedWithContext_BindingContext">ICanBeUpdatedWithContext.BindingContext</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.ICanBeUpdated.html#DrawnUi_Draw_ICanBeUpdated_Update">ICanBeUpdated.Update()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.ILayoutInsideViewport.html#DrawnUi_Draw_ILayoutInsideViewport_GetVisibleChildIndexAt_SkiaSharp_SKPoint_">ILayoutInsideViewport.GetVisibleChildIndexAt(SKPoint)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.ILayoutInsideViewport.html#DrawnUi_Draw_ILayoutInsideViewport_GetChildIndexAt_SkiaSharp_SKPoint_">ILayoutInsideViewport.GetChildIndexAt(SKPoint)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IInsideViewport.html#DrawnUi_Draw_IInsideViewport_OnViewportWasChanged_DrawnUi_Draw_ScaledRect_">IInsideViewport.OnViewportWasChanged(ScaledRect)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IInsideViewport.html#DrawnUi_Draw_IInsideViewport_OnLoaded">IInsideViewport.OnLoaded()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IVisibilityAware.html#DrawnUi_Draw_IVisibilityAware_OnAppearing">IVisibilityAware.OnAppearing()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IVisibilityAware.html#DrawnUi_Draw_IVisibilityAware_OnAppeared">IVisibilityAware.OnAppeared()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IVisibilityAware.html#DrawnUi_Draw_IVisibilityAware_OnDisappeared">IVisibilityAware.OnDisappeared()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.IVisibilityAware.html#DrawnUi_Draw_IVisibilityAware_OnDisappearing">IVisibilityAware.OnDisappearing()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.idisposable.dispose">IDisposable.Dispose()</a>
    </div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="DrawnUi.html">DrawnUi</a>.<a class="xref" href="DrawnUi.Draw.html">Draw</a></h6>
  <h6><strong>Assembly</strong>: DrawnUi.Maui.dll</h6>
  <h5 id="DrawnUi_Draw_ISkiaLayout_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public interface ISkiaLayout : ISkiaControl, IDrawnBase, ICanBeUpdatedWithContext, ICanBeUpdated, ILayoutInsideViewport, IInsideViewport, IVisibilityAware, IDisposable</code></pre>
  </div>
  <h3 id="properties">Properties
</h3>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_ISkiaLayout_NeedAutoHeight.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.ISkiaLayout.NeedAutoHeight%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Interfaces/ISkiaLayout.cs/#L7">View Source</a>
  </span>
  <a id="DrawnUi_Draw_ISkiaLayout_NeedAutoHeight_" data-uid="DrawnUi.Draw.ISkiaLayout.NeedAutoHeight*"></a>
  <h4 id="DrawnUi_Draw_ISkiaLayout_NeedAutoHeight" data-uid="DrawnUi.Draw.ISkiaLayout.NeedAutoHeight">NeedAutoHeight</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool NeedAutoHeight { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_ISkiaLayout_NeedAutoSize.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.ISkiaLayout.NeedAutoSize%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Interfaces/ISkiaLayout.cs/#L5">View Source</a>
  </span>
  <a id="DrawnUi_Draw_ISkiaLayout_NeedAutoSize_" data-uid="DrawnUi.Draw.ISkiaLayout.NeedAutoSize*"></a>
  <h4 id="DrawnUi_Draw_ISkiaLayout_NeedAutoSize" data-uid="DrawnUi.Draw.ISkiaLayout.NeedAutoSize">NeedAutoSize</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool NeedAutoSize { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_ISkiaLayout_NeedAutoWidth.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.ISkiaLayout.NeedAutoWidth%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Interfaces/ISkiaLayout.cs/#L9">View Source</a>
  </span>
  <a id="DrawnUi_Draw_ISkiaLayout_NeedAutoWidth_" data-uid="DrawnUi.Draw.ISkiaLayout.NeedAutoWidth*"></a>
  <h4 id="DrawnUi_Draw_ISkiaLayout_NeedAutoWidth" data-uid="DrawnUi.Draw.ISkiaLayout.NeedAutoWidth">NeedAutoWidth</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool NeedAutoWidth { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="extensionmethods">Extension Methods</h3>
  <div>
      <a class="xref" href="DrawnUi.Draw.DrawnExtensions.html#DrawnUi_Draw_DrawnExtensions_GetVelocityRatioForChild_DrawnUi_Draw_IDrawnBase_DrawnUi_Draw_ISkiaControl_">DrawnExtensions.GetVelocityRatioForChild(IDrawnBase, ISkiaControl)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_">InternalExtensions.FromPlatform(object)</a>
  </div>

</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_ISkiaLayout.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.ISkiaLayout%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A" class="contribution-link">Edit this page</a>
                  </li>
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Interfaces/ISkiaLayout.cs/#L3" class="contribution-link">View Source</a>
                  </li>
                </ul>
              </div>
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In this article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      
      <span>Generated by <strong>DocFX</strong></span>
          </div>
        </div>
      </footer>
    </div>

    <script type="text/javascript" src="../styles/docfx.vendor.min.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
