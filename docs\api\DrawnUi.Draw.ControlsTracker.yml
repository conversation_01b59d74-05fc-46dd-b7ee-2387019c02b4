### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.ControlsTracker
  commentId: T:DrawnUi.Draw.ControlsTracker
  id: ControlsTracker
  parent: DrawnU<PERSON>.Draw
  children:
  - DrawnUi.Draw.ControlsTracker.Add(DrawnUi.Draw.SkiaControl)
  - DrawnUi.Draw.ControlsTracker.Clear
  - DrawnUi.Draw.ControlsTracker.GetList
  - DrawnUi.Draw.ControlsTracker.Invalidate
  - DrawnUi.Draw.ControlsTracker.Remove(DrawnUi.Draw.SkiaControl)
  - DrawnUi.Draw.ControlsTracker.Sorted
  - DrawnUi.Draw.ControlsTracker._dic
  - DrawnUi.Draw.ControlsTracker._isDirty
  langs:
  - csharp
  - vb
  name: ControlsTracker
  nameWithType: ControlsTracker
  fullName: DrawnUi.Draw.ControlsTracker
  type: Class
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ControlsTracker.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ControlsTracker
    path: ../src/Shared/Draw/Internals/Models/ControlsTracker.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public class ControlsTracker
    content.vb: Public Class ControlsTracker
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.ControlsTracker._dic
  commentId: F:DrawnUi.Draw.ControlsTracker._dic
  id: _dic
  parent: DrawnUi.Draw.ControlsTracker
  langs:
  - csharp
  - vb
  name: _dic
  nameWithType: ControlsTracker._dic
  fullName: DrawnUi.Draw.ControlsTracker._dic
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ControlsTracker.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: _dic
    path: ../src/Shared/Draw/Internals/Models/ControlsTracker.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: protected readonly Dictionary<Guid, SkiaControl> _dic
    return:
      type: System.Collections.Generic.Dictionary{System.Guid,DrawnUi.Draw.SkiaControl}
    content.vb: Protected ReadOnly _dic As Dictionary(Of Guid, SkiaControl)
- uid: DrawnUi.Draw.ControlsTracker.Sorted
  commentId: P:DrawnUi.Draw.ControlsTracker.Sorted
  id: Sorted
  parent: DrawnUi.Draw.ControlsTracker
  langs:
  - csharp
  - vb
  name: Sorted
  nameWithType: ControlsTracker.Sorted
  fullName: DrawnUi.Draw.ControlsTracker.Sorted
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ControlsTracker.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Sorted
    path: ../src/Shared/Draw/Internals/Models/ControlsTracker.cs
    startLine: 5
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: protected List<SkiaControl> Sorted { get; set; }
    parameters: []
    return:
      type: System.Collections.Generic.List{DrawnUi.Draw.SkiaControl}
    content.vb: Protected Property Sorted As List(Of SkiaControl)
  overload: DrawnUi.Draw.ControlsTracker.Sorted*
- uid: DrawnUi.Draw.ControlsTracker._isDirty
  commentId: F:DrawnUi.Draw.ControlsTracker._isDirty
  id: _isDirty
  parent: DrawnUi.Draw.ControlsTracker
  langs:
  - csharp
  - vb
  name: _isDirty
  nameWithType: ControlsTracker._isDirty
  fullName: DrawnUi.Draw.ControlsTracker._isDirty
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ControlsTracker.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: _isDirty
    path: ../src/Shared/Draw/Internals/Models/ControlsTracker.cs
    startLine: 6
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: protected bool _isDirty
    return:
      type: System.Boolean
    content.vb: Protected _isDirty As Boolean
- uid: DrawnUi.Draw.ControlsTracker.Clear
  commentId: M:DrawnUi.Draw.ControlsTracker.Clear
  id: Clear
  parent: DrawnUi.Draw.ControlsTracker
  langs:
  - csharp
  - vb
  name: Clear()
  nameWithType: ControlsTracker.Clear()
  fullName: DrawnUi.Draw.ControlsTracker.Clear()
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ControlsTracker.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Clear
    path: ../src/Shared/Draw/Internals/Models/ControlsTracker.cs
    startLine: 8
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public void Clear()
    content.vb: Public Sub Clear()
  overload: DrawnUi.Draw.ControlsTracker.Clear*
- uid: DrawnUi.Draw.ControlsTracker.GetList
  commentId: M:DrawnUi.Draw.ControlsTracker.GetList
  id: GetList
  parent: DrawnUi.Draw.ControlsTracker
  langs:
  - csharp
  - vb
  name: GetList()
  nameWithType: ControlsTracker.GetList()
  fullName: DrawnUi.Draw.ControlsTracker.GetList()
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ControlsTracker.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: GetList
    path: ../src/Shared/Draw/Internals/Models/ControlsTracker.cs
    startLine: 17
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public List<SkiaControl> GetList()
    return:
      type: System.Collections.Generic.List{DrawnUi.Draw.SkiaControl}
    content.vb: Public Function GetList() As List(Of SkiaControl)
  overload: DrawnUi.Draw.ControlsTracker.GetList*
- uid: DrawnUi.Draw.ControlsTracker.Add(DrawnUi.Draw.SkiaControl)
  commentId: M:DrawnUi.Draw.ControlsTracker.Add(DrawnUi.Draw.SkiaControl)
  id: Add(DrawnUi.Draw.SkiaControl)
  parent: DrawnUi.Draw.ControlsTracker
  langs:
  - csharp
  - vb
  name: Add(SkiaControl)
  nameWithType: ControlsTracker.Add(SkiaControl)
  fullName: DrawnUi.Draw.ControlsTracker.Add(DrawnUi.Draw.SkiaControl)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ControlsTracker.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Add
    path: ../src/Shared/Draw/Internals/Models/ControlsTracker.cs
    startLine: 31
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public void Add(SkiaControl item)
    parameters:
    - id: item
      type: DrawnUi.Draw.SkiaControl
    content.vb: Public Sub Add(item As SkiaControl)
  overload: DrawnUi.Draw.ControlsTracker.Add*
- uid: DrawnUi.Draw.ControlsTracker.Remove(DrawnUi.Draw.SkiaControl)
  commentId: M:DrawnUi.Draw.ControlsTracker.Remove(DrawnUi.Draw.SkiaControl)
  id: Remove(DrawnUi.Draw.SkiaControl)
  parent: DrawnUi.Draw.ControlsTracker
  langs:
  - csharp
  - vb
  name: Remove(SkiaControl)
  nameWithType: ControlsTracker.Remove(SkiaControl)
  fullName: DrawnUi.Draw.ControlsTracker.Remove(DrawnUi.Draw.SkiaControl)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ControlsTracker.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Remove
    path: ../src/Shared/Draw/Internals/Models/ControlsTracker.cs
    startLine: 40
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public void Remove(SkiaControl item)
    parameters:
    - id: item
      type: DrawnUi.Draw.SkiaControl
    content.vb: Public Sub Remove(item As SkiaControl)
  overload: DrawnUi.Draw.ControlsTracker.Remove*
- uid: DrawnUi.Draw.ControlsTracker.Invalidate
  commentId: M:DrawnUi.Draw.ControlsTracker.Invalidate
  id: Invalidate
  parent: DrawnUi.Draw.ControlsTracker
  langs:
  - csharp
  - vb
  name: Invalidate()
  nameWithType: ControlsTracker.Invalidate()
  fullName: DrawnUi.Draw.ControlsTracker.Invalidate()
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ControlsTracker.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Invalidate
    path: ../src/Shared/Draw/Internals/Models/ControlsTracker.cs
    startLine: 51
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public void Invalidate()
    content.vb: Public Sub Invalidate()
  overload: DrawnUi.Draw.ControlsTracker.Invalidate*
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: System.Collections.Generic.Dictionary{System.Guid,DrawnUi.Draw.SkiaControl}
  commentId: T:System.Collections.Generic.Dictionary{System.Guid,DrawnUi.Draw.SkiaControl}
  parent: System.Collections.Generic
  definition: System.Collections.Generic.Dictionary`2
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2
  name: Dictionary<Guid, SkiaControl>
  nameWithType: Dictionary<Guid, SkiaControl>
  fullName: System.Collections.Generic.Dictionary<System.Guid, DrawnUi.Draw.SkiaControl>
  nameWithType.vb: Dictionary(Of Guid, SkiaControl)
  fullName.vb: System.Collections.Generic.Dictionary(Of System.Guid, DrawnUi.Draw.SkiaControl)
  name.vb: Dictionary(Of Guid, SkiaControl)
  spec.csharp:
  - uid: System.Collections.Generic.Dictionary`2
    name: Dictionary
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2
  - name: <
  - uid: System.Guid
    name: Guid
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.guid
  - name: ','
  - name: " "
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.Dictionary`2
    name: Dictionary
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2
  - name: (
  - name: Of
  - name: " "
  - uid: System.Guid
    name: Guid
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.guid
  - name: ','
  - name: " "
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
- uid: System.Collections.Generic.Dictionary`2
  commentId: T:System.Collections.Generic.Dictionary`2
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2
  name: Dictionary<TKey, TValue>
  nameWithType: Dictionary<TKey, TValue>
  fullName: System.Collections.Generic.Dictionary<TKey, TValue>
  nameWithType.vb: Dictionary(Of TKey, TValue)
  fullName.vb: System.Collections.Generic.Dictionary(Of TKey, TValue)
  name.vb: Dictionary(Of TKey, TValue)
  spec.csharp:
  - uid: System.Collections.Generic.Dictionary`2
    name: Dictionary
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2
  - name: <
  - name: TKey
  - name: ','
  - name: " "
  - name: TValue
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.Dictionary`2
    name: Dictionary
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2
  - name: (
  - name: Of
  - name: " "
  - name: TKey
  - name: ','
  - name: " "
  - name: TValue
  - name: )
- uid: System.Collections.Generic
  commentId: N:System.Collections.Generic
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Collections.Generic
  nameWithType: System.Collections.Generic
  fullName: System.Collections.Generic
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Generic
    name: Generic
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Generic
    name: Generic
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic
- uid: DrawnUi.Draw.ControlsTracker.Sorted*
  commentId: Overload:DrawnUi.Draw.ControlsTracker.Sorted
  href: DrawnUi.Draw.ControlsTracker.html#DrawnUi_Draw_ControlsTracker_Sorted
  name: Sorted
  nameWithType: ControlsTracker.Sorted
  fullName: DrawnUi.Draw.ControlsTracker.Sorted
- uid: System.Collections.Generic.List{DrawnUi.Draw.SkiaControl}
  commentId: T:System.Collections.Generic.List{DrawnUi.Draw.SkiaControl}
  parent: System.Collections.Generic
  definition: System.Collections.Generic.List`1
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  name: List<SkiaControl>
  nameWithType: List<SkiaControl>
  fullName: System.Collections.Generic.List<DrawnUi.Draw.SkiaControl>
  nameWithType.vb: List(Of SkiaControl)
  fullName.vb: System.Collections.Generic.List(Of DrawnUi.Draw.SkiaControl)
  name.vb: List(Of SkiaControl)
  spec.csharp:
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: <
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
- uid: System.Collections.Generic.List`1
  commentId: T:System.Collections.Generic.List`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  name: List<T>
  nameWithType: List<T>
  fullName: System.Collections.Generic.List<T>
  nameWithType.vb: List(Of T)
  fullName.vb: System.Collections.Generic.List(Of T)
  name.vb: List(Of T)
  spec.csharp:
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: DrawnUi.Draw.ControlsTracker.Clear*
  commentId: Overload:DrawnUi.Draw.ControlsTracker.Clear
  href: DrawnUi.Draw.ControlsTracker.html#DrawnUi_Draw_ControlsTracker_Clear
  name: Clear
  nameWithType: ControlsTracker.Clear
  fullName: DrawnUi.Draw.ControlsTracker.Clear
- uid: DrawnUi.Draw.ControlsTracker.GetList*
  commentId: Overload:DrawnUi.Draw.ControlsTracker.GetList
  href: DrawnUi.Draw.ControlsTracker.html#DrawnUi_Draw_ControlsTracker_GetList
  name: GetList
  nameWithType: ControlsTracker.GetList
  fullName: DrawnUi.Draw.ControlsTracker.GetList
- uid: DrawnUi.Draw.ControlsTracker.Add*
  commentId: Overload:DrawnUi.Draw.ControlsTracker.Add
  href: DrawnUi.Draw.ControlsTracker.html#DrawnUi_Draw_ControlsTracker_Add_DrawnUi_Draw_SkiaControl_
  name: Add
  nameWithType: ControlsTracker.Add
  fullName: DrawnUi.Draw.ControlsTracker.Add
- uid: DrawnUi.Draw.SkiaControl
  commentId: T:DrawnUi.Draw.SkiaControl
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaControl.html
  name: SkiaControl
  nameWithType: SkiaControl
  fullName: DrawnUi.Draw.SkiaControl
- uid: DrawnUi.Draw.ControlsTracker.Remove*
  commentId: Overload:DrawnUi.Draw.ControlsTracker.Remove
  href: DrawnUi.Draw.ControlsTracker.html#DrawnUi_Draw_ControlsTracker_Remove_DrawnUi_Draw_SkiaControl_
  name: Remove
  nameWithType: ControlsTracker.Remove
  fullName: DrawnUi.Draw.ControlsTracker.Remove
- uid: DrawnUi.Draw.ControlsTracker.Invalidate*
  commentId: Overload:DrawnUi.Draw.ControlsTracker.Invalidate
  href: DrawnUi.Draw.ControlsTracker.html#DrawnUi_Draw_ControlsTracker_Invalidate
  name: Invalidate
  nameWithType: ControlsTracker.Invalidate
  fullName: DrawnUi.Draw.ControlsTracker.Invalidate
