### YamlMime:ManagedReference
items:
- uid: DrawnUi.Infrastructure.Helpers.RubberBandUtils
  commentId: T:DrawnUi.Infrastructure.Helpers.RubberBandUtils
  id: RubberBandUtils
  parent: DrawnUi.Infrastructure.Helpers
  children:
  - DrawnUi.Infrastructure.Helpers.RubberBandUtils.Clamp(System.Numerics.Vector2,System.Numerics.Vector2,SkiaSharp.SKRect,System.Single)
  - DrawnUi.Infrastructure.Helpers.RubberBandUtils.ClampOnTrack(System.Numerics.Vector2,SkiaSharp.SKRect,System.Single,System.Nullable{System.Numerics.Vector2})
  - DrawnUi.Infrastructure.Helpers.RubberBandUtils.RubberBandClamp(System.Single,System.Single,DrawnUi.Draw.RangeF,System.Single,System.Single)
  - DrawnUi.Infrastructure.Helpers.RubberBandUtils.RubberBandClamp(System.Single,System.Single,System.Single,System.Single)
  langs:
  - csharp
  - vb
  name: RubberBandUtils
  nameWithType: RubberBandUtils
  fullName: DrawnUi.Infrastructure.Helpers.RubberBandUtils
  type: Class
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/RubberBandUtils.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: RubberBandUtils
    path: ../src/Shared/Draw/Internals/Helpers/RubberBandUtils.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure.Helpers
  syntax:
    content: public static class RubberBandUtils
    content.vb: Public Module RubberBandUtils
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
- uid: DrawnUi.Infrastructure.Helpers.RubberBandUtils.ClampOnTrack(System.Numerics.Vector2,SkiaSharp.SKRect,System.Single,System.Nullable{System.Numerics.Vector2})
  commentId: M:DrawnUi.Infrastructure.Helpers.RubberBandUtils.ClampOnTrack(System.Numerics.Vector2,SkiaSharp.SKRect,System.Single,System.Nullable{System.Numerics.Vector2})
  id: ClampOnTrack(System.Numerics.Vector2,SkiaSharp.SKRect,System.Single,System.Nullable{System.Numerics.Vector2})
  parent: DrawnUi.Infrastructure.Helpers.RubberBandUtils
  langs:
  - csharp
  - vb
  name: ClampOnTrack(Vector2, SKRect, float, Vector2?)
  nameWithType: RubberBandUtils.ClampOnTrack(Vector2, SKRect, float, Vector2?)
  fullName: DrawnUi.Infrastructure.Helpers.RubberBandUtils.ClampOnTrack(System.Numerics.Vector2, SkiaSharp.SKRect, float, System.Numerics.Vector2?)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/RubberBandUtils.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ClampOnTrack
    path: ../src/Shared/Draw/Internals/Helpers/RubberBandUtils.cs
    startLine: 28
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure.Helpers
  summary: track is the bounds of the possible scrolling offset, for example can be like {0, -1000, 0, 0}
  example: []
  syntax:
    content: public static Vector2 ClampOnTrack(Vector2 point, SKRect track, float coeff = 0.55, Vector2? customDims = null)
    parameters:
    - id: point
      type: System.Numerics.Vector2
      description: ''
    - id: track
      type: SkiaSharp.SKRect
      description: ''
    - id: coeff
      type: System.Single
      description: ''
    - id: customDims
      type: System.Nullable{System.Numerics.Vector2}
    return:
      type: System.Numerics.Vector2
      description: ''
    content.vb: Public Shared Function ClampOnTrack(point As Vector2, track As SKRect, coeff As Single = 0.55, customDims As Vector2? = Nothing) As Vector2
  overload: DrawnUi.Infrastructure.Helpers.RubberBandUtils.ClampOnTrack*
  nameWithType.vb: RubberBandUtils.ClampOnTrack(Vector2, SKRect, Single, Vector2?)
  fullName.vb: DrawnUi.Infrastructure.Helpers.RubberBandUtils.ClampOnTrack(System.Numerics.Vector2, SkiaSharp.SKRect, Single, System.Numerics.Vector2?)
  name.vb: ClampOnTrack(Vector2, SKRect, Single, Vector2?)
- uid: DrawnUi.Infrastructure.Helpers.RubberBandUtils.Clamp(System.Numerics.Vector2,System.Numerics.Vector2,SkiaSharp.SKRect,System.Single)
  commentId: M:DrawnUi.Infrastructure.Helpers.RubberBandUtils.Clamp(System.Numerics.Vector2,System.Numerics.Vector2,SkiaSharp.SKRect,System.Single)
  id: Clamp(System.Numerics.Vector2,System.Numerics.Vector2,SkiaSharp.SKRect,System.Single)
  parent: DrawnUi.Infrastructure.Helpers.RubberBandUtils
  langs:
  - csharp
  - vb
  name: Clamp(Vector2, Vector2, SKRect, float)
  nameWithType: RubberBandUtils.Clamp(Vector2, Vector2, SKRect, float)
  fullName: DrawnUi.Infrastructure.Helpers.RubberBandUtils.Clamp(System.Numerics.Vector2, System.Numerics.Vector2, SkiaSharp.SKRect, float)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/RubberBandUtils.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Clamp
    path: ../src/Shared/Draw/Internals/Helpers/RubberBandUtils.cs
    startLine: 39
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure.Helpers
  syntax:
    content: public static Vector2 Clamp(Vector2 point, Vector2 dims, SKRect bounds, float coeff = 0.55)
    parameters:
    - id: point
      type: System.Numerics.Vector2
    - id: dims
      type: System.Numerics.Vector2
    - id: bounds
      type: SkiaSharp.SKRect
    - id: coeff
      type: System.Single
    return:
      type: System.Numerics.Vector2
    content.vb: Public Shared Function Clamp(point As Vector2, dims As Vector2, bounds As SKRect, coeff As Single = 0.55) As Vector2
  overload: DrawnUi.Infrastructure.Helpers.RubberBandUtils.Clamp*
  nameWithType.vb: RubberBandUtils.Clamp(Vector2, Vector2, SKRect, Single)
  fullName.vb: DrawnUi.Infrastructure.Helpers.RubberBandUtils.Clamp(System.Numerics.Vector2, System.Numerics.Vector2, SkiaSharp.SKRect, Single)
  name.vb: Clamp(Vector2, Vector2, SKRect, Single)
- uid: DrawnUi.Infrastructure.Helpers.RubberBandUtils.RubberBandClamp(System.Single,System.Single,System.Single,System.Single)
  commentId: M:DrawnUi.Infrastructure.Helpers.RubberBandUtils.RubberBandClamp(System.Single,System.Single,System.Single,System.Single)
  id: RubberBandClamp(System.Single,System.Single,System.Single,System.Single)
  parent: DrawnUi.Infrastructure.Helpers.RubberBandUtils
  langs:
  - csharp
  - vb
  name: RubberBandClamp(float, float, float, float)
  nameWithType: RubberBandUtils.RubberBandClamp(float, float, float, float)
  fullName: DrawnUi.Infrastructure.Helpers.RubberBandUtils.RubberBandClamp(float, float, float, float)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/RubberBandUtils.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: RubberBandClamp
    path: ../src/Shared/Draw/Internals/Helpers/RubberBandUtils.cs
    startLine: 52
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure.Helpers
  syntax:
    content: public static float RubberBandClamp(float diff, float coeff, float dim, float onEmpty)
    parameters:
    - id: diff
      type: System.Single
    - id: coeff
      type: System.Single
    - id: dim
      type: System.Single
    - id: onEmpty
      type: System.Single
    return:
      type: System.Single
    content.vb: Public Shared Function RubberBandClamp(diff As Single, coeff As Single, [dim] As Single, onEmpty As Single) As Single
  overload: DrawnUi.Infrastructure.Helpers.RubberBandUtils.RubberBandClamp*
  nameWithType.vb: RubberBandUtils.RubberBandClamp(Single, Single, Single, Single)
  fullName.vb: DrawnUi.Infrastructure.Helpers.RubberBandUtils.RubberBandClamp(Single, Single, Single, Single)
  name.vb: RubberBandClamp(Single, Single, Single, Single)
- uid: DrawnUi.Infrastructure.Helpers.RubberBandUtils.RubberBandClamp(System.Single,System.Single,DrawnUi.Draw.RangeF,System.Single,System.Single)
  commentId: M:DrawnUi.Infrastructure.Helpers.RubberBandUtils.RubberBandClamp(System.Single,System.Single,DrawnUi.Draw.RangeF,System.Single,System.Single)
  id: RubberBandClamp(System.Single,System.Single,DrawnUi.Draw.RangeF,System.Single,System.Single)
  parent: DrawnUi.Infrastructure.Helpers.RubberBandUtils
  langs:
  - csharp
  - vb
  name: RubberBandClamp(float, float, RangeF, float, float)
  nameWithType: RubberBandUtils.RubberBandClamp(float, float, RangeF, float, float)
  fullName: DrawnUi.Infrastructure.Helpers.RubberBandUtils.RubberBandClamp(float, float, DrawnUi.Draw.RangeF, float, float)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/RubberBandUtils.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: RubberBandClamp
    path: ../src/Shared/Draw/Internals/Helpers/RubberBandUtils.cs
    startLine: 99
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure.Helpers
  summary: onEmpty - how much to simulate scrollable area when its zero
  example: []
  syntax:
    content: public static float RubberBandClamp(float coord, float dim, RangeF limits, float coeff = 0.275, float onEmpty = 40)
    parameters:
    - id: coord
      type: System.Single
      description: ''
    - id: dim
      type: System.Single
      description: ''
    - id: limits
      type: DrawnUi.Draw.RangeF
      description: ''
    - id: coeff
      type: System.Single
      description: ''
    - id: onEmpty
      type: System.Single
      description: ''
    return:
      type: System.Single
      description: ''
    content.vb: Public Shared Function RubberBandClamp(coord As Single, [dim] As Single, limits As RangeF, coeff As Single = 0.275, onEmpty As Single = 40) As Single
  overload: DrawnUi.Infrastructure.Helpers.RubberBandUtils.RubberBandClamp*
  nameWithType.vb: RubberBandUtils.RubberBandClamp(Single, Single, RangeF, Single, Single)
  fullName.vb: DrawnUi.Infrastructure.Helpers.RubberBandUtils.RubberBandClamp(Single, Single, DrawnUi.Draw.RangeF, Single, Single)
  name.vb: RubberBandClamp(Single, Single, RangeF, Single, Single)
references:
- uid: DrawnUi.Infrastructure.Helpers
  commentId: N:DrawnUi.Infrastructure.Helpers
  href: DrawnUi.html
  name: DrawnUi.Infrastructure.Helpers
  nameWithType: DrawnUi.Infrastructure.Helpers
  fullName: DrawnUi.Infrastructure.Helpers
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Infrastructure
    name: Infrastructure
    href: DrawnUi.Infrastructure.html
  - name: .
  - uid: DrawnUi.Infrastructure.Helpers
    name: Helpers
    href: DrawnUi.Infrastructure.Helpers.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Infrastructure
    name: Infrastructure
    href: DrawnUi.Infrastructure.html
  - name: .
  - uid: DrawnUi.Infrastructure.Helpers
    name: Helpers
    href: DrawnUi.Infrastructure.Helpers.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Infrastructure.Helpers.RubberBandUtils.ClampOnTrack*
  commentId: Overload:DrawnUi.Infrastructure.Helpers.RubberBandUtils.ClampOnTrack
  href: DrawnUi.Infrastructure.Helpers.RubberBandUtils.html#DrawnUi_Infrastructure_Helpers_RubberBandUtils_ClampOnTrack_System_Numerics_Vector2_SkiaSharp_SKRect_System_Single_System_Nullable_System_Numerics_Vector2__
  name: ClampOnTrack
  nameWithType: RubberBandUtils.ClampOnTrack
  fullName: DrawnUi.Infrastructure.Helpers.RubberBandUtils.ClampOnTrack
- uid: System.Numerics.Vector2
  commentId: T:System.Numerics.Vector2
  parent: System.Numerics
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.numerics.vector2
  name: Vector2
  nameWithType: Vector2
  fullName: System.Numerics.Vector2
- uid: SkiaSharp.SKRect
  commentId: T:SkiaSharp.SKRect
  parent: SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.skrect
  name: SKRect
  nameWithType: SKRect
  fullName: SkiaSharp.SKRect
- uid: System.Single
  commentId: T:System.Single
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.single
  name: float
  nameWithType: float
  fullName: float
  nameWithType.vb: Single
  fullName.vb: Single
  name.vb: Single
- uid: System.Nullable{System.Numerics.Vector2}
  commentId: T:System.Nullable{System.Numerics.Vector2}
  parent: System
  definition: System.Nullable`1
  href: https://learn.microsoft.com/dotnet/api/system.numerics.vector2
  name: Vector2?
  nameWithType: Vector2?
  fullName: System.Numerics.Vector2?
  spec.csharp:
  - uid: System.Numerics.Vector2
    name: Vector2
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.numerics.vector2
  - name: '?'
  spec.vb:
  - uid: System.Numerics.Vector2
    name: Vector2
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.numerics.vector2
  - name: '?'
- uid: System.Numerics
  commentId: N:System.Numerics
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Numerics
  nameWithType: System.Numerics
  fullName: System.Numerics
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Numerics
    name: Numerics
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.numerics
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Numerics
    name: Numerics
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.numerics
- uid: SkiaSharp
  commentId: N:SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp
  name: SkiaSharp
  nameWithType: SkiaSharp
  fullName: SkiaSharp
- uid: System.Nullable`1
  commentId: T:System.Nullable`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.nullable-1
  name: Nullable<T>
  nameWithType: Nullable<T>
  fullName: System.Nullable<T>
  nameWithType.vb: Nullable(Of T)
  fullName.vb: System.Nullable(Of T)
  name.vb: Nullable(Of T)
  spec.csharp:
  - uid: System.Nullable`1
    name: Nullable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.nullable-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Nullable`1
    name: Nullable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.nullable-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: DrawnUi.Infrastructure.Helpers.RubberBandUtils.Clamp*
  commentId: Overload:DrawnUi.Infrastructure.Helpers.RubberBandUtils.Clamp
  href: DrawnUi.Infrastructure.Helpers.RubberBandUtils.html#DrawnUi_Infrastructure_Helpers_RubberBandUtils_Clamp_System_Numerics_Vector2_System_Numerics_Vector2_SkiaSharp_SKRect_System_Single_
  name: Clamp
  nameWithType: RubberBandUtils.Clamp
  fullName: DrawnUi.Infrastructure.Helpers.RubberBandUtils.Clamp
- uid: DrawnUi.Infrastructure.Helpers.RubberBandUtils.RubberBandClamp*
  commentId: Overload:DrawnUi.Infrastructure.Helpers.RubberBandUtils.RubberBandClamp
  href: DrawnUi.Infrastructure.Helpers.RubberBandUtils.html#DrawnUi_Infrastructure_Helpers_RubberBandUtils_RubberBandClamp_System_Single_System_Single_System_Single_System_Single_
  name: RubberBandClamp
  nameWithType: RubberBandUtils.RubberBandClamp
  fullName: DrawnUi.Infrastructure.Helpers.RubberBandUtils.RubberBandClamp
- uid: DrawnUi.Draw.RangeF
  commentId: T:DrawnUi.Draw.RangeF
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.RangeF.html
  name: RangeF
  nameWithType: RangeF
  fullName: DrawnUi.Draw.RangeF
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
