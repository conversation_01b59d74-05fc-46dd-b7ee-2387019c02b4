<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <title>Class PerpetualPendulum | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width">
      <meta name="title" content="Class PerpetualPendulum | DrawnUi Documentation ">
    
    
      <link rel="shortcut icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../styles/docfx.vendor.min.css">
      <link rel="stylesheet" href="../styles/docfx.css">
      <link rel="stylesheet" href="../styles/main.css">
      <meta property="docfx:navrel" content="../toc.html">
      <meta property="docfx:tocrel" content="toc.html">
    
    <meta property="docfx:rel" content="../">
    
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first=First data-prev=Previous data-next=Next data-last=Last></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="DrawnUi.Infrastructure.PerpetualPendulum">



  <h1 id="DrawnUi_Infrastructure_PerpetualPendulum" data-uid="DrawnUi.Infrastructure.PerpetualPendulum" class="text-break">Class PerpetualPendulum</h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></div>
    <div class="level1"><a class="xref" href="DrawnUi.Infrastructure.Pendulum.html">Pendulum</a></div>
    <div class="level2"><span class="xref">PerpetualPendulum</span></div>
  </div>
  <div class="inheritedMembers">
    <h5>Inherited Members</h5>
    <div>
      <a class="xref" href="DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_wireVector">Pendulum.wireVector</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_accelerationVector">Pendulum.accelerationVector</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_velocityVector">Pendulum.velocityVector</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_angularAcceleration">Pendulum.angularAcceleration</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_AngularVelocity">Pendulum.AngularVelocity</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_RodLength">Pendulum.RodLength</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_AirResistance">Pendulum.AirResistance</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_Gravity">Pendulum.Gravity</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_angle">Pendulum.angle</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_Reset">Pendulum.Reset()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_SetAmplitude_System_Double_">Pendulum.SetAmplitude(double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_getInitialAngle">Pendulum.getInitialAngle()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_setInitialAngle_System_Double_">Pendulum.setInitialAngle(double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_getInitialVelocity">Pendulum.getInitialVelocity()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_setInitialVelocity_System_Double_">Pendulum.setInitialVelocity(double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_getAngle">Pendulum.getAngle()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_getAirResistence">Pendulum.getAirResistence()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_setAirResistance_System_Double_">Pendulum.setAirResistance(double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_getWireVector">Pendulum.getWireVector()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_getAccelerationVector">Pendulum.getAccelerationVector()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_getVelocityVector">Pendulum.getVelocityVector()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_getAngularAcceleration">Pendulum.getAngularAcceleration()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_getAngularVelocity">Pendulum.getAngularVelocity()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)">object.Equals(object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)">object.Equals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gethashcode">object.GetHashCode()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gettype">object.GetType()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone">object.MemberwiseClone()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.referenceequals">object.ReferenceEquals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.tostring">object.ToString()</a>
    </div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="DrawnUi.html">DrawnUi</a>.<a class="xref" href="DrawnUi.Infrastructure.html">Infrastructure</a></h6>
  <h6><strong>Assembly</strong>: DrawnUi.Maui.dll</h6>
  <h5 id="DrawnUi_Infrastructure_PerpetualPendulum_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class PerpetualPendulum : Pendulum</code></pre>
  </div>
  <h3 id="methods">Methods
</h3>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Infrastructure_PerpetualPendulum_Update_System_Double_.md&amp;value=---%0Auid%3A%20DrawnUi.Infrastructure.PerpetualPendulum.Update(System.Double)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Helpers/Pendulum.cs/#L5">View Source</a>
  </span>
  <a id="DrawnUi_Infrastructure_PerpetualPendulum_Update_" data-uid="DrawnUi.Infrastructure.PerpetualPendulum.Update*"></a>
  <h4 id="DrawnUi_Infrastructure_PerpetualPendulum_Update_System_Double_" data-uid="DrawnUi.Infrastructure.PerpetualPendulum.Update(System.Double)">Update(double)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override void Update(double timeStep)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></td>
        <td><span class="parametername">timeStep</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="overrides">Overrides</h5>
  <div><a class="xref" href="DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_Update_System_Double_">Pendulum.Update(double)</a></div>
  <h3 id="extensionmethods">Extension Methods</h3>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_">InternalExtensions.FromPlatform(object)</a>
  </div>

</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Infrastructure_PerpetualPendulum.md&amp;value=---%0Auid%3A%20DrawnUi.Infrastructure.PerpetualPendulum%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A" class="contribution-link">Edit this page</a>
                  </li>
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Helpers/Pendulum.cs/#L3" class="contribution-link">View Source</a>
                  </li>
                </ul>
              </div>
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In this article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      
      <span>Generated by <strong>DocFX</strong></span>
          </div>
        </div>
      </footer>
    </div>

    <script type="text/javascript" src="../styles/docfx.vendor.min.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
