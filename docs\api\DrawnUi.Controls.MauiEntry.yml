### YamlMime:ManagedReference
items:
- uid: DrawnUi.Controls.MauiEntry
  commentId: T:DrawnUi.Controls.MauiEntry
  id: MauiEntry
  parent: DrawnUi.Controls
  children:
  - DrawnUi.Controls.MauiEntry.#ctor
  - DrawnUi.Controls.MauiEntry.Completed
  - DrawnUi.Controls.MauiEntry.MaxLines
  - DrawnUi.Controls.MauiEntry.MaxLinesProperty
  - DrawnUi.Controls.MauiEntry.OnCompleted
  - DrawnUi.Controls.MauiEntry.OnHandlerChanging(Microsoft.Maui.Controls.HandlerChangingEventArgs)
  langs:
  - csharp
  - vb
  name: MauiEntry
  nameWithType: MauiEntry
  fullName: DrawnUi.Controls.MauiEntry
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/EditText/MauiEntry.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: MauiEntry
    path: ../src/Maui/DrawnUi/Controls/EditText/MauiEntry.cs
    startLine: 3
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: 'public class MauiEntry : Entry, INotifyPropertyChanged, IVisualTreeElement, IEffectControlProvider, IToolTipElement, IContextFlyoutElement, IAnimatable, IViewController, IVisualElementController, IElementController, IGestureController, IGestureRecognizers, IPropertyMapperView, IHotReloadableView, IReplaceableView, IEntryController, IElementConfiguration<Entry>, IEntry, IEditor, IView, IElement, ITransform, ITextInput, IText, ITextStyle, IPlaceholder, ITextAlignment'
    content.vb: Public Class MauiEntry Inherits Entry Implements INotifyPropertyChanged, IVisualTreeElement, IEffectControlProvider, IToolTipElement, IContextFlyoutElement, IAnimatable, IViewController, IVisualElementController, IElementController, IGestureController, IGestureRecognizers, IPropertyMapperView, IHotReloadableView, IReplaceableView, IEntryController, IElementConfiguration(Of Entry), IEntry, IEditor, IView, IElement, ITransform, ITextInput, IText, ITextStyle, IPlaceholder, ITextAlignment
  inheritance:
  - System.Object
  - Microsoft.Maui.Controls.BindableObject
  - Microsoft.Maui.Controls.Element
  - Microsoft.Maui.Controls.StyleableElement
  - Microsoft.Maui.Controls.NavigableElement
  - Microsoft.Maui.Controls.VisualElement
  - Microsoft.Maui.Controls.View
  - Microsoft.Maui.Controls.InputView
  - Microsoft.Maui.Controls.Entry
  implements:
  - System.ComponentModel.INotifyPropertyChanged
  - Microsoft.Maui.IVisualTreeElement
  - Microsoft.Maui.Controls.IEffectControlProvider
  - Microsoft.Maui.IToolTipElement
  - Microsoft.Maui.IContextFlyoutElement
  - Microsoft.Maui.Controls.IAnimatable
  - Microsoft.Maui.Controls.IViewController
  - Microsoft.Maui.Controls.IVisualElementController
  - Microsoft.Maui.Controls.IElementController
  - Microsoft.Maui.Controls.Internals.IGestureController
  - Microsoft.Maui.Controls.IGestureRecognizers
  - Microsoft.Maui.IPropertyMapperView
  - Microsoft.Maui.HotReload.IHotReloadableView
  - Microsoft.Maui.IReplaceableView
  - Microsoft.Maui.Controls.IEntryController
  - Microsoft.Maui.Controls.IElementConfiguration{Microsoft.Maui.Controls.Entry}
  - Microsoft.Maui.IEntry
  - Microsoft.Maui.IEditor
  - Microsoft.Maui.IView
  - Microsoft.Maui.IElement
  - Microsoft.Maui.ITransform
  - Microsoft.Maui.ITextInput
  - Microsoft.Maui.IText
  - Microsoft.Maui.ITextStyle
  - Microsoft.Maui.IPlaceholder
  - Microsoft.Maui.ITextAlignment
  inheritedMembers:
  - Microsoft.Maui.Controls.Entry.ReturnTypeProperty
  - Microsoft.Maui.Controls.Entry.ReturnCommandProperty
  - Microsoft.Maui.Controls.Entry.ReturnCommandParameterProperty
  - Microsoft.Maui.Controls.Entry.PlaceholderProperty
  - Microsoft.Maui.Controls.Entry.PlaceholderColorProperty
  - Microsoft.Maui.Controls.Entry.IsPasswordProperty
  - Microsoft.Maui.Controls.Entry.TextProperty
  - Microsoft.Maui.Controls.Entry.TextColorProperty
  - Microsoft.Maui.Controls.Entry.KeyboardProperty
  - Microsoft.Maui.Controls.Entry.CharacterSpacingProperty
  - Microsoft.Maui.Controls.Entry.HorizontalTextAlignmentProperty
  - Microsoft.Maui.Controls.Entry.VerticalTextAlignmentProperty
  - Microsoft.Maui.Controls.Entry.FontFamilyProperty
  - Microsoft.Maui.Controls.Entry.FontSizeProperty
  - Microsoft.Maui.Controls.Entry.FontAttributesProperty
  - Microsoft.Maui.Controls.Entry.FontAutoScalingEnabledProperty
  - Microsoft.Maui.Controls.Entry.IsTextPredictionEnabledProperty
  - Microsoft.Maui.Controls.Entry.CursorPositionProperty
  - Microsoft.Maui.Controls.Entry.SelectionLengthProperty
  - Microsoft.Maui.Controls.Entry.ClearButtonVisibilityProperty
  - Microsoft.Maui.Controls.Entry.On``1
  - Microsoft.Maui.Controls.Entry.MapText(Microsoft.Maui.Handlers.EntryHandler,Microsoft.Maui.Controls.Entry)
  - Microsoft.Maui.Controls.Entry.MapText(Microsoft.Maui.Handlers.IEntryHandler,Microsoft.Maui.Controls.Entry)
  - Microsoft.Maui.Controls.Entry.HorizontalTextAlignment
  - Microsoft.Maui.Controls.Entry.VerticalTextAlignment
  - Microsoft.Maui.Controls.Entry.IsPassword
  - Microsoft.Maui.Controls.Entry.ReturnType
  - Microsoft.Maui.Controls.Entry.ReturnCommand
  - Microsoft.Maui.Controls.Entry.ReturnCommandParameter
  - Microsoft.Maui.Controls.Entry.ClearButtonVisibility
  - Microsoft.Maui.Controls.InputView.IsSpellCheckEnabledProperty
  - Microsoft.Maui.Controls.InputView.MaxLengthProperty
  - Microsoft.Maui.Controls.InputView.IsReadOnlyProperty
  - Microsoft.Maui.Controls.InputView.TextTransformProperty
  - Microsoft.Maui.Controls.InputView.OnTextChanged(System.String,System.String)
  - Microsoft.Maui.Controls.InputView.OnTextTransformChanged(Microsoft.Maui.TextTransform,Microsoft.Maui.TextTransform)
  - Microsoft.Maui.Controls.InputView.UpdateFormsText(System.String,Microsoft.Maui.TextTransform)
  - Microsoft.Maui.Controls.InputView.MaxLength
  - Microsoft.Maui.Controls.InputView.Text
  - Microsoft.Maui.Controls.InputView.Keyboard
  - Microsoft.Maui.Controls.InputView.IsSpellCheckEnabled
  - Microsoft.Maui.Controls.InputView.IsTextPredictionEnabled
  - Microsoft.Maui.Controls.InputView.IsReadOnly
  - Microsoft.Maui.Controls.InputView.Placeholder
  - Microsoft.Maui.Controls.InputView.PlaceholderColor
  - Microsoft.Maui.Controls.InputView.TextColor
  - Microsoft.Maui.Controls.InputView.CharacterSpacing
  - Microsoft.Maui.Controls.InputView.TextTransform
  - Microsoft.Maui.Controls.InputView.CursorPosition
  - Microsoft.Maui.Controls.InputView.SelectionLength
  - Microsoft.Maui.Controls.InputView.FontAttributes
  - Microsoft.Maui.Controls.InputView.FontFamily
  - Microsoft.Maui.Controls.InputView.FontSize
  - Microsoft.Maui.Controls.InputView.FontAutoScalingEnabled
  - Microsoft.Maui.Controls.InputView.TextChanged
  - Microsoft.Maui.Controls.View.VerticalOptionsProperty
  - Microsoft.Maui.Controls.View.HorizontalOptionsProperty
  - Microsoft.Maui.Controls.View.MarginProperty
  - Microsoft.Maui.Controls.View.propertyMapper
  - Microsoft.Maui.Controls.View.ChangeVisualState
  - Microsoft.Maui.Controls.View.GetChildElements(Microsoft.Maui.Graphics.Point)
  - Microsoft.Maui.Controls.View.OnBindingContextChanged
  - Microsoft.Maui.Controls.View.GetRendererOverrides``1
  - Microsoft.Maui.Controls.View.GestureController
  - Microsoft.Maui.Controls.View.GestureRecognizers
  - Microsoft.Maui.Controls.View.HorizontalOptions
  - Microsoft.Maui.Controls.View.Margin
  - Microsoft.Maui.Controls.View.VerticalOptions
  - Microsoft.Maui.Controls.VisualElement.NavigationProperty
  - Microsoft.Maui.Controls.VisualElement.StyleProperty
  - Microsoft.Maui.Controls.VisualElement.InputTransparentProperty
  - Microsoft.Maui.Controls.VisualElement.IsEnabledProperty
  - Microsoft.Maui.Controls.VisualElement.XProperty
  - Microsoft.Maui.Controls.VisualElement.YProperty
  - Microsoft.Maui.Controls.VisualElement.AnchorXProperty
  - Microsoft.Maui.Controls.VisualElement.AnchorYProperty
  - Microsoft.Maui.Controls.VisualElement.TranslationXProperty
  - Microsoft.Maui.Controls.VisualElement.TranslationYProperty
  - Microsoft.Maui.Controls.VisualElement.WidthProperty
  - Microsoft.Maui.Controls.VisualElement.HeightProperty
  - Microsoft.Maui.Controls.VisualElement.RotationProperty
  - Microsoft.Maui.Controls.VisualElement.RotationXProperty
  - Microsoft.Maui.Controls.VisualElement.RotationYProperty
  - Microsoft.Maui.Controls.VisualElement.ScaleProperty
  - Microsoft.Maui.Controls.VisualElement.ScaleXProperty
  - Microsoft.Maui.Controls.VisualElement.ScaleYProperty
  - Microsoft.Maui.Controls.VisualElement.ClipProperty
  - Microsoft.Maui.Controls.VisualElement.VisualProperty
  - Microsoft.Maui.Controls.VisualElement.IsVisibleProperty
  - Microsoft.Maui.Controls.VisualElement.OpacityProperty
  - Microsoft.Maui.Controls.VisualElement.BackgroundColorProperty
  - Microsoft.Maui.Controls.VisualElement.BackgroundProperty
  - Microsoft.Maui.Controls.VisualElement.BehaviorsProperty
  - Microsoft.Maui.Controls.VisualElement.TriggersProperty
  - Microsoft.Maui.Controls.VisualElement.WidthRequestProperty
  - Microsoft.Maui.Controls.VisualElement.HeightRequestProperty
  - Microsoft.Maui.Controls.VisualElement.MinimumWidthRequestProperty
  - Microsoft.Maui.Controls.VisualElement.MinimumHeightRequestProperty
  - Microsoft.Maui.Controls.VisualElement.MaximumWidthRequestProperty
  - Microsoft.Maui.Controls.VisualElement.MaximumHeightRequestProperty
  - Microsoft.Maui.Controls.VisualElement.IsFocusedProperty
  - Microsoft.Maui.Controls.VisualElement.FlowDirectionProperty
  - Microsoft.Maui.Controls.VisualElement.WindowProperty
  - Microsoft.Maui.Controls.VisualElement.ShadowProperty
  - Microsoft.Maui.Controls.VisualElement.ZIndexProperty
  - Microsoft.Maui.Controls.VisualElement.BatchBegin
  - Microsoft.Maui.Controls.VisualElement.BatchCommit
  - Microsoft.Maui.Controls.VisualElement.Focus
  - Microsoft.Maui.Controls.VisualElement.Measure(System.Double,System.Double)
  - Microsoft.Maui.Controls.VisualElement.Measure(System.Double,System.Double,Microsoft.Maui.Controls.MeasureFlags)
  - Microsoft.Maui.Controls.VisualElement.Unfocus
  - Microsoft.Maui.Controls.VisualElement.InvalidateMeasure
  - Microsoft.Maui.Controls.VisualElement.OnChildAdded(Microsoft.Maui.Controls.Element)
  - Microsoft.Maui.Controls.VisualElement.OnChildRemoved(Microsoft.Maui.Controls.Element,System.Int32)
  - Microsoft.Maui.Controls.VisualElement.OnChildrenReordered
  - Microsoft.Maui.Controls.VisualElement.OnMeasure(System.Double,System.Double)
  - Microsoft.Maui.Controls.VisualElement.OnSizeAllocated(System.Double,System.Double)
  - Microsoft.Maui.Controls.VisualElement.SizeAllocated(System.Double,System.Double)
  - Microsoft.Maui.Controls.VisualElement.RefreshIsEnabledProperty
  - Microsoft.Maui.Controls.VisualElement.Arrange(Microsoft.Maui.Graphics.Rect)
  - Microsoft.Maui.Controls.VisualElement.ArrangeOverride(Microsoft.Maui.Graphics.Rect)
  - Microsoft.Maui.Controls.VisualElement.Layout(Microsoft.Maui.Graphics.Rect)
  - Microsoft.Maui.Controls.VisualElement.InvalidateMeasureOverride
  - Microsoft.Maui.Controls.VisualElement.MeasureOverride(System.Double,System.Double)
  - Microsoft.Maui.Controls.VisualElement.MapBackgroundColor(Microsoft.Maui.IViewHandler,Microsoft.Maui.IView)
  - Microsoft.Maui.Controls.VisualElement.MapBackgroundImageSource(Microsoft.Maui.IViewHandler,Microsoft.Maui.IView)
  - Microsoft.Maui.Controls.VisualElement.Visual
  - Microsoft.Maui.Controls.VisualElement.FlowDirection
  - Microsoft.Maui.Controls.VisualElement.Window
  - Microsoft.Maui.Controls.VisualElement.AnchorX
  - Microsoft.Maui.Controls.VisualElement.AnchorY
  - Microsoft.Maui.Controls.VisualElement.BackgroundColor
  - Microsoft.Maui.Controls.VisualElement.Background
  - Microsoft.Maui.Controls.VisualElement.Behaviors
  - Microsoft.Maui.Controls.VisualElement.Bounds
  - Microsoft.Maui.Controls.VisualElement.Height
  - Microsoft.Maui.Controls.VisualElement.HeightRequest
  - Microsoft.Maui.Controls.VisualElement.InputTransparent
  - Microsoft.Maui.Controls.VisualElement.IsEnabled
  - Microsoft.Maui.Controls.VisualElement.IsEnabledCore
  - Microsoft.Maui.Controls.VisualElement.IsFocused
  - Microsoft.Maui.Controls.VisualElement.IsVisible
  - Microsoft.Maui.Controls.VisualElement.MinimumHeightRequest
  - Microsoft.Maui.Controls.VisualElement.MinimumWidthRequest
  - Microsoft.Maui.Controls.VisualElement.MaximumHeightRequest
  - Microsoft.Maui.Controls.VisualElement.MaximumWidthRequest
  - Microsoft.Maui.Controls.VisualElement.Opacity
  - Microsoft.Maui.Controls.VisualElement.Rotation
  - Microsoft.Maui.Controls.VisualElement.RotationX
  - Microsoft.Maui.Controls.VisualElement.RotationY
  - Microsoft.Maui.Controls.VisualElement.Scale
  - Microsoft.Maui.Controls.VisualElement.ScaleX
  - Microsoft.Maui.Controls.VisualElement.ScaleY
  - Microsoft.Maui.Controls.VisualElement.TranslationX
  - Microsoft.Maui.Controls.VisualElement.TranslationY
  - Microsoft.Maui.Controls.VisualElement.Triggers
  - Microsoft.Maui.Controls.VisualElement.Width
  - Microsoft.Maui.Controls.VisualElement.WidthRequest
  - Microsoft.Maui.Controls.VisualElement.X
  - Microsoft.Maui.Controls.VisualElement.Y
  - Microsoft.Maui.Controls.VisualElement.Clip
  - Microsoft.Maui.Controls.VisualElement.Resources
  - Microsoft.Maui.Controls.VisualElement.Frame
  - Microsoft.Maui.Controls.VisualElement.Handler
  - Microsoft.Maui.Controls.VisualElement.Shadow
  - Microsoft.Maui.Controls.VisualElement.ZIndex
  - Microsoft.Maui.Controls.VisualElement.DesiredSize
  - Microsoft.Maui.Controls.VisualElement.IsLoaded
  - Microsoft.Maui.Controls.VisualElement.ChildrenReordered
  - Microsoft.Maui.Controls.VisualElement.Focused
  - Microsoft.Maui.Controls.VisualElement.MeasureInvalidated
  - Microsoft.Maui.Controls.VisualElement.SizeChanged
  - Microsoft.Maui.Controls.VisualElement.Unfocused
  - Microsoft.Maui.Controls.VisualElement.Loaded
  - Microsoft.Maui.Controls.VisualElement.Unloaded
  - Microsoft.Maui.Controls.NavigableElement.OnParentSet
  - Microsoft.Maui.Controls.NavigableElement.Navigation
  - Microsoft.Maui.Controls.StyleableElement.Style
  - Microsoft.Maui.Controls.StyleableElement.StyleClass
  - Microsoft.Maui.Controls.StyleableElement.class
  - Microsoft.Maui.Controls.Element.AutomationIdProperty
  - Microsoft.Maui.Controls.Element.ClassIdProperty
  - Microsoft.Maui.Controls.Element.InsertLogicalChild(System.Int32,Microsoft.Maui.Controls.Element)
  - Microsoft.Maui.Controls.Element.AddLogicalChild(Microsoft.Maui.Controls.Element)
  - Microsoft.Maui.Controls.Element.RemoveLogicalChild(Microsoft.Maui.Controls.Element)
  - Microsoft.Maui.Controls.Element.ClearLogicalChildren
  - Microsoft.Maui.Controls.Element.FindByName(System.String)
  - Microsoft.Maui.Controls.Element.RemoveDynamicResource(Microsoft.Maui.Controls.BindableProperty)
  - Microsoft.Maui.Controls.Element.SetDynamicResource(Microsoft.Maui.Controls.BindableProperty,System.String)
  - Microsoft.Maui.Controls.Element.OnPropertyChanged(System.String)
  - Microsoft.Maui.Controls.Element.OnParentChanging(Microsoft.Maui.Controls.ParentChangingEventArgs)
  - Microsoft.Maui.Controls.Element.OnParentChanged
  - Microsoft.Maui.Controls.Element.OnHandlerChanged
  - Microsoft.Maui.Controls.Element.MapAutomationPropertiesIsInAccessibleTree(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
  - Microsoft.Maui.Controls.Element.MapAutomationPropertiesExcludedWithChildren(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
  - Microsoft.Maui.Controls.Element.AutomationId
  - Microsoft.Maui.Controls.Element.ClassId
  - Microsoft.Maui.Controls.Element.Effects
  - Microsoft.Maui.Controls.Element.Id
  - Microsoft.Maui.Controls.Element.StyleId
  - Microsoft.Maui.Controls.Element.Parent
  - Microsoft.Maui.Controls.Element.ChildAdded
  - Microsoft.Maui.Controls.Element.ChildRemoved
  - Microsoft.Maui.Controls.Element.DescendantAdded
  - Microsoft.Maui.Controls.Element.DescendantRemoved
  - Microsoft.Maui.Controls.Element.ParentChanging
  - Microsoft.Maui.Controls.Element.ParentChanged
  - Microsoft.Maui.Controls.Element.HandlerChanging
  - Microsoft.Maui.Controls.Element.HandlerChanged
  - Microsoft.Maui.Controls.BindableObject.BindingContextProperty
  - Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindableProperty)
  - Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindablePropertyKey)
  - Microsoft.Maui.Controls.BindableObject.GetValue(Microsoft.Maui.Controls.BindableProperty)
  - Microsoft.Maui.Controls.BindableObject.IsSet(Microsoft.Maui.Controls.BindableProperty)
  - Microsoft.Maui.Controls.BindableObject.RemoveBinding(Microsoft.Maui.Controls.BindableProperty)
  - Microsoft.Maui.Controls.BindableObject.SetBinding(Microsoft.Maui.Controls.BindableProperty,Microsoft.Maui.Controls.BindingBase)
  - Microsoft.Maui.Controls.BindableObject.ApplyBindings
  - Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(System.String)
  - Microsoft.Maui.Controls.BindableObject.UnapplyBindings
  - Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty,System.Object)
  - Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey,System.Object)
  - Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindableProperty)
  - Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindablePropertyKey)
  - Microsoft.Maui.Controls.BindableObject.Dispatcher
  - Microsoft.Maui.Controls.BindableObject.BindingContext
  - Microsoft.Maui.Controls.BindableObject.PropertyChanged
  - Microsoft.Maui.Controls.BindableObject.PropertyChanging
  - Microsoft.Maui.Controls.BindableObject.BindingContextChanged
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - DrawnUi.Controls.MauiEntry.DrawnUi.Draw.FluentExtensions.AssignNative``1(DrawnUi.Controls.MauiEntry@)
  - Microsoft.Maui.Controls.Element.DrawnUi.Draw.StaticResourcesExtensions.FindParent``1
  - Microsoft.Maui.Controls.Element.DrawnUi.Extensions.InternalExtensions.FindMauiContext(System.Boolean)
  - Microsoft.Maui.Controls.Element.DrawnUi.Extensions.InternalExtensions.GetParentsPath
  - Microsoft.Maui.Controls.VisualElement.DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents
  - Microsoft.Maui.IView.DrawnUi.Extensions.InternalExtensions.DisposeControlAndChildren
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Controls.MauiEntry.#ctor
  commentId: M:DrawnUi.Controls.MauiEntry.#ctor
  id: '#ctor'
  parent: DrawnUi.Controls.MauiEntry
  langs:
  - csharp
  - vb
  name: MauiEntry()
  nameWithType: MauiEntry.MauiEntry()
  fullName: DrawnUi.Controls.MauiEntry.MauiEntry()
  type: Constructor
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/EditText/MauiEntry.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Maui/DrawnUi/Controls/EditText/MauiEntry.cs
    startLine: 5
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: public MauiEntry()
    content.vb: Public Sub New()
  overload: DrawnUi.Controls.MauiEntry.#ctor*
  nameWithType.vb: MauiEntry.New()
  fullName.vb: DrawnUi.Controls.MauiEntry.New()
  name.vb: New()
- uid: DrawnUi.Controls.MauiEntry.MaxLinesProperty
  commentId: F:DrawnUi.Controls.MauiEntry.MaxLinesProperty
  id: MaxLinesProperty
  parent: DrawnUi.Controls.MauiEntry
  langs:
  - csharp
  - vb
  name: MaxLinesProperty
  nameWithType: MauiEntry.MaxLinesProperty
  fullName: DrawnUi.Controls.MauiEntry.MaxLinesProperty
  type: Field
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/EditText/MauiEntry.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: MaxLinesProperty
    path: ../src/Maui/DrawnUi/Controls/EditText/MauiEntry.cs
    startLine: 15
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: public static readonly BindableProperty MaxLinesProperty
    return:
      type: Microsoft.Maui.Controls.BindableProperty
    content.vb: Public Shared ReadOnly MaxLinesProperty As BindableProperty
- uid: DrawnUi.Controls.MauiEntry.MaxLines
  commentId: P:DrawnUi.Controls.MauiEntry.MaxLines
  id: MaxLines
  parent: DrawnUi.Controls.MauiEntry
  langs:
  - csharp
  - vb
  name: MaxLines
  nameWithType: MauiEntry.MaxLines
  fullName: DrawnUi.Controls.MauiEntry.MaxLines
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/EditText/MauiEntry.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: MaxLines
    path: ../src/Maui/DrawnUi/Controls/EditText/MauiEntry.cs
    startLine: 20
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  summary: WIth 1 will behave like an ordinary Entry, with -1 (auto) or explicitly set you get an Editor
  example: []
  syntax:
    content: public int MaxLines { get; set; }
    parameters: []
    return:
      type: System.Int32
    content.vb: Public Property MaxLines As Integer
  overload: DrawnUi.Controls.MauiEntry.MaxLines*
- uid: DrawnUi.Controls.MauiEntry.OnCompleted
  commentId: E:DrawnUi.Controls.MauiEntry.OnCompleted
  id: OnCompleted
  parent: DrawnUi.Controls.MauiEntry
  langs:
  - csharp
  - vb
  name: OnCompleted
  nameWithType: MauiEntry.OnCompleted
  fullName: DrawnUi.Controls.MauiEntry.OnCompleted
  type: Event
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/EditText/MauiEntry.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: OnCompleted
    path: ../src/Maui/DrawnUi/Controls/EditText/MauiEntry.cs
    startLine: 29
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  summary: Occurs when the user finalizes the text in an entry with the return key.
  example: []
  syntax:
    content: public event EventHandler OnCompleted
    return:
      type: System.EventHandler
    content.vb: Public Event OnCompleted As EventHandler
- uid: DrawnUi.Controls.MauiEntry.Completed
  commentId: M:DrawnUi.Controls.MauiEntry.Completed
  id: Completed
  parent: DrawnUi.Controls.MauiEntry
  langs:
  - csharp
  - vb
  name: Completed()
  nameWithType: MauiEntry.Completed()
  fullName: DrawnUi.Controls.MauiEntry.Completed()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/EditText/MauiEntry.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Completed
    path: ../src/Maui/DrawnUi/Controls/EditText/MauiEntry.cs
    startLine: 31
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  summary: Occurs when the user finalizes the text in an editor with the return key.
  example: []
  syntax:
    content: public void Completed()
    content.vb: Public Sub Completed()
  overload: DrawnUi.Controls.MauiEntry.Completed*
  implements:
  - Microsoft.Maui.IEditor.Completed
- uid: DrawnUi.Controls.MauiEntry.OnHandlerChanging(Microsoft.Maui.Controls.HandlerChangingEventArgs)
  commentId: M:DrawnUi.Controls.MauiEntry.OnHandlerChanging(Microsoft.Maui.Controls.HandlerChangingEventArgs)
  id: OnHandlerChanging(Microsoft.Maui.Controls.HandlerChangingEventArgs)
  parent: DrawnUi.Controls.MauiEntry
  langs:
  - csharp
  - vb
  name: OnHandlerChanging(HandlerChangingEventArgs)
  nameWithType: MauiEntry.OnHandlerChanging(HandlerChangingEventArgs)
  fullName: DrawnUi.Controls.MauiEntry.OnHandlerChanging(Microsoft.Maui.Controls.HandlerChangingEventArgs)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/EditText/MauiEntry.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: OnHandlerChanging
    path: ../src/Maui/DrawnUi/Controls/EditText/MauiEntry.cs
    startLine: 36
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  summary: When overridden in a derived class, should raise the <xref href="Microsoft.Maui.Controls.Element.HandlerChanging" data-throw-if-not-resolved="false"></xref> event.
  remarks: It is the implementor's responsibility to raise the <xref href="Microsoft.Maui.Controls.Element.HandlerChanging" data-throw-if-not-resolved="false"></xref> event.
  example: []
  syntax:
    content: protected override void OnHandlerChanging(HandlerChangingEventArgs args)
    parameters:
    - id: args
      type: Microsoft.Maui.Controls.HandlerChangingEventArgs
      description: Provides data for the <xref href="Microsoft.Maui.Controls.Element.HandlerChanging" data-throw-if-not-resolved="false"></xref> event.
    content.vb: Protected Overrides Sub OnHandlerChanging(args As HandlerChangingEventArgs)
  overridden: Microsoft.Maui.Controls.Element.OnHandlerChanging(Microsoft.Maui.Controls.HandlerChangingEventArgs)
  overload: DrawnUi.Controls.MauiEntry.OnHandlerChanging*
references:
- uid: DrawnUi.Controls
  commentId: N:DrawnUi.Controls
  href: DrawnUi.html
  name: DrawnUi.Controls
  nameWithType: DrawnUi.Controls
  fullName: DrawnUi.Controls
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Controls
    name: Controls
    href: DrawnUi.Controls.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Controls
    name: Controls
    href: DrawnUi.Controls.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: Microsoft.Maui.Controls.BindableObject
  commentId: T:Microsoft.Maui.Controls.BindableObject
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject
  name: BindableObject
  nameWithType: BindableObject
  fullName: Microsoft.Maui.Controls.BindableObject
- uid: Microsoft.Maui.Controls.Element
  commentId: T:Microsoft.Maui.Controls.Element
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  name: Element
  nameWithType: Element
  fullName: Microsoft.Maui.Controls.Element
- uid: Microsoft.Maui.Controls.StyleableElement
  commentId: T:Microsoft.Maui.Controls.StyleableElement
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.styleableelement
  name: StyleableElement
  nameWithType: StyleableElement
  fullName: Microsoft.Maui.Controls.StyleableElement
- uid: Microsoft.Maui.Controls.NavigableElement
  commentId: T:Microsoft.Maui.Controls.NavigableElement
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.navigableelement
  name: NavigableElement
  nameWithType: NavigableElement
  fullName: Microsoft.Maui.Controls.NavigableElement
- uid: Microsoft.Maui.Controls.VisualElement
  commentId: T:Microsoft.Maui.Controls.VisualElement
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement
  name: VisualElement
  nameWithType: VisualElement
  fullName: Microsoft.Maui.Controls.VisualElement
- uid: Microsoft.Maui.Controls.View
  commentId: T:Microsoft.Maui.Controls.View
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view
  name: View
  nameWithType: View
  fullName: Microsoft.Maui.Controls.View
- uid: Microsoft.Maui.Controls.InputView
  commentId: T:Microsoft.Maui.Controls.InputView
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.inputview
  name: InputView
  nameWithType: InputView
  fullName: Microsoft.Maui.Controls.InputView
- uid: Microsoft.Maui.Controls.Entry
  commentId: T:Microsoft.Maui.Controls.Entry
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.entry
  name: Entry
  nameWithType: Entry
  fullName: Microsoft.Maui.Controls.Entry
- uid: System.ComponentModel.INotifyPropertyChanged
  commentId: T:System.ComponentModel.INotifyPropertyChanged
  parent: System.ComponentModel
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.inotifypropertychanged
  name: INotifyPropertyChanged
  nameWithType: INotifyPropertyChanged
  fullName: System.ComponentModel.INotifyPropertyChanged
- uid: Microsoft.Maui.IVisualTreeElement
  commentId: T:Microsoft.Maui.IVisualTreeElement
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.ivisualtreeelement
  name: IVisualTreeElement
  nameWithType: IVisualTreeElement
  fullName: Microsoft.Maui.IVisualTreeElement
- uid: Microsoft.Maui.Controls.IEffectControlProvider
  commentId: T:Microsoft.Maui.Controls.IEffectControlProvider
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ieffectcontrolprovider
  name: IEffectControlProvider
  nameWithType: IEffectControlProvider
  fullName: Microsoft.Maui.Controls.IEffectControlProvider
- uid: Microsoft.Maui.IToolTipElement
  commentId: T:Microsoft.Maui.IToolTipElement
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.itooltipelement
  name: IToolTipElement
  nameWithType: IToolTipElement
  fullName: Microsoft.Maui.IToolTipElement
- uid: Microsoft.Maui.IContextFlyoutElement
  commentId: T:Microsoft.Maui.IContextFlyoutElement
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.icontextflyoutelement
  name: IContextFlyoutElement
  nameWithType: IContextFlyoutElement
  fullName: Microsoft.Maui.IContextFlyoutElement
- uid: Microsoft.Maui.Controls.IAnimatable
  commentId: T:Microsoft.Maui.Controls.IAnimatable
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ianimatable
  name: IAnimatable
  nameWithType: IAnimatable
  fullName: Microsoft.Maui.Controls.IAnimatable
- uid: Microsoft.Maui.Controls.IViewController
  commentId: T:Microsoft.Maui.Controls.IViewController
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.iviewcontroller
  name: IViewController
  nameWithType: IViewController
  fullName: Microsoft.Maui.Controls.IViewController
- uid: Microsoft.Maui.Controls.IVisualElementController
  commentId: T:Microsoft.Maui.Controls.IVisualElementController
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ivisualelementcontroller
  name: IVisualElementController
  nameWithType: IVisualElementController
  fullName: Microsoft.Maui.Controls.IVisualElementController
- uid: Microsoft.Maui.Controls.IElementController
  commentId: T:Microsoft.Maui.Controls.IElementController
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ielementcontroller
  name: IElementController
  nameWithType: IElementController
  fullName: Microsoft.Maui.Controls.IElementController
- uid: Microsoft.Maui.Controls.Internals.IGestureController
  commentId: T:Microsoft.Maui.Controls.Internals.IGestureController
  parent: Microsoft.Maui.Controls.Internals
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.internals.igesturecontroller
  name: IGestureController
  nameWithType: IGestureController
  fullName: Microsoft.Maui.Controls.Internals.IGestureController
- uid: Microsoft.Maui.Controls.IGestureRecognizers
  commentId: T:Microsoft.Maui.Controls.IGestureRecognizers
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.igesturerecognizers
  name: IGestureRecognizers
  nameWithType: IGestureRecognizers
  fullName: Microsoft.Maui.Controls.IGestureRecognizers
- uid: Microsoft.Maui.IPropertyMapperView
  commentId: T:Microsoft.Maui.IPropertyMapperView
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.ipropertymapperview
  name: IPropertyMapperView
  nameWithType: IPropertyMapperView
  fullName: Microsoft.Maui.IPropertyMapperView
- uid: Microsoft.Maui.HotReload.IHotReloadableView
  commentId: T:Microsoft.Maui.HotReload.IHotReloadableView
  parent: Microsoft.Maui.HotReload
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.hotreload.ihotreloadableview
  name: IHotReloadableView
  nameWithType: IHotReloadableView
  fullName: Microsoft.Maui.HotReload.IHotReloadableView
- uid: Microsoft.Maui.IReplaceableView
  commentId: T:Microsoft.Maui.IReplaceableView
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.ireplaceableview
  name: IReplaceableView
  nameWithType: IReplaceableView
  fullName: Microsoft.Maui.IReplaceableView
- uid: Microsoft.Maui.Controls.IEntryController
  commentId: T:Microsoft.Maui.Controls.IEntryController
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ientrycontroller
  name: IEntryController
  nameWithType: IEntryController
  fullName: Microsoft.Maui.Controls.IEntryController
- uid: Microsoft.Maui.Controls.IElementConfiguration{Microsoft.Maui.Controls.Entry}
  commentId: T:Microsoft.Maui.Controls.IElementConfiguration{Microsoft.Maui.Controls.Entry}
  parent: Microsoft.Maui.Controls
  definition: Microsoft.Maui.Controls.IElementConfiguration`1
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ielementconfiguration-1
  name: IElementConfiguration<Entry>
  nameWithType: IElementConfiguration<Entry>
  fullName: Microsoft.Maui.Controls.IElementConfiguration<Microsoft.Maui.Controls.Entry>
  nameWithType.vb: IElementConfiguration(Of Entry)
  fullName.vb: Microsoft.Maui.Controls.IElementConfiguration(Of Microsoft.Maui.Controls.Entry)
  name.vb: IElementConfiguration(Of Entry)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.IElementConfiguration`1
    name: IElementConfiguration
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ielementconfiguration-1
  - name: <
  - uid: Microsoft.Maui.Controls.Entry
    name: Entry
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.entry
  - name: '>'
  spec.vb:
  - uid: Microsoft.Maui.Controls.IElementConfiguration`1
    name: IElementConfiguration
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ielementconfiguration-1
  - name: (
  - name: Of
  - name: " "
  - uid: Microsoft.Maui.Controls.Entry
    name: Entry
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.entry
  - name: )
- uid: Microsoft.Maui.IEntry
  commentId: T:Microsoft.Maui.IEntry
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.ientry
  name: IEntry
  nameWithType: IEntry
  fullName: Microsoft.Maui.IEntry
- uid: Microsoft.Maui.IEditor
  commentId: T:Microsoft.Maui.IEditor
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.ieditor
  name: IEditor
  nameWithType: IEditor
  fullName: Microsoft.Maui.IEditor
- uid: Microsoft.Maui.IView
  commentId: T:Microsoft.Maui.IView
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iview
  name: IView
  nameWithType: IView
  fullName: Microsoft.Maui.IView
- uid: Microsoft.Maui.IElement
  commentId: T:Microsoft.Maui.IElement
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.ielement
  name: IElement
  nameWithType: IElement
  fullName: Microsoft.Maui.IElement
- uid: Microsoft.Maui.ITransform
  commentId: T:Microsoft.Maui.ITransform
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.itransform
  name: ITransform
  nameWithType: ITransform
  fullName: Microsoft.Maui.ITransform
- uid: Microsoft.Maui.ITextInput
  commentId: T:Microsoft.Maui.ITextInput
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.itextinput
  name: ITextInput
  nameWithType: ITextInput
  fullName: Microsoft.Maui.ITextInput
- uid: Microsoft.Maui.IText
  commentId: T:Microsoft.Maui.IText
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.itext
  name: IText
  nameWithType: IText
  fullName: Microsoft.Maui.IText
- uid: Microsoft.Maui.ITextStyle
  commentId: T:Microsoft.Maui.ITextStyle
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.itextstyle
  name: ITextStyle
  nameWithType: ITextStyle
  fullName: Microsoft.Maui.ITextStyle
- uid: Microsoft.Maui.IPlaceholder
  commentId: T:Microsoft.Maui.IPlaceholder
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iplaceholder
  name: IPlaceholder
  nameWithType: IPlaceholder
  fullName: Microsoft.Maui.IPlaceholder
- uid: Microsoft.Maui.ITextAlignment
  commentId: T:Microsoft.Maui.ITextAlignment
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.itextalignment
  name: ITextAlignment
  nameWithType: ITextAlignment
  fullName: Microsoft.Maui.ITextAlignment
- uid: Microsoft.Maui.Controls.Entry.ReturnTypeProperty
  commentId: F:Microsoft.Maui.Controls.Entry.ReturnTypeProperty
  parent: Microsoft.Maui.Controls.Entry
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.entry.returntypeproperty
  name: ReturnTypeProperty
  nameWithType: Entry.ReturnTypeProperty
  fullName: Microsoft.Maui.Controls.Entry.ReturnTypeProperty
- uid: Microsoft.Maui.Controls.Entry.ReturnCommandProperty
  commentId: F:Microsoft.Maui.Controls.Entry.ReturnCommandProperty
  parent: Microsoft.Maui.Controls.Entry
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.entry.returncommandproperty
  name: ReturnCommandProperty
  nameWithType: Entry.ReturnCommandProperty
  fullName: Microsoft.Maui.Controls.Entry.ReturnCommandProperty
- uid: Microsoft.Maui.Controls.Entry.ReturnCommandParameterProperty
  commentId: F:Microsoft.Maui.Controls.Entry.ReturnCommandParameterProperty
  parent: Microsoft.Maui.Controls.Entry
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.entry.returncommandparameterproperty
  name: ReturnCommandParameterProperty
  nameWithType: Entry.ReturnCommandParameterProperty
  fullName: Microsoft.Maui.Controls.Entry.ReturnCommandParameterProperty
- uid: Microsoft.Maui.Controls.Entry.PlaceholderProperty
  commentId: F:Microsoft.Maui.Controls.Entry.PlaceholderProperty
  parent: Microsoft.Maui.Controls.Entry
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.entry.placeholderproperty
  name: PlaceholderProperty
  nameWithType: Entry.PlaceholderProperty
  fullName: Microsoft.Maui.Controls.Entry.PlaceholderProperty
- uid: Microsoft.Maui.Controls.Entry.PlaceholderColorProperty
  commentId: F:Microsoft.Maui.Controls.Entry.PlaceholderColorProperty
  parent: Microsoft.Maui.Controls.Entry
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.entry.placeholdercolorproperty
  name: PlaceholderColorProperty
  nameWithType: Entry.PlaceholderColorProperty
  fullName: Microsoft.Maui.Controls.Entry.PlaceholderColorProperty
- uid: Microsoft.Maui.Controls.Entry.IsPasswordProperty
  commentId: F:Microsoft.Maui.Controls.Entry.IsPasswordProperty
  parent: Microsoft.Maui.Controls.Entry
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.entry.ispasswordproperty
  name: IsPasswordProperty
  nameWithType: Entry.IsPasswordProperty
  fullName: Microsoft.Maui.Controls.Entry.IsPasswordProperty
- uid: Microsoft.Maui.Controls.Entry.TextProperty
  commentId: F:Microsoft.Maui.Controls.Entry.TextProperty
  parent: Microsoft.Maui.Controls.Entry
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.entry.textproperty
  name: TextProperty
  nameWithType: Entry.TextProperty
  fullName: Microsoft.Maui.Controls.Entry.TextProperty
- uid: Microsoft.Maui.Controls.Entry.TextColorProperty
  commentId: F:Microsoft.Maui.Controls.Entry.TextColorProperty
  parent: Microsoft.Maui.Controls.Entry
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.entry.textcolorproperty
  name: TextColorProperty
  nameWithType: Entry.TextColorProperty
  fullName: Microsoft.Maui.Controls.Entry.TextColorProperty
- uid: Microsoft.Maui.Controls.Entry.KeyboardProperty
  commentId: F:Microsoft.Maui.Controls.Entry.KeyboardProperty
  parent: Microsoft.Maui.Controls.Entry
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.entry.keyboardproperty
  name: KeyboardProperty
  nameWithType: Entry.KeyboardProperty
  fullName: Microsoft.Maui.Controls.Entry.KeyboardProperty
- uid: Microsoft.Maui.Controls.Entry.CharacterSpacingProperty
  commentId: F:Microsoft.Maui.Controls.Entry.CharacterSpacingProperty
  parent: Microsoft.Maui.Controls.Entry
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.entry.characterspacingproperty
  name: CharacterSpacingProperty
  nameWithType: Entry.CharacterSpacingProperty
  fullName: Microsoft.Maui.Controls.Entry.CharacterSpacingProperty
- uid: Microsoft.Maui.Controls.Entry.HorizontalTextAlignmentProperty
  commentId: F:Microsoft.Maui.Controls.Entry.HorizontalTextAlignmentProperty
  parent: Microsoft.Maui.Controls.Entry
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.entry.horizontaltextalignmentproperty
  name: HorizontalTextAlignmentProperty
  nameWithType: Entry.HorizontalTextAlignmentProperty
  fullName: Microsoft.Maui.Controls.Entry.HorizontalTextAlignmentProperty
- uid: Microsoft.Maui.Controls.Entry.VerticalTextAlignmentProperty
  commentId: F:Microsoft.Maui.Controls.Entry.VerticalTextAlignmentProperty
  parent: Microsoft.Maui.Controls.Entry
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.entry.verticaltextalignmentproperty
  name: VerticalTextAlignmentProperty
  nameWithType: Entry.VerticalTextAlignmentProperty
  fullName: Microsoft.Maui.Controls.Entry.VerticalTextAlignmentProperty
- uid: Microsoft.Maui.Controls.Entry.FontFamilyProperty
  commentId: F:Microsoft.Maui.Controls.Entry.FontFamilyProperty
  parent: Microsoft.Maui.Controls.Entry
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.entry.fontfamilyproperty
  name: FontFamilyProperty
  nameWithType: Entry.FontFamilyProperty
  fullName: Microsoft.Maui.Controls.Entry.FontFamilyProperty
- uid: Microsoft.Maui.Controls.Entry.FontSizeProperty
  commentId: F:Microsoft.Maui.Controls.Entry.FontSizeProperty
  parent: Microsoft.Maui.Controls.Entry
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.entry.fontsizeproperty
  name: FontSizeProperty
  nameWithType: Entry.FontSizeProperty
  fullName: Microsoft.Maui.Controls.Entry.FontSizeProperty
- uid: Microsoft.Maui.Controls.Entry.FontAttributesProperty
  commentId: F:Microsoft.Maui.Controls.Entry.FontAttributesProperty
  parent: Microsoft.Maui.Controls.Entry
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.entry.fontattributesproperty
  name: FontAttributesProperty
  nameWithType: Entry.FontAttributesProperty
  fullName: Microsoft.Maui.Controls.Entry.FontAttributesProperty
- uid: Microsoft.Maui.Controls.Entry.FontAutoScalingEnabledProperty
  commentId: F:Microsoft.Maui.Controls.Entry.FontAutoScalingEnabledProperty
  parent: Microsoft.Maui.Controls.Entry
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.entry.fontautoscalingenabledproperty
  name: FontAutoScalingEnabledProperty
  nameWithType: Entry.FontAutoScalingEnabledProperty
  fullName: Microsoft.Maui.Controls.Entry.FontAutoScalingEnabledProperty
- uid: Microsoft.Maui.Controls.Entry.IsTextPredictionEnabledProperty
  commentId: F:Microsoft.Maui.Controls.Entry.IsTextPredictionEnabledProperty
  parent: Microsoft.Maui.Controls.Entry
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.entry.istextpredictionenabledproperty
  name: IsTextPredictionEnabledProperty
  nameWithType: Entry.IsTextPredictionEnabledProperty
  fullName: Microsoft.Maui.Controls.Entry.IsTextPredictionEnabledProperty
- uid: Microsoft.Maui.Controls.Entry.CursorPositionProperty
  commentId: F:Microsoft.Maui.Controls.Entry.CursorPositionProperty
  parent: Microsoft.Maui.Controls.Entry
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.entry.cursorpositionproperty
  name: CursorPositionProperty
  nameWithType: Entry.CursorPositionProperty
  fullName: Microsoft.Maui.Controls.Entry.CursorPositionProperty
- uid: Microsoft.Maui.Controls.Entry.SelectionLengthProperty
  commentId: F:Microsoft.Maui.Controls.Entry.SelectionLengthProperty
  parent: Microsoft.Maui.Controls.Entry
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.entry.selectionlengthproperty
  name: SelectionLengthProperty
  nameWithType: Entry.SelectionLengthProperty
  fullName: Microsoft.Maui.Controls.Entry.SelectionLengthProperty
- uid: Microsoft.Maui.Controls.Entry.ClearButtonVisibilityProperty
  commentId: F:Microsoft.Maui.Controls.Entry.ClearButtonVisibilityProperty
  parent: Microsoft.Maui.Controls.Entry
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.entry.clearbuttonvisibilityproperty
  name: ClearButtonVisibilityProperty
  nameWithType: Entry.ClearButtonVisibilityProperty
  fullName: Microsoft.Maui.Controls.Entry.ClearButtonVisibilityProperty
- uid: Microsoft.Maui.Controls.Entry.On``1
  commentId: M:Microsoft.Maui.Controls.Entry.On``1
  parent: Microsoft.Maui.Controls.Entry
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.entry.on
  name: On<T>()
  nameWithType: Entry.On<T>()
  fullName: Microsoft.Maui.Controls.Entry.On<T>()
  nameWithType.vb: Entry.On(Of T)()
  fullName.vb: Microsoft.Maui.Controls.Entry.On(Of T)()
  name.vb: On(Of T)()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Entry.On``1
    name: On
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.entry.on
  - name: <
  - name: T
  - name: '>'
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Entry.On``1
    name: On
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.entry.on
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.Entry.MapText(Microsoft.Maui.Handlers.EntryHandler,Microsoft.Maui.Controls.Entry)
  commentId: M:Microsoft.Maui.Controls.Entry.MapText(Microsoft.Maui.Handlers.EntryHandler,Microsoft.Maui.Controls.Entry)
  parent: Microsoft.Maui.Controls.Entry
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.entry.maptext#microsoft-maui-controls-entry-maptext(microsoft-maui-handlers-entryhandler-microsoft-maui-controls-entry)
  name: MapText(EntryHandler, Entry)
  nameWithType: Entry.MapText(EntryHandler, Entry)
  fullName: Microsoft.Maui.Controls.Entry.MapText(Microsoft.Maui.Handlers.EntryHandler, Microsoft.Maui.Controls.Entry)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Entry.MapText(Microsoft.Maui.Handlers.EntryHandler,Microsoft.Maui.Controls.Entry)
    name: MapText
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.entry.maptext#microsoft-maui-controls-entry-maptext(microsoft-maui-handlers-entryhandler-microsoft-maui-controls-entry)
  - name: (
  - uid: Microsoft.Maui.Handlers.EntryHandler
    name: EntryHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.entryhandler
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.Entry
    name: Entry
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.entry
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Entry.MapText(Microsoft.Maui.Handlers.EntryHandler,Microsoft.Maui.Controls.Entry)
    name: MapText
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.entry.maptext#microsoft-maui-controls-entry-maptext(microsoft-maui-handlers-entryhandler-microsoft-maui-controls-entry)
  - name: (
  - uid: Microsoft.Maui.Handlers.EntryHandler
    name: EntryHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.entryhandler
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.Entry
    name: Entry
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.entry
  - name: )
- uid: Microsoft.Maui.Controls.Entry.MapText(Microsoft.Maui.Handlers.IEntryHandler,Microsoft.Maui.Controls.Entry)
  commentId: M:Microsoft.Maui.Controls.Entry.MapText(Microsoft.Maui.Handlers.IEntryHandler,Microsoft.Maui.Controls.Entry)
  parent: Microsoft.Maui.Controls.Entry
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.entry.maptext#microsoft-maui-controls-entry-maptext(microsoft-maui-handlers-ientryhandler-microsoft-maui-controls-entry)
  name: MapText(IEntryHandler, Entry)
  nameWithType: Entry.MapText(IEntryHandler, Entry)
  fullName: Microsoft.Maui.Controls.Entry.MapText(Microsoft.Maui.Handlers.IEntryHandler, Microsoft.Maui.Controls.Entry)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Entry.MapText(Microsoft.Maui.Handlers.IEntryHandler,Microsoft.Maui.Controls.Entry)
    name: MapText
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.entry.maptext#microsoft-maui-controls-entry-maptext(microsoft-maui-handlers-ientryhandler-microsoft-maui-controls-entry)
  - name: (
  - uid: Microsoft.Maui.Handlers.IEntryHandler
    name: IEntryHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.ientryhandler
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.Entry
    name: Entry
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.entry
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Entry.MapText(Microsoft.Maui.Handlers.IEntryHandler,Microsoft.Maui.Controls.Entry)
    name: MapText
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.entry.maptext#microsoft-maui-controls-entry-maptext(microsoft-maui-handlers-ientryhandler-microsoft-maui-controls-entry)
  - name: (
  - uid: Microsoft.Maui.Handlers.IEntryHandler
    name: IEntryHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.handlers.ientryhandler
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.Entry
    name: Entry
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.entry
  - name: )
- uid: Microsoft.Maui.Controls.Entry.HorizontalTextAlignment
  commentId: P:Microsoft.Maui.Controls.Entry.HorizontalTextAlignment
  parent: Microsoft.Maui.Controls.Entry
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.entry.horizontaltextalignment
  name: HorizontalTextAlignment
  nameWithType: Entry.HorizontalTextAlignment
  fullName: Microsoft.Maui.Controls.Entry.HorizontalTextAlignment
- uid: Microsoft.Maui.Controls.Entry.VerticalTextAlignment
  commentId: P:Microsoft.Maui.Controls.Entry.VerticalTextAlignment
  parent: Microsoft.Maui.Controls.Entry
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.entry.verticaltextalignment
  name: VerticalTextAlignment
  nameWithType: Entry.VerticalTextAlignment
  fullName: Microsoft.Maui.Controls.Entry.VerticalTextAlignment
- uid: Microsoft.Maui.Controls.Entry.IsPassword
  commentId: P:Microsoft.Maui.Controls.Entry.IsPassword
  parent: Microsoft.Maui.Controls.Entry
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.entry.ispassword
  name: IsPassword
  nameWithType: Entry.IsPassword
  fullName: Microsoft.Maui.Controls.Entry.IsPassword
- uid: Microsoft.Maui.Controls.Entry.ReturnType
  commentId: P:Microsoft.Maui.Controls.Entry.ReturnType
  parent: Microsoft.Maui.Controls.Entry
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.entry.returntype
  name: ReturnType
  nameWithType: Entry.ReturnType
  fullName: Microsoft.Maui.Controls.Entry.ReturnType
- uid: Microsoft.Maui.Controls.Entry.ReturnCommand
  commentId: P:Microsoft.Maui.Controls.Entry.ReturnCommand
  parent: Microsoft.Maui.Controls.Entry
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.entry.returncommand
  name: ReturnCommand
  nameWithType: Entry.ReturnCommand
  fullName: Microsoft.Maui.Controls.Entry.ReturnCommand
- uid: Microsoft.Maui.Controls.Entry.ReturnCommandParameter
  commentId: P:Microsoft.Maui.Controls.Entry.ReturnCommandParameter
  parent: Microsoft.Maui.Controls.Entry
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.entry.returncommandparameter
  name: ReturnCommandParameter
  nameWithType: Entry.ReturnCommandParameter
  fullName: Microsoft.Maui.Controls.Entry.ReturnCommandParameter
- uid: Microsoft.Maui.Controls.Entry.ClearButtonVisibility
  commentId: P:Microsoft.Maui.Controls.Entry.ClearButtonVisibility
  parent: Microsoft.Maui.Controls.Entry
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.entry.clearbuttonvisibility
  name: ClearButtonVisibility
  nameWithType: Entry.ClearButtonVisibility
  fullName: Microsoft.Maui.Controls.Entry.ClearButtonVisibility
- uid: Microsoft.Maui.Controls.InputView.IsSpellCheckEnabledProperty
  commentId: F:Microsoft.Maui.Controls.InputView.IsSpellCheckEnabledProperty
  parent: Microsoft.Maui.Controls.InputView
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.inputview.isspellcheckenabledproperty
  name: IsSpellCheckEnabledProperty
  nameWithType: InputView.IsSpellCheckEnabledProperty
  fullName: Microsoft.Maui.Controls.InputView.IsSpellCheckEnabledProperty
- uid: Microsoft.Maui.Controls.InputView.MaxLengthProperty
  commentId: F:Microsoft.Maui.Controls.InputView.MaxLengthProperty
  parent: Microsoft.Maui.Controls.InputView
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.inputview.maxlengthproperty
  name: MaxLengthProperty
  nameWithType: InputView.MaxLengthProperty
  fullName: Microsoft.Maui.Controls.InputView.MaxLengthProperty
- uid: Microsoft.Maui.Controls.InputView.IsReadOnlyProperty
  commentId: F:Microsoft.Maui.Controls.InputView.IsReadOnlyProperty
  parent: Microsoft.Maui.Controls.InputView
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.inputview.isreadonlyproperty
  name: IsReadOnlyProperty
  nameWithType: InputView.IsReadOnlyProperty
  fullName: Microsoft.Maui.Controls.InputView.IsReadOnlyProperty
- uid: Microsoft.Maui.Controls.InputView.TextTransformProperty
  commentId: F:Microsoft.Maui.Controls.InputView.TextTransformProperty
  parent: Microsoft.Maui.Controls.InputView
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.inputview.texttransformproperty
  name: TextTransformProperty
  nameWithType: InputView.TextTransformProperty
  fullName: Microsoft.Maui.Controls.InputView.TextTransformProperty
- uid: Microsoft.Maui.Controls.InputView.OnTextChanged(System.String,System.String)
  commentId: M:Microsoft.Maui.Controls.InputView.OnTextChanged(System.String,System.String)
  parent: Microsoft.Maui.Controls.InputView
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.inputview.ontextchanged
  name: OnTextChanged(string, string)
  nameWithType: InputView.OnTextChanged(string, string)
  fullName: Microsoft.Maui.Controls.InputView.OnTextChanged(string, string)
  nameWithType.vb: InputView.OnTextChanged(String, String)
  fullName.vb: Microsoft.Maui.Controls.InputView.OnTextChanged(String, String)
  name.vb: OnTextChanged(String, String)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.InputView.OnTextChanged(System.String,System.String)
    name: OnTextChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.inputview.ontextchanged
  - name: (
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.InputView.OnTextChanged(System.String,System.String)
    name: OnTextChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.inputview.ontextchanged
  - name: (
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: Microsoft.Maui.Controls.InputView.OnTextTransformChanged(Microsoft.Maui.TextTransform,Microsoft.Maui.TextTransform)
  commentId: M:Microsoft.Maui.Controls.InputView.OnTextTransformChanged(Microsoft.Maui.TextTransform,Microsoft.Maui.TextTransform)
  parent: Microsoft.Maui.Controls.InputView
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.inputview.ontexttransformchanged
  name: OnTextTransformChanged(TextTransform, TextTransform)
  nameWithType: InputView.OnTextTransformChanged(TextTransform, TextTransform)
  fullName: Microsoft.Maui.Controls.InputView.OnTextTransformChanged(Microsoft.Maui.TextTransform, Microsoft.Maui.TextTransform)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.InputView.OnTextTransformChanged(Microsoft.Maui.TextTransform,Microsoft.Maui.TextTransform)
    name: OnTextTransformChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.inputview.ontexttransformchanged
  - name: (
  - uid: Microsoft.Maui.TextTransform
    name: TextTransform
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.texttransform
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.TextTransform
    name: TextTransform
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.texttransform
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.InputView.OnTextTransformChanged(Microsoft.Maui.TextTransform,Microsoft.Maui.TextTransform)
    name: OnTextTransformChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.inputview.ontexttransformchanged
  - name: (
  - uid: Microsoft.Maui.TextTransform
    name: TextTransform
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.texttransform
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.TextTransform
    name: TextTransform
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.texttransform
  - name: )
- uid: Microsoft.Maui.Controls.InputView.UpdateFormsText(System.String,Microsoft.Maui.TextTransform)
  commentId: M:Microsoft.Maui.Controls.InputView.UpdateFormsText(System.String,Microsoft.Maui.TextTransform)
  parent: Microsoft.Maui.Controls.InputView
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.inputview.updateformstext
  name: UpdateFormsText(string, TextTransform)
  nameWithType: InputView.UpdateFormsText(string, TextTransform)
  fullName: Microsoft.Maui.Controls.InputView.UpdateFormsText(string, Microsoft.Maui.TextTransform)
  nameWithType.vb: InputView.UpdateFormsText(String, TextTransform)
  fullName.vb: Microsoft.Maui.Controls.InputView.UpdateFormsText(String, Microsoft.Maui.TextTransform)
  name.vb: UpdateFormsText(String, TextTransform)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.InputView.UpdateFormsText(System.String,Microsoft.Maui.TextTransform)
    name: UpdateFormsText
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.inputview.updateformstext
  - name: (
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.TextTransform
    name: TextTransform
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.texttransform
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.InputView.UpdateFormsText(System.String,Microsoft.Maui.TextTransform)
    name: UpdateFormsText
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.inputview.updateformstext
  - name: (
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.TextTransform
    name: TextTransform
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.texttransform
  - name: )
- uid: Microsoft.Maui.Controls.InputView.MaxLength
  commentId: P:Microsoft.Maui.Controls.InputView.MaxLength
  parent: Microsoft.Maui.Controls.InputView
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.inputview.maxlength
  name: MaxLength
  nameWithType: InputView.MaxLength
  fullName: Microsoft.Maui.Controls.InputView.MaxLength
- uid: Microsoft.Maui.Controls.InputView.Text
  commentId: P:Microsoft.Maui.Controls.InputView.Text
  parent: Microsoft.Maui.Controls.InputView
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.inputview.text
  name: Text
  nameWithType: InputView.Text
  fullName: Microsoft.Maui.Controls.InputView.Text
- uid: Microsoft.Maui.Controls.InputView.Keyboard
  commentId: P:Microsoft.Maui.Controls.InputView.Keyboard
  parent: Microsoft.Maui.Controls.InputView
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.inputview.keyboard
  name: Keyboard
  nameWithType: InputView.Keyboard
  fullName: Microsoft.Maui.Controls.InputView.Keyboard
- uid: Microsoft.Maui.Controls.InputView.IsSpellCheckEnabled
  commentId: P:Microsoft.Maui.Controls.InputView.IsSpellCheckEnabled
  parent: Microsoft.Maui.Controls.InputView
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.inputview.isspellcheckenabled
  name: IsSpellCheckEnabled
  nameWithType: InputView.IsSpellCheckEnabled
  fullName: Microsoft.Maui.Controls.InputView.IsSpellCheckEnabled
- uid: Microsoft.Maui.Controls.InputView.IsTextPredictionEnabled
  commentId: P:Microsoft.Maui.Controls.InputView.IsTextPredictionEnabled
  parent: Microsoft.Maui.Controls.InputView
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.inputview.istextpredictionenabled
  name: IsTextPredictionEnabled
  nameWithType: InputView.IsTextPredictionEnabled
  fullName: Microsoft.Maui.Controls.InputView.IsTextPredictionEnabled
- uid: Microsoft.Maui.Controls.InputView.IsReadOnly
  commentId: P:Microsoft.Maui.Controls.InputView.IsReadOnly
  parent: Microsoft.Maui.Controls.InputView
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.inputview.isreadonly
  name: IsReadOnly
  nameWithType: InputView.IsReadOnly
  fullName: Microsoft.Maui.Controls.InputView.IsReadOnly
- uid: Microsoft.Maui.Controls.InputView.Placeholder
  commentId: P:Microsoft.Maui.Controls.InputView.Placeholder
  parent: Microsoft.Maui.Controls.InputView
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.inputview.placeholder
  name: Placeholder
  nameWithType: InputView.Placeholder
  fullName: Microsoft.Maui.Controls.InputView.Placeholder
- uid: Microsoft.Maui.Controls.InputView.PlaceholderColor
  commentId: P:Microsoft.Maui.Controls.InputView.PlaceholderColor
  parent: Microsoft.Maui.Controls.InputView
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.inputview.placeholdercolor
  name: PlaceholderColor
  nameWithType: InputView.PlaceholderColor
  fullName: Microsoft.Maui.Controls.InputView.PlaceholderColor
- uid: Microsoft.Maui.Controls.InputView.TextColor
  commentId: P:Microsoft.Maui.Controls.InputView.TextColor
  parent: Microsoft.Maui.Controls.InputView
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.inputview.textcolor
  name: TextColor
  nameWithType: InputView.TextColor
  fullName: Microsoft.Maui.Controls.InputView.TextColor
- uid: Microsoft.Maui.Controls.InputView.CharacterSpacing
  commentId: P:Microsoft.Maui.Controls.InputView.CharacterSpacing
  parent: Microsoft.Maui.Controls.InputView
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.inputview.characterspacing
  name: CharacterSpacing
  nameWithType: InputView.CharacterSpacing
  fullName: Microsoft.Maui.Controls.InputView.CharacterSpacing
- uid: Microsoft.Maui.Controls.InputView.TextTransform
  commentId: P:Microsoft.Maui.Controls.InputView.TextTransform
  parent: Microsoft.Maui.Controls.InputView
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.inputview.texttransform
  name: TextTransform
  nameWithType: InputView.TextTransform
  fullName: Microsoft.Maui.Controls.InputView.TextTransform
- uid: Microsoft.Maui.Controls.InputView.CursorPosition
  commentId: P:Microsoft.Maui.Controls.InputView.CursorPosition
  parent: Microsoft.Maui.Controls.InputView
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.inputview.cursorposition
  name: CursorPosition
  nameWithType: InputView.CursorPosition
  fullName: Microsoft.Maui.Controls.InputView.CursorPosition
- uid: Microsoft.Maui.Controls.InputView.SelectionLength
  commentId: P:Microsoft.Maui.Controls.InputView.SelectionLength
  parent: Microsoft.Maui.Controls.InputView
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.inputview.selectionlength
  name: SelectionLength
  nameWithType: InputView.SelectionLength
  fullName: Microsoft.Maui.Controls.InputView.SelectionLength
- uid: Microsoft.Maui.Controls.InputView.FontAttributes
  commentId: P:Microsoft.Maui.Controls.InputView.FontAttributes
  parent: Microsoft.Maui.Controls.InputView
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.inputview.fontattributes
  name: FontAttributes
  nameWithType: InputView.FontAttributes
  fullName: Microsoft.Maui.Controls.InputView.FontAttributes
- uid: Microsoft.Maui.Controls.InputView.FontFamily
  commentId: P:Microsoft.Maui.Controls.InputView.FontFamily
  parent: Microsoft.Maui.Controls.InputView
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.inputview.fontfamily
  name: FontFamily
  nameWithType: InputView.FontFamily
  fullName: Microsoft.Maui.Controls.InputView.FontFamily
- uid: Microsoft.Maui.Controls.InputView.FontSize
  commentId: P:Microsoft.Maui.Controls.InputView.FontSize
  parent: Microsoft.Maui.Controls.InputView
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.inputview.fontsize
  name: FontSize
  nameWithType: InputView.FontSize
  fullName: Microsoft.Maui.Controls.InputView.FontSize
- uid: Microsoft.Maui.Controls.InputView.FontAutoScalingEnabled
  commentId: P:Microsoft.Maui.Controls.InputView.FontAutoScalingEnabled
  parent: Microsoft.Maui.Controls.InputView
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.inputview.fontautoscalingenabled
  name: FontAutoScalingEnabled
  nameWithType: InputView.FontAutoScalingEnabled
  fullName: Microsoft.Maui.Controls.InputView.FontAutoScalingEnabled
- uid: Microsoft.Maui.Controls.InputView.TextChanged
  commentId: E:Microsoft.Maui.Controls.InputView.TextChanged
  parent: Microsoft.Maui.Controls.InputView
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.inputview.textchanged
  name: TextChanged
  nameWithType: InputView.TextChanged
  fullName: Microsoft.Maui.Controls.InputView.TextChanged
- uid: Microsoft.Maui.Controls.View.VerticalOptionsProperty
  commentId: F:Microsoft.Maui.Controls.View.VerticalOptionsProperty
  parent: Microsoft.Maui.Controls.View
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.verticaloptionsproperty
  name: VerticalOptionsProperty
  nameWithType: View.VerticalOptionsProperty
  fullName: Microsoft.Maui.Controls.View.VerticalOptionsProperty
- uid: Microsoft.Maui.Controls.View.HorizontalOptionsProperty
  commentId: F:Microsoft.Maui.Controls.View.HorizontalOptionsProperty
  parent: Microsoft.Maui.Controls.View
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.horizontaloptionsproperty
  name: HorizontalOptionsProperty
  nameWithType: View.HorizontalOptionsProperty
  fullName: Microsoft.Maui.Controls.View.HorizontalOptionsProperty
- uid: Microsoft.Maui.Controls.View.MarginProperty
  commentId: F:Microsoft.Maui.Controls.View.MarginProperty
  parent: Microsoft.Maui.Controls.View
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.marginproperty
  name: MarginProperty
  nameWithType: View.MarginProperty
  fullName: Microsoft.Maui.Controls.View.MarginProperty
- uid: Microsoft.Maui.Controls.View.propertyMapper
  commentId: F:Microsoft.Maui.Controls.View.propertyMapper
  parent: Microsoft.Maui.Controls.View
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.propertymapper
  name: propertyMapper
  nameWithType: View.propertyMapper
  fullName: Microsoft.Maui.Controls.View.propertyMapper
- uid: Microsoft.Maui.Controls.View.ChangeVisualState
  commentId: M:Microsoft.Maui.Controls.View.ChangeVisualState
  parent: Microsoft.Maui.Controls.View
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.changevisualstate
  name: ChangeVisualState()
  nameWithType: View.ChangeVisualState()
  fullName: Microsoft.Maui.Controls.View.ChangeVisualState()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.View.ChangeVisualState
    name: ChangeVisualState
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.changevisualstate
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.View.ChangeVisualState
    name: ChangeVisualState
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.changevisualstate
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.View.GetChildElements(Microsoft.Maui.Graphics.Point)
  commentId: M:Microsoft.Maui.Controls.View.GetChildElements(Microsoft.Maui.Graphics.Point)
  parent: Microsoft.Maui.Controls.View
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.getchildelements
  name: GetChildElements(Point)
  nameWithType: View.GetChildElements(Point)
  fullName: Microsoft.Maui.Controls.View.GetChildElements(Microsoft.Maui.Graphics.Point)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.View.GetChildElements(Microsoft.Maui.Graphics.Point)
    name: GetChildElements
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.getchildelements
  - name: (
  - uid: Microsoft.Maui.Graphics.Point
    name: Point
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.point
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.View.GetChildElements(Microsoft.Maui.Graphics.Point)
    name: GetChildElements
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.getchildelements
  - name: (
  - uid: Microsoft.Maui.Graphics.Point
    name: Point
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.point
  - name: )
- uid: Microsoft.Maui.Controls.View.OnBindingContextChanged
  commentId: M:Microsoft.Maui.Controls.View.OnBindingContextChanged
  parent: Microsoft.Maui.Controls.View
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.onbindingcontextchanged
  name: OnBindingContextChanged()
  nameWithType: View.OnBindingContextChanged()
  fullName: Microsoft.Maui.Controls.View.OnBindingContextChanged()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.View.OnBindingContextChanged
    name: OnBindingContextChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.onbindingcontextchanged
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.View.OnBindingContextChanged
    name: OnBindingContextChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.onbindingcontextchanged
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.View.GetRendererOverrides``1
  commentId: M:Microsoft.Maui.Controls.View.GetRendererOverrides``1
  parent: Microsoft.Maui.Controls.View
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.getrendereroverrides
  name: GetRendererOverrides<T>()
  nameWithType: View.GetRendererOverrides<T>()
  fullName: Microsoft.Maui.Controls.View.GetRendererOverrides<T>()
  nameWithType.vb: View.GetRendererOverrides(Of T)()
  fullName.vb: Microsoft.Maui.Controls.View.GetRendererOverrides(Of T)()
  name.vb: GetRendererOverrides(Of T)()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.View.GetRendererOverrides``1
    name: GetRendererOverrides
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.getrendereroverrides
  - name: <
  - name: T
  - name: '>'
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.View.GetRendererOverrides``1
    name: GetRendererOverrides
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.getrendereroverrides
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.View.GestureController
  commentId: P:Microsoft.Maui.Controls.View.GestureController
  parent: Microsoft.Maui.Controls.View
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.gesturecontroller
  name: GestureController
  nameWithType: View.GestureController
  fullName: Microsoft.Maui.Controls.View.GestureController
- uid: Microsoft.Maui.Controls.View.GestureRecognizers
  commentId: P:Microsoft.Maui.Controls.View.GestureRecognizers
  parent: Microsoft.Maui.Controls.View
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.gesturerecognizers
  name: GestureRecognizers
  nameWithType: View.GestureRecognizers
  fullName: Microsoft.Maui.Controls.View.GestureRecognizers
- uid: Microsoft.Maui.Controls.View.HorizontalOptions
  commentId: P:Microsoft.Maui.Controls.View.HorizontalOptions
  parent: Microsoft.Maui.Controls.View
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.horizontaloptions
  name: HorizontalOptions
  nameWithType: View.HorizontalOptions
  fullName: Microsoft.Maui.Controls.View.HorizontalOptions
- uid: Microsoft.Maui.Controls.View.Margin
  commentId: P:Microsoft.Maui.Controls.View.Margin
  parent: Microsoft.Maui.Controls.View
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.margin
  name: Margin
  nameWithType: View.Margin
  fullName: Microsoft.Maui.Controls.View.Margin
- uid: Microsoft.Maui.Controls.View.VerticalOptions
  commentId: P:Microsoft.Maui.Controls.View.VerticalOptions
  parent: Microsoft.Maui.Controls.View
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.verticaloptions
  name: VerticalOptions
  nameWithType: View.VerticalOptions
  fullName: Microsoft.Maui.Controls.View.VerticalOptions
- uid: Microsoft.Maui.Controls.VisualElement.NavigationProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.NavigationProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.navigationproperty
  name: NavigationProperty
  nameWithType: VisualElement.NavigationProperty
  fullName: Microsoft.Maui.Controls.VisualElement.NavigationProperty
- uid: Microsoft.Maui.Controls.VisualElement.StyleProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.StyleProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.styleproperty
  name: StyleProperty
  nameWithType: VisualElement.StyleProperty
  fullName: Microsoft.Maui.Controls.VisualElement.StyleProperty
- uid: Microsoft.Maui.Controls.VisualElement.InputTransparentProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.InputTransparentProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.inputtransparentproperty
  name: InputTransparentProperty
  nameWithType: VisualElement.InputTransparentProperty
  fullName: Microsoft.Maui.Controls.VisualElement.InputTransparentProperty
- uid: Microsoft.Maui.Controls.VisualElement.IsEnabledProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.IsEnabledProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isenabledproperty
  name: IsEnabledProperty
  nameWithType: VisualElement.IsEnabledProperty
  fullName: Microsoft.Maui.Controls.VisualElement.IsEnabledProperty
- uid: Microsoft.Maui.Controls.VisualElement.XProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.XProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.xproperty
  name: XProperty
  nameWithType: VisualElement.XProperty
  fullName: Microsoft.Maui.Controls.VisualElement.XProperty
- uid: Microsoft.Maui.Controls.VisualElement.YProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.YProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.yproperty
  name: YProperty
  nameWithType: VisualElement.YProperty
  fullName: Microsoft.Maui.Controls.VisualElement.YProperty
- uid: Microsoft.Maui.Controls.VisualElement.AnchorXProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.AnchorXProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.anchorxproperty
  name: AnchorXProperty
  nameWithType: VisualElement.AnchorXProperty
  fullName: Microsoft.Maui.Controls.VisualElement.AnchorXProperty
- uid: Microsoft.Maui.Controls.VisualElement.AnchorYProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.AnchorYProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.anchoryproperty
  name: AnchorYProperty
  nameWithType: VisualElement.AnchorYProperty
  fullName: Microsoft.Maui.Controls.VisualElement.AnchorYProperty
- uid: Microsoft.Maui.Controls.VisualElement.TranslationXProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.TranslationXProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.translationxproperty
  name: TranslationXProperty
  nameWithType: VisualElement.TranslationXProperty
  fullName: Microsoft.Maui.Controls.VisualElement.TranslationXProperty
- uid: Microsoft.Maui.Controls.VisualElement.TranslationYProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.TranslationYProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.translationyproperty
  name: TranslationYProperty
  nameWithType: VisualElement.TranslationYProperty
  fullName: Microsoft.Maui.Controls.VisualElement.TranslationYProperty
- uid: Microsoft.Maui.Controls.VisualElement.WidthProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.WidthProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.widthproperty
  name: WidthProperty
  nameWithType: VisualElement.WidthProperty
  fullName: Microsoft.Maui.Controls.VisualElement.WidthProperty
- uid: Microsoft.Maui.Controls.VisualElement.HeightProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.HeightProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.heightproperty
  name: HeightProperty
  nameWithType: VisualElement.HeightProperty
  fullName: Microsoft.Maui.Controls.VisualElement.HeightProperty
- uid: Microsoft.Maui.Controls.VisualElement.RotationProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.RotationProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotationproperty
  name: RotationProperty
  nameWithType: VisualElement.RotationProperty
  fullName: Microsoft.Maui.Controls.VisualElement.RotationProperty
- uid: Microsoft.Maui.Controls.VisualElement.RotationXProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.RotationXProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotationxproperty
  name: RotationXProperty
  nameWithType: VisualElement.RotationXProperty
  fullName: Microsoft.Maui.Controls.VisualElement.RotationXProperty
- uid: Microsoft.Maui.Controls.VisualElement.RotationYProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.RotationYProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotationyproperty
  name: RotationYProperty
  nameWithType: VisualElement.RotationYProperty
  fullName: Microsoft.Maui.Controls.VisualElement.RotationYProperty
- uid: Microsoft.Maui.Controls.VisualElement.ScaleProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.ScaleProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.scaleproperty
  name: ScaleProperty
  nameWithType: VisualElement.ScaleProperty
  fullName: Microsoft.Maui.Controls.VisualElement.ScaleProperty
- uid: Microsoft.Maui.Controls.VisualElement.ScaleXProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.ScaleXProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.scalexproperty
  name: ScaleXProperty
  nameWithType: VisualElement.ScaleXProperty
  fullName: Microsoft.Maui.Controls.VisualElement.ScaleXProperty
- uid: Microsoft.Maui.Controls.VisualElement.ScaleYProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.ScaleYProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.scaleyproperty
  name: ScaleYProperty
  nameWithType: VisualElement.ScaleYProperty
  fullName: Microsoft.Maui.Controls.VisualElement.ScaleYProperty
- uid: Microsoft.Maui.Controls.VisualElement.ClipProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.ClipProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.clipproperty
  name: ClipProperty
  nameWithType: VisualElement.ClipProperty
  fullName: Microsoft.Maui.Controls.VisualElement.ClipProperty
- uid: Microsoft.Maui.Controls.VisualElement.VisualProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.VisualProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.visualproperty
  name: VisualProperty
  nameWithType: VisualElement.VisualProperty
  fullName: Microsoft.Maui.Controls.VisualElement.VisualProperty
- uid: Microsoft.Maui.Controls.VisualElement.IsVisibleProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.IsVisibleProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isvisibleproperty
  name: IsVisibleProperty
  nameWithType: VisualElement.IsVisibleProperty
  fullName: Microsoft.Maui.Controls.VisualElement.IsVisibleProperty
- uid: Microsoft.Maui.Controls.VisualElement.OpacityProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.OpacityProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.opacityproperty
  name: OpacityProperty
  nameWithType: VisualElement.OpacityProperty
  fullName: Microsoft.Maui.Controls.VisualElement.OpacityProperty
- uid: Microsoft.Maui.Controls.VisualElement.BackgroundColorProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.BackgroundColorProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.backgroundcolorproperty
  name: BackgroundColorProperty
  nameWithType: VisualElement.BackgroundColorProperty
  fullName: Microsoft.Maui.Controls.VisualElement.BackgroundColorProperty
- uid: Microsoft.Maui.Controls.VisualElement.BackgroundProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.BackgroundProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.backgroundproperty
  name: BackgroundProperty
  nameWithType: VisualElement.BackgroundProperty
  fullName: Microsoft.Maui.Controls.VisualElement.BackgroundProperty
- uid: Microsoft.Maui.Controls.VisualElement.BehaviorsProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.BehaviorsProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.behaviorsproperty
  name: BehaviorsProperty
  nameWithType: VisualElement.BehaviorsProperty
  fullName: Microsoft.Maui.Controls.VisualElement.BehaviorsProperty
- uid: Microsoft.Maui.Controls.VisualElement.TriggersProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.TriggersProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.triggersproperty
  name: TriggersProperty
  nameWithType: VisualElement.TriggersProperty
  fullName: Microsoft.Maui.Controls.VisualElement.TriggersProperty
- uid: Microsoft.Maui.Controls.VisualElement.WidthRequestProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.WidthRequestProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.widthrequestproperty
  name: WidthRequestProperty
  nameWithType: VisualElement.WidthRequestProperty
  fullName: Microsoft.Maui.Controls.VisualElement.WidthRequestProperty
- uid: Microsoft.Maui.Controls.VisualElement.HeightRequestProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.HeightRequestProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.heightrequestproperty
  name: HeightRequestProperty
  nameWithType: VisualElement.HeightRequestProperty
  fullName: Microsoft.Maui.Controls.VisualElement.HeightRequestProperty
- uid: Microsoft.Maui.Controls.VisualElement.MinimumWidthRequestProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.MinimumWidthRequestProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.minimumwidthrequestproperty
  name: MinimumWidthRequestProperty
  nameWithType: VisualElement.MinimumWidthRequestProperty
  fullName: Microsoft.Maui.Controls.VisualElement.MinimumWidthRequestProperty
- uid: Microsoft.Maui.Controls.VisualElement.MinimumHeightRequestProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.MinimumHeightRequestProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.minimumheightrequestproperty
  name: MinimumHeightRequestProperty
  nameWithType: VisualElement.MinimumHeightRequestProperty
  fullName: Microsoft.Maui.Controls.VisualElement.MinimumHeightRequestProperty
- uid: Microsoft.Maui.Controls.VisualElement.MaximumWidthRequestProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.MaximumWidthRequestProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.maximumwidthrequestproperty
  name: MaximumWidthRequestProperty
  nameWithType: VisualElement.MaximumWidthRequestProperty
  fullName: Microsoft.Maui.Controls.VisualElement.MaximumWidthRequestProperty
- uid: Microsoft.Maui.Controls.VisualElement.MaximumHeightRequestProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.MaximumHeightRequestProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.maximumheightrequestproperty
  name: MaximumHeightRequestProperty
  nameWithType: VisualElement.MaximumHeightRequestProperty
  fullName: Microsoft.Maui.Controls.VisualElement.MaximumHeightRequestProperty
- uid: Microsoft.Maui.Controls.VisualElement.IsFocusedProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.IsFocusedProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isfocusedproperty
  name: IsFocusedProperty
  nameWithType: VisualElement.IsFocusedProperty
  fullName: Microsoft.Maui.Controls.VisualElement.IsFocusedProperty
- uid: Microsoft.Maui.Controls.VisualElement.FlowDirectionProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.FlowDirectionProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.flowdirectionproperty
  name: FlowDirectionProperty
  nameWithType: VisualElement.FlowDirectionProperty
  fullName: Microsoft.Maui.Controls.VisualElement.FlowDirectionProperty
- uid: Microsoft.Maui.Controls.VisualElement.WindowProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.WindowProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.windowproperty
  name: WindowProperty
  nameWithType: VisualElement.WindowProperty
  fullName: Microsoft.Maui.Controls.VisualElement.WindowProperty
- uid: Microsoft.Maui.Controls.VisualElement.ShadowProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.ShadowProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.shadowproperty
  name: ShadowProperty
  nameWithType: VisualElement.ShadowProperty
  fullName: Microsoft.Maui.Controls.VisualElement.ShadowProperty
- uid: Microsoft.Maui.Controls.VisualElement.ZIndexProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.ZIndexProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.zindexproperty
  name: ZIndexProperty
  nameWithType: VisualElement.ZIndexProperty
  fullName: Microsoft.Maui.Controls.VisualElement.ZIndexProperty
- uid: Microsoft.Maui.Controls.VisualElement.BatchBegin
  commentId: M:Microsoft.Maui.Controls.VisualElement.BatchBegin
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.batchbegin
  name: BatchBegin()
  nameWithType: VisualElement.BatchBegin()
  fullName: Microsoft.Maui.Controls.VisualElement.BatchBegin()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.BatchBegin
    name: BatchBegin
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.batchbegin
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.BatchBegin
    name: BatchBegin
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.batchbegin
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.BatchCommit
  commentId: M:Microsoft.Maui.Controls.VisualElement.BatchCommit
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.batchcommit
  name: BatchCommit()
  nameWithType: VisualElement.BatchCommit()
  fullName: Microsoft.Maui.Controls.VisualElement.BatchCommit()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.BatchCommit
    name: BatchCommit
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.batchcommit
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.BatchCommit
    name: BatchCommit
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.batchcommit
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.Focus
  commentId: M:Microsoft.Maui.Controls.VisualElement.Focus
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.focus
  name: Focus()
  nameWithType: VisualElement.Focus()
  fullName: Microsoft.Maui.Controls.VisualElement.Focus()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.Focus
    name: Focus
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.focus
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.Focus
    name: Focus
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.focus
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.Measure(System.Double,System.Double)
  commentId: M:Microsoft.Maui.Controls.VisualElement.Measure(System.Double,System.Double)
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.measure#microsoft-maui-controls-visualelement-measure(system-double-system-double)
  name: Measure(double, double)
  nameWithType: VisualElement.Measure(double, double)
  fullName: Microsoft.Maui.Controls.VisualElement.Measure(double, double)
  nameWithType.vb: VisualElement.Measure(Double, Double)
  fullName.vb: Microsoft.Maui.Controls.VisualElement.Measure(Double, Double)
  name.vb: Measure(Double, Double)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.Measure(System.Double,System.Double)
    name: Measure
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.measure#microsoft-maui-controls-visualelement-measure(system-double-system-double)
  - name: (
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.Measure(System.Double,System.Double)
    name: Measure
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.measure#microsoft-maui-controls-visualelement-measure(system-double-system-double)
  - name: (
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.Measure(System.Double,System.Double,Microsoft.Maui.Controls.MeasureFlags)
  commentId: M:Microsoft.Maui.Controls.VisualElement.Measure(System.Double,System.Double,Microsoft.Maui.Controls.MeasureFlags)
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.measure#microsoft-maui-controls-visualelement-measure(system-double-system-double-microsoft-maui-controls-measureflags)
  name: Measure(double, double, MeasureFlags)
  nameWithType: VisualElement.Measure(double, double, MeasureFlags)
  fullName: Microsoft.Maui.Controls.VisualElement.Measure(double, double, Microsoft.Maui.Controls.MeasureFlags)
  nameWithType.vb: VisualElement.Measure(Double, Double, MeasureFlags)
  fullName.vb: Microsoft.Maui.Controls.VisualElement.Measure(Double, Double, Microsoft.Maui.Controls.MeasureFlags)
  name.vb: Measure(Double, Double, MeasureFlags)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.Measure(System.Double,System.Double,Microsoft.Maui.Controls.MeasureFlags)
    name: Measure
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.measure#microsoft-maui-controls-visualelement-measure(system-double-system-double-microsoft-maui-controls-measureflags)
  - name: (
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.MeasureFlags
    name: MeasureFlags
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.measureflags
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.Measure(System.Double,System.Double,Microsoft.Maui.Controls.MeasureFlags)
    name: Measure
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.measure#microsoft-maui-controls-visualelement-measure(system-double-system-double-microsoft-maui-controls-measureflags)
  - name: (
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.MeasureFlags
    name: MeasureFlags
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.measureflags
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.Unfocus
  commentId: M:Microsoft.Maui.Controls.VisualElement.Unfocus
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.unfocus
  name: Unfocus()
  nameWithType: VisualElement.Unfocus()
  fullName: Microsoft.Maui.Controls.VisualElement.Unfocus()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.Unfocus
    name: Unfocus
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.unfocus
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.Unfocus
    name: Unfocus
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.unfocus
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.InvalidateMeasure
  commentId: M:Microsoft.Maui.Controls.VisualElement.InvalidateMeasure
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.invalidatemeasure
  name: InvalidateMeasure()
  nameWithType: VisualElement.InvalidateMeasure()
  fullName: Microsoft.Maui.Controls.VisualElement.InvalidateMeasure()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.InvalidateMeasure
    name: InvalidateMeasure
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.invalidatemeasure
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.InvalidateMeasure
    name: InvalidateMeasure
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.invalidatemeasure
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.OnChildAdded(Microsoft.Maui.Controls.Element)
  commentId: M:Microsoft.Maui.Controls.VisualElement.OnChildAdded(Microsoft.Maui.Controls.Element)
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onchildadded
  name: OnChildAdded(Element)
  nameWithType: VisualElement.OnChildAdded(Element)
  fullName: Microsoft.Maui.Controls.VisualElement.OnChildAdded(Microsoft.Maui.Controls.Element)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.OnChildAdded(Microsoft.Maui.Controls.Element)
    name: OnChildAdded
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onchildadded
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.OnChildAdded(Microsoft.Maui.Controls.Element)
    name: OnChildAdded
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onchildadded
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.OnChildRemoved(Microsoft.Maui.Controls.Element,System.Int32)
  commentId: M:Microsoft.Maui.Controls.VisualElement.OnChildRemoved(Microsoft.Maui.Controls.Element,System.Int32)
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onchildremoved
  name: OnChildRemoved(Element, int)
  nameWithType: VisualElement.OnChildRemoved(Element, int)
  fullName: Microsoft.Maui.Controls.VisualElement.OnChildRemoved(Microsoft.Maui.Controls.Element, int)
  nameWithType.vb: VisualElement.OnChildRemoved(Element, Integer)
  fullName.vb: Microsoft.Maui.Controls.VisualElement.OnChildRemoved(Microsoft.Maui.Controls.Element, Integer)
  name.vb: OnChildRemoved(Element, Integer)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.OnChildRemoved(Microsoft.Maui.Controls.Element,System.Int32)
    name: OnChildRemoved
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onchildremoved
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: ','
  - name: " "
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.OnChildRemoved(Microsoft.Maui.Controls.Element,System.Int32)
    name: OnChildRemoved
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onchildremoved
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: ','
  - name: " "
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.OnChildrenReordered
  commentId: M:Microsoft.Maui.Controls.VisualElement.OnChildrenReordered
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onchildrenreordered
  name: OnChildrenReordered()
  nameWithType: VisualElement.OnChildrenReordered()
  fullName: Microsoft.Maui.Controls.VisualElement.OnChildrenReordered()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.OnChildrenReordered
    name: OnChildrenReordered
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onchildrenreordered
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.OnChildrenReordered
    name: OnChildrenReordered
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onchildrenreordered
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.OnMeasure(System.Double,System.Double)
  commentId: M:Microsoft.Maui.Controls.VisualElement.OnMeasure(System.Double,System.Double)
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onmeasure
  name: OnMeasure(double, double)
  nameWithType: VisualElement.OnMeasure(double, double)
  fullName: Microsoft.Maui.Controls.VisualElement.OnMeasure(double, double)
  nameWithType.vb: VisualElement.OnMeasure(Double, Double)
  fullName.vb: Microsoft.Maui.Controls.VisualElement.OnMeasure(Double, Double)
  name.vb: OnMeasure(Double, Double)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.OnMeasure(System.Double,System.Double)
    name: OnMeasure
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onmeasure
  - name: (
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.OnMeasure(System.Double,System.Double)
    name: OnMeasure
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onmeasure
  - name: (
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.OnSizeAllocated(System.Double,System.Double)
  commentId: M:Microsoft.Maui.Controls.VisualElement.OnSizeAllocated(System.Double,System.Double)
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onsizeallocated
  name: OnSizeAllocated(double, double)
  nameWithType: VisualElement.OnSizeAllocated(double, double)
  fullName: Microsoft.Maui.Controls.VisualElement.OnSizeAllocated(double, double)
  nameWithType.vb: VisualElement.OnSizeAllocated(Double, Double)
  fullName.vb: Microsoft.Maui.Controls.VisualElement.OnSizeAllocated(Double, Double)
  name.vb: OnSizeAllocated(Double, Double)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.OnSizeAllocated(System.Double,System.Double)
    name: OnSizeAllocated
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onsizeallocated
  - name: (
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.OnSizeAllocated(System.Double,System.Double)
    name: OnSizeAllocated
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onsizeallocated
  - name: (
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.SizeAllocated(System.Double,System.Double)
  commentId: M:Microsoft.Maui.Controls.VisualElement.SizeAllocated(System.Double,System.Double)
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.sizeallocated
  name: SizeAllocated(double, double)
  nameWithType: VisualElement.SizeAllocated(double, double)
  fullName: Microsoft.Maui.Controls.VisualElement.SizeAllocated(double, double)
  nameWithType.vb: VisualElement.SizeAllocated(Double, Double)
  fullName.vb: Microsoft.Maui.Controls.VisualElement.SizeAllocated(Double, Double)
  name.vb: SizeAllocated(Double, Double)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.SizeAllocated(System.Double,System.Double)
    name: SizeAllocated
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.sizeallocated
  - name: (
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.SizeAllocated(System.Double,System.Double)
    name: SizeAllocated
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.sizeallocated
  - name: (
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.RefreshIsEnabledProperty
  commentId: M:Microsoft.Maui.Controls.VisualElement.RefreshIsEnabledProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.refreshisenabledproperty
  name: RefreshIsEnabledProperty()
  nameWithType: VisualElement.RefreshIsEnabledProperty()
  fullName: Microsoft.Maui.Controls.VisualElement.RefreshIsEnabledProperty()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.RefreshIsEnabledProperty
    name: RefreshIsEnabledProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.refreshisenabledproperty
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.RefreshIsEnabledProperty
    name: RefreshIsEnabledProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.refreshisenabledproperty
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.Arrange(Microsoft.Maui.Graphics.Rect)
  commentId: M:Microsoft.Maui.Controls.VisualElement.Arrange(Microsoft.Maui.Graphics.Rect)
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.arrange
  name: Arrange(Rect)
  nameWithType: VisualElement.Arrange(Rect)
  fullName: Microsoft.Maui.Controls.VisualElement.Arrange(Microsoft.Maui.Graphics.Rect)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.Arrange(Microsoft.Maui.Graphics.Rect)
    name: Arrange
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.arrange
  - name: (
  - uid: Microsoft.Maui.Graphics.Rect
    name: Rect
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.rect
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.Arrange(Microsoft.Maui.Graphics.Rect)
    name: Arrange
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.arrange
  - name: (
  - uid: Microsoft.Maui.Graphics.Rect
    name: Rect
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.rect
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.ArrangeOverride(Microsoft.Maui.Graphics.Rect)
  commentId: M:Microsoft.Maui.Controls.VisualElement.ArrangeOverride(Microsoft.Maui.Graphics.Rect)
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.arrangeoverride
  name: ArrangeOverride(Rect)
  nameWithType: VisualElement.ArrangeOverride(Rect)
  fullName: Microsoft.Maui.Controls.VisualElement.ArrangeOverride(Microsoft.Maui.Graphics.Rect)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.ArrangeOverride(Microsoft.Maui.Graphics.Rect)
    name: ArrangeOverride
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.arrangeoverride
  - name: (
  - uid: Microsoft.Maui.Graphics.Rect
    name: Rect
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.rect
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.ArrangeOverride(Microsoft.Maui.Graphics.Rect)
    name: ArrangeOverride
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.arrangeoverride
  - name: (
  - uid: Microsoft.Maui.Graphics.Rect
    name: Rect
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.rect
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.Layout(Microsoft.Maui.Graphics.Rect)
  commentId: M:Microsoft.Maui.Controls.VisualElement.Layout(Microsoft.Maui.Graphics.Rect)
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.layout
  name: Layout(Rect)
  nameWithType: VisualElement.Layout(Rect)
  fullName: Microsoft.Maui.Controls.VisualElement.Layout(Microsoft.Maui.Graphics.Rect)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.Layout(Microsoft.Maui.Graphics.Rect)
    name: Layout
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.layout
  - name: (
  - uid: Microsoft.Maui.Graphics.Rect
    name: Rect
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.rect
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.Layout(Microsoft.Maui.Graphics.Rect)
    name: Layout
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.layout
  - name: (
  - uid: Microsoft.Maui.Graphics.Rect
    name: Rect
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.rect
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.InvalidateMeasureOverride
  commentId: M:Microsoft.Maui.Controls.VisualElement.InvalidateMeasureOverride
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.invalidatemeasureoverride
  name: InvalidateMeasureOverride()
  nameWithType: VisualElement.InvalidateMeasureOverride()
  fullName: Microsoft.Maui.Controls.VisualElement.InvalidateMeasureOverride()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.InvalidateMeasureOverride
    name: InvalidateMeasureOverride
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.invalidatemeasureoverride
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.InvalidateMeasureOverride
    name: InvalidateMeasureOverride
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.invalidatemeasureoverride
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.MeasureOverride(System.Double,System.Double)
  commentId: M:Microsoft.Maui.Controls.VisualElement.MeasureOverride(System.Double,System.Double)
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.measureoverride
  name: MeasureOverride(double, double)
  nameWithType: VisualElement.MeasureOverride(double, double)
  fullName: Microsoft.Maui.Controls.VisualElement.MeasureOverride(double, double)
  nameWithType.vb: VisualElement.MeasureOverride(Double, Double)
  fullName.vb: Microsoft.Maui.Controls.VisualElement.MeasureOverride(Double, Double)
  name.vb: MeasureOverride(Double, Double)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.MeasureOverride(System.Double,System.Double)
    name: MeasureOverride
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.measureoverride
  - name: (
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.MeasureOverride(System.Double,System.Double)
    name: MeasureOverride
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.measureoverride
  - name: (
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.MapBackgroundColor(Microsoft.Maui.IViewHandler,Microsoft.Maui.IView)
  commentId: M:Microsoft.Maui.Controls.VisualElement.MapBackgroundColor(Microsoft.Maui.IViewHandler,Microsoft.Maui.IView)
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.mapbackgroundcolor
  name: MapBackgroundColor(IViewHandler, IView)
  nameWithType: VisualElement.MapBackgroundColor(IViewHandler, IView)
  fullName: Microsoft.Maui.Controls.VisualElement.MapBackgroundColor(Microsoft.Maui.IViewHandler, Microsoft.Maui.IView)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.MapBackgroundColor(Microsoft.Maui.IViewHandler,Microsoft.Maui.IView)
    name: MapBackgroundColor
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.mapbackgroundcolor
  - name: (
  - uid: Microsoft.Maui.IViewHandler
    name: IViewHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iviewhandler
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.IView
    name: IView
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iview
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.MapBackgroundColor(Microsoft.Maui.IViewHandler,Microsoft.Maui.IView)
    name: MapBackgroundColor
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.mapbackgroundcolor
  - name: (
  - uid: Microsoft.Maui.IViewHandler
    name: IViewHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iviewhandler
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.IView
    name: IView
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iview
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.MapBackgroundImageSource(Microsoft.Maui.IViewHandler,Microsoft.Maui.IView)
  commentId: M:Microsoft.Maui.Controls.VisualElement.MapBackgroundImageSource(Microsoft.Maui.IViewHandler,Microsoft.Maui.IView)
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.mapbackgroundimagesource
  name: MapBackgroundImageSource(IViewHandler, IView)
  nameWithType: VisualElement.MapBackgroundImageSource(IViewHandler, IView)
  fullName: Microsoft.Maui.Controls.VisualElement.MapBackgroundImageSource(Microsoft.Maui.IViewHandler, Microsoft.Maui.IView)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.MapBackgroundImageSource(Microsoft.Maui.IViewHandler,Microsoft.Maui.IView)
    name: MapBackgroundImageSource
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.mapbackgroundimagesource
  - name: (
  - uid: Microsoft.Maui.IViewHandler
    name: IViewHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iviewhandler
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.IView
    name: IView
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iview
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.MapBackgroundImageSource(Microsoft.Maui.IViewHandler,Microsoft.Maui.IView)
    name: MapBackgroundImageSource
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.mapbackgroundimagesource
  - name: (
  - uid: Microsoft.Maui.IViewHandler
    name: IViewHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iviewhandler
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.IView
    name: IView
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iview
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.Visual
  commentId: P:Microsoft.Maui.Controls.VisualElement.Visual
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.visual
  name: Visual
  nameWithType: VisualElement.Visual
  fullName: Microsoft.Maui.Controls.VisualElement.Visual
- uid: Microsoft.Maui.Controls.VisualElement.FlowDirection
  commentId: P:Microsoft.Maui.Controls.VisualElement.FlowDirection
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.flowdirection
  name: FlowDirection
  nameWithType: VisualElement.FlowDirection
  fullName: Microsoft.Maui.Controls.VisualElement.FlowDirection
- uid: Microsoft.Maui.Controls.VisualElement.Window
  commentId: P:Microsoft.Maui.Controls.VisualElement.Window
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.window
  name: Window
  nameWithType: VisualElement.Window
  fullName: Microsoft.Maui.Controls.VisualElement.Window
- uid: Microsoft.Maui.Controls.VisualElement.AnchorX
  commentId: P:Microsoft.Maui.Controls.VisualElement.AnchorX
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.anchorx
  name: AnchorX
  nameWithType: VisualElement.AnchorX
  fullName: Microsoft.Maui.Controls.VisualElement.AnchorX
- uid: Microsoft.Maui.Controls.VisualElement.AnchorY
  commentId: P:Microsoft.Maui.Controls.VisualElement.AnchorY
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.anchory
  name: AnchorY
  nameWithType: VisualElement.AnchorY
  fullName: Microsoft.Maui.Controls.VisualElement.AnchorY
- uid: Microsoft.Maui.Controls.VisualElement.BackgroundColor
  commentId: P:Microsoft.Maui.Controls.VisualElement.BackgroundColor
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.backgroundcolor
  name: BackgroundColor
  nameWithType: VisualElement.BackgroundColor
  fullName: Microsoft.Maui.Controls.VisualElement.BackgroundColor
- uid: Microsoft.Maui.Controls.VisualElement.Background
  commentId: P:Microsoft.Maui.Controls.VisualElement.Background
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.background
  name: Background
  nameWithType: VisualElement.Background
  fullName: Microsoft.Maui.Controls.VisualElement.Background
- uid: Microsoft.Maui.Controls.VisualElement.Behaviors
  commentId: P:Microsoft.Maui.Controls.VisualElement.Behaviors
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.behaviors
  name: Behaviors
  nameWithType: VisualElement.Behaviors
  fullName: Microsoft.Maui.Controls.VisualElement.Behaviors
- uid: Microsoft.Maui.Controls.VisualElement.Bounds
  commentId: P:Microsoft.Maui.Controls.VisualElement.Bounds
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.bounds
  name: Bounds
  nameWithType: VisualElement.Bounds
  fullName: Microsoft.Maui.Controls.VisualElement.Bounds
- uid: Microsoft.Maui.Controls.VisualElement.Height
  commentId: P:Microsoft.Maui.Controls.VisualElement.Height
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.height
  name: Height
  nameWithType: VisualElement.Height
  fullName: Microsoft.Maui.Controls.VisualElement.Height
- uid: Microsoft.Maui.Controls.VisualElement.HeightRequest
  commentId: P:Microsoft.Maui.Controls.VisualElement.HeightRequest
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.heightrequest
  name: HeightRequest
  nameWithType: VisualElement.HeightRequest
  fullName: Microsoft.Maui.Controls.VisualElement.HeightRequest
- uid: Microsoft.Maui.Controls.VisualElement.InputTransparent
  commentId: P:Microsoft.Maui.Controls.VisualElement.InputTransparent
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.inputtransparent
  name: InputTransparent
  nameWithType: VisualElement.InputTransparent
  fullName: Microsoft.Maui.Controls.VisualElement.InputTransparent
- uid: Microsoft.Maui.Controls.VisualElement.IsEnabled
  commentId: P:Microsoft.Maui.Controls.VisualElement.IsEnabled
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isenabled
  name: IsEnabled
  nameWithType: VisualElement.IsEnabled
  fullName: Microsoft.Maui.Controls.VisualElement.IsEnabled
- uid: Microsoft.Maui.Controls.VisualElement.IsEnabledCore
  commentId: P:Microsoft.Maui.Controls.VisualElement.IsEnabledCore
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isenabledcore
  name: IsEnabledCore
  nameWithType: VisualElement.IsEnabledCore
  fullName: Microsoft.Maui.Controls.VisualElement.IsEnabledCore
- uid: Microsoft.Maui.Controls.VisualElement.IsFocused
  commentId: P:Microsoft.Maui.Controls.VisualElement.IsFocused
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isfocused
  name: IsFocused
  nameWithType: VisualElement.IsFocused
  fullName: Microsoft.Maui.Controls.VisualElement.IsFocused
- uid: Microsoft.Maui.Controls.VisualElement.IsVisible
  commentId: P:Microsoft.Maui.Controls.VisualElement.IsVisible
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isvisible
  name: IsVisible
  nameWithType: VisualElement.IsVisible
  fullName: Microsoft.Maui.Controls.VisualElement.IsVisible
- uid: Microsoft.Maui.Controls.VisualElement.MinimumHeightRequest
  commentId: P:Microsoft.Maui.Controls.VisualElement.MinimumHeightRequest
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.minimumheightrequest
  name: MinimumHeightRequest
  nameWithType: VisualElement.MinimumHeightRequest
  fullName: Microsoft.Maui.Controls.VisualElement.MinimumHeightRequest
- uid: Microsoft.Maui.Controls.VisualElement.MinimumWidthRequest
  commentId: P:Microsoft.Maui.Controls.VisualElement.MinimumWidthRequest
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.minimumwidthrequest
  name: MinimumWidthRequest
  nameWithType: VisualElement.MinimumWidthRequest
  fullName: Microsoft.Maui.Controls.VisualElement.MinimumWidthRequest
- uid: Microsoft.Maui.Controls.VisualElement.MaximumHeightRequest
  commentId: P:Microsoft.Maui.Controls.VisualElement.MaximumHeightRequest
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.maximumheightrequest
  name: MaximumHeightRequest
  nameWithType: VisualElement.MaximumHeightRequest
  fullName: Microsoft.Maui.Controls.VisualElement.MaximumHeightRequest
- uid: Microsoft.Maui.Controls.VisualElement.MaximumWidthRequest
  commentId: P:Microsoft.Maui.Controls.VisualElement.MaximumWidthRequest
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.maximumwidthrequest
  name: MaximumWidthRequest
  nameWithType: VisualElement.MaximumWidthRequest
  fullName: Microsoft.Maui.Controls.VisualElement.MaximumWidthRequest
- uid: Microsoft.Maui.Controls.VisualElement.Opacity
  commentId: P:Microsoft.Maui.Controls.VisualElement.Opacity
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.opacity
  name: Opacity
  nameWithType: VisualElement.Opacity
  fullName: Microsoft.Maui.Controls.VisualElement.Opacity
- uid: Microsoft.Maui.Controls.VisualElement.Rotation
  commentId: P:Microsoft.Maui.Controls.VisualElement.Rotation
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotation
  name: Rotation
  nameWithType: VisualElement.Rotation
  fullName: Microsoft.Maui.Controls.VisualElement.Rotation
- uid: Microsoft.Maui.Controls.VisualElement.RotationX
  commentId: P:Microsoft.Maui.Controls.VisualElement.RotationX
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotationx
  name: RotationX
  nameWithType: VisualElement.RotationX
  fullName: Microsoft.Maui.Controls.VisualElement.RotationX
- uid: Microsoft.Maui.Controls.VisualElement.RotationY
  commentId: P:Microsoft.Maui.Controls.VisualElement.RotationY
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotationy
  name: RotationY
  nameWithType: VisualElement.RotationY
  fullName: Microsoft.Maui.Controls.VisualElement.RotationY
- uid: Microsoft.Maui.Controls.VisualElement.Scale
  commentId: P:Microsoft.Maui.Controls.VisualElement.Scale
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.scale
  name: Scale
  nameWithType: VisualElement.Scale
  fullName: Microsoft.Maui.Controls.VisualElement.Scale
- uid: Microsoft.Maui.Controls.VisualElement.ScaleX
  commentId: P:Microsoft.Maui.Controls.VisualElement.ScaleX
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.scalex
  name: ScaleX
  nameWithType: VisualElement.ScaleX
  fullName: Microsoft.Maui.Controls.VisualElement.ScaleX
- uid: Microsoft.Maui.Controls.VisualElement.ScaleY
  commentId: P:Microsoft.Maui.Controls.VisualElement.ScaleY
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.scaley
  name: ScaleY
  nameWithType: VisualElement.ScaleY
  fullName: Microsoft.Maui.Controls.VisualElement.ScaleY
- uid: Microsoft.Maui.Controls.VisualElement.TranslationX
  commentId: P:Microsoft.Maui.Controls.VisualElement.TranslationX
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.translationx
  name: TranslationX
  nameWithType: VisualElement.TranslationX
  fullName: Microsoft.Maui.Controls.VisualElement.TranslationX
- uid: Microsoft.Maui.Controls.VisualElement.TranslationY
  commentId: P:Microsoft.Maui.Controls.VisualElement.TranslationY
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.translationy
  name: TranslationY
  nameWithType: VisualElement.TranslationY
  fullName: Microsoft.Maui.Controls.VisualElement.TranslationY
- uid: Microsoft.Maui.Controls.VisualElement.Triggers
  commentId: P:Microsoft.Maui.Controls.VisualElement.Triggers
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.triggers
  name: Triggers
  nameWithType: VisualElement.Triggers
  fullName: Microsoft.Maui.Controls.VisualElement.Triggers
- uid: Microsoft.Maui.Controls.VisualElement.Width
  commentId: P:Microsoft.Maui.Controls.VisualElement.Width
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.width
  name: Width
  nameWithType: VisualElement.Width
  fullName: Microsoft.Maui.Controls.VisualElement.Width
- uid: Microsoft.Maui.Controls.VisualElement.WidthRequest
  commentId: P:Microsoft.Maui.Controls.VisualElement.WidthRequest
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.widthrequest
  name: WidthRequest
  nameWithType: VisualElement.WidthRequest
  fullName: Microsoft.Maui.Controls.VisualElement.WidthRequest
- uid: Microsoft.Maui.Controls.VisualElement.X
  commentId: P:Microsoft.Maui.Controls.VisualElement.X
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.x
  name: X
  nameWithType: VisualElement.X
  fullName: Microsoft.Maui.Controls.VisualElement.X
- uid: Microsoft.Maui.Controls.VisualElement.Y
  commentId: P:Microsoft.Maui.Controls.VisualElement.Y
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.y
  name: Y
  nameWithType: VisualElement.Y
  fullName: Microsoft.Maui.Controls.VisualElement.Y
- uid: Microsoft.Maui.Controls.VisualElement.Clip
  commentId: P:Microsoft.Maui.Controls.VisualElement.Clip
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.clip
  name: Clip
  nameWithType: VisualElement.Clip
  fullName: Microsoft.Maui.Controls.VisualElement.Clip
- uid: Microsoft.Maui.Controls.VisualElement.Resources
  commentId: P:Microsoft.Maui.Controls.VisualElement.Resources
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.resources
  name: Resources
  nameWithType: VisualElement.Resources
  fullName: Microsoft.Maui.Controls.VisualElement.Resources
- uid: Microsoft.Maui.Controls.VisualElement.Frame
  commentId: P:Microsoft.Maui.Controls.VisualElement.Frame
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.frame
  name: Frame
  nameWithType: VisualElement.Frame
  fullName: Microsoft.Maui.Controls.VisualElement.Frame
- uid: Microsoft.Maui.Controls.VisualElement.Handler
  commentId: P:Microsoft.Maui.Controls.VisualElement.Handler
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.handler
  name: Handler
  nameWithType: VisualElement.Handler
  fullName: Microsoft.Maui.Controls.VisualElement.Handler
- uid: Microsoft.Maui.Controls.VisualElement.Shadow
  commentId: P:Microsoft.Maui.Controls.VisualElement.Shadow
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.shadow
  name: Shadow
  nameWithType: VisualElement.Shadow
  fullName: Microsoft.Maui.Controls.VisualElement.Shadow
- uid: Microsoft.Maui.Controls.VisualElement.ZIndex
  commentId: P:Microsoft.Maui.Controls.VisualElement.ZIndex
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.zindex
  name: ZIndex
  nameWithType: VisualElement.ZIndex
  fullName: Microsoft.Maui.Controls.VisualElement.ZIndex
- uid: Microsoft.Maui.Controls.VisualElement.DesiredSize
  commentId: P:Microsoft.Maui.Controls.VisualElement.DesiredSize
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.desiredsize
  name: DesiredSize
  nameWithType: VisualElement.DesiredSize
  fullName: Microsoft.Maui.Controls.VisualElement.DesiredSize
- uid: Microsoft.Maui.Controls.VisualElement.IsLoaded
  commentId: P:Microsoft.Maui.Controls.VisualElement.IsLoaded
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isloaded
  name: IsLoaded
  nameWithType: VisualElement.IsLoaded
  fullName: Microsoft.Maui.Controls.VisualElement.IsLoaded
- uid: Microsoft.Maui.Controls.VisualElement.ChildrenReordered
  commentId: E:Microsoft.Maui.Controls.VisualElement.ChildrenReordered
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.childrenreordered
  name: ChildrenReordered
  nameWithType: VisualElement.ChildrenReordered
  fullName: Microsoft.Maui.Controls.VisualElement.ChildrenReordered
- uid: Microsoft.Maui.Controls.VisualElement.Focused
  commentId: E:Microsoft.Maui.Controls.VisualElement.Focused
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.focused
  name: Focused
  nameWithType: VisualElement.Focused
  fullName: Microsoft.Maui.Controls.VisualElement.Focused
- uid: Microsoft.Maui.Controls.VisualElement.MeasureInvalidated
  commentId: E:Microsoft.Maui.Controls.VisualElement.MeasureInvalidated
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.measureinvalidated
  name: MeasureInvalidated
  nameWithType: VisualElement.MeasureInvalidated
  fullName: Microsoft.Maui.Controls.VisualElement.MeasureInvalidated
- uid: Microsoft.Maui.Controls.VisualElement.SizeChanged
  commentId: E:Microsoft.Maui.Controls.VisualElement.SizeChanged
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.sizechanged
  name: SizeChanged
  nameWithType: VisualElement.SizeChanged
  fullName: Microsoft.Maui.Controls.VisualElement.SizeChanged
- uid: Microsoft.Maui.Controls.VisualElement.Unfocused
  commentId: E:Microsoft.Maui.Controls.VisualElement.Unfocused
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.unfocused
  name: Unfocused
  nameWithType: VisualElement.Unfocused
  fullName: Microsoft.Maui.Controls.VisualElement.Unfocused
- uid: Microsoft.Maui.Controls.VisualElement.Loaded
  commentId: E:Microsoft.Maui.Controls.VisualElement.Loaded
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.loaded
  name: Loaded
  nameWithType: VisualElement.Loaded
  fullName: Microsoft.Maui.Controls.VisualElement.Loaded
- uid: Microsoft.Maui.Controls.VisualElement.Unloaded
  commentId: E:Microsoft.Maui.Controls.VisualElement.Unloaded
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.unloaded
  name: Unloaded
  nameWithType: VisualElement.Unloaded
  fullName: Microsoft.Maui.Controls.VisualElement.Unloaded
- uid: Microsoft.Maui.Controls.NavigableElement.OnParentSet
  commentId: M:Microsoft.Maui.Controls.NavigableElement.OnParentSet
  parent: Microsoft.Maui.Controls.NavigableElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.navigableelement.onparentset
  name: OnParentSet()
  nameWithType: NavigableElement.OnParentSet()
  fullName: Microsoft.Maui.Controls.NavigableElement.OnParentSet()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.NavigableElement.OnParentSet
    name: OnParentSet
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.navigableelement.onparentset
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.NavigableElement.OnParentSet
    name: OnParentSet
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.navigableelement.onparentset
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.NavigableElement.Navigation
  commentId: P:Microsoft.Maui.Controls.NavigableElement.Navigation
  parent: Microsoft.Maui.Controls.NavigableElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.navigableelement.navigation
  name: Navigation
  nameWithType: NavigableElement.Navigation
  fullName: Microsoft.Maui.Controls.NavigableElement.Navigation
- uid: Microsoft.Maui.Controls.StyleableElement.Style
  commentId: P:Microsoft.Maui.Controls.StyleableElement.Style
  parent: Microsoft.Maui.Controls.StyleableElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.styleableelement.style
  name: Style
  nameWithType: StyleableElement.Style
  fullName: Microsoft.Maui.Controls.StyleableElement.Style
- uid: Microsoft.Maui.Controls.StyleableElement.StyleClass
  commentId: P:Microsoft.Maui.Controls.StyleableElement.StyleClass
  parent: Microsoft.Maui.Controls.StyleableElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.styleableelement.styleclass
  name: StyleClass
  nameWithType: StyleableElement.StyleClass
  fullName: Microsoft.Maui.Controls.StyleableElement.StyleClass
- uid: Microsoft.Maui.Controls.StyleableElement.class
  commentId: P:Microsoft.Maui.Controls.StyleableElement.class
  parent: Microsoft.Maui.Controls.StyleableElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.styleableelement.class
  name: class
  nameWithType: StyleableElement.class
  fullName: Microsoft.Maui.Controls.StyleableElement.class
- uid: Microsoft.Maui.Controls.Element.AutomationIdProperty
  commentId: F:Microsoft.Maui.Controls.Element.AutomationIdProperty
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.automationidproperty
  name: AutomationIdProperty
  nameWithType: Element.AutomationIdProperty
  fullName: Microsoft.Maui.Controls.Element.AutomationIdProperty
- uid: Microsoft.Maui.Controls.Element.ClassIdProperty
  commentId: F:Microsoft.Maui.Controls.Element.ClassIdProperty
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.classidproperty
  name: ClassIdProperty
  nameWithType: Element.ClassIdProperty
  fullName: Microsoft.Maui.Controls.Element.ClassIdProperty
- uid: Microsoft.Maui.Controls.Element.InsertLogicalChild(System.Int32,Microsoft.Maui.Controls.Element)
  commentId: M:Microsoft.Maui.Controls.Element.InsertLogicalChild(System.Int32,Microsoft.Maui.Controls.Element)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.insertlogicalchild
  name: InsertLogicalChild(int, Element)
  nameWithType: Element.InsertLogicalChild(int, Element)
  fullName: Microsoft.Maui.Controls.Element.InsertLogicalChild(int, Microsoft.Maui.Controls.Element)
  nameWithType.vb: Element.InsertLogicalChild(Integer, Element)
  fullName.vb: Microsoft.Maui.Controls.Element.InsertLogicalChild(Integer, Microsoft.Maui.Controls.Element)
  name.vb: InsertLogicalChild(Integer, Element)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.InsertLogicalChild(System.Int32,Microsoft.Maui.Controls.Element)
    name: InsertLogicalChild
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.insertlogicalchild
  - name: (
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.InsertLogicalChild(System.Int32,Microsoft.Maui.Controls.Element)
    name: InsertLogicalChild
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.insertlogicalchild
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: Microsoft.Maui.Controls.Element.AddLogicalChild(Microsoft.Maui.Controls.Element)
  commentId: M:Microsoft.Maui.Controls.Element.AddLogicalChild(Microsoft.Maui.Controls.Element)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.addlogicalchild
  name: AddLogicalChild(Element)
  nameWithType: Element.AddLogicalChild(Element)
  fullName: Microsoft.Maui.Controls.Element.AddLogicalChild(Microsoft.Maui.Controls.Element)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.AddLogicalChild(Microsoft.Maui.Controls.Element)
    name: AddLogicalChild
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.addlogicalchild
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.AddLogicalChild(Microsoft.Maui.Controls.Element)
    name: AddLogicalChild
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.addlogicalchild
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: Microsoft.Maui.Controls.Element.RemoveLogicalChild(Microsoft.Maui.Controls.Element)
  commentId: M:Microsoft.Maui.Controls.Element.RemoveLogicalChild(Microsoft.Maui.Controls.Element)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.removelogicalchild
  name: RemoveLogicalChild(Element)
  nameWithType: Element.RemoveLogicalChild(Element)
  fullName: Microsoft.Maui.Controls.Element.RemoveLogicalChild(Microsoft.Maui.Controls.Element)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.RemoveLogicalChild(Microsoft.Maui.Controls.Element)
    name: RemoveLogicalChild
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.removelogicalchild
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.RemoveLogicalChild(Microsoft.Maui.Controls.Element)
    name: RemoveLogicalChild
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.removelogicalchild
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: Microsoft.Maui.Controls.Element.ClearLogicalChildren
  commentId: M:Microsoft.Maui.Controls.Element.ClearLogicalChildren
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.clearlogicalchildren
  name: ClearLogicalChildren()
  nameWithType: Element.ClearLogicalChildren()
  fullName: Microsoft.Maui.Controls.Element.ClearLogicalChildren()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.ClearLogicalChildren
    name: ClearLogicalChildren
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.clearlogicalchildren
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.ClearLogicalChildren
    name: ClearLogicalChildren
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.clearlogicalchildren
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.Element.FindByName(System.String)
  commentId: M:Microsoft.Maui.Controls.Element.FindByName(System.String)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.findbyname
  name: FindByName(string)
  nameWithType: Element.FindByName(string)
  fullName: Microsoft.Maui.Controls.Element.FindByName(string)
  nameWithType.vb: Element.FindByName(String)
  fullName.vb: Microsoft.Maui.Controls.Element.FindByName(String)
  name.vb: FindByName(String)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.FindByName(System.String)
    name: FindByName
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.findbyname
  - name: (
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.FindByName(System.String)
    name: FindByName
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.findbyname
  - name: (
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: Microsoft.Maui.Controls.Element.RemoveDynamicResource(Microsoft.Maui.Controls.BindableProperty)
  commentId: M:Microsoft.Maui.Controls.Element.RemoveDynamicResource(Microsoft.Maui.Controls.BindableProperty)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.removedynamicresource
  name: RemoveDynamicResource(BindableProperty)
  nameWithType: Element.RemoveDynamicResource(BindableProperty)
  fullName: Microsoft.Maui.Controls.Element.RemoveDynamicResource(Microsoft.Maui.Controls.BindableProperty)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.RemoveDynamicResource(Microsoft.Maui.Controls.BindableProperty)
    name: RemoveDynamicResource
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.removedynamicresource
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.RemoveDynamicResource(Microsoft.Maui.Controls.BindableProperty)
    name: RemoveDynamicResource
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.removedynamicresource
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
- uid: Microsoft.Maui.Controls.Element.SetDynamicResource(Microsoft.Maui.Controls.BindableProperty,System.String)
  commentId: M:Microsoft.Maui.Controls.Element.SetDynamicResource(Microsoft.Maui.Controls.BindableProperty,System.String)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.setdynamicresource
  name: SetDynamicResource(BindableProperty, string)
  nameWithType: Element.SetDynamicResource(BindableProperty, string)
  fullName: Microsoft.Maui.Controls.Element.SetDynamicResource(Microsoft.Maui.Controls.BindableProperty, string)
  nameWithType.vb: Element.SetDynamicResource(BindableProperty, String)
  fullName.vb: Microsoft.Maui.Controls.Element.SetDynamicResource(Microsoft.Maui.Controls.BindableProperty, String)
  name.vb: SetDynamicResource(BindableProperty, String)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.SetDynamicResource(Microsoft.Maui.Controls.BindableProperty,System.String)
    name: SetDynamicResource
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.setdynamicresource
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.SetDynamicResource(Microsoft.Maui.Controls.BindableProperty,System.String)
    name: SetDynamicResource
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.setdynamicresource
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: Microsoft.Maui.Controls.Element.OnPropertyChanged(System.String)
  commentId: M:Microsoft.Maui.Controls.Element.OnPropertyChanged(System.String)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onpropertychanged
  name: OnPropertyChanged(string)
  nameWithType: Element.OnPropertyChanged(string)
  fullName: Microsoft.Maui.Controls.Element.OnPropertyChanged(string)
  nameWithType.vb: Element.OnPropertyChanged(String)
  fullName.vb: Microsoft.Maui.Controls.Element.OnPropertyChanged(String)
  name.vb: OnPropertyChanged(String)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.OnPropertyChanged(System.String)
    name: OnPropertyChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onpropertychanged
  - name: (
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.OnPropertyChanged(System.String)
    name: OnPropertyChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onpropertychanged
  - name: (
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: Microsoft.Maui.Controls.Element.OnParentChanging(Microsoft.Maui.Controls.ParentChangingEventArgs)
  commentId: M:Microsoft.Maui.Controls.Element.OnParentChanging(Microsoft.Maui.Controls.ParentChangingEventArgs)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentchanging
  name: OnParentChanging(ParentChangingEventArgs)
  nameWithType: Element.OnParentChanging(ParentChangingEventArgs)
  fullName: Microsoft.Maui.Controls.Element.OnParentChanging(Microsoft.Maui.Controls.ParentChangingEventArgs)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.OnParentChanging(Microsoft.Maui.Controls.ParentChangingEventArgs)
    name: OnParentChanging
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentchanging
  - name: (
  - uid: Microsoft.Maui.Controls.ParentChangingEventArgs
    name: ParentChangingEventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.parentchangingeventargs
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.OnParentChanging(Microsoft.Maui.Controls.ParentChangingEventArgs)
    name: OnParentChanging
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentchanging
  - name: (
  - uid: Microsoft.Maui.Controls.ParentChangingEventArgs
    name: ParentChangingEventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.parentchangingeventargs
  - name: )
- uid: Microsoft.Maui.Controls.Element.OnParentChanged
  commentId: M:Microsoft.Maui.Controls.Element.OnParentChanged
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentchanged
  name: OnParentChanged()
  nameWithType: Element.OnParentChanged()
  fullName: Microsoft.Maui.Controls.Element.OnParentChanged()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.OnParentChanged
    name: OnParentChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentchanged
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.OnParentChanged
    name: OnParentChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentchanged
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.Element.OnHandlerChanged
  commentId: M:Microsoft.Maui.Controls.Element.OnHandlerChanged
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onhandlerchanged
  name: OnHandlerChanged()
  nameWithType: Element.OnHandlerChanged()
  fullName: Microsoft.Maui.Controls.Element.OnHandlerChanged()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.OnHandlerChanged
    name: OnHandlerChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onhandlerchanged
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.OnHandlerChanged
    name: OnHandlerChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onhandlerchanged
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.Element.MapAutomationPropertiesIsInAccessibleTree(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
  commentId: M:Microsoft.Maui.Controls.Element.MapAutomationPropertiesIsInAccessibleTree(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.mapautomationpropertiesisinaccessibletree
  name: MapAutomationPropertiesIsInAccessibleTree(IElementHandler, Element)
  nameWithType: Element.MapAutomationPropertiesIsInAccessibleTree(IElementHandler, Element)
  fullName: Microsoft.Maui.Controls.Element.MapAutomationPropertiesIsInAccessibleTree(Microsoft.Maui.IElementHandler, Microsoft.Maui.Controls.Element)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.MapAutomationPropertiesIsInAccessibleTree(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
    name: MapAutomationPropertiesIsInAccessibleTree
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.mapautomationpropertiesisinaccessibletree
  - name: (
  - uid: Microsoft.Maui.IElementHandler
    name: IElementHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.ielementhandler
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.MapAutomationPropertiesIsInAccessibleTree(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
    name: MapAutomationPropertiesIsInAccessibleTree
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.mapautomationpropertiesisinaccessibletree
  - name: (
  - uid: Microsoft.Maui.IElementHandler
    name: IElementHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.ielementhandler
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: Microsoft.Maui.Controls.Element.MapAutomationPropertiesExcludedWithChildren(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
  commentId: M:Microsoft.Maui.Controls.Element.MapAutomationPropertiesExcludedWithChildren(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.mapautomationpropertiesexcludedwithchildren
  name: MapAutomationPropertiesExcludedWithChildren(IElementHandler, Element)
  nameWithType: Element.MapAutomationPropertiesExcludedWithChildren(IElementHandler, Element)
  fullName: Microsoft.Maui.Controls.Element.MapAutomationPropertiesExcludedWithChildren(Microsoft.Maui.IElementHandler, Microsoft.Maui.Controls.Element)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.MapAutomationPropertiesExcludedWithChildren(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
    name: MapAutomationPropertiesExcludedWithChildren
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.mapautomationpropertiesexcludedwithchildren
  - name: (
  - uid: Microsoft.Maui.IElementHandler
    name: IElementHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.ielementhandler
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.MapAutomationPropertiesExcludedWithChildren(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
    name: MapAutomationPropertiesExcludedWithChildren
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.mapautomationpropertiesexcludedwithchildren
  - name: (
  - uid: Microsoft.Maui.IElementHandler
    name: IElementHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.ielementhandler
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: Microsoft.Maui.Controls.Element.AutomationId
  commentId: P:Microsoft.Maui.Controls.Element.AutomationId
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.automationid
  name: AutomationId
  nameWithType: Element.AutomationId
  fullName: Microsoft.Maui.Controls.Element.AutomationId
- uid: Microsoft.Maui.Controls.Element.ClassId
  commentId: P:Microsoft.Maui.Controls.Element.ClassId
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.classid
  name: ClassId
  nameWithType: Element.ClassId
  fullName: Microsoft.Maui.Controls.Element.ClassId
- uid: Microsoft.Maui.Controls.Element.Effects
  commentId: P:Microsoft.Maui.Controls.Element.Effects
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.effects
  name: Effects
  nameWithType: Element.Effects
  fullName: Microsoft.Maui.Controls.Element.Effects
- uid: Microsoft.Maui.Controls.Element.Id
  commentId: P:Microsoft.Maui.Controls.Element.Id
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.id
  name: Id
  nameWithType: Element.Id
  fullName: Microsoft.Maui.Controls.Element.Id
- uid: Microsoft.Maui.Controls.Element.StyleId
  commentId: P:Microsoft.Maui.Controls.Element.StyleId
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.styleid
  name: StyleId
  nameWithType: Element.StyleId
  fullName: Microsoft.Maui.Controls.Element.StyleId
- uid: Microsoft.Maui.Controls.Element.Parent
  commentId: P:Microsoft.Maui.Controls.Element.Parent
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.parent
  name: Parent
  nameWithType: Element.Parent
  fullName: Microsoft.Maui.Controls.Element.Parent
- uid: Microsoft.Maui.Controls.Element.ChildAdded
  commentId: E:Microsoft.Maui.Controls.Element.ChildAdded
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.childadded
  name: ChildAdded
  nameWithType: Element.ChildAdded
  fullName: Microsoft.Maui.Controls.Element.ChildAdded
- uid: Microsoft.Maui.Controls.Element.ChildRemoved
  commentId: E:Microsoft.Maui.Controls.Element.ChildRemoved
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.childremoved
  name: ChildRemoved
  nameWithType: Element.ChildRemoved
  fullName: Microsoft.Maui.Controls.Element.ChildRemoved
- uid: Microsoft.Maui.Controls.Element.DescendantAdded
  commentId: E:Microsoft.Maui.Controls.Element.DescendantAdded
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.descendantadded
  name: DescendantAdded
  nameWithType: Element.DescendantAdded
  fullName: Microsoft.Maui.Controls.Element.DescendantAdded
- uid: Microsoft.Maui.Controls.Element.DescendantRemoved
  commentId: E:Microsoft.Maui.Controls.Element.DescendantRemoved
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.descendantremoved
  name: DescendantRemoved
  nameWithType: Element.DescendantRemoved
  fullName: Microsoft.Maui.Controls.Element.DescendantRemoved
- uid: Microsoft.Maui.Controls.Element.ParentChanging
  commentId: E:Microsoft.Maui.Controls.Element.ParentChanging
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.parentchanging
  name: ParentChanging
  nameWithType: Element.ParentChanging
  fullName: Microsoft.Maui.Controls.Element.ParentChanging
- uid: Microsoft.Maui.Controls.Element.ParentChanged
  commentId: E:Microsoft.Maui.Controls.Element.ParentChanged
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.parentchanged
  name: ParentChanged
  nameWithType: Element.ParentChanged
  fullName: Microsoft.Maui.Controls.Element.ParentChanged
- uid: Microsoft.Maui.Controls.Element.HandlerChanging
  commentId: E:Microsoft.Maui.Controls.Element.HandlerChanging
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.handlerchanging
  name: HandlerChanging
  nameWithType: Element.HandlerChanging
  fullName: Microsoft.Maui.Controls.Element.HandlerChanging
- uid: Microsoft.Maui.Controls.Element.HandlerChanged
  commentId: E:Microsoft.Maui.Controls.Element.HandlerChanged
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.handlerchanged
  name: HandlerChanged
  nameWithType: Element.HandlerChanged
  fullName: Microsoft.Maui.Controls.Element.HandlerChanged
- uid: Microsoft.Maui.Controls.BindableObject.BindingContextProperty
  commentId: F:Microsoft.Maui.Controls.BindableObject.BindingContextProperty
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.bindingcontextproperty
  name: BindingContextProperty
  nameWithType: BindableObject.BindingContextProperty
  fullName: Microsoft.Maui.Controls.BindableObject.BindingContextProperty
- uid: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindableProperty)
  commentId: M:Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindableProperty)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindableproperty)
  name: ClearValue(BindableProperty)
  nameWithType: BindableObject.ClearValue(BindableProperty)
  fullName: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindableProperty)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindableProperty)
    name: ClearValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindableproperty)
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindableProperty)
    name: ClearValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindableproperty)
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindablePropertyKey)
  commentId: M:Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindablePropertyKey)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindablepropertykey)
  name: ClearValue(BindablePropertyKey)
  nameWithType: BindableObject.ClearValue(BindablePropertyKey)
  fullName: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindablePropertyKey)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindablePropertyKey)
    name: ClearValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindablepropertykey)
  - name: (
  - uid: Microsoft.Maui.Controls.BindablePropertyKey
    name: BindablePropertyKey
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindablepropertykey
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindablePropertyKey)
    name: ClearValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindablepropertykey)
  - name: (
  - uid: Microsoft.Maui.Controls.BindablePropertyKey
    name: BindablePropertyKey
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindablepropertykey
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.GetValue(Microsoft.Maui.Controls.BindableProperty)
  commentId: M:Microsoft.Maui.Controls.BindableObject.GetValue(Microsoft.Maui.Controls.BindableProperty)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.getvalue
  name: GetValue(BindableProperty)
  nameWithType: BindableObject.GetValue(BindableProperty)
  fullName: Microsoft.Maui.Controls.BindableObject.GetValue(Microsoft.Maui.Controls.BindableProperty)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.GetValue(Microsoft.Maui.Controls.BindableProperty)
    name: GetValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.getvalue
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.GetValue(Microsoft.Maui.Controls.BindableProperty)
    name: GetValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.getvalue
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.IsSet(Microsoft.Maui.Controls.BindableProperty)
  commentId: M:Microsoft.Maui.Controls.BindableObject.IsSet(Microsoft.Maui.Controls.BindableProperty)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.isset
  name: IsSet(BindableProperty)
  nameWithType: BindableObject.IsSet(BindableProperty)
  fullName: Microsoft.Maui.Controls.BindableObject.IsSet(Microsoft.Maui.Controls.BindableProperty)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.IsSet(Microsoft.Maui.Controls.BindableProperty)
    name: IsSet
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.isset
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.IsSet(Microsoft.Maui.Controls.BindableProperty)
    name: IsSet
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.isset
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.RemoveBinding(Microsoft.Maui.Controls.BindableProperty)
  commentId: M:Microsoft.Maui.Controls.BindableObject.RemoveBinding(Microsoft.Maui.Controls.BindableProperty)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.removebinding
  name: RemoveBinding(BindableProperty)
  nameWithType: BindableObject.RemoveBinding(BindableProperty)
  fullName: Microsoft.Maui.Controls.BindableObject.RemoveBinding(Microsoft.Maui.Controls.BindableProperty)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.RemoveBinding(Microsoft.Maui.Controls.BindableProperty)
    name: RemoveBinding
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.removebinding
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.RemoveBinding(Microsoft.Maui.Controls.BindableProperty)
    name: RemoveBinding
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.removebinding
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.SetBinding(Microsoft.Maui.Controls.BindableProperty,Microsoft.Maui.Controls.BindingBase)
  commentId: M:Microsoft.Maui.Controls.BindableObject.SetBinding(Microsoft.Maui.Controls.BindableProperty,Microsoft.Maui.Controls.BindingBase)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setbinding
  name: SetBinding(BindableProperty, BindingBase)
  nameWithType: BindableObject.SetBinding(BindableProperty, BindingBase)
  fullName: Microsoft.Maui.Controls.BindableObject.SetBinding(Microsoft.Maui.Controls.BindableProperty, Microsoft.Maui.Controls.BindingBase)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.SetBinding(Microsoft.Maui.Controls.BindableProperty,Microsoft.Maui.Controls.BindingBase)
    name: SetBinding
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setbinding
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.BindingBase
    name: BindingBase
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindingbase
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.SetBinding(Microsoft.Maui.Controls.BindableProperty,Microsoft.Maui.Controls.BindingBase)
    name: SetBinding
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setbinding
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.BindingBase
    name: BindingBase
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindingbase
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.ApplyBindings
  commentId: M:Microsoft.Maui.Controls.BindableObject.ApplyBindings
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.applybindings
  name: ApplyBindings()
  nameWithType: BindableObject.ApplyBindings()
  fullName: Microsoft.Maui.Controls.BindableObject.ApplyBindings()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.ApplyBindings
    name: ApplyBindings
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.applybindings
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.ApplyBindings
    name: ApplyBindings
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.applybindings
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(System.String)
  commentId: M:Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(System.String)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.onpropertychanging
  name: OnPropertyChanging(string)
  nameWithType: BindableObject.OnPropertyChanging(string)
  fullName: Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(string)
  nameWithType.vb: BindableObject.OnPropertyChanging(String)
  fullName.vb: Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(String)
  name.vb: OnPropertyChanging(String)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(System.String)
    name: OnPropertyChanging
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.onpropertychanging
  - name: (
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(System.String)
    name: OnPropertyChanging
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.onpropertychanging
  - name: (
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.UnapplyBindings
  commentId: M:Microsoft.Maui.Controls.BindableObject.UnapplyBindings
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.unapplybindings
  name: UnapplyBindings()
  nameWithType: BindableObject.UnapplyBindings()
  fullName: Microsoft.Maui.Controls.BindableObject.UnapplyBindings()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.UnapplyBindings
    name: UnapplyBindings
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.unapplybindings
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.UnapplyBindings
    name: UnapplyBindings
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.unapplybindings
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty,System.Object)
  commentId: M:Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty,System.Object)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindableproperty-system-object)
  name: SetValue(BindableProperty, object)
  nameWithType: BindableObject.SetValue(BindableProperty, object)
  fullName: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty, object)
  nameWithType.vb: BindableObject.SetValue(BindableProperty, Object)
  fullName.vb: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty, Object)
  name.vb: SetValue(BindableProperty, Object)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty,System.Object)
    name: SetValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindableproperty-system-object)
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty,System.Object)
    name: SetValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindableproperty-system-object)
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey,System.Object)
  commentId: M:Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey,System.Object)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindablepropertykey-system-object)
  name: SetValue(BindablePropertyKey, object)
  nameWithType: BindableObject.SetValue(BindablePropertyKey, object)
  fullName: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey, object)
  nameWithType.vb: BindableObject.SetValue(BindablePropertyKey, Object)
  fullName.vb: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey, Object)
  name.vb: SetValue(BindablePropertyKey, Object)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey,System.Object)
    name: SetValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindablepropertykey-system-object)
  - name: (
  - uid: Microsoft.Maui.Controls.BindablePropertyKey
    name: BindablePropertyKey
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindablepropertykey
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey,System.Object)
    name: SetValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindablepropertykey-system-object)
  - name: (
  - uid: Microsoft.Maui.Controls.BindablePropertyKey
    name: BindablePropertyKey
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindablepropertykey
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindableProperty)
  commentId: M:Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindableProperty)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindableproperty)
  name: CoerceValue(BindableProperty)
  nameWithType: BindableObject.CoerceValue(BindableProperty)
  fullName: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindableProperty)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindableProperty)
    name: CoerceValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindableproperty)
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindableProperty)
    name: CoerceValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindableproperty)
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindablePropertyKey)
  commentId: M:Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindablePropertyKey)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindablepropertykey)
  name: CoerceValue(BindablePropertyKey)
  nameWithType: BindableObject.CoerceValue(BindablePropertyKey)
  fullName: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindablePropertyKey)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindablePropertyKey)
    name: CoerceValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindablepropertykey)
  - name: (
  - uid: Microsoft.Maui.Controls.BindablePropertyKey
    name: BindablePropertyKey
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindablepropertykey
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindablePropertyKey)
    name: CoerceValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindablepropertykey)
  - name: (
  - uid: Microsoft.Maui.Controls.BindablePropertyKey
    name: BindablePropertyKey
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindablepropertykey
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.Dispatcher
  commentId: P:Microsoft.Maui.Controls.BindableObject.Dispatcher
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.dispatcher
  name: Dispatcher
  nameWithType: BindableObject.Dispatcher
  fullName: Microsoft.Maui.Controls.BindableObject.Dispatcher
- uid: Microsoft.Maui.Controls.BindableObject.BindingContext
  commentId: P:Microsoft.Maui.Controls.BindableObject.BindingContext
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.bindingcontext
  name: BindingContext
  nameWithType: BindableObject.BindingContext
  fullName: Microsoft.Maui.Controls.BindableObject.BindingContext
- uid: Microsoft.Maui.Controls.BindableObject.PropertyChanged
  commentId: E:Microsoft.Maui.Controls.BindableObject.PropertyChanged
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.propertychanged
  name: PropertyChanged
  nameWithType: BindableObject.PropertyChanged
  fullName: Microsoft.Maui.Controls.BindableObject.PropertyChanged
- uid: Microsoft.Maui.Controls.BindableObject.PropertyChanging
  commentId: E:Microsoft.Maui.Controls.BindableObject.PropertyChanging
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.propertychanging
  name: PropertyChanging
  nameWithType: BindableObject.PropertyChanging
  fullName: Microsoft.Maui.Controls.BindableObject.PropertyChanging
- uid: Microsoft.Maui.Controls.BindableObject.BindingContextChanged
  commentId: E:Microsoft.Maui.Controls.BindableObject.BindingContextChanged
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.bindingcontextchanged
  name: BindingContextChanged
  nameWithType: BindableObject.BindingContextChanged
  fullName: Microsoft.Maui.Controls.BindableObject.BindingContextChanged
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: DrawnUi.Controls.MauiEntry.DrawnUi.Draw.FluentExtensions.AssignNative``1(DrawnUi.Controls.MauiEntry@)
  commentId: M:DrawnUi.Draw.FluentExtensions.AssignNative``1(``0,``0@)
  parent: DrawnUi.Draw.FluentExtensions
  definition: DrawnUi.Draw.FluentExtensions.AssignNative``1(``0,``0@)
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_AssignNative__1___0___0__
  name: AssignNative<MauiEntry>(MauiEntry, out MauiEntry)
  nameWithType: FluentExtensions.AssignNative<MauiEntry>(MauiEntry, out MauiEntry)
  fullName: DrawnUi.Draw.FluentExtensions.AssignNative<DrawnUi.Controls.MauiEntry>(DrawnUi.Controls.MauiEntry, out DrawnUi.Controls.MauiEntry)
  nameWithType.vb: FluentExtensions.AssignNative(Of MauiEntry)(MauiEntry, MauiEntry)
  fullName.vb: DrawnUi.Draw.FluentExtensions.AssignNative(Of DrawnUi.Controls.MauiEntry)(DrawnUi.Controls.MauiEntry, DrawnUi.Controls.MauiEntry)
  name.vb: AssignNative(Of MauiEntry)(MauiEntry, MauiEntry)
  spec.csharp:
  - uid: DrawnUi.Draw.FluentExtensions.AssignNative``1(DrawnUi.Controls.MauiEntry,DrawnUi.Controls.MauiEntry@)
    name: AssignNative
    href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_AssignNative__1___0___0__
  - name: <
  - uid: DrawnUi.Controls.MauiEntry
    name: MauiEntry
    href: DrawnUi.Controls.MauiEntry.html
  - name: '>'
  - name: (
  - uid: DrawnUi.Controls.MauiEntry
    name: MauiEntry
    href: DrawnUi.Controls.MauiEntry.html
  - name: ','
  - name: " "
  - name: out
  - name: " "
  - uid: DrawnUi.Controls.MauiEntry
    name: MauiEntry
    href: DrawnUi.Controls.MauiEntry.html
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.FluentExtensions.AssignNative``1(DrawnUi.Controls.MauiEntry,DrawnUi.Controls.MauiEntry@)
    name: AssignNative
    href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_AssignNative__1___0___0__
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Controls.MauiEntry
    name: MauiEntry
    href: DrawnUi.Controls.MauiEntry.html
  - name: )
  - name: (
  - uid: DrawnUi.Controls.MauiEntry
    name: MauiEntry
    href: DrawnUi.Controls.MauiEntry.html
  - name: ','
  - name: " "
  - uid: DrawnUi.Controls.MauiEntry
    name: MauiEntry
    href: DrawnUi.Controls.MauiEntry.html
  - name: )
- uid: Microsoft.Maui.Controls.Element.DrawnUi.Draw.StaticResourcesExtensions.FindParent``1
  commentId: M:DrawnUi.Draw.StaticResourcesExtensions.FindParent``1(Microsoft.Maui.Controls.Element)
  parent: DrawnUi.Draw.StaticResourcesExtensions
  definition: DrawnUi.Draw.StaticResourcesExtensions.FindParent``1(Microsoft.Maui.Controls.Element)
  href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_FindParent__1_Microsoft_Maui_Controls_Element_
  name: FindParent<T>(Element)
  nameWithType: StaticResourcesExtensions.FindParent<T>(Element)
  fullName: DrawnUi.Draw.StaticResourcesExtensions.FindParent<T>(Microsoft.Maui.Controls.Element)
  nameWithType.vb: StaticResourcesExtensions.FindParent(Of T)(Element)
  fullName.vb: DrawnUi.Draw.StaticResourcesExtensions.FindParent(Of T)(Microsoft.Maui.Controls.Element)
  name.vb: FindParent(Of T)(Element)
  spec.csharp:
  - uid: DrawnUi.Draw.StaticResourcesExtensions.FindParent``1(Microsoft.Maui.Controls.Element)
    name: FindParent
    href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_FindParent__1_Microsoft_Maui_Controls_Element_
  - name: <
  - name: T
  - name: '>'
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.StaticResourcesExtensions.FindParent``1(Microsoft.Maui.Controls.Element)
    name: FindParent
    href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_FindParent__1_Microsoft_Maui_Controls_Element_
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: Microsoft.Maui.Controls.Element.DrawnUi.Extensions.InternalExtensions.FindMauiContext(System.Boolean)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element,System.Boolean)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element,System.Boolean)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FindMauiContext_Microsoft_Maui_Controls_Element_System_Boolean_
  name: FindMauiContext(Element, bool)
  nameWithType: InternalExtensions.FindMauiContext(Element, bool)
  fullName: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element, bool)
  nameWithType.vb: InternalExtensions.FindMauiContext(Element, Boolean)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element, Boolean)
  name.vb: FindMauiContext(Element, Boolean)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element,System.Boolean)
    name: FindMauiContext
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FindMauiContext_Microsoft_Maui_Controls_Element_System_Boolean_
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: bool
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element,System.Boolean)
    name: FindMauiContext
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FindMauiContext_Microsoft_Maui_Controls_Element_System_Boolean_
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: Boolean
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
- uid: Microsoft.Maui.Controls.Element.DrawnUi.Extensions.InternalExtensions.GetParentsPath
  commentId: M:DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_GetParentsPath_Microsoft_Maui_Controls_Element_
  name: GetParentsPath(Element)
  nameWithType: InternalExtensions.GetParentsPath(Element)
  fullName: DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
    name: GetParentsPath
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_GetParentsPath_Microsoft_Maui_Controls_Element_
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
    name: GetParentsPath
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_GetParentsPath_Microsoft_Maui_Controls_Element_
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents
  commentId: M:DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents(Microsoft.Maui.Controls.VisualElement)
  parent: DrawnUi.Draw.StaticResourcesExtensions
  definition: DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents(Microsoft.Maui.Controls.VisualElement)
  href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_GetAllWithMyselfParents_Microsoft_Maui_Controls_VisualElement_
  name: GetAllWithMyselfParents(VisualElement)
  nameWithType: StaticResourcesExtensions.GetAllWithMyselfParents(VisualElement)
  fullName: DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents(Microsoft.Maui.Controls.VisualElement)
  spec.csharp:
  - uid: DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents(Microsoft.Maui.Controls.VisualElement)
    name: GetAllWithMyselfParents
    href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_GetAllWithMyselfParents_Microsoft_Maui_Controls_VisualElement_
  - name: (
  - uid: Microsoft.Maui.Controls.VisualElement
    name: VisualElement
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents(Microsoft.Maui.Controls.VisualElement)
    name: GetAllWithMyselfParents
    href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_GetAllWithMyselfParents_Microsoft_Maui_Controls_VisualElement_
  - name: (
  - uid: Microsoft.Maui.Controls.VisualElement
    name: VisualElement
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement
  - name: )
- uid: Microsoft.Maui.IView.DrawnUi.Extensions.InternalExtensions.DisposeControlAndChildren
  commentId: M:DrawnUi.Extensions.InternalExtensions.DisposeControlAndChildren(Microsoft.Maui.IView)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.DisposeControlAndChildren(Microsoft.Maui.IView)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_DisposeControlAndChildren_Microsoft_Maui_IView_
  name: DisposeControlAndChildren(IView)
  nameWithType: InternalExtensions.DisposeControlAndChildren(IView)
  fullName: DrawnUi.Extensions.InternalExtensions.DisposeControlAndChildren(Microsoft.Maui.IView)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.DisposeControlAndChildren(Microsoft.Maui.IView)
    name: DisposeControlAndChildren
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_DisposeControlAndChildren_Microsoft_Maui_IView_
  - name: (
  - uid: Microsoft.Maui.IView
    name: IView
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iview
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.DisposeControlAndChildren(Microsoft.Maui.IView)
    name: DisposeControlAndChildren
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_DisposeControlAndChildren_Microsoft_Maui_IView_
  - name: (
  - uid: Microsoft.Maui.IView
    name: IView
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iview
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: Microsoft.Maui.Controls
  commentId: N:Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft
  name: Microsoft.Maui.Controls
  nameWithType: Microsoft.Maui.Controls
  fullName: Microsoft.Maui.Controls
  spec.csharp:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Controls
    name: Controls
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls
  spec.vb:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Controls
    name: Controls
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls
- uid: System.ComponentModel
  commentId: N:System.ComponentModel
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.ComponentModel
  nameWithType: System.ComponentModel
  fullName: System.ComponentModel
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.ComponentModel
    name: ComponentModel
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.ComponentModel
    name: ComponentModel
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel
- uid: Microsoft.Maui
  commentId: N:Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft
  name: Microsoft.Maui
  nameWithType: Microsoft.Maui
  fullName: Microsoft.Maui
  spec.csharp:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  spec.vb:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
- uid: Microsoft.Maui.Controls.Internals
  commentId: N:Microsoft.Maui.Controls.Internals
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft
  name: Microsoft.Maui.Controls.Internals
  nameWithType: Microsoft.Maui.Controls.Internals
  fullName: Microsoft.Maui.Controls.Internals
  spec.csharp:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Controls
    name: Controls
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls
  - name: .
  - uid: Microsoft.Maui.Controls.Internals
    name: Internals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.internals
  spec.vb:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Controls
    name: Controls
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls
  - name: .
  - uid: Microsoft.Maui.Controls.Internals
    name: Internals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.internals
- uid: Microsoft.Maui.HotReload
  commentId: N:Microsoft.Maui.HotReload
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft
  name: Microsoft.Maui.HotReload
  nameWithType: Microsoft.Maui.HotReload
  fullName: Microsoft.Maui.HotReload
  spec.csharp:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.HotReload
    name: HotReload
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.hotreload
  spec.vb:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.HotReload
    name: HotReload
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.hotreload
- uid: Microsoft.Maui.Controls.IElementConfiguration`1
  commentId: T:Microsoft.Maui.Controls.IElementConfiguration`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ielementconfiguration-1
  name: IElementConfiguration<TElement>
  nameWithType: IElementConfiguration<TElement>
  fullName: Microsoft.Maui.Controls.IElementConfiguration<TElement>
  nameWithType.vb: IElementConfiguration(Of TElement)
  fullName.vb: Microsoft.Maui.Controls.IElementConfiguration(Of TElement)
  name.vb: IElementConfiguration(Of TElement)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.IElementConfiguration`1
    name: IElementConfiguration
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ielementconfiguration-1
  - name: <
  - name: TElement
  - name: '>'
  spec.vb:
  - uid: Microsoft.Maui.Controls.IElementConfiguration`1
    name: IElementConfiguration
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ielementconfiguration-1
  - name: (
  - name: Of
  - name: " "
  - name: TElement
  - name: )
- uid: DrawnUi.Draw.FluentExtensions.AssignNative``1(``0,``0@)
  commentId: M:DrawnUi.Draw.FluentExtensions.AssignNative``1(``0,``0@)
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_AssignNative__1___0___0__
  name: AssignNative<T>(T, out T)
  nameWithType: FluentExtensions.AssignNative<T>(T, out T)
  fullName: DrawnUi.Draw.FluentExtensions.AssignNative<T>(T, out T)
  nameWithType.vb: FluentExtensions.AssignNative(Of T)(T, T)
  fullName.vb: DrawnUi.Draw.FluentExtensions.AssignNative(Of T)(T, T)
  name.vb: AssignNative(Of T)(T, T)
  spec.csharp:
  - uid: DrawnUi.Draw.FluentExtensions.AssignNative``1(``0,``0@)
    name: AssignNative
    href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_AssignNative__1___0___0__
  - name: <
  - name: T
  - name: '>'
  - name: (
  - name: T
  - name: ','
  - name: " "
  - name: out
  - name: " "
  - name: T
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.FluentExtensions.AssignNative``1(``0,``0@)
    name: AssignNative
    href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_AssignNative__1___0___0__
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
  - name: (
  - name: T
  - name: ','
  - name: " "
  - name: T
  - name: )
- uid: DrawnUi.Draw.FluentExtensions
  commentId: T:DrawnUi.Draw.FluentExtensions
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.FluentExtensions.html
  name: FluentExtensions
  nameWithType: FluentExtensions
  fullName: DrawnUi.Draw.FluentExtensions
- uid: DrawnUi.Draw.StaticResourcesExtensions.FindParent``1(Microsoft.Maui.Controls.Element)
  commentId: M:DrawnUi.Draw.StaticResourcesExtensions.FindParent``1(Microsoft.Maui.Controls.Element)
  isExternal: true
  href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_FindParent__1_Microsoft_Maui_Controls_Element_
  name: FindParent<T>(Element)
  nameWithType: StaticResourcesExtensions.FindParent<T>(Element)
  fullName: DrawnUi.Draw.StaticResourcesExtensions.FindParent<T>(Microsoft.Maui.Controls.Element)
  nameWithType.vb: StaticResourcesExtensions.FindParent(Of T)(Element)
  fullName.vb: DrawnUi.Draw.StaticResourcesExtensions.FindParent(Of T)(Microsoft.Maui.Controls.Element)
  name.vb: FindParent(Of T)(Element)
  spec.csharp:
  - uid: DrawnUi.Draw.StaticResourcesExtensions.FindParent``1(Microsoft.Maui.Controls.Element)
    name: FindParent
    href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_FindParent__1_Microsoft_Maui_Controls_Element_
  - name: <
  - name: T
  - name: '>'
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.StaticResourcesExtensions.FindParent``1(Microsoft.Maui.Controls.Element)
    name: FindParent
    href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_FindParent__1_Microsoft_Maui_Controls_Element_
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: DrawnUi.Draw.StaticResourcesExtensions
  commentId: T:DrawnUi.Draw.StaticResourcesExtensions
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.StaticResourcesExtensions.html
  name: StaticResourcesExtensions
  nameWithType: StaticResourcesExtensions
  fullName: DrawnUi.Draw.StaticResourcesExtensions
- uid: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element,System.Boolean)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element,System.Boolean)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FindMauiContext_Microsoft_Maui_Controls_Element_System_Boolean_
  name: FindMauiContext(Element, bool)
  nameWithType: InternalExtensions.FindMauiContext(Element, bool)
  fullName: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element, bool)
  nameWithType.vb: InternalExtensions.FindMauiContext(Element, Boolean)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element, Boolean)
  name.vb: FindMauiContext(Element, Boolean)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element,System.Boolean)
    name: FindMauiContext
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FindMauiContext_Microsoft_Maui_Controls_Element_System_Boolean_
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: bool
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element,System.Boolean)
    name: FindMauiContext
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FindMauiContext_Microsoft_Maui_Controls_Element_System_Boolean_
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: Boolean
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
  commentId: M:DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_GetParentsPath_Microsoft_Maui_Controls_Element_
  name: GetParentsPath(Element)
  nameWithType: InternalExtensions.GetParentsPath(Element)
  fullName: DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
    name: GetParentsPath
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_GetParentsPath_Microsoft_Maui_Controls_Element_
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
    name: GetParentsPath
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_GetParentsPath_Microsoft_Maui_Controls_Element_
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents(Microsoft.Maui.Controls.VisualElement)
  commentId: M:DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents(Microsoft.Maui.Controls.VisualElement)
  isExternal: true
  href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_GetAllWithMyselfParents_Microsoft_Maui_Controls_VisualElement_
  name: GetAllWithMyselfParents(VisualElement)
  nameWithType: StaticResourcesExtensions.GetAllWithMyselfParents(VisualElement)
  fullName: DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents(Microsoft.Maui.Controls.VisualElement)
  spec.csharp:
  - uid: DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents(Microsoft.Maui.Controls.VisualElement)
    name: GetAllWithMyselfParents
    href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_GetAllWithMyselfParents_Microsoft_Maui_Controls_VisualElement_
  - name: (
  - uid: Microsoft.Maui.Controls.VisualElement
    name: VisualElement
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents(Microsoft.Maui.Controls.VisualElement)
    name: GetAllWithMyselfParents
    href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_GetAllWithMyselfParents_Microsoft_Maui_Controls_VisualElement_
  - name: (
  - uid: Microsoft.Maui.Controls.VisualElement
    name: VisualElement
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.DisposeControlAndChildren(Microsoft.Maui.IView)
  commentId: M:DrawnUi.Extensions.InternalExtensions.DisposeControlAndChildren(Microsoft.Maui.IView)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_DisposeControlAndChildren_Microsoft_Maui_IView_
  name: DisposeControlAndChildren(IView)
  nameWithType: InternalExtensions.DisposeControlAndChildren(IView)
  fullName: DrawnUi.Extensions.InternalExtensions.DisposeControlAndChildren(Microsoft.Maui.IView)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.DisposeControlAndChildren(Microsoft.Maui.IView)
    name: DisposeControlAndChildren
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_DisposeControlAndChildren_Microsoft_Maui_IView_
  - name: (
  - uid: Microsoft.Maui.IView
    name: IView
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iview
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.DisposeControlAndChildren(Microsoft.Maui.IView)
    name: DisposeControlAndChildren
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_DisposeControlAndChildren_Microsoft_Maui_IView_
  - name: (
  - uid: Microsoft.Maui.IView
    name: IView
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iview
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Controls.MauiEntry.#ctor*
  commentId: Overload:DrawnUi.Controls.MauiEntry.#ctor
  href: DrawnUi.Controls.MauiEntry.html#DrawnUi_Controls_MauiEntry__ctor
  name: MauiEntry
  nameWithType: MauiEntry.MauiEntry
  fullName: DrawnUi.Controls.MauiEntry.MauiEntry
  nameWithType.vb: MauiEntry.New
  fullName.vb: DrawnUi.Controls.MauiEntry.New
  name.vb: New
- uid: Microsoft.Maui.Controls.BindableProperty
  commentId: T:Microsoft.Maui.Controls.BindableProperty
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  name: BindableProperty
  nameWithType: BindableProperty
  fullName: Microsoft.Maui.Controls.BindableProperty
- uid: DrawnUi.Controls.MauiEntry.MaxLines*
  commentId: Overload:DrawnUi.Controls.MauiEntry.MaxLines
  href: DrawnUi.Controls.MauiEntry.html#DrawnUi_Controls_MauiEntry_MaxLines
  name: MaxLines
  nameWithType: MauiEntry.MaxLines
  fullName: DrawnUi.Controls.MauiEntry.MaxLines
- uid: System.Int32
  commentId: T:System.Int32
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.int32
  name: int
  nameWithType: int
  fullName: int
  nameWithType.vb: Integer
  fullName.vb: Integer
  name.vb: Integer
- uid: System.EventHandler
  commentId: T:System.EventHandler
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.eventhandler
  name: EventHandler
  nameWithType: EventHandler
  fullName: System.EventHandler
- uid: DrawnUi.Controls.MauiEntry.Completed*
  commentId: Overload:DrawnUi.Controls.MauiEntry.Completed
  href: DrawnUi.Controls.MauiEntry.html#DrawnUi_Controls_MauiEntry_Completed
  name: Completed
  nameWithType: MauiEntry.Completed
  fullName: DrawnUi.Controls.MauiEntry.Completed
- uid: Microsoft.Maui.IEditor.Completed
  commentId: M:Microsoft.Maui.IEditor.Completed
  parent: Microsoft.Maui.IEditor
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.ieditor.completed
  name: Completed()
  nameWithType: IEditor.Completed()
  fullName: Microsoft.Maui.IEditor.Completed()
  spec.csharp:
  - uid: Microsoft.Maui.IEditor.Completed
    name: Completed
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.ieditor.completed
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.IEditor.Completed
    name: Completed
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.ieditor.completed
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.Element.OnHandlerChanging(Microsoft.Maui.Controls.HandlerChangingEventArgs)
  commentId: M:Microsoft.Maui.Controls.Element.OnHandlerChanging(Microsoft.Maui.Controls.HandlerChangingEventArgs)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onhandlerchanging
  name: OnHandlerChanging(HandlerChangingEventArgs)
  nameWithType: Element.OnHandlerChanging(HandlerChangingEventArgs)
  fullName: Microsoft.Maui.Controls.Element.OnHandlerChanging(Microsoft.Maui.Controls.HandlerChangingEventArgs)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.OnHandlerChanging(Microsoft.Maui.Controls.HandlerChangingEventArgs)
    name: OnHandlerChanging
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onhandlerchanging
  - name: (
  - uid: Microsoft.Maui.Controls.HandlerChangingEventArgs
    name: HandlerChangingEventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.handlerchangingeventargs
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.OnHandlerChanging(Microsoft.Maui.Controls.HandlerChangingEventArgs)
    name: OnHandlerChanging
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onhandlerchanging
  - name: (
  - uid: Microsoft.Maui.Controls.HandlerChangingEventArgs
    name: HandlerChangingEventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.handlerchangingeventargs
  - name: )
- uid: DrawnUi.Controls.MauiEntry.OnHandlerChanging*
  commentId: Overload:DrawnUi.Controls.MauiEntry.OnHandlerChanging
  href: DrawnUi.Controls.MauiEntry.html#DrawnUi_Controls_MauiEntry_OnHandlerChanging_Microsoft_Maui_Controls_HandlerChangingEventArgs_
  name: OnHandlerChanging
  nameWithType: MauiEntry.OnHandlerChanging
  fullName: DrawnUi.Controls.MauiEntry.OnHandlerChanging
- uid: Microsoft.Maui.Controls.HandlerChangingEventArgs
  commentId: T:Microsoft.Maui.Controls.HandlerChangingEventArgs
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.handlerchangingeventargs
  name: HandlerChangingEventArgs
  nameWithType: HandlerChangingEventArgs
  fullName: Microsoft.Maui.Controls.HandlerChangingEventArgs
