### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.IOverlayEffect
  commentId: T:DrawnUi.Draw.IOverlayEffect
  id: IOverlayEffect
  parent: DrawnUi.Draw
  children: []
  langs:
  - csharp
  - vb
  name: IOverlayEffect
  nameWithType: IOverlayEffect
  fullName: DrawnUi.Draw.IOverlayEffect
  type: Interface
  source:
    remote:
      path: src/Shared/Features/Animations/Interfaces/ICanRenderOnCanvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IOverlayEffect
    path: ../src/Shared/Features/Animations/Interfaces/ICanRenderOnCanvas.cs
    startLine: 14
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: 'public interface IOverlayEffect : ICanRenderOnCanvas, ISkiaAnimator, IDisposable'
    content.vb: Public Interface IOverlayEffect Inherits ICanRenderOnCanvas, ISkiaAnimator, IDisposable
  inheritedMembers:
  - DrawnUi.Draw.ICanRenderOnCanvas.Render(DrawnUi.Draw.DrawingContext,DrawnUi.Draw.IDrawnBase)
  - DrawnUi.Draw.ISkiaAnimator.TickFrame(System.Int64)
  - DrawnUi.Draw.ISkiaAnimator.IsRunning
  - DrawnUi.Draw.ISkiaAnimator.WasStarted
  - DrawnUi.Draw.ISkiaAnimator.Uid
  - DrawnUi.Draw.ISkiaAnimator.Stop
  - DrawnUi.Draw.ISkiaAnimator.Pause
  - DrawnUi.Draw.ISkiaAnimator.Resume
  - DrawnUi.Draw.ISkiaAnimator.Start(System.Double)
  - DrawnUi.Draw.ISkiaAnimator.Parent
  - DrawnUi.Draw.ISkiaAnimator.IsDeactivated
  - DrawnUi.Draw.ISkiaAnimator.IsPaused
  - DrawnUi.Draw.ISkiaAnimator.IsHiddenInViewTree
  - System.IDisposable.Dispose
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: DrawnUi.Draw.ICanRenderOnCanvas.Render(DrawnUi.Draw.DrawingContext,DrawnUi.Draw.IDrawnBase)
  commentId: M:DrawnUi.Draw.ICanRenderOnCanvas.Render(DrawnUi.Draw.DrawingContext,DrawnUi.Draw.IDrawnBase)
  parent: DrawnUi.Draw.ICanRenderOnCanvas
  href: DrawnUi.Draw.ICanRenderOnCanvas.html#DrawnUi_Draw_ICanRenderOnCanvas_Render_DrawnUi_Draw_DrawingContext_DrawnUi_Draw_IDrawnBase_
  name: Render(DrawingContext, IDrawnBase)
  nameWithType: ICanRenderOnCanvas.Render(DrawingContext, IDrawnBase)
  fullName: DrawnUi.Draw.ICanRenderOnCanvas.Render(DrawnUi.Draw.DrawingContext, DrawnUi.Draw.IDrawnBase)
  spec.csharp:
  - uid: DrawnUi.Draw.ICanRenderOnCanvas.Render(DrawnUi.Draw.DrawingContext,DrawnUi.Draw.IDrawnBase)
    name: Render
    href: DrawnUi.Draw.ICanRenderOnCanvas.html#DrawnUi_Draw_ICanRenderOnCanvas_Render_DrawnUi_Draw_DrawingContext_DrawnUi_Draw_IDrawnBase_
  - name: (
  - uid: DrawnUi.Draw.DrawingContext
    name: DrawingContext
    href: DrawnUi.Draw.DrawingContext.html
  - name: ','
  - name: " "
  - uid: DrawnUi.Draw.IDrawnBase
    name: IDrawnBase
    href: DrawnUi.Draw.IDrawnBase.html
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.ICanRenderOnCanvas.Render(DrawnUi.Draw.DrawingContext,DrawnUi.Draw.IDrawnBase)
    name: Render
    href: DrawnUi.Draw.ICanRenderOnCanvas.html#DrawnUi_Draw_ICanRenderOnCanvas_Render_DrawnUi_Draw_DrawingContext_DrawnUi_Draw_IDrawnBase_
  - name: (
  - uid: DrawnUi.Draw.DrawingContext
    name: DrawingContext
    href: DrawnUi.Draw.DrawingContext.html
  - name: ','
  - name: " "
  - uid: DrawnUi.Draw.IDrawnBase
    name: IDrawnBase
    href: DrawnUi.Draw.IDrawnBase.html
  - name: )
- uid: DrawnUi.Draw.ISkiaAnimator.TickFrame(System.Int64)
  commentId: M:DrawnUi.Draw.ISkiaAnimator.TickFrame(System.Int64)
  parent: DrawnUi.Draw.ISkiaAnimator
  isExternal: true
  href: DrawnUi.Draw.ISkiaAnimator.html#DrawnUi_Draw_ISkiaAnimator_TickFrame_System_Int64_
  name: TickFrame(long)
  nameWithType: ISkiaAnimator.TickFrame(long)
  fullName: DrawnUi.Draw.ISkiaAnimator.TickFrame(long)
  nameWithType.vb: ISkiaAnimator.TickFrame(Long)
  fullName.vb: DrawnUi.Draw.ISkiaAnimator.TickFrame(Long)
  name.vb: TickFrame(Long)
  spec.csharp:
  - uid: DrawnUi.Draw.ISkiaAnimator.TickFrame(System.Int64)
    name: TickFrame
    href: DrawnUi.Draw.ISkiaAnimator.html#DrawnUi_Draw_ISkiaAnimator_TickFrame_System_Int64_
  - name: (
  - uid: System.Int64
    name: long
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int64
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.ISkiaAnimator.TickFrame(System.Int64)
    name: TickFrame
    href: DrawnUi.Draw.ISkiaAnimator.html#DrawnUi_Draw_ISkiaAnimator_TickFrame_System_Int64_
  - name: (
  - uid: System.Int64
    name: Long
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int64
  - name: )
- uid: DrawnUi.Draw.ISkiaAnimator.IsRunning
  commentId: P:DrawnUi.Draw.ISkiaAnimator.IsRunning
  parent: DrawnUi.Draw.ISkiaAnimator
  href: DrawnUi.Draw.ISkiaAnimator.html#DrawnUi_Draw_ISkiaAnimator_IsRunning
  name: IsRunning
  nameWithType: ISkiaAnimator.IsRunning
  fullName: DrawnUi.Draw.ISkiaAnimator.IsRunning
- uid: DrawnUi.Draw.ISkiaAnimator.WasStarted
  commentId: P:DrawnUi.Draw.ISkiaAnimator.WasStarted
  parent: DrawnUi.Draw.ISkiaAnimator
  href: DrawnUi.Draw.ISkiaAnimator.html#DrawnUi_Draw_ISkiaAnimator_WasStarted
  name: WasStarted
  nameWithType: ISkiaAnimator.WasStarted
  fullName: DrawnUi.Draw.ISkiaAnimator.WasStarted
- uid: DrawnUi.Draw.ISkiaAnimator.Uid
  commentId: P:DrawnUi.Draw.ISkiaAnimator.Uid
  parent: DrawnUi.Draw.ISkiaAnimator
  href: DrawnUi.Draw.ISkiaAnimator.html#DrawnUi_Draw_ISkiaAnimator_Uid
  name: Uid
  nameWithType: ISkiaAnimator.Uid
  fullName: DrawnUi.Draw.ISkiaAnimator.Uid
- uid: DrawnUi.Draw.ISkiaAnimator.Stop
  commentId: M:DrawnUi.Draw.ISkiaAnimator.Stop
  parent: DrawnUi.Draw.ISkiaAnimator
  href: DrawnUi.Draw.ISkiaAnimator.html#DrawnUi_Draw_ISkiaAnimator_Stop
  name: Stop()
  nameWithType: ISkiaAnimator.Stop()
  fullName: DrawnUi.Draw.ISkiaAnimator.Stop()
  spec.csharp:
  - uid: DrawnUi.Draw.ISkiaAnimator.Stop
    name: Stop
    href: DrawnUi.Draw.ISkiaAnimator.html#DrawnUi_Draw_ISkiaAnimator_Stop
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.ISkiaAnimator.Stop
    name: Stop
    href: DrawnUi.Draw.ISkiaAnimator.html#DrawnUi_Draw_ISkiaAnimator_Stop
  - name: (
  - name: )
- uid: DrawnUi.Draw.ISkiaAnimator.Pause
  commentId: M:DrawnUi.Draw.ISkiaAnimator.Pause
  parent: DrawnUi.Draw.ISkiaAnimator
  href: DrawnUi.Draw.ISkiaAnimator.html#DrawnUi_Draw_ISkiaAnimator_Pause
  name: Pause()
  nameWithType: ISkiaAnimator.Pause()
  fullName: DrawnUi.Draw.ISkiaAnimator.Pause()
  spec.csharp:
  - uid: DrawnUi.Draw.ISkiaAnimator.Pause
    name: Pause
    href: DrawnUi.Draw.ISkiaAnimator.html#DrawnUi_Draw_ISkiaAnimator_Pause
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.ISkiaAnimator.Pause
    name: Pause
    href: DrawnUi.Draw.ISkiaAnimator.html#DrawnUi_Draw_ISkiaAnimator_Pause
  - name: (
  - name: )
- uid: DrawnUi.Draw.ISkiaAnimator.Resume
  commentId: M:DrawnUi.Draw.ISkiaAnimator.Resume
  parent: DrawnUi.Draw.ISkiaAnimator
  href: DrawnUi.Draw.ISkiaAnimator.html#DrawnUi_Draw_ISkiaAnimator_Resume
  name: Resume()
  nameWithType: ISkiaAnimator.Resume()
  fullName: DrawnUi.Draw.ISkiaAnimator.Resume()
  spec.csharp:
  - uid: DrawnUi.Draw.ISkiaAnimator.Resume
    name: Resume
    href: DrawnUi.Draw.ISkiaAnimator.html#DrawnUi_Draw_ISkiaAnimator_Resume
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.ISkiaAnimator.Resume
    name: Resume
    href: DrawnUi.Draw.ISkiaAnimator.html#DrawnUi_Draw_ISkiaAnimator_Resume
  - name: (
  - name: )
- uid: DrawnUi.Draw.ISkiaAnimator.Start(System.Double)
  commentId: M:DrawnUi.Draw.ISkiaAnimator.Start(System.Double)
  parent: DrawnUi.Draw.ISkiaAnimator
  isExternal: true
  href: DrawnUi.Draw.ISkiaAnimator.html#DrawnUi_Draw_ISkiaAnimator_Start_System_Double_
  name: Start(double)
  nameWithType: ISkiaAnimator.Start(double)
  fullName: DrawnUi.Draw.ISkiaAnimator.Start(double)
  nameWithType.vb: ISkiaAnimator.Start(Double)
  fullName.vb: DrawnUi.Draw.ISkiaAnimator.Start(Double)
  name.vb: Start(Double)
  spec.csharp:
  - uid: DrawnUi.Draw.ISkiaAnimator.Start(System.Double)
    name: Start
    href: DrawnUi.Draw.ISkiaAnimator.html#DrawnUi_Draw_ISkiaAnimator_Start_System_Double_
  - name: (
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.ISkiaAnimator.Start(System.Double)
    name: Start
    href: DrawnUi.Draw.ISkiaAnimator.html#DrawnUi_Draw_ISkiaAnimator_Start_System_Double_
  - name: (
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
- uid: DrawnUi.Draw.ISkiaAnimator.Parent
  commentId: P:DrawnUi.Draw.ISkiaAnimator.Parent
  parent: DrawnUi.Draw.ISkiaAnimator
  href: DrawnUi.Draw.ISkiaAnimator.html#DrawnUi_Draw_ISkiaAnimator_Parent
  name: Parent
  nameWithType: ISkiaAnimator.Parent
  fullName: DrawnUi.Draw.ISkiaAnimator.Parent
- uid: DrawnUi.Draw.ISkiaAnimator.IsDeactivated
  commentId: P:DrawnUi.Draw.ISkiaAnimator.IsDeactivated
  parent: DrawnUi.Draw.ISkiaAnimator
  href: DrawnUi.Draw.ISkiaAnimator.html#DrawnUi_Draw_ISkiaAnimator_IsDeactivated
  name: IsDeactivated
  nameWithType: ISkiaAnimator.IsDeactivated
  fullName: DrawnUi.Draw.ISkiaAnimator.IsDeactivated
- uid: DrawnUi.Draw.ISkiaAnimator.IsPaused
  commentId: P:DrawnUi.Draw.ISkiaAnimator.IsPaused
  parent: DrawnUi.Draw.ISkiaAnimator
  href: DrawnUi.Draw.ISkiaAnimator.html#DrawnUi_Draw_ISkiaAnimator_IsPaused
  name: IsPaused
  nameWithType: ISkiaAnimator.IsPaused
  fullName: DrawnUi.Draw.ISkiaAnimator.IsPaused
- uid: DrawnUi.Draw.ISkiaAnimator.IsHiddenInViewTree
  commentId: P:DrawnUi.Draw.ISkiaAnimator.IsHiddenInViewTree
  parent: DrawnUi.Draw.ISkiaAnimator
  href: DrawnUi.Draw.ISkiaAnimator.html#DrawnUi_Draw_ISkiaAnimator_IsHiddenInViewTree
  name: IsHiddenInViewTree
  nameWithType: ISkiaAnimator.IsHiddenInViewTree
  fullName: DrawnUi.Draw.ISkiaAnimator.IsHiddenInViewTree
- uid: System.IDisposable.Dispose
  commentId: M:System.IDisposable.Dispose
  parent: System.IDisposable
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.idisposable.dispose
  name: Dispose()
  nameWithType: IDisposable.Dispose()
  fullName: System.IDisposable.Dispose()
  spec.csharp:
  - uid: System.IDisposable.Dispose
    name: Dispose
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.idisposable.dispose
  - name: (
  - name: )
  spec.vb:
  - uid: System.IDisposable.Dispose
    name: Dispose
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.idisposable.dispose
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Draw.ICanRenderOnCanvas
  commentId: T:DrawnUi.Draw.ICanRenderOnCanvas
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ICanRenderOnCanvas.html
  name: ICanRenderOnCanvas
  nameWithType: ICanRenderOnCanvas
  fullName: DrawnUi.Draw.ICanRenderOnCanvas
- uid: DrawnUi.Draw.ISkiaAnimator
  commentId: T:DrawnUi.Draw.ISkiaAnimator
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ISkiaAnimator.html
  name: ISkiaAnimator
  nameWithType: ISkiaAnimator
  fullName: DrawnUi.Draw.ISkiaAnimator
- uid: System.IDisposable
  commentId: T:System.IDisposable
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.idisposable
  name: IDisposable
  nameWithType: IDisposable
  fullName: System.IDisposable
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
