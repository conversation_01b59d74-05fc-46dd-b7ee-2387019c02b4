### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.Sk3dView
  commentId: T:DrawnUi.Draw.Sk3dView
  id: Sk3dView
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.Sk3dView.#ctor
  - DrawnUi.Draw.Sk3dView.ApplyToCanvas(SkiaSharp.SKCanvas)
  - DrawnUi.Draw.Sk3dView.CameraDistance
  - DrawnUi.Draw.Sk3dView.Invalidated
  - DrawnUi.Draw.Sk3dView.Matrix
  - DrawnUi.Draw.Sk3dView.OnReset
  - DrawnUi.Draw.Sk3dView.OnRestore
  - DrawnUi.Draw.Sk3dView.Reset
  - DrawnUi.Draw.Sk3dView.Restore
  - DrawnUi.Draw.Sk3dView.RotateXDegrees(System.Single)
  - DrawnUi.Draw.Sk3dView.RotateYDegrees(System.Single)
  - DrawnUi.Draw.Sk3dView.RotateZDegrees(System.Single)
  - DrawnUi.Draw.Sk3dView.Save
  - DrawnUi.Draw.Sk3dView.Translate(System.Single,System.Single,System.Single)
  - DrawnUi.Draw.Sk3dView.TranslateX(System.Single)
  - DrawnUi.Draw.Sk3dView.TranslateY(System.Single)
  - DrawnUi.Draw.Sk3dView.TranslateZ(System.Single)
  langs:
  - csharp
  - vb
  name: Sk3dView
  nameWithType: Sk3dView
  fullName: DrawnUi.Draw.Sk3dView
  type: Class
  source:
    remote:
      path: src/Shared/Internals/Helpers/3D/Sk3dView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Sk3dView
    path: ../src/Shared/Internals/Helpers/3D/Sk3dView.cs
    startLine: 9
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Custom implementation of Android's Camera 3D helper for SkiaSharp
  example: []
  syntax:
    content: public class Sk3dView
    content.vb: Public Class Sk3dView
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.Sk3dView.#ctor
  commentId: M:DrawnUi.Draw.Sk3dView.#ctor
  id: '#ctor'
  parent: DrawnUi.Draw.Sk3dView
  langs:
  - csharp
  - vb
  name: Sk3dView()
  nameWithType: Sk3dView.Sk3dView()
  fullName: DrawnUi.Draw.Sk3dView.Sk3dView()
  type: Constructor
  source:
    remote:
      path: src/Shared/Internals/Helpers/3D/Sk3dView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Shared/Internals/Helpers/3D/Sk3dView.cs
    startLine: 19
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public Sk3dView()
    content.vb: Public Sub New()
  overload: DrawnUi.Draw.Sk3dView.#ctor*
  nameWithType.vb: Sk3dView.New()
  fullName.vb: DrawnUi.Draw.Sk3dView.New()
  name.vb: New()
- uid: DrawnUi.Draw.Sk3dView.Invalidated
  commentId: F:DrawnUi.Draw.Sk3dView.Invalidated
  id: Invalidated
  parent: DrawnUi.Draw.Sk3dView
  langs:
  - csharp
  - vb
  name: Invalidated
  nameWithType: Sk3dView.Invalidated
  fullName: DrawnUi.Draw.Sk3dView.Invalidated
  type: Field
  source:
    remote:
      path: src/Shared/Internals/Helpers/3D/Sk3dView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Invalidated
    path: ../src/Shared/Internals/Helpers/3D/Sk3dView.cs
    startLine: 24
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: protected bool Invalidated
    return:
      type: System.Boolean
    content.vb: Protected Invalidated As Boolean
- uid: DrawnUi.Draw.Sk3dView.CameraDistance
  commentId: F:DrawnUi.Draw.Sk3dView.CameraDistance
  id: CameraDistance
  parent: DrawnUi.Draw.Sk3dView
  langs:
  - csharp
  - vb
  name: CameraDistance
  nameWithType: Sk3dView.CameraDistance
  fullName: DrawnUi.Draw.Sk3dView.CameraDistance
  type: Field
  source:
    remote:
      path: src/Shared/Internals/Helpers/3D/Sk3dView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: CameraDistance
    path: ../src/Shared/Internals/Helpers/3D/Sk3dView.cs
    startLine: 33
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: 3D camera distance (8 inches in pixels, similar to Android implementation)
  example: []
  syntax:
    content: public float CameraDistance
    return:
      type: System.Single
    content.vb: Public CameraDistance As Single
- uid: DrawnUi.Draw.Sk3dView.Save
  commentId: M:DrawnUi.Draw.Sk3dView.Save
  id: Save
  parent: DrawnUi.Draw.Sk3dView
  langs:
  - csharp
  - vb
  name: Save()
  nameWithType: Sk3dView.Save()
  fullName: DrawnUi.Draw.Sk3dView.Save()
  type: Method
  source:
    remote:
      path: src/Shared/Internals/Helpers/3D/Sk3dView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Save
    path: ../src/Shared/Internals/Helpers/3D/Sk3dView.cs
    startLine: 38
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Saves the current transformation state
  example: []
  syntax:
    content: public void Save()
    content.vb: Public Sub Save()
  overload: DrawnUi.Draw.Sk3dView.Save*
- uid: DrawnUi.Draw.Sk3dView.Restore
  commentId: M:DrawnUi.Draw.Sk3dView.Restore
  id: Restore
  parent: DrawnUi.Draw.Sk3dView
  langs:
  - csharp
  - vb
  name: Restore()
  nameWithType: Sk3dView.Restore()
  fullName: DrawnUi.Draw.Sk3dView.Restore()
  type: Method
  source:
    remote:
      path: src/Shared/Internals/Helpers/3D/Sk3dView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Restore
    path: ../src/Shared/Internals/Helpers/3D/Sk3dView.cs
    startLine: 55
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Restores the previously saved transformation state
  example: []
  syntax:
    content: public void Restore()
    content.vb: Public Sub Restore()
  overload: DrawnUi.Draw.Sk3dView.Restore*
- uid: DrawnUi.Draw.Sk3dView.ApplyToCanvas(SkiaSharp.SKCanvas)
  commentId: M:DrawnUi.Draw.Sk3dView.ApplyToCanvas(SkiaSharp.SKCanvas)
  id: ApplyToCanvas(SkiaSharp.SKCanvas)
  parent: DrawnUi.Draw.Sk3dView
  langs:
  - csharp
  - vb
  name: ApplyToCanvas(SKCanvas)
  nameWithType: Sk3dView.ApplyToCanvas(SKCanvas)
  fullName: DrawnUi.Draw.Sk3dView.ApplyToCanvas(SkiaSharp.SKCanvas)
  type: Method
  source:
    remote:
      path: src/Shared/Internals/Helpers/3D/Sk3dView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ApplyToCanvas
    path: ../src/Shared/Internals/Helpers/3D/Sk3dView.cs
    startLine: 77
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Applies the current 3D transformation to the canvas
  example: []
  syntax:
    content: public void ApplyToCanvas(SKCanvas canvas)
    parameters:
    - id: canvas
      type: SkiaSharp.SKCanvas
    content.vb: Public Sub ApplyToCanvas(canvas As SKCanvas)
  overload: DrawnUi.Draw.Sk3dView.ApplyToCanvas*
- uid: DrawnUi.Draw.Sk3dView.Reset
  commentId: M:DrawnUi.Draw.Sk3dView.Reset
  id: Reset
  parent: DrawnUi.Draw.Sk3dView
  langs:
  - csharp
  - vb
  name: Reset()
  nameWithType: Sk3dView.Reset()
  fullName: DrawnUi.Draw.Sk3dView.Reset()
  type: Method
  source:
    remote:
      path: src/Shared/Internals/Helpers/3D/Sk3dView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Reset
    path: ../src/Shared/Internals/Helpers/3D/Sk3dView.cs
    startLine: 88
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Resets the current state and clears all saved states
  example: []
  syntax:
    content: public void Reset()
    content.vb: Public Sub Reset()
  overload: DrawnUi.Draw.Sk3dView.Reset*
- uid: DrawnUi.Draw.Sk3dView.OnReset
  commentId: M:DrawnUi.Draw.Sk3dView.OnReset
  id: OnReset
  parent: DrawnUi.Draw.Sk3dView
  langs:
  - csharp
  - vb
  name: OnReset()
  nameWithType: Sk3dView.OnReset()
  fullName: DrawnUi.Draw.Sk3dView.OnReset()
  type: Method
  source:
    remote:
      path: src/Shared/Internals/Helpers/3D/Sk3dView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: OnReset
    path: ../src/Shared/Internals/Helpers/3D/Sk3dView.cs
    startLine: 106
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: protected virtual void OnReset()
    content.vb: Protected Overridable Sub OnReset()
  overload: DrawnUi.Draw.Sk3dView.OnReset*
- uid: DrawnUi.Draw.Sk3dView.OnRestore
  commentId: M:DrawnUi.Draw.Sk3dView.OnRestore
  id: OnRestore
  parent: DrawnUi.Draw.Sk3dView
  langs:
  - csharp
  - vb
  name: OnRestore()
  nameWithType: Sk3dView.OnRestore()
  fullName: DrawnUi.Draw.Sk3dView.OnRestore()
  type: Method
  source:
    remote:
      path: src/Shared/Internals/Helpers/3D/Sk3dView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: OnRestore
    path: ../src/Shared/Internals/Helpers/3D/Sk3dView.cs
    startLine: 108
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: protected virtual void OnRestore()
    content.vb: Protected Overridable Sub OnRestore()
  overload: DrawnUi.Draw.Sk3dView.OnRestore*
- uid: DrawnUi.Draw.Sk3dView.RotateXDegrees(System.Single)
  commentId: M:DrawnUi.Draw.Sk3dView.RotateXDegrees(System.Single)
  id: RotateXDegrees(System.Single)
  parent: DrawnUi.Draw.Sk3dView
  langs:
  - csharp
  - vb
  name: RotateXDegrees(float)
  nameWithType: Sk3dView.RotateXDegrees(float)
  fullName: DrawnUi.Draw.Sk3dView.RotateXDegrees(float)
  type: Method
  source:
    remote:
      path: src/Shared/Internals/Helpers/3D/Sk3dView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: RotateXDegrees
    path: ../src/Shared/Internals/Helpers/3D/Sk3dView.cs
    startLine: 113
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Rotates around the X axis
  example: []
  syntax:
    content: public void RotateXDegrees(float degrees)
    parameters:
    - id: degrees
      type: System.Single
    content.vb: Public Sub RotateXDegrees(degrees As Single)
  overload: DrawnUi.Draw.Sk3dView.RotateXDegrees*
  nameWithType.vb: Sk3dView.RotateXDegrees(Single)
  fullName.vb: DrawnUi.Draw.Sk3dView.RotateXDegrees(Single)
  name.vb: RotateXDegrees(Single)
- uid: DrawnUi.Draw.Sk3dView.RotateYDegrees(System.Single)
  commentId: M:DrawnUi.Draw.Sk3dView.RotateYDegrees(System.Single)
  id: RotateYDegrees(System.Single)
  parent: DrawnUi.Draw.Sk3dView
  langs:
  - csharp
  - vb
  name: RotateYDegrees(float)
  nameWithType: Sk3dView.RotateYDegrees(float)
  fullName: DrawnUi.Draw.Sk3dView.RotateYDegrees(float)
  type: Method
  source:
    remote:
      path: src/Shared/Internals/Helpers/3D/Sk3dView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: RotateYDegrees
    path: ../src/Shared/Internals/Helpers/3D/Sk3dView.cs
    startLine: 122
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Rotates around the Y axis
  example: []
  syntax:
    content: public void RotateYDegrees(float degrees)
    parameters:
    - id: degrees
      type: System.Single
    content.vb: Public Sub RotateYDegrees(degrees As Single)
  overload: DrawnUi.Draw.Sk3dView.RotateYDegrees*
  nameWithType.vb: Sk3dView.RotateYDegrees(Single)
  fullName.vb: DrawnUi.Draw.Sk3dView.RotateYDegrees(Single)
  name.vb: RotateYDegrees(Single)
- uid: DrawnUi.Draw.Sk3dView.RotateZDegrees(System.Single)
  commentId: M:DrawnUi.Draw.Sk3dView.RotateZDegrees(System.Single)
  id: RotateZDegrees(System.Single)
  parent: DrawnUi.Draw.Sk3dView
  langs:
  - csharp
  - vb
  name: RotateZDegrees(float)
  nameWithType: Sk3dView.RotateZDegrees(float)
  fullName: DrawnUi.Draw.Sk3dView.RotateZDegrees(float)
  type: Method
  source:
    remote:
      path: src/Shared/Internals/Helpers/3D/Sk3dView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: RotateZDegrees
    path: ../src/Shared/Internals/Helpers/3D/Sk3dView.cs
    startLine: 131
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Rotates around the Z axis
  example: []
  syntax:
    content: public void RotateZDegrees(float degrees)
    parameters:
    - id: degrees
      type: System.Single
    content.vb: Public Sub RotateZDegrees(degrees As Single)
  overload: DrawnUi.Draw.Sk3dView.RotateZDegrees*
  nameWithType.vb: Sk3dView.RotateZDegrees(Single)
  fullName.vb: DrawnUi.Draw.Sk3dView.RotateZDegrees(Single)
  name.vb: RotateZDegrees(Single)
- uid: DrawnUi.Draw.Sk3dView.TranslateX(System.Single)
  commentId: M:DrawnUi.Draw.Sk3dView.TranslateX(System.Single)
  id: TranslateX(System.Single)
  parent: DrawnUi.Draw.Sk3dView
  langs:
  - csharp
  - vb
  name: TranslateX(float)
  nameWithType: Sk3dView.TranslateX(float)
  fullName: DrawnUi.Draw.Sk3dView.TranslateX(float)
  type: Method
  source:
    remote:
      path: src/Shared/Internals/Helpers/3D/Sk3dView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: TranslateX
    path: ../src/Shared/Internals/Helpers/3D/Sk3dView.cs
    startLine: 140
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Translates along the X axis
  example: []
  syntax:
    content: public void TranslateX(float value)
    parameters:
    - id: value
      type: System.Single
    content.vb: Public Sub TranslateX(value As Single)
  overload: DrawnUi.Draw.Sk3dView.TranslateX*
  nameWithType.vb: Sk3dView.TranslateX(Single)
  fullName.vb: DrawnUi.Draw.Sk3dView.TranslateX(Single)
  name.vb: TranslateX(Single)
- uid: DrawnUi.Draw.Sk3dView.TranslateY(System.Single)
  commentId: M:DrawnUi.Draw.Sk3dView.TranslateY(System.Single)
  id: TranslateY(System.Single)
  parent: DrawnUi.Draw.Sk3dView
  langs:
  - csharp
  - vb
  name: TranslateY(float)
  nameWithType: Sk3dView.TranslateY(float)
  fullName: DrawnUi.Draw.Sk3dView.TranslateY(float)
  type: Method
  source:
    remote:
      path: src/Shared/Internals/Helpers/3D/Sk3dView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: TranslateY
    path: ../src/Shared/Internals/Helpers/3D/Sk3dView.cs
    startLine: 149
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Translates along the Y axis
  example: []
  syntax:
    content: public void TranslateY(float value)
    parameters:
    - id: value
      type: System.Single
    content.vb: Public Sub TranslateY(value As Single)
  overload: DrawnUi.Draw.Sk3dView.TranslateY*
  nameWithType.vb: Sk3dView.TranslateY(Single)
  fullName.vb: DrawnUi.Draw.Sk3dView.TranslateY(Single)
  name.vb: TranslateY(Single)
- uid: DrawnUi.Draw.Sk3dView.TranslateZ(System.Single)
  commentId: M:DrawnUi.Draw.Sk3dView.TranslateZ(System.Single)
  id: TranslateZ(System.Single)
  parent: DrawnUi.Draw.Sk3dView
  langs:
  - csharp
  - vb
  name: TranslateZ(float)
  nameWithType: Sk3dView.TranslateZ(float)
  fullName: DrawnUi.Draw.Sk3dView.TranslateZ(float)
  type: Method
  source:
    remote:
      path: src/Shared/Internals/Helpers/3D/Sk3dView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: TranslateZ
    path: ../src/Shared/Internals/Helpers/3D/Sk3dView.cs
    startLine: 158
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Translates along the Z axis
  example: []
  syntax:
    content: public void TranslateZ(float value)
    parameters:
    - id: value
      type: System.Single
    content.vb: Public Sub TranslateZ(value As Single)
  overload: DrawnUi.Draw.Sk3dView.TranslateZ*
  nameWithType.vb: Sk3dView.TranslateZ(Single)
  fullName.vb: DrawnUi.Draw.Sk3dView.TranslateZ(Single)
  name.vb: TranslateZ(Single)
- uid: DrawnUi.Draw.Sk3dView.Translate(System.Single,System.Single,System.Single)
  commentId: M:DrawnUi.Draw.Sk3dView.Translate(System.Single,System.Single,System.Single)
  id: Translate(System.Single,System.Single,System.Single)
  parent: DrawnUi.Draw.Sk3dView
  langs:
  - csharp
  - vb
  name: Translate(float, float, float)
  nameWithType: Sk3dView.Translate(float, float, float)
  fullName: DrawnUi.Draw.Sk3dView.Translate(float, float, float)
  type: Method
  source:
    remote:
      path: src/Shared/Internals/Helpers/3D/Sk3dView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Translate
    path: ../src/Shared/Internals/Helpers/3D/Sk3dView.cs
    startLine: 167
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Translates along all axes
  example: []
  syntax:
    content: public void Translate(float x, float y, float z)
    parameters:
    - id: x
      type: System.Single
    - id: y
      type: System.Single
    - id: z
      type: System.Single
    content.vb: Public Sub Translate(x As Single, y As Single, z As Single)
  overload: DrawnUi.Draw.Sk3dView.Translate*
  nameWithType.vb: Sk3dView.Translate(Single, Single, Single)
  fullName.vb: DrawnUi.Draw.Sk3dView.Translate(Single, Single, Single)
  name.vb: Translate(Single, Single, Single)
- uid: DrawnUi.Draw.Sk3dView.Matrix
  commentId: P:DrawnUi.Draw.Sk3dView.Matrix
  id: Matrix
  parent: DrawnUi.Draw.Sk3dView
  langs:
  - csharp
  - vb
  name: Matrix
  nameWithType: Sk3dView.Matrix
  fullName: DrawnUi.Draw.Sk3dView.Matrix
  type: Property
  source:
    remote:
      path: src/Shared/Internals/Helpers/3D/Sk3dView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Matrix
    path: ../src/Shared/Internals/Helpers/3D/Sk3dView.cs
    startLine: 178
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Gets the current transformation matrix
  example: []
  syntax:
    content: public SKMatrix Matrix { get; }
    parameters: []
    return:
      type: SkiaSharp.SKMatrix
    content.vb: Public ReadOnly Property Matrix As SKMatrix
  overload: DrawnUi.Draw.Sk3dView.Matrix*
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.Sk3dView.#ctor*
  commentId: Overload:DrawnUi.Draw.Sk3dView.#ctor
  href: DrawnUi.Draw.Sk3dView.html#DrawnUi_Draw_Sk3dView__ctor
  name: Sk3dView
  nameWithType: Sk3dView.Sk3dView
  fullName: DrawnUi.Draw.Sk3dView.Sk3dView
  nameWithType.vb: Sk3dView.New
  fullName.vb: DrawnUi.Draw.Sk3dView.New
  name.vb: New
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: System.Single
  commentId: T:System.Single
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.single
  name: float
  nameWithType: float
  fullName: float
  nameWithType.vb: Single
  fullName.vb: Single
  name.vb: Single
- uid: DrawnUi.Draw.Sk3dView.Save*
  commentId: Overload:DrawnUi.Draw.Sk3dView.Save
  href: DrawnUi.Draw.Sk3dView.html#DrawnUi_Draw_Sk3dView_Save
  name: Save
  nameWithType: Sk3dView.Save
  fullName: DrawnUi.Draw.Sk3dView.Save
- uid: DrawnUi.Draw.Sk3dView.Restore*
  commentId: Overload:DrawnUi.Draw.Sk3dView.Restore
  href: DrawnUi.Draw.Sk3dView.html#DrawnUi_Draw_Sk3dView_Restore
  name: Restore
  nameWithType: Sk3dView.Restore
  fullName: DrawnUi.Draw.Sk3dView.Restore
- uid: DrawnUi.Draw.Sk3dView.ApplyToCanvas*
  commentId: Overload:DrawnUi.Draw.Sk3dView.ApplyToCanvas
  href: DrawnUi.Draw.Sk3dView.html#DrawnUi_Draw_Sk3dView_ApplyToCanvas_SkiaSharp_SKCanvas_
  name: ApplyToCanvas
  nameWithType: Sk3dView.ApplyToCanvas
  fullName: DrawnUi.Draw.Sk3dView.ApplyToCanvas
- uid: SkiaSharp.SKCanvas
  commentId: T:SkiaSharp.SKCanvas
  parent: SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.skcanvas
  name: SKCanvas
  nameWithType: SKCanvas
  fullName: SkiaSharp.SKCanvas
- uid: SkiaSharp
  commentId: N:SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp
  name: SkiaSharp
  nameWithType: SkiaSharp
  fullName: SkiaSharp
- uid: DrawnUi.Draw.Sk3dView.Reset*
  commentId: Overload:DrawnUi.Draw.Sk3dView.Reset
  href: DrawnUi.Draw.Sk3dView.html#DrawnUi_Draw_Sk3dView_Reset
  name: Reset
  nameWithType: Sk3dView.Reset
  fullName: DrawnUi.Draw.Sk3dView.Reset
- uid: DrawnUi.Draw.Sk3dView.OnReset*
  commentId: Overload:DrawnUi.Draw.Sk3dView.OnReset
  href: DrawnUi.Draw.Sk3dView.html#DrawnUi_Draw_Sk3dView_OnReset
  name: OnReset
  nameWithType: Sk3dView.OnReset
  fullName: DrawnUi.Draw.Sk3dView.OnReset
- uid: DrawnUi.Draw.Sk3dView.OnRestore*
  commentId: Overload:DrawnUi.Draw.Sk3dView.OnRestore
  href: DrawnUi.Draw.Sk3dView.html#DrawnUi_Draw_Sk3dView_OnRestore
  name: OnRestore
  nameWithType: Sk3dView.OnRestore
  fullName: DrawnUi.Draw.Sk3dView.OnRestore
- uid: DrawnUi.Draw.Sk3dView.RotateXDegrees*
  commentId: Overload:DrawnUi.Draw.Sk3dView.RotateXDegrees
  href: DrawnUi.Draw.Sk3dView.html#DrawnUi_Draw_Sk3dView_RotateXDegrees_System_Single_
  name: RotateXDegrees
  nameWithType: Sk3dView.RotateXDegrees
  fullName: DrawnUi.Draw.Sk3dView.RotateXDegrees
- uid: DrawnUi.Draw.Sk3dView.RotateYDegrees*
  commentId: Overload:DrawnUi.Draw.Sk3dView.RotateYDegrees
  href: DrawnUi.Draw.Sk3dView.html#DrawnUi_Draw_Sk3dView_RotateYDegrees_System_Single_
  name: RotateYDegrees
  nameWithType: Sk3dView.RotateYDegrees
  fullName: DrawnUi.Draw.Sk3dView.RotateYDegrees
- uid: DrawnUi.Draw.Sk3dView.RotateZDegrees*
  commentId: Overload:DrawnUi.Draw.Sk3dView.RotateZDegrees
  href: DrawnUi.Draw.Sk3dView.html#DrawnUi_Draw_Sk3dView_RotateZDegrees_System_Single_
  name: RotateZDegrees
  nameWithType: Sk3dView.RotateZDegrees
  fullName: DrawnUi.Draw.Sk3dView.RotateZDegrees
- uid: DrawnUi.Draw.Sk3dView.TranslateX*
  commentId: Overload:DrawnUi.Draw.Sk3dView.TranslateX
  href: DrawnUi.Draw.Sk3dView.html#DrawnUi_Draw_Sk3dView_TranslateX_System_Single_
  name: TranslateX
  nameWithType: Sk3dView.TranslateX
  fullName: DrawnUi.Draw.Sk3dView.TranslateX
- uid: DrawnUi.Draw.Sk3dView.TranslateY*
  commentId: Overload:DrawnUi.Draw.Sk3dView.TranslateY
  href: DrawnUi.Draw.Sk3dView.html#DrawnUi_Draw_Sk3dView_TranslateY_System_Single_
  name: TranslateY
  nameWithType: Sk3dView.TranslateY
  fullName: DrawnUi.Draw.Sk3dView.TranslateY
- uid: DrawnUi.Draw.Sk3dView.TranslateZ*
  commentId: Overload:DrawnUi.Draw.Sk3dView.TranslateZ
  href: DrawnUi.Draw.Sk3dView.html#DrawnUi_Draw_Sk3dView_TranslateZ_System_Single_
  name: TranslateZ
  nameWithType: Sk3dView.TranslateZ
  fullName: DrawnUi.Draw.Sk3dView.TranslateZ
- uid: DrawnUi.Draw.Sk3dView.Translate*
  commentId: Overload:DrawnUi.Draw.Sk3dView.Translate
  href: DrawnUi.Draw.Sk3dView.html#DrawnUi_Draw_Sk3dView_Translate_System_Single_System_Single_System_Single_
  name: Translate
  nameWithType: Sk3dView.Translate
  fullName: DrawnUi.Draw.Sk3dView.Translate
- uid: DrawnUi.Draw.Sk3dView.Matrix*
  commentId: Overload:DrawnUi.Draw.Sk3dView.Matrix
  href: DrawnUi.Draw.Sk3dView.html#DrawnUi_Draw_Sk3dView_Matrix
  name: Matrix
  nameWithType: Sk3dView.Matrix
  fullName: DrawnUi.Draw.Sk3dView.Matrix
- uid: SkiaSharp.SKMatrix
  commentId: T:SkiaSharp.SKMatrix
  parent: SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.skmatrix
  name: SKMatrix
  nameWithType: SKMatrix
  fullName: SkiaSharp.SKMatrix
