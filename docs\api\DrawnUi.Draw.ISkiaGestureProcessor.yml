### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.ISkiaGestureProcessor
  commentId: T:DrawnUi.Draw.ISkiaGestureProcessor
  id: ISkiaGestureProcessor
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.ISkiaGestureProcessor.ProcessGestures(DrawnUi.Draw.SkiaGesturesParameters,DrawnUi.Draw.GestureEventProcessingInfo)
  langs:
  - csharp
  - vb
  name: ISkiaGestureProcessor
  nameWithType: ISkiaGestureProcessor
  fullName: DrawnUi.Draw.ISkiaGestureProcessor
  type: Interface
  source:
    remote:
      path: src/Shared/Draw/Internals/Interfaces/ISkiaGestureProcessor.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ISkiaGestureProcessor
    path: ../src/Shared/Draw/Internals/Interfaces/ISkiaGestureProcessor.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public interface ISkiaGestureProcessor
    content.vb: Public Interface ISkiaGestureProcessor
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.ISkiaGestureProcessor.ProcessGestures(DrawnUi.Draw.SkiaGesturesParameters,DrawnUi.Draw.GestureEventProcessingInfo)
  commentId: M:DrawnUi.Draw.ISkiaGestureProcessor.ProcessGestures(DrawnUi.Draw.SkiaGesturesParameters,DrawnUi.Draw.GestureEventProcessingInfo)
  id: ProcessGestures(DrawnUi.Draw.SkiaGesturesParameters,DrawnUi.Draw.GestureEventProcessingInfo)
  parent: DrawnUi.Draw.ISkiaGestureProcessor
  langs:
  - csharp
  - vb
  name: ProcessGestures(SkiaGesturesParameters, GestureEventProcessingInfo)
  nameWithType: ISkiaGestureProcessor.ProcessGestures(SkiaGesturesParameters, GestureEventProcessingInfo)
  fullName: DrawnUi.Draw.ISkiaGestureProcessor.ProcessGestures(DrawnUi.Draw.SkiaGesturesParameters, DrawnUi.Draw.GestureEventProcessingInfo)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Interfaces/ISkiaGestureProcessor.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ProcessGestures
    path: ../src/Shared/Draw/Internals/Interfaces/ISkiaGestureProcessor.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: ISkiaGestureListener ProcessGestures(SkiaGesturesParameters args, GestureEventProcessingInfo apply)
    parameters:
    - id: args
      type: DrawnUi.Draw.SkiaGesturesParameters
    - id: apply
      type: DrawnUi.Draw.GestureEventProcessingInfo
    return:
      type: DrawnUi.Draw.ISkiaGestureListener
    content.vb: Function ProcessGestures(args As SkiaGesturesParameters, apply As GestureEventProcessingInfo) As ISkiaGestureListener
  overload: DrawnUi.Draw.ISkiaGestureProcessor.ProcessGestures*
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.ISkiaGestureProcessor.ProcessGestures*
  commentId: Overload:DrawnUi.Draw.ISkiaGestureProcessor.ProcessGestures
  href: DrawnUi.Draw.ISkiaGestureProcessor.html#DrawnUi_Draw_ISkiaGestureProcessor_ProcessGestures_DrawnUi_Draw_SkiaGesturesParameters_DrawnUi_Draw_GestureEventProcessingInfo_
  name: ProcessGestures
  nameWithType: ISkiaGestureProcessor.ProcessGestures
  fullName: DrawnUi.Draw.ISkiaGestureProcessor.ProcessGestures
- uid: DrawnUi.Draw.SkiaGesturesParameters
  commentId: T:DrawnUi.Draw.SkiaGesturesParameters
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaGesturesParameters.html
  name: SkiaGesturesParameters
  nameWithType: SkiaGesturesParameters
  fullName: DrawnUi.Draw.SkiaGesturesParameters
- uid: DrawnUi.Draw.GestureEventProcessingInfo
  commentId: T:DrawnUi.Draw.GestureEventProcessingInfo
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.GestureEventProcessingInfo.html
  name: GestureEventProcessingInfo
  nameWithType: GestureEventProcessingInfo
  fullName: DrawnUi.Draw.GestureEventProcessingInfo
- uid: DrawnUi.Draw.ISkiaGestureListener
  commentId: T:DrawnUi.Draw.ISkiaGestureListener
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ISkiaGestureListener.html
  name: ISkiaGestureListener
  nameWithType: ISkiaGestureListener
  fullName: DrawnUi.Draw.ISkiaGestureListener
