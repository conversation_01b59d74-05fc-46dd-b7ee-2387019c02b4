### YamlMime:ManagedReference
items:
- uid: DrawnUi.Controls.SkiaShell.IHandleGoBack
  commentId: T:DrawnUi.Controls.SkiaShell.IHandleGoBack
  id: SkiaShell.IHandleGoBack
  parent: DrawnUi.Controls
  children:
  - DrawnUi.Controls.SkiaShell.IHandleGoBack.OnShellGoBack(System.Boolean)
  langs:
  - csharp
  - vb
  name: SkiaShell.IHandleGoBack
  nameWithType: SkiaShell.IHandleGoBack
  fullName: DrawnUi.Controls.SkiaShell.IHandleGoBack
  type: Interface
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IHandleGoBack
    path: ../src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs
    startLine: 524
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: public interface SkiaShell.IHandleGoBack
    content.vb: Public Interface SkiaShell.IHandleGoBack
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Controls.SkiaShell.IHandleGoBack.OnShellGoBack(System.Boolean)
  commentId: M:DrawnUi.Controls.SkiaShell.IHandleGoBack.OnShellGoBack(System.Boolean)
  id: OnShellGoBack(System.Boolean)
  parent: DrawnUi.Controls.SkiaShell.IHandleGoBack
  langs:
  - csharp
  - vb
  name: OnShellGoBack(bool)
  nameWithType: SkiaShell.IHandleGoBack.OnShellGoBack(bool)
  fullName: DrawnUi.Controls.SkiaShell.IHandleGoBack.OnShellGoBack(bool)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: OnShellGoBack
    path: ../src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs
    startLine: 530
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  summary: Return true if comsumed, false will use default system behaivour.
  example: []
  syntax:
    content: bool OnShellGoBack(bool animate)
    parameters:
    - id: animate
      type: System.Boolean
    return:
      type: System.Boolean
      description: ''
    content.vb: Function OnShellGoBack(animate As Boolean) As Boolean
  overload: DrawnUi.Controls.SkiaShell.IHandleGoBack.OnShellGoBack*
  nameWithType.vb: SkiaShell.IHandleGoBack.OnShellGoBack(Boolean)
  fullName.vb: DrawnUi.Controls.SkiaShell.IHandleGoBack.OnShellGoBack(Boolean)
  name.vb: OnShellGoBack(Boolean)
references:
- uid: DrawnUi.Controls
  commentId: N:DrawnUi.Controls
  href: DrawnUi.html
  name: DrawnUi.Controls
  nameWithType: DrawnUi.Controls
  fullName: DrawnUi.Controls
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Controls
    name: Controls
    href: DrawnUi.Controls.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Controls
    name: Controls
    href: DrawnUi.Controls.html
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Controls.SkiaShell.IHandleGoBack.OnShellGoBack*
  commentId: Overload:DrawnUi.Controls.SkiaShell.IHandleGoBack.OnShellGoBack
  href: DrawnUi.Controls.SkiaShell.IHandleGoBack.html#DrawnUi_Controls_SkiaShell_IHandleGoBack_OnShellGoBack_System_Boolean_
  name: OnShellGoBack
  nameWithType: SkiaShell.IHandleGoBack.OnShellGoBack
  fullName: DrawnUi.Controls.SkiaShell.IHandleGoBack.OnShellGoBack
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
