### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.IRenderObject
  commentId: T:DrawnUi.Draw.IRenderObject
  id: IRenderObject
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.IRenderObject.UseRenderingObject(DrawnUi.Draw.RenderDrawingContext,SkiaSharp.SKRect,System.Single)
  langs:
  - csharp
  - vb
  name: IRenderObject
  nameWithType: IRenderObject
  fullName: DrawnUi.Draw.IRenderObject
  type: Interface
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IRenderObject
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
    startLine: 15
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public interface IRenderObject
    content.vb: Public Interface IRenderObject
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.IRenderObject.UseRenderingObject(DrawnUi.Draw.RenderDrawingContext,SkiaSharp.SKRect,System.Single)
  commentId: M:DrawnUi.Draw.IRenderObject.UseRenderingObject(DrawnUi.Draw.RenderDrawingContext,SkiaSharp.SKRect,System.Single)
  id: UseRenderingObject(DrawnUi.Draw.RenderDrawingContext,SkiaSharp.SKRect,System.Single)
  parent: DrawnUi.Draw.IRenderObject
  langs:
  - csharp
  - vb
  name: UseRenderingObject(RenderDrawingContext, SKRect, float)
  nameWithType: IRenderObject.UseRenderingObject(RenderDrawingContext, SKRect, float)
  fullName: DrawnUi.Draw.IRenderObject.UseRenderingObject(DrawnUi.Draw.RenderDrawingContext, SkiaSharp.SKRect, float)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: UseRenderingObject
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
    startLine: 17
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: bool UseRenderingObject(RenderDrawingContext context, SKRect destination, float scale)
    parameters:
    - id: context
      type: DrawnUi.Draw.RenderDrawingContext
    - id: destination
      type: SkiaSharp.SKRect
    - id: scale
      type: System.Single
    return:
      type: System.Boolean
    content.vb: Function UseRenderingObject(context As RenderDrawingContext, destination As SKRect, scale As Single) As Boolean
  overload: DrawnUi.Draw.IRenderObject.UseRenderingObject*
  nameWithType.vb: IRenderObject.UseRenderingObject(RenderDrawingContext, SKRect, Single)
  fullName.vb: DrawnUi.Draw.IRenderObject.UseRenderingObject(DrawnUi.Draw.RenderDrawingContext, SkiaSharp.SKRect, Single)
  name.vb: UseRenderingObject(RenderDrawingContext, SKRect, Single)
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.IRenderObject.UseRenderingObject*
  commentId: Overload:DrawnUi.Draw.IRenderObject.UseRenderingObject
  href: DrawnUi.Draw.IRenderObject.html#DrawnUi_Draw_IRenderObject_UseRenderingObject_DrawnUi_Draw_RenderDrawingContext_SkiaSharp_SKRect_System_Single_
  name: UseRenderingObject
  nameWithType: IRenderObject.UseRenderingObject
  fullName: DrawnUi.Draw.IRenderObject.UseRenderingObject
- uid: DrawnUi.Draw.RenderDrawingContext
  commentId: T:DrawnUi.Draw.RenderDrawingContext
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.RenderDrawingContext.html
  name: RenderDrawingContext
  nameWithType: RenderDrawingContext
  fullName: DrawnUi.Draw.RenderDrawingContext
- uid: SkiaSharp.SKRect
  commentId: T:SkiaSharp.SKRect
  parent: SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.skrect
  name: SKRect
  nameWithType: SKRect
  fullName: SkiaSharp.SKRect
- uid: System.Single
  commentId: T:System.Single
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.single
  name: float
  nameWithType: float
  fullName: float
  nameWithType.vb: Single
  fullName.vb: Single
  name.vb: Single
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: SkiaSharp
  commentId: N:SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp
  name: SkiaSharp
  nameWithType: SkiaSharp
  fullName: SkiaSharp
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
