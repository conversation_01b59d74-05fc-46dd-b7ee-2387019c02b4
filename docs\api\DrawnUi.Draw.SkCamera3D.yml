### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.SkCamera3D
  commentId: T:DrawnUi.Draw.SkCamera3D
  id: SkCamera3D
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.SkCamera3D.#ctor
  - DrawnUi.Draw.SkCamera3D.DoUpdate
  - DrawnUi.Draw.SkCamera3D.PatchToMatrix(DrawnUi.Draw.SkPatch3D)
  - DrawnUi.Draw.SkCamera3D.Reset
  - DrawnUi.Draw.SkCamera3D.Update
  - DrawnUi.Draw.SkCamera3D.fAxis
  - DrawnUi.Draw.SkCamera3D.fLocation
  - DrawnUi.Draw.SkCamera3D.fObserver
  - DrawnUi.Draw.SkCamera3D.fZenith
  langs:
  - csharp
  - vb
  name: SkCamera3D
  nameWithType: SkCamera3D
  fullName: DrawnUi.Draw.SkCamera3D
  type: Class
  source:
    remote:
      path: src/Shared/Internals/Helpers/3D/SkCamera3D.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: SkCamera3D
    path: ../src/Shared/Internals/Helpers/3D/SkCamera3D.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public class SkCamera3D
    content.vb: Public Class SkCamera3D
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.SkCamera3D.#ctor
  commentId: M:DrawnUi.Draw.SkCamera3D.#ctor
  id: '#ctor'
  parent: DrawnUi.Draw.SkCamera3D
  langs:
  - csharp
  - vb
  name: SkCamera3D()
  nameWithType: SkCamera3D.SkCamera3D()
  fullName: DrawnUi.Draw.SkCamera3D.SkCamera3D()
  type: Constructor
  source:
    remote:
      path: src/Shared/Internals/Helpers/3D/SkCamera3D.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Shared/Internals/Helpers/3D/SkCamera3D.cs
    startLine: 6
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public SkCamera3D()
    content.vb: Public Sub New()
  overload: DrawnUi.Draw.SkCamera3D.#ctor*
  nameWithType.vb: SkCamera3D.New()
  fullName.vb: DrawnUi.Draw.SkCamera3D.New()
  name.vb: New()
- uid: DrawnUi.Draw.SkCamera3D.fLocation
  commentId: F:DrawnUi.Draw.SkCamera3D.fLocation
  id: fLocation
  parent: DrawnUi.Draw.SkCamera3D
  langs:
  - csharp
  - vb
  name: fLocation
  nameWithType: SkCamera3D.fLocation
  fullName: DrawnUi.Draw.SkCamera3D.fLocation
  type: Field
  source:
    remote:
      path: src/Shared/Internals/Helpers/3D/SkCamera3D.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: fLocation
    path: ../src/Shared/Internals/Helpers/3D/SkCamera3D.cs
    startLine: 11
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public Vector3 fLocation
    return:
      type: System.Numerics.Vector3
    content.vb: Public fLocation As Vector3
- uid: DrawnUi.Draw.SkCamera3D.fAxis
  commentId: F:DrawnUi.Draw.SkCamera3D.fAxis
  id: fAxis
  parent: DrawnUi.Draw.SkCamera3D
  langs:
  - csharp
  - vb
  name: fAxis
  nameWithType: SkCamera3D.fAxis
  fullName: DrawnUi.Draw.SkCamera3D.fAxis
  type: Field
  source:
    remote:
      path: src/Shared/Internals/Helpers/3D/SkCamera3D.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: fAxis
    path: ../src/Shared/Internals/Helpers/3D/SkCamera3D.cs
    startLine: 12
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public Vector3 fAxis
    return:
      type: System.Numerics.Vector3
    content.vb: Public fAxis As Vector3
- uid: DrawnUi.Draw.SkCamera3D.fZenith
  commentId: F:DrawnUi.Draw.SkCamera3D.fZenith
  id: fZenith
  parent: DrawnUi.Draw.SkCamera3D
  langs:
  - csharp
  - vb
  name: fZenith
  nameWithType: SkCamera3D.fZenith
  fullName: DrawnUi.Draw.SkCamera3D.fZenith
  type: Field
  source:
    remote:
      path: src/Shared/Internals/Helpers/3D/SkCamera3D.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: fZenith
    path: ../src/Shared/Internals/Helpers/3D/SkCamera3D.cs
    startLine: 13
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public Vector3 fZenith
    return:
      type: System.Numerics.Vector3
    content.vb: Public fZenith As Vector3
- uid: DrawnUi.Draw.SkCamera3D.fObserver
  commentId: F:DrawnUi.Draw.SkCamera3D.fObserver
  id: fObserver
  parent: DrawnUi.Draw.SkCamera3D
  langs:
  - csharp
  - vb
  name: fObserver
  nameWithType: SkCamera3D.fObserver
  fullName: DrawnUi.Draw.SkCamera3D.fObserver
  type: Field
  source:
    remote:
      path: src/Shared/Internals/Helpers/3D/SkCamera3D.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: fObserver
    path: ../src/Shared/Internals/Helpers/3D/SkCamera3D.cs
    startLine: 14
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public Vector3 fObserver
    return:
      type: System.Numerics.Vector3
    content.vb: Public fObserver As Vector3
- uid: DrawnUi.Draw.SkCamera3D.DoUpdate
  commentId: M:DrawnUi.Draw.SkCamera3D.DoUpdate
  id: DoUpdate
  parent: DrawnUi.Draw.SkCamera3D
  langs:
  - csharp
  - vb
  name: DoUpdate()
  nameWithType: SkCamera3D.DoUpdate()
  fullName: DrawnUi.Draw.SkCamera3D.DoUpdate()
  type: Method
  source:
    remote:
      path: src/Shared/Internals/Helpers/3D/SkCamera3D.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: DoUpdate
    path: ../src/Shared/Internals/Helpers/3D/SkCamera3D.cs
    startLine: 21
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: protected void DoUpdate()
    content.vb: Protected Sub DoUpdate()
  overload: DrawnUi.Draw.SkCamera3D.DoUpdate*
- uid: DrawnUi.Draw.SkCamera3D.Update
  commentId: M:DrawnUi.Draw.SkCamera3D.Update
  id: Update
  parent: DrawnUi.Draw.SkCamera3D
  langs:
  - csharp
  - vb
  name: Update()
  nameWithType: SkCamera3D.Update()
  fullName: DrawnUi.Draw.SkCamera3D.Update()
  type: Method
  source:
    remote:
      path: src/Shared/Internals/Helpers/3D/SkCamera3D.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Update
    path: ../src/Shared/Internals/Helpers/3D/SkCamera3D.cs
    startLine: 66
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public void Update()
    content.vb: Public Sub Update()
  overload: DrawnUi.Draw.SkCamera3D.Update*
- uid: DrawnUi.Draw.SkCamera3D.Reset
  commentId: M:DrawnUi.Draw.SkCamera3D.Reset
  id: Reset
  parent: DrawnUi.Draw.SkCamera3D
  langs:
  - csharp
  - vb
  name: Reset()
  nameWithType: SkCamera3D.Reset()
  fullName: DrawnUi.Draw.SkCamera3D.Reset()
  type: Method
  source:
    remote:
      path: src/Shared/Internals/Helpers/3D/SkCamera3D.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Reset
    path: ../src/Shared/Internals/Helpers/3D/SkCamera3D.cs
    startLine: 73
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public void Reset()
    content.vb: Public Sub Reset()
  overload: DrawnUi.Draw.SkCamera3D.Reset*
- uid: DrawnUi.Draw.SkCamera3D.PatchToMatrix(DrawnUi.Draw.SkPatch3D)
  commentId: M:DrawnUi.Draw.SkCamera3D.PatchToMatrix(DrawnUi.Draw.SkPatch3D)
  id: PatchToMatrix(DrawnUi.Draw.SkPatch3D)
  parent: DrawnUi.Draw.SkCamera3D
  langs:
  - csharp
  - vb
  name: PatchToMatrix(SkPatch3D)
  nameWithType: SkCamera3D.PatchToMatrix(SkPatch3D)
  fullName: DrawnUi.Draw.SkCamera3D.PatchToMatrix(DrawnUi.Draw.SkPatch3D)
  type: Method
  source:
    remote:
      path: src/Shared/Internals/Helpers/3D/SkCamera3D.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: PatchToMatrix
    path: ../src/Shared/Internals/Helpers/3D/SkCamera3D.cs
    startLine: 126
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public SKMatrix PatchToMatrix(SkPatch3D quilt)
    parameters:
    - id: quilt
      type: DrawnUi.Draw.SkPatch3D
    return:
      type: SkiaSharp.SKMatrix
    content.vb: Public Function PatchToMatrix(quilt As SkPatch3D) As SKMatrix
  overload: DrawnUi.Draw.SkCamera3D.PatchToMatrix*
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.SkCamera3D.#ctor*
  commentId: Overload:DrawnUi.Draw.SkCamera3D.#ctor
  href: DrawnUi.Draw.SkCamera3D.html#DrawnUi_Draw_SkCamera3D__ctor
  name: SkCamera3D
  nameWithType: SkCamera3D.SkCamera3D
  fullName: DrawnUi.Draw.SkCamera3D.SkCamera3D
  nameWithType.vb: SkCamera3D.New
  fullName.vb: DrawnUi.Draw.SkCamera3D.New
  name.vb: New
- uid: System.Numerics.Vector3
  commentId: T:System.Numerics.Vector3
  parent: System.Numerics
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.numerics.vector3
  name: Vector3
  nameWithType: Vector3
  fullName: System.Numerics.Vector3
- uid: System.Numerics
  commentId: N:System.Numerics
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Numerics
  nameWithType: System.Numerics
  fullName: System.Numerics
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Numerics
    name: Numerics
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.numerics
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Numerics
    name: Numerics
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.numerics
- uid: DrawnUi.Draw.SkCamera3D.DoUpdate*
  commentId: Overload:DrawnUi.Draw.SkCamera3D.DoUpdate
  href: DrawnUi.Draw.SkCamera3D.html#DrawnUi_Draw_SkCamera3D_DoUpdate
  name: DoUpdate
  nameWithType: SkCamera3D.DoUpdate
  fullName: DrawnUi.Draw.SkCamera3D.DoUpdate
- uid: DrawnUi.Draw.SkCamera3D.Update*
  commentId: Overload:DrawnUi.Draw.SkCamera3D.Update
  href: DrawnUi.Draw.SkCamera3D.html#DrawnUi_Draw_SkCamera3D_Update
  name: Update
  nameWithType: SkCamera3D.Update
  fullName: DrawnUi.Draw.SkCamera3D.Update
- uid: DrawnUi.Draw.SkCamera3D.Reset*
  commentId: Overload:DrawnUi.Draw.SkCamera3D.Reset
  href: DrawnUi.Draw.SkCamera3D.html#DrawnUi_Draw_SkCamera3D_Reset
  name: Reset
  nameWithType: SkCamera3D.Reset
  fullName: DrawnUi.Draw.SkCamera3D.Reset
- uid: DrawnUi.Draw.SkCamera3D.PatchToMatrix*
  commentId: Overload:DrawnUi.Draw.SkCamera3D.PatchToMatrix
  href: DrawnUi.Draw.SkCamera3D.html#DrawnUi_Draw_SkCamera3D_PatchToMatrix_DrawnUi_Draw_SkPatch3D_
  name: PatchToMatrix
  nameWithType: SkCamera3D.PatchToMatrix
  fullName: DrawnUi.Draw.SkCamera3D.PatchToMatrix
- uid: DrawnUi.Draw.SkPatch3D
  commentId: T:DrawnUi.Draw.SkPatch3D
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkPatch3D.html
  name: SkPatch3D
  nameWithType: SkPatch3D
  fullName: DrawnUi.Draw.SkPatch3D
- uid: SkiaSharp.SKMatrix
  commentId: T:SkiaSharp.SKMatrix
  parent: SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.skmatrix
  name: SKMatrix
  nameWithType: SKMatrix
  fullName: SkiaSharp.SKMatrix
- uid: SkiaSharp
  commentId: N:SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp
  name: SkiaSharp
  nameWithType: SkiaSharp
  fullName: SkiaSharp
