# Advanced Topics

This section covers advanced features and concepts for DrawnUi development.

## Architecture and Performance

- [Layout System Architecture](layout-system.md) - Deep dive into how the layout system works
- [Platform-Specific Styling](platform-styling.md) - Creating platform-specific UI styles

## Visual Features

- [Gradients](gradients.md) - Creating and using gradient effects
- [SkiaScroll & Virtualization](skiascroll.md) - Advanced scrolling and performance optimization

## Interaction

- [Gestures & Touch Input](gestures.md) - Handling complex touch interactions

## Specialized Use Cases

- [Game UI & Interactive Games](game-ui.md) - Building game interfaces and interactive experiences

## Best Practices

When working with advanced DrawnUi features:

1. **Performance**: Use appropriate caching strategies for your use case
2. **Platform Differences**: Test platform-specific features on all target platforms
3. **Memory Management**: Be mindful of resource usage, especially with images and animations
4. **Touch Handling**: Understand the gesture system for complex interactions

## Getting Help

For advanced topics not covered here, check:
- The source code examples in the Sandbox project
- Community discussions and issues on GitHub
- The API documentation for detailed method signatures
