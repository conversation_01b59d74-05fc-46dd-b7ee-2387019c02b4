### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.SkiaGesturesParameters
  commentId: T:DrawnUi.Draw.SkiaGesturesParameters
  id: SkiaGesturesParameters
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.SkiaGesturesParameters.Create(AppoMobi.Maui.Gestures.TouchActionResult,AppoMobi.Maui.Gestures.TouchActionEventArgs)
  - DrawnUi.Draw.SkiaGesturesParameters.Empty
  - DrawnUi.Draw.SkiaGesturesParameters.Event
  - DrawnUi.Draw.SkiaGesturesParameters.Type
  langs:
  - csharp
  - vb
  name: SkiaGesturesParameters
  nameWithType: SkiaGesturesParameters
  fullName: DrawnUi.Draw.SkiaGesturesParameters
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Gestures/SkiaGesturesParameters.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: SkiaGesturesParameters
    path: ../src/Maui/DrawnUi/Features/Gestures/SkiaGesturesParameters.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public class SkiaGesturesParameters
    content.vb: Public Class SkiaGesturesParameters
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.SkiaGesturesParameters.Type
  commentId: P:DrawnUi.Draw.SkiaGesturesParameters.Type
  id: Type
  parent: DrawnUi.Draw.SkiaGesturesParameters
  langs:
  - csharp
  - vb
  name: Type
  nameWithType: SkiaGesturesParameters.Type
  fullName: DrawnUi.Draw.SkiaGesturesParameters.Type
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Gestures/SkiaGesturesParameters.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Type
    path: ../src/Maui/DrawnUi/Features/Gestures/SkiaGesturesParameters.cs
    startLine: 5
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public TouchActionResult Type { get; set; }
    parameters: []
    return:
      type: AppoMobi.Maui.Gestures.TouchActionResult
    content.vb: Public Property Type As TouchActionResult
  overload: DrawnUi.Draw.SkiaGesturesParameters.Type*
- uid: DrawnUi.Draw.SkiaGesturesParameters.Event
  commentId: P:DrawnUi.Draw.SkiaGesturesParameters.Event
  id: Event
  parent: DrawnUi.Draw.SkiaGesturesParameters
  langs:
  - csharp
  - vb
  name: Event
  nameWithType: SkiaGesturesParameters.Event
  fullName: DrawnUi.Draw.SkiaGesturesParameters.Event
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Gestures/SkiaGesturesParameters.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Event
    path: ../src/Maui/DrawnUi/Features/Gestures/SkiaGesturesParameters.cs
    startLine: 6
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public TouchActionEventArgs Event { get; set; }
    parameters: []
    return:
      type: AppoMobi.Maui.Gestures.TouchActionEventArgs
    content.vb: Public Property [Event] As TouchActionEventArgs
  overload: DrawnUi.Draw.SkiaGesturesParameters.Event*
- uid: DrawnUi.Draw.SkiaGesturesParameters.Create(AppoMobi.Maui.Gestures.TouchActionResult,AppoMobi.Maui.Gestures.TouchActionEventArgs)
  commentId: M:DrawnUi.Draw.SkiaGesturesParameters.Create(AppoMobi.Maui.Gestures.TouchActionResult,AppoMobi.Maui.Gestures.TouchActionEventArgs)
  id: Create(AppoMobi.Maui.Gestures.TouchActionResult,AppoMobi.Maui.Gestures.TouchActionEventArgs)
  parent: DrawnUi.Draw.SkiaGesturesParameters
  langs:
  - csharp
  - vb
  name: Create(TouchActionResult, TouchActionEventArgs)
  nameWithType: SkiaGesturesParameters.Create(TouchActionResult, TouchActionEventArgs)
  fullName: DrawnUi.Draw.SkiaGesturesParameters.Create(AppoMobi.Maui.Gestures.TouchActionResult, AppoMobi.Maui.Gestures.TouchActionEventArgs)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Gestures/SkiaGesturesParameters.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Create
    path: ../src/Maui/DrawnUi/Features/Gestures/SkiaGesturesParameters.cs
    startLine: 8
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static SkiaGesturesParameters Create(TouchActionResult action, TouchActionEventArgs args)
    parameters:
    - id: action
      type: AppoMobi.Maui.Gestures.TouchActionResult
    - id: args
      type: AppoMobi.Maui.Gestures.TouchActionEventArgs
    return:
      type: DrawnUi.Draw.SkiaGesturesParameters
    content.vb: Public Shared Function Create(action As TouchActionResult, args As TouchActionEventArgs) As SkiaGesturesParameters
  overload: DrawnUi.Draw.SkiaGesturesParameters.Create*
- uid: DrawnUi.Draw.SkiaGesturesParameters.Empty
  commentId: P:DrawnUi.Draw.SkiaGesturesParameters.Empty
  id: Empty
  parent: DrawnUi.Draw.SkiaGesturesParameters
  langs:
  - csharp
  - vb
  name: Empty
  nameWithType: SkiaGesturesParameters.Empty
  fullName: DrawnUi.Draw.SkiaGesturesParameters.Empty
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Gestures/SkiaGesturesParameters.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Empty
    path: ../src/Maui/DrawnUi/Features/Gestures/SkiaGesturesParameters.cs
    startLine: 17
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static SkiaGesturesParameters Empty { get; }
    parameters: []
    return:
      type: DrawnUi.Draw.SkiaGesturesParameters
    content.vb: Public Shared ReadOnly Property Empty As SkiaGesturesParameters
  overload: DrawnUi.Draw.SkiaGesturesParameters.Empty*
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.SkiaGesturesParameters.Type*
  commentId: Overload:DrawnUi.Draw.SkiaGesturesParameters.Type
  href: DrawnUi.Draw.SkiaGesturesParameters.html#DrawnUi_Draw_SkiaGesturesParameters_Type
  name: Type
  nameWithType: SkiaGesturesParameters.Type
  fullName: DrawnUi.Draw.SkiaGesturesParameters.Type
- uid: AppoMobi.Maui.Gestures.TouchActionResult
  commentId: T:AppoMobi.Maui.Gestures.TouchActionResult
  parent: AppoMobi.Maui.Gestures
  isExternal: true
  name: TouchActionResult
  nameWithType: TouchActionResult
  fullName: AppoMobi.Maui.Gestures.TouchActionResult
- uid: AppoMobi.Maui.Gestures
  commentId: N:AppoMobi.Maui.Gestures
  isExternal: true
  name: AppoMobi.Maui.Gestures
  nameWithType: AppoMobi.Maui.Gestures
  fullName: AppoMobi.Maui.Gestures
  spec.csharp:
  - uid: AppoMobi
    name: AppoMobi
    isExternal: true
  - name: .
  - uid: AppoMobi.Maui
    name: Maui
    isExternal: true
  - name: .
  - uid: AppoMobi.Maui.Gestures
    name: Gestures
    isExternal: true
  spec.vb:
  - uid: AppoMobi
    name: AppoMobi
    isExternal: true
  - name: .
  - uid: AppoMobi.Maui
    name: Maui
    isExternal: true
  - name: .
  - uid: AppoMobi.Maui.Gestures
    name: Gestures
    isExternal: true
- uid: DrawnUi.Draw.SkiaGesturesParameters.Event*
  commentId: Overload:DrawnUi.Draw.SkiaGesturesParameters.Event
  href: DrawnUi.Draw.SkiaGesturesParameters.html#DrawnUi_Draw_SkiaGesturesParameters_Event
  name: Event
  nameWithType: SkiaGesturesParameters.Event
  fullName: DrawnUi.Draw.SkiaGesturesParameters.Event
- uid: AppoMobi.Maui.Gestures.TouchActionEventArgs
  commentId: T:AppoMobi.Maui.Gestures.TouchActionEventArgs
  parent: AppoMobi.Maui.Gestures
  isExternal: true
  name: TouchActionEventArgs
  nameWithType: TouchActionEventArgs
  fullName: AppoMobi.Maui.Gestures.TouchActionEventArgs
- uid: DrawnUi.Draw.SkiaGesturesParameters.Create*
  commentId: Overload:DrawnUi.Draw.SkiaGesturesParameters.Create
  href: DrawnUi.Draw.SkiaGesturesParameters.html#DrawnUi_Draw_SkiaGesturesParameters_Create_AppoMobi_Maui_Gestures_TouchActionResult_AppoMobi_Maui_Gestures_TouchActionEventArgs_
  name: Create
  nameWithType: SkiaGesturesParameters.Create
  fullName: DrawnUi.Draw.SkiaGesturesParameters.Create
- uid: DrawnUi.Draw.SkiaGesturesParameters
  commentId: T:DrawnUi.Draw.SkiaGesturesParameters
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaGesturesParameters.html
  name: SkiaGesturesParameters
  nameWithType: SkiaGesturesParameters
  fullName: DrawnUi.Draw.SkiaGesturesParameters
- uid: DrawnUi.Draw.SkiaGesturesParameters.Empty*
  commentId: Overload:DrawnUi.Draw.SkiaGesturesParameters.Empty
  href: DrawnUi.Draw.SkiaGesturesParameters.html#DrawnUi_Draw_SkiaGesturesParameters_Empty
  name: Empty
  nameWithType: SkiaGesturesParameters.Empty
  fullName: DrawnUi.Draw.SkiaGesturesParameters.Empty
