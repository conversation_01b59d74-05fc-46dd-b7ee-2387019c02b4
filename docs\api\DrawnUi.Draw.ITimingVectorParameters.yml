### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.ITimingVectorParameters
  commentId: T:DrawnUi.Draw.ITimingVectorParameters
  id: ITimingVectorParameters
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.ITimingVectorParameters.DurationSecs
  - DrawnUi.Draw.ITimingVectorParameters.ValueAt(System.Single)
  langs:
  - csharp
  - vb
  name: ITimingVectorParameters
  nameWithType: ITimingVectorParameters
  fullName: DrawnUi.Draw.ITimingVectorParameters
  type: Interface
  source:
    remote:
      path: src/Shared/Features/Animations/Parameters/ITimingVectorParameters.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ITimingVectorParameters
    path: ../src/Shared/Features/Animations/Parameters/ITimingVectorParameters.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public interface ITimingVectorParameters
    content.vb: Public Interface ITimingVectorParameters
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.ITimingVectorParameters.DurationSecs
  commentId: P:DrawnUi.Draw.ITimingVectorParameters.DurationSecs
  id: DurationSecs
  parent: DrawnUi.Draw.ITimingVectorParameters
  langs:
  - csharp
  - vb
  name: DurationSecs
  nameWithType: ITimingVectorParameters.DurationSecs
  fullName: DrawnUi.Draw.ITimingVectorParameters.DurationSecs
  type: Property
  source:
    remote:
      path: src/Shared/Features/Animations/Parameters/ITimingVectorParameters.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: DurationSecs
    path: ../src/Shared/Features/Animations/Parameters/ITimingVectorParameters.cs
    startLine: 6
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: float DurationSecs { get; }
    parameters: []
    return:
      type: System.Single
    content.vb: ReadOnly Property DurationSecs As Single
  overload: DrawnUi.Draw.ITimingVectorParameters.DurationSecs*
- uid: DrawnUi.Draw.ITimingVectorParameters.ValueAt(System.Single)
  commentId: M:DrawnUi.Draw.ITimingVectorParameters.ValueAt(System.Single)
  id: ValueAt(System.Single)
  parent: DrawnUi.Draw.ITimingVectorParameters
  langs:
  - csharp
  - vb
  name: ValueAt(float)
  nameWithType: ITimingVectorParameters.ValueAt(float)
  fullName: DrawnUi.Draw.ITimingVectorParameters.ValueAt(float)
  type: Method
  source:
    remote:
      path: src/Shared/Features/Animations/Parameters/ITimingVectorParameters.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ValueAt
    path: ../src/Shared/Features/Animations/Parameters/ITimingVectorParameters.cs
    startLine: 7
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: Vector2 ValueAt(float offsetSecs)
    parameters:
    - id: offsetSecs
      type: System.Single
    return:
      type: System.Numerics.Vector2
    content.vb: Function ValueAt(offsetSecs As Single) As Vector2
  overload: DrawnUi.Draw.ITimingVectorParameters.ValueAt*
  nameWithType.vb: ITimingVectorParameters.ValueAt(Single)
  fullName.vb: DrawnUi.Draw.ITimingVectorParameters.ValueAt(Single)
  name.vb: ValueAt(Single)
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.ITimingVectorParameters.DurationSecs*
  commentId: Overload:DrawnUi.Draw.ITimingVectorParameters.DurationSecs
  href: DrawnUi.Draw.ITimingVectorParameters.html#DrawnUi_Draw_ITimingVectorParameters_DurationSecs
  name: DurationSecs
  nameWithType: ITimingVectorParameters.DurationSecs
  fullName: DrawnUi.Draw.ITimingVectorParameters.DurationSecs
- uid: System.Single
  commentId: T:System.Single
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.single
  name: float
  nameWithType: float
  fullName: float
  nameWithType.vb: Single
  fullName.vb: Single
  name.vb: Single
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Draw.ITimingVectorParameters.ValueAt*
  commentId: Overload:DrawnUi.Draw.ITimingVectorParameters.ValueAt
  href: DrawnUi.Draw.ITimingVectorParameters.html#DrawnUi_Draw_ITimingVectorParameters_ValueAt_System_Single_
  name: ValueAt
  nameWithType: ITimingVectorParameters.ValueAt
  fullName: DrawnUi.Draw.ITimingVectorParameters.ValueAt
- uid: System.Numerics.Vector2
  commentId: T:System.Numerics.Vector2
  parent: System.Numerics
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.numerics.vector2
  name: Vector2
  nameWithType: Vector2
  fullName: System.Numerics.Vector2
- uid: System.Numerics
  commentId: N:System.Numerics
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Numerics
  nameWithType: System.Numerics
  fullName: System.Numerics
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Numerics
    name: Numerics
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.numerics
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Numerics
    name: Numerics
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.numerics
