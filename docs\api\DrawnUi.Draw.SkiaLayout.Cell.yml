### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.SkiaLayout.Cell
  commentId: T:DrawnUi.Draw.SkiaLayout.Cell
  id: SkiaLayout.Cell
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.SkiaLayout.Cell.#ctor(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,DrawnUi.Models.GridLengthType,DrawnUi.Models.GridLengthType)
  - DrawnUi.Draw.SkiaLayout.Cell.Column
  - DrawnUi.Draw.SkiaLayout.Cell.ColumnGridLengthType
  - DrawnUi.Draw.SkiaLayout.Cell.ColumnSpan
  - DrawnUi.Draw.SkiaLayout.Cell.IsAbsolute
  - DrawnUi.Draw.SkiaLayout.Cell.IsColumnSpanAuto
  - DrawnUi.Draw.SkiaLayout.Cell.IsColumnSpanStar
  - DrawnUi.Draw.SkiaLayout.Cell.IsRowSpanAuto
  - DrawnUi.Draw.SkiaLayout.Cell.IsRowSpanStar
  - DrawnUi.Draw.SkiaLayout.Cell.NeedsKnownMeasurePass
  - DrawnUi.Draw.SkiaLayout.Cell.Row
  - DrawnUi.Draw.SkiaLayout.Cell.RowGridLengthType
  - DrawnUi.Draw.SkiaLayout.Cell.RowSpan
  - DrawnUi.Draw.SkiaLayout.Cell.ViewIndex
  langs:
  - csharp
  - vb
  name: SkiaLayout.Cell
  nameWithType: SkiaLayout.Cell
  fullName: DrawnUi.Draw.SkiaLayout.Cell
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.Cell.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Cell
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.Cell.cs
    startLine: 6
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public class SkiaLayout.Cell
    content.vb: Public Class SkiaLayout.Cell
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.SkiaLayout.Cell.ViewIndex
  commentId: P:DrawnUi.Draw.SkiaLayout.Cell.ViewIndex
  id: ViewIndex
  parent: DrawnUi.Draw.SkiaLayout.Cell
  langs:
  - csharp
  - vb
  name: ViewIndex
  nameWithType: SkiaLayout.Cell.ViewIndex
  fullName: DrawnUi.Draw.SkiaLayout.Cell.ViewIndex
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.Cell.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ViewIndex
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.Cell.cs
    startLine: 8
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public int ViewIndex { get; }
    parameters: []
    return:
      type: System.Int32
    content.vb: Public ReadOnly Property ViewIndex As Integer
  overload: DrawnUi.Draw.SkiaLayout.Cell.ViewIndex*
- uid: DrawnUi.Draw.SkiaLayout.Cell.Row
  commentId: P:DrawnUi.Draw.SkiaLayout.Cell.Row
  id: Row
  parent: DrawnUi.Draw.SkiaLayout.Cell
  langs:
  - csharp
  - vb
  name: Row
  nameWithType: SkiaLayout.Cell.Row
  fullName: DrawnUi.Draw.SkiaLayout.Cell.Row
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.Cell.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Row
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.Cell.cs
    startLine: 9
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public int Row { get; }
    parameters: []
    return:
      type: System.Int32
    content.vb: Public ReadOnly Property Row As Integer
  overload: DrawnUi.Draw.SkiaLayout.Cell.Row*
- uid: DrawnUi.Draw.SkiaLayout.Cell.Column
  commentId: P:DrawnUi.Draw.SkiaLayout.Cell.Column
  id: Column
  parent: DrawnUi.Draw.SkiaLayout.Cell
  langs:
  - csharp
  - vb
  name: Column
  nameWithType: SkiaLayout.Cell.Column
  fullName: DrawnUi.Draw.SkiaLayout.Cell.Column
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.Cell.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Column
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.Cell.cs
    startLine: 10
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public int Column { get; }
    parameters: []
    return:
      type: System.Int32
    content.vb: Public ReadOnly Property Column As Integer
  overload: DrawnUi.Draw.SkiaLayout.Cell.Column*
- uid: DrawnUi.Draw.SkiaLayout.Cell.RowSpan
  commentId: P:DrawnUi.Draw.SkiaLayout.Cell.RowSpan
  id: RowSpan
  parent: DrawnUi.Draw.SkiaLayout.Cell
  langs:
  - csharp
  - vb
  name: RowSpan
  nameWithType: SkiaLayout.Cell.RowSpan
  fullName: DrawnUi.Draw.SkiaLayout.Cell.RowSpan
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.Cell.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: RowSpan
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.Cell.cs
    startLine: 11
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public int RowSpan { get; }
    parameters: []
    return:
      type: System.Int32
    content.vb: Public ReadOnly Property RowSpan As Integer
  overload: DrawnUi.Draw.SkiaLayout.Cell.RowSpan*
- uid: DrawnUi.Draw.SkiaLayout.Cell.ColumnSpan
  commentId: P:DrawnUi.Draw.SkiaLayout.Cell.ColumnSpan
  id: ColumnSpan
  parent: DrawnUi.Draw.SkiaLayout.Cell
  langs:
  - csharp
  - vb
  name: ColumnSpan
  nameWithType: SkiaLayout.Cell.ColumnSpan
  fullName: DrawnUi.Draw.SkiaLayout.Cell.ColumnSpan
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.Cell.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ColumnSpan
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.Cell.cs
    startLine: 12
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public int ColumnSpan { get; }
    parameters: []
    return:
      type: System.Int32
    content.vb: Public ReadOnly Property ColumnSpan As Integer
  overload: DrawnUi.Draw.SkiaLayout.Cell.ColumnSpan*
- uid: DrawnUi.Draw.SkiaLayout.Cell.ColumnGridLengthType
  commentId: P:DrawnUi.Draw.SkiaLayout.Cell.ColumnGridLengthType
  id: ColumnGridLengthType
  parent: DrawnUi.Draw.SkiaLayout.Cell
  langs:
  - csharp
  - vb
  name: ColumnGridLengthType
  nameWithType: SkiaLayout.Cell.ColumnGridLengthType
  fullName: DrawnUi.Draw.SkiaLayout.Cell.ColumnGridLengthType
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.Cell.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ColumnGridLengthType
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.Cell.cs
    startLine: 17
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: A combination of all the measurement types in the columns this cell spans
  example: []
  syntax:
    content: public GridLengthType ColumnGridLengthType { get; }
    parameters: []
    return:
      type: DrawnUi.Models.GridLengthType
    content.vb: Public ReadOnly Property ColumnGridLengthType As GridLengthType
  overload: DrawnUi.Draw.SkiaLayout.Cell.ColumnGridLengthType*
- uid: DrawnUi.Draw.SkiaLayout.Cell.RowGridLengthType
  commentId: P:DrawnUi.Draw.SkiaLayout.Cell.RowGridLengthType
  id: RowGridLengthType
  parent: DrawnUi.Draw.SkiaLayout.Cell
  langs:
  - csharp
  - vb
  name: RowGridLengthType
  nameWithType: SkiaLayout.Cell.RowGridLengthType
  fullName: DrawnUi.Draw.SkiaLayout.Cell.RowGridLengthType
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.Cell.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: RowGridLengthType
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.Cell.cs
    startLine: 22
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: A combination of all the measurement types in the rows this cell spans
  example: []
  syntax:
    content: public GridLengthType RowGridLengthType { get; }
    parameters: []
    return:
      type: DrawnUi.Models.GridLengthType
    content.vb: Public ReadOnly Property RowGridLengthType As GridLengthType
  overload: DrawnUi.Draw.SkiaLayout.Cell.RowGridLengthType*
- uid: DrawnUi.Draw.SkiaLayout.Cell.#ctor(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,DrawnUi.Models.GridLengthType,DrawnUi.Models.GridLengthType)
  commentId: M:DrawnUi.Draw.SkiaLayout.Cell.#ctor(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,DrawnUi.Models.GridLengthType,DrawnUi.Models.GridLengthType)
  id: '#ctor(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,DrawnUi.Models.GridLengthType,DrawnUi.Models.GridLengthType)'
  parent: DrawnUi.Draw.SkiaLayout.Cell
  langs:
  - csharp
  - vb
  name: Cell(int, int, int, int, int, GridLengthType, GridLengthType)
  nameWithType: SkiaLayout.Cell.Cell(int, int, int, int, int, GridLengthType, GridLengthType)
  fullName: DrawnUi.Draw.SkiaLayout.Cell.Cell(int, int, int, int, int, DrawnUi.Models.GridLengthType, DrawnUi.Models.GridLengthType)
  type: Constructor
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.Cell.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.Cell.cs
    startLine: 24
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public Cell(int viewIndex, int row, int column, int rowSpan, int columnSpan, GridLengthType columnGridLengthType, GridLengthType rowGridLengthType)
    parameters:
    - id: viewIndex
      type: System.Int32
    - id: row
      type: System.Int32
    - id: column
      type: System.Int32
    - id: rowSpan
      type: System.Int32
    - id: columnSpan
      type: System.Int32
    - id: columnGridLengthType
      type: DrawnUi.Models.GridLengthType
    - id: rowGridLengthType
      type: DrawnUi.Models.GridLengthType
    content.vb: Public Sub New(viewIndex As Integer, row As Integer, column As Integer, rowSpan As Integer, columnSpan As Integer, columnGridLengthType As GridLengthType, rowGridLengthType As GridLengthType)
  overload: DrawnUi.Draw.SkiaLayout.Cell.#ctor*
  nameWithType.vb: SkiaLayout.Cell.New(Integer, Integer, Integer, Integer, Integer, GridLengthType, GridLengthType)
  fullName.vb: DrawnUi.Draw.SkiaLayout.Cell.New(Integer, Integer, Integer, Integer, Integer, DrawnUi.Models.GridLengthType, DrawnUi.Models.GridLengthType)
  name.vb: New(Integer, Integer, Integer, Integer, Integer, GridLengthType, GridLengthType)
- uid: DrawnUi.Draw.SkiaLayout.Cell.IsColumnSpanAuto
  commentId: P:DrawnUi.Draw.SkiaLayout.Cell.IsColumnSpanAuto
  id: IsColumnSpanAuto
  parent: DrawnUi.Draw.SkiaLayout.Cell
  langs:
  - csharp
  - vb
  name: IsColumnSpanAuto
  nameWithType: SkiaLayout.Cell.IsColumnSpanAuto
  fullName: DrawnUi.Draw.SkiaLayout.Cell.IsColumnSpanAuto
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.Cell.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IsColumnSpanAuto
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.Cell.cs
    startLine: 36
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public bool IsColumnSpanAuto { get; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public ReadOnly Property IsColumnSpanAuto As Boolean
  overload: DrawnUi.Draw.SkiaLayout.Cell.IsColumnSpanAuto*
- uid: DrawnUi.Draw.SkiaLayout.Cell.IsRowSpanAuto
  commentId: P:DrawnUi.Draw.SkiaLayout.Cell.IsRowSpanAuto
  id: IsRowSpanAuto
  parent: DrawnUi.Draw.SkiaLayout.Cell
  langs:
  - csharp
  - vb
  name: IsRowSpanAuto
  nameWithType: SkiaLayout.Cell.IsRowSpanAuto
  fullName: DrawnUi.Draw.SkiaLayout.Cell.IsRowSpanAuto
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.Cell.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IsRowSpanAuto
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.Cell.cs
    startLine: 37
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public bool IsRowSpanAuto { get; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public ReadOnly Property IsRowSpanAuto As Boolean
  overload: DrawnUi.Draw.SkiaLayout.Cell.IsRowSpanAuto*
- uid: DrawnUi.Draw.SkiaLayout.Cell.IsColumnSpanStar
  commentId: P:DrawnUi.Draw.SkiaLayout.Cell.IsColumnSpanStar
  id: IsColumnSpanStar
  parent: DrawnUi.Draw.SkiaLayout.Cell
  langs:
  - csharp
  - vb
  name: IsColumnSpanStar
  nameWithType: SkiaLayout.Cell.IsColumnSpanStar
  fullName: DrawnUi.Draw.SkiaLayout.Cell.IsColumnSpanStar
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.Cell.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IsColumnSpanStar
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.Cell.cs
    startLine: 38
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public bool IsColumnSpanStar { get; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public ReadOnly Property IsColumnSpanStar As Boolean
  overload: DrawnUi.Draw.SkiaLayout.Cell.IsColumnSpanStar*
- uid: DrawnUi.Draw.SkiaLayout.Cell.IsRowSpanStar
  commentId: P:DrawnUi.Draw.SkiaLayout.Cell.IsRowSpanStar
  id: IsRowSpanStar
  parent: DrawnUi.Draw.SkiaLayout.Cell
  langs:
  - csharp
  - vb
  name: IsRowSpanStar
  nameWithType: SkiaLayout.Cell.IsRowSpanStar
  fullName: DrawnUi.Draw.SkiaLayout.Cell.IsRowSpanStar
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.Cell.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IsRowSpanStar
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.Cell.cs
    startLine: 39
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public bool IsRowSpanStar { get; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public ReadOnly Property IsRowSpanStar As Boolean
  overload: DrawnUi.Draw.SkiaLayout.Cell.IsRowSpanStar*
- uid: DrawnUi.Draw.SkiaLayout.Cell.IsAbsolute
  commentId: P:DrawnUi.Draw.SkiaLayout.Cell.IsAbsolute
  id: IsAbsolute
  parent: DrawnUi.Draw.SkiaLayout.Cell
  langs:
  - csharp
  - vb
  name: IsAbsolute
  nameWithType: SkiaLayout.Cell.IsAbsolute
  fullName: DrawnUi.Draw.SkiaLayout.Cell.IsAbsolute
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.Cell.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IsAbsolute
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.Cell.cs
    startLine: 40
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public bool IsAbsolute { get; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public ReadOnly Property IsAbsolute As Boolean
  overload: DrawnUi.Draw.SkiaLayout.Cell.IsAbsolute*
- uid: DrawnUi.Draw.SkiaLayout.Cell.NeedsKnownMeasurePass
  commentId: P:DrawnUi.Draw.SkiaLayout.Cell.NeedsKnownMeasurePass
  id: NeedsKnownMeasurePass
  parent: DrawnUi.Draw.SkiaLayout.Cell
  langs:
  - csharp
  - vb
  name: NeedsKnownMeasurePass
  nameWithType: SkiaLayout.Cell.NeedsKnownMeasurePass
  fullName: DrawnUi.Draw.SkiaLayout.Cell.NeedsKnownMeasurePass
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.Cell.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: NeedsKnownMeasurePass
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.Grid.Cell.cs
    startLine: 45
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public bool NeedsKnownMeasurePass { get; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public ReadOnly Property NeedsKnownMeasurePass As Boolean
  overload: DrawnUi.Draw.SkiaLayout.Cell.NeedsKnownMeasurePass*
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.SkiaLayout.Cell.ViewIndex*
  commentId: Overload:DrawnUi.Draw.SkiaLayout.Cell.ViewIndex
  href: DrawnUi.Draw.SkiaLayout.Cell.html#DrawnUi_Draw_SkiaLayout_Cell_ViewIndex
  name: ViewIndex
  nameWithType: SkiaLayout.Cell.ViewIndex
  fullName: DrawnUi.Draw.SkiaLayout.Cell.ViewIndex
- uid: System.Int32
  commentId: T:System.Int32
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.int32
  name: int
  nameWithType: int
  fullName: int
  nameWithType.vb: Integer
  fullName.vb: Integer
  name.vb: Integer
- uid: DrawnUi.Draw.SkiaLayout.Cell.Row*
  commentId: Overload:DrawnUi.Draw.SkiaLayout.Cell.Row
  href: DrawnUi.Draw.SkiaLayout.Cell.html#DrawnUi_Draw_SkiaLayout_Cell_Row
  name: Row
  nameWithType: SkiaLayout.Cell.Row
  fullName: DrawnUi.Draw.SkiaLayout.Cell.Row
- uid: DrawnUi.Draw.SkiaLayout.Cell.Column*
  commentId: Overload:DrawnUi.Draw.SkiaLayout.Cell.Column
  href: DrawnUi.Draw.SkiaLayout.Cell.html#DrawnUi_Draw_SkiaLayout_Cell_Column
  name: Column
  nameWithType: SkiaLayout.Cell.Column
  fullName: DrawnUi.Draw.SkiaLayout.Cell.Column
- uid: DrawnUi.Draw.SkiaLayout.Cell.RowSpan*
  commentId: Overload:DrawnUi.Draw.SkiaLayout.Cell.RowSpan
  href: DrawnUi.Draw.SkiaLayout.Cell.html#DrawnUi_Draw_SkiaLayout_Cell_RowSpan
  name: RowSpan
  nameWithType: SkiaLayout.Cell.RowSpan
  fullName: DrawnUi.Draw.SkiaLayout.Cell.RowSpan
- uid: DrawnUi.Draw.SkiaLayout.Cell.ColumnSpan*
  commentId: Overload:DrawnUi.Draw.SkiaLayout.Cell.ColumnSpan
  href: DrawnUi.Draw.SkiaLayout.Cell.html#DrawnUi_Draw_SkiaLayout_Cell_ColumnSpan
  name: ColumnSpan
  nameWithType: SkiaLayout.Cell.ColumnSpan
  fullName: DrawnUi.Draw.SkiaLayout.Cell.ColumnSpan
- uid: DrawnUi.Draw.SkiaLayout.Cell.ColumnGridLengthType*
  commentId: Overload:DrawnUi.Draw.SkiaLayout.Cell.ColumnGridLengthType
  href: DrawnUi.Draw.SkiaLayout.Cell.html#DrawnUi_Draw_SkiaLayout_Cell_ColumnGridLengthType
  name: ColumnGridLengthType
  nameWithType: SkiaLayout.Cell.ColumnGridLengthType
  fullName: DrawnUi.Draw.SkiaLayout.Cell.ColumnGridLengthType
- uid: DrawnUi.Models.GridLengthType
  commentId: T:DrawnUi.Models.GridLengthType
  parent: DrawnUi.Models
  href: DrawnUi.Models.GridLengthType.html
  name: GridLengthType
  nameWithType: GridLengthType
  fullName: DrawnUi.Models.GridLengthType
- uid: DrawnUi.Models
  commentId: N:DrawnUi.Models
  href: DrawnUi.html
  name: DrawnUi.Models
  nameWithType: DrawnUi.Models
  fullName: DrawnUi.Models
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Models
    name: Models
    href: DrawnUi.Models.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Models
    name: Models
    href: DrawnUi.Models.html
- uid: DrawnUi.Draw.SkiaLayout.Cell.RowGridLengthType*
  commentId: Overload:DrawnUi.Draw.SkiaLayout.Cell.RowGridLengthType
  href: DrawnUi.Draw.SkiaLayout.Cell.html#DrawnUi_Draw_SkiaLayout_Cell_RowGridLengthType
  name: RowGridLengthType
  nameWithType: SkiaLayout.Cell.RowGridLengthType
  fullName: DrawnUi.Draw.SkiaLayout.Cell.RowGridLengthType
- uid: DrawnUi.Draw.SkiaLayout.Cell.#ctor*
  commentId: Overload:DrawnUi.Draw.SkiaLayout.Cell.#ctor
  href: DrawnUi.Draw.SkiaLayout.Cell.html#DrawnUi_Draw_SkiaLayout_Cell__ctor_System_Int32_System_Int32_System_Int32_System_Int32_System_Int32_DrawnUi_Models_GridLengthType_DrawnUi_Models_GridLengthType_
  name: Cell
  nameWithType: SkiaLayout.Cell.Cell
  fullName: DrawnUi.Draw.SkiaLayout.Cell.Cell
  nameWithType.vb: SkiaLayout.Cell.New
  fullName.vb: DrawnUi.Draw.SkiaLayout.Cell.New
  name.vb: New
- uid: DrawnUi.Draw.SkiaLayout.Cell.IsColumnSpanAuto*
  commentId: Overload:DrawnUi.Draw.SkiaLayout.Cell.IsColumnSpanAuto
  href: DrawnUi.Draw.SkiaLayout.Cell.html#DrawnUi_Draw_SkiaLayout_Cell_IsColumnSpanAuto
  name: IsColumnSpanAuto
  nameWithType: SkiaLayout.Cell.IsColumnSpanAuto
  fullName: DrawnUi.Draw.SkiaLayout.Cell.IsColumnSpanAuto
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: DrawnUi.Draw.SkiaLayout.Cell.IsRowSpanAuto*
  commentId: Overload:DrawnUi.Draw.SkiaLayout.Cell.IsRowSpanAuto
  href: DrawnUi.Draw.SkiaLayout.Cell.html#DrawnUi_Draw_SkiaLayout_Cell_IsRowSpanAuto
  name: IsRowSpanAuto
  nameWithType: SkiaLayout.Cell.IsRowSpanAuto
  fullName: DrawnUi.Draw.SkiaLayout.Cell.IsRowSpanAuto
- uid: DrawnUi.Draw.SkiaLayout.Cell.IsColumnSpanStar*
  commentId: Overload:DrawnUi.Draw.SkiaLayout.Cell.IsColumnSpanStar
  href: DrawnUi.Draw.SkiaLayout.Cell.html#DrawnUi_Draw_SkiaLayout_Cell_IsColumnSpanStar
  name: IsColumnSpanStar
  nameWithType: SkiaLayout.Cell.IsColumnSpanStar
  fullName: DrawnUi.Draw.SkiaLayout.Cell.IsColumnSpanStar
- uid: DrawnUi.Draw.SkiaLayout.Cell.IsRowSpanStar*
  commentId: Overload:DrawnUi.Draw.SkiaLayout.Cell.IsRowSpanStar
  href: DrawnUi.Draw.SkiaLayout.Cell.html#DrawnUi_Draw_SkiaLayout_Cell_IsRowSpanStar
  name: IsRowSpanStar
  nameWithType: SkiaLayout.Cell.IsRowSpanStar
  fullName: DrawnUi.Draw.SkiaLayout.Cell.IsRowSpanStar
- uid: DrawnUi.Draw.SkiaLayout.Cell.IsAbsolute*
  commentId: Overload:DrawnUi.Draw.SkiaLayout.Cell.IsAbsolute
  href: DrawnUi.Draw.SkiaLayout.Cell.html#DrawnUi_Draw_SkiaLayout_Cell_IsAbsolute
  name: IsAbsolute
  nameWithType: SkiaLayout.Cell.IsAbsolute
  fullName: DrawnUi.Draw.SkiaLayout.Cell.IsAbsolute
- uid: DrawnUi.Draw.SkiaLayout.Cell.NeedsKnownMeasurePass*
  commentId: Overload:DrawnUi.Draw.SkiaLayout.Cell.NeedsKnownMeasurePass
  href: DrawnUi.Draw.SkiaLayout.Cell.html#DrawnUi_Draw_SkiaLayout_Cell_NeedsKnownMeasurePass
  name: NeedsKnownMeasurePass
  nameWithType: SkiaLayout.Cell.NeedsKnownMeasurePass
  fullName: DrawnUi.Draw.SkiaLayout.Cell.NeedsKnownMeasurePass
