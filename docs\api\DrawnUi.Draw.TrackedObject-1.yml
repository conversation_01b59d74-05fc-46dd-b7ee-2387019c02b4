### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.TrackedObject`1
  commentId: T:DrawnUi.Draw.TrackedObject`1
  id: TrackedObject`1
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.TrackedObject`1.#ctor(`0)
  - DrawnUi.Draw.TrackedObject`1.Disposable
  - DrawnUi.Draw.TrackedObject`1.Dispose
  - DrawnUi.Draw.TrackedObject`1.Finalize
  langs:
  - csharp
  - vb
  name: TrackedObject<T>
  nameWithType: TrackedObject<T>
  fullName: DrawnUi.Draw.TrackedObject<T>
  type: Class
  source:
    remote:
      path: src/Shared/Internals/Helpers/TrackedObject.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: TrackedObject
    path: ../src/Shared/Internals/Helpers/TrackedObject.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: 'public class TrackedObject<T> where T : IDisposable'
    typeParameters:
    - id: T
    content.vb: Public Class TrackedObject(Of T As IDisposable)
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  nameWithType.vb: TrackedObject(Of T)
  fullName.vb: DrawnUi.Draw.TrackedObject(Of T)
  name.vb: TrackedObject(Of T)
- uid: DrawnUi.Draw.TrackedObject`1.Disposable
  commentId: P:DrawnUi.Draw.TrackedObject`1.Disposable
  id: Disposable
  parent: DrawnUi.Draw.TrackedObject`1
  langs:
  - csharp
  - vb
  name: Disposable
  nameWithType: TrackedObject<T>.Disposable
  fullName: DrawnUi.Draw.TrackedObject<T>.Disposable
  type: Property
  source:
    remote:
      path: src/Shared/Internals/Helpers/TrackedObject.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Disposable
    path: ../src/Shared/Internals/Helpers/TrackedObject.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public T Disposable { get; }
    parameters: []
    return:
      type: '{T}'
    content.vb: Public ReadOnly Property Disposable As T
  overload: DrawnUi.Draw.TrackedObject`1.Disposable*
  nameWithType.vb: TrackedObject(Of T).Disposable
  fullName.vb: DrawnUi.Draw.TrackedObject(Of T).Disposable
- uid: DrawnUi.Draw.TrackedObject`1.#ctor(`0)
  commentId: M:DrawnUi.Draw.TrackedObject`1.#ctor(`0)
  id: '#ctor(`0)'
  parent: DrawnUi.Draw.TrackedObject`1
  langs:
  - csharp
  - vb
  name: TrackedObject(T)
  nameWithType: TrackedObject<T>.TrackedObject(T)
  fullName: DrawnUi.Draw.TrackedObject<T>.TrackedObject(T)
  type: Constructor
  source:
    remote:
      path: src/Shared/Internals/Helpers/TrackedObject.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Shared/Internals/Helpers/TrackedObject.cs
    startLine: 6
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public TrackedObject(T disposable)
    parameters:
    - id: disposable
      type: '{T}'
    content.vb: Public Sub New(disposable As T)
  overload: DrawnUi.Draw.TrackedObject`1.#ctor*
  nameWithType.vb: TrackedObject(Of T).New(T)
  fullName.vb: DrawnUi.Draw.TrackedObject(Of T).New(T)
  name.vb: New(T)
- uid: DrawnUi.Draw.TrackedObject`1.Finalize
  commentId: M:DrawnUi.Draw.TrackedObject`1.Finalize
  id: Finalize
  parent: DrawnUi.Draw.TrackedObject`1
  langs:
  - csharp
  - vb
  name: ~TrackedObject()
  nameWithType: TrackedObject<T>.~TrackedObject()
  fullName: DrawnUi.Draw.TrackedObject<T>.~TrackedObject()
  type: Method
  source:
    remote:
      path: src/Shared/Internals/Helpers/TrackedObject.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Finalize
    path: ../src/Shared/Internals/Helpers/TrackedObject.cs
    startLine: 11
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: protected ~TrackedObject()
    content.vb: 'Protected '
  overload: DrawnUi.Draw.TrackedObject`1.Finalize*
  nameWithType.vb: ''
  fullName.vb: ''
  name.vb: ''
- uid: DrawnUi.Draw.TrackedObject`1.Dispose
  commentId: M:DrawnUi.Draw.TrackedObject`1.Dispose
  id: Dispose
  parent: DrawnUi.Draw.TrackedObject`1
  langs:
  - csharp
  - vb
  name: Dispose()
  nameWithType: TrackedObject<T>.Dispose()
  fullName: DrawnUi.Draw.TrackedObject<T>.Dispose()
  type: Method
  source:
    remote:
      path: src/Shared/Internals/Helpers/TrackedObject.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Dispose
    path: ../src/Shared/Internals/Helpers/TrackedObject.cs
    startLine: 16
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public void Dispose()
    content.vb: Public Sub Dispose()
  overload: DrawnUi.Draw.TrackedObject`1.Dispose*
  nameWithType.vb: TrackedObject(Of T).Dispose()
  fullName.vb: DrawnUi.Draw.TrackedObject(Of T).Dispose()
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.TrackedObject`1.Disposable*
  commentId: Overload:DrawnUi.Draw.TrackedObject`1.Disposable
  href: DrawnUi.Draw.TrackedObject-1.html#DrawnUi_Draw_TrackedObject_1_Disposable
  name: Disposable
  nameWithType: TrackedObject<T>.Disposable
  fullName: DrawnUi.Draw.TrackedObject<T>.Disposable
  nameWithType.vb: TrackedObject(Of T).Disposable
  fullName.vb: DrawnUi.Draw.TrackedObject(Of T).Disposable
- uid: '{T}'
  commentId: '!:T'
  definition: T
  name: T
  nameWithType: T
  fullName: T
- uid: T
  name: T
  nameWithType: T
  fullName: T
- uid: DrawnUi.Draw.TrackedObject`1.#ctor*
  commentId: Overload:DrawnUi.Draw.TrackedObject`1.#ctor
  href: DrawnUi.Draw.TrackedObject-1.html#DrawnUi_Draw_TrackedObject_1__ctor__0_
  name: TrackedObject
  nameWithType: TrackedObject<T>.TrackedObject
  fullName: DrawnUi.Draw.TrackedObject<T>.TrackedObject
  nameWithType.vb: TrackedObject(Of T).New
  fullName.vb: DrawnUi.Draw.TrackedObject(Of T).New
  name.vb: New
- uid: DrawnUi.Draw.TrackedObject`1.Finalize*
  commentId: Overload:DrawnUi.Draw.TrackedObject`1.Finalize
  href: DrawnUi.Draw.TrackedObject-1.html#DrawnUi_Draw_TrackedObject_1_Finalize
  name: ~TrackedObject
  nameWithType: TrackedObject<T>.~TrackedObject
  fullName: DrawnUi.Draw.TrackedObject<T>.~TrackedObject
  spec.csharp:
  - name: "~"
  - uid: DrawnUi.Draw.TrackedObject`1.Finalize*
    name: TrackedObject
    href: DrawnUi.Draw.TrackedObject-1.html#DrawnUi_Draw_TrackedObject_1_Finalize
- uid: DrawnUi.Draw.TrackedObject`1.Dispose*
  commentId: Overload:DrawnUi.Draw.TrackedObject`1.Dispose
  href: DrawnUi.Draw.TrackedObject-1.html#DrawnUi_Draw_TrackedObject_1_Dispose
  name: Dispose
  nameWithType: TrackedObject<T>.Dispose
  fullName: DrawnUi.Draw.TrackedObject<T>.Dispose
  nameWithType.vb: TrackedObject(Of T).Dispose
  fullName.vb: DrawnUi.Draw.TrackedObject(Of T).Dispose
