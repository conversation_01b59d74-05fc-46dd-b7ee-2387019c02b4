### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.IHasBanner
  commentId: T:DrawnUi.Draw.IHasBanner
  id: IHasBanner
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.IHasBanner.Banner
  - DrawnUi.Draw.IHasBanner.BannerPreloadOrdered
  langs:
  - csharp
  - vb
  name: IHasBanner
  nameWithType: IHasBanner
  fullName: DrawnUi.Draw.IHasBanner
  type: Interface
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Images/IHasBanner.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IHasBanner
    path: ../src/Maui/DrawnUi/Features/Images/IHasBanner.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public interface IHasBanner
    content.vb: Public Interface IHasBanner
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.IHasBanner.Banner
  commentId: P:DrawnUi.Draw.IHasBanner.Banner
  id: Banner
  parent: DrawnUi.Draw.IHasBanner
  langs:
  - csharp
  - vb
  name: Banner
  nameWithType: IHasBanner.Banner
  fullName: DrawnUi.Draw.IHasBanner.Banner
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Images/IHasBanner.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Banner
    path: ../src/Maui/DrawnUi/Features/Images/IHasBanner.cs
    startLine: 7
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Main image
  example: []
  syntax:
    content: string Banner { get; set; }
    parameters: []
    return:
      type: System.String
    content.vb: Property Banner As String
  overload: DrawnUi.Draw.IHasBanner.Banner*
- uid: DrawnUi.Draw.IHasBanner.BannerPreloadOrdered
  commentId: P:DrawnUi.Draw.IHasBanner.BannerPreloadOrdered
  id: BannerPreloadOrdered
  parent: DrawnUi.Draw.IHasBanner
  langs:
  - csharp
  - vb
  name: BannerPreloadOrdered
  nameWithType: IHasBanner.BannerPreloadOrdered
  fullName: DrawnUi.Draw.IHasBanner.BannerPreloadOrdered
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Images/IHasBanner.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: BannerPreloadOrdered
    path: ../src/Maui/DrawnUi/Features/Images/IHasBanner.cs
    startLine: 12
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Indicates that it's already preloading
  example: []
  syntax:
    content: bool BannerPreloadOrdered { get; set; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Property BannerPreloadOrdered As Boolean
  overload: DrawnUi.Draw.IHasBanner.BannerPreloadOrdered*
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.IHasBanner.Banner*
  commentId: Overload:DrawnUi.Draw.IHasBanner.Banner
  href: DrawnUi.Draw.IHasBanner.html#DrawnUi_Draw_IHasBanner_Banner
  name: Banner
  nameWithType: IHasBanner.Banner
  fullName: DrawnUi.Draw.IHasBanner.Banner
- uid: System.String
  commentId: T:System.String
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.string
  name: string
  nameWithType: string
  fullName: string
  nameWithType.vb: String
  fullName.vb: String
  name.vb: String
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Draw.IHasBanner.BannerPreloadOrdered*
  commentId: Overload:DrawnUi.Draw.IHasBanner.BannerPreloadOrdered
  href: DrawnUi.Draw.IHasBanner.html#DrawnUi_Draw_IHasBanner_BannerPreloadOrdered
  name: BannerPreloadOrdered
  nameWithType: IHasBanner.BannerPreloadOrdered
  fullName: DrawnUi.Draw.IHasBanner.BannerPreloadOrdered
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
