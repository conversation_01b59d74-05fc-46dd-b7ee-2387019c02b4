### YamlMime:ManagedReference
items:
- uid: DrawnUi.Controls.GifAnimation
  commentId: T:DrawnUi.Controls.GifAnimation
  id: GifAnimation
  parent: DrawnUi.Controls
  children:
  - DrawnUi.Controls.GifAnimation.Dispose
  - DrawnUi.Controls.GifAnimation.DurationMs
  - DrawnUi.Controls.GifAnimation.Frame
  - DrawnUi.Controls.GifAnimation.Frames
  - DrawnUi.Controls.GifAnimation.FramesPositionsMs
  - DrawnUi.Controls.GifAnimation.GetFrameNumber(System.Double)
  - DrawnUi.Controls.GifAnimation.Height
  - DrawnUi.Controls.GifAnimation.IsDisposed
  - DrawnUi.Controls.GifAnimation.LoadFromStream(System.IO.Stream)
  - DrawnUi.Controls.GifAnimation.LockFrames
  - DrawnUi.Controls.GifAnimation.SeekFrame(System.Int32)
  - DrawnUi.Controls.GifAnimation.TotalFrames
  - DrawnUi.Controls.GifAnimation.Width
  langs:
  - csharp
  - vb
  name: GifAnimation
  nameWithType: GifAnimation
  fullName: DrawnUi.Controls.GifAnimation
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/PlayFrames/GifAnimation.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: GifAnimation
    path: ../src/Maui/DrawnUi/Controls/PlayFrames/GifAnimation.cs
    startLine: 5
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: 'public class GifAnimation : IDisposable'
    content.vb: Public Class GifAnimation Implements IDisposable
  inheritance:
  - System.Object
  implements:
  - System.IDisposable
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Controls.GifAnimation.LockFrames
  commentId: F:DrawnUi.Controls.GifAnimation.LockFrames
  id: LockFrames
  parent: DrawnUi.Controls.GifAnimation
  langs:
  - csharp
  - vb
  name: LockFrames
  nameWithType: GifAnimation.LockFrames
  fullName: DrawnUi.Controls.GifAnimation.LockFrames
  type: Field
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/PlayFrames/GifAnimation.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: LockFrames
    path: ../src/Maui/DrawnUi/Controls/PlayFrames/GifAnimation.cs
    startLine: 7
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: protected object LockFrames
    return:
      type: System.Object
    content.vb: Protected LockFrames As Object
- uid: DrawnUi.Controls.GifAnimation.Frames
  commentId: P:DrawnUi.Controls.GifAnimation.Frames
  id: Frames
  parent: DrawnUi.Controls.GifAnimation
  langs:
  - csharp
  - vb
  name: Frames
  nameWithType: GifAnimation.Frames
  fullName: DrawnUi.Controls.GifAnimation.Frames
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/PlayFrames/GifAnimation.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Frames
    path: ../src/Maui/DrawnUi/Controls/PlayFrames/GifAnimation.cs
    startLine: 9
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: protected SKBitmap[] Frames { get; set; }
    parameters: []
    return:
      type: SkiaSharp.SKBitmap[]
    content.vb: Protected Property Frames As SKBitmap()
  overload: DrawnUi.Controls.GifAnimation.Frames*
- uid: DrawnUi.Controls.GifAnimation.Frame
  commentId: P:DrawnUi.Controls.GifAnimation.Frame
  id: Frame
  parent: DrawnUi.Controls.GifAnimation
  langs:
  - csharp
  - vb
  name: Frame
  nameWithType: GifAnimation.Frame
  fullName: DrawnUi.Controls.GifAnimation.Frame
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/PlayFrames/GifAnimation.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Frame
    path: ../src/Maui/DrawnUi/Controls/PlayFrames/GifAnimation.cs
    startLine: 14
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  summary: Current frame bitmap, can change with SeekFrame method
  example: []
  syntax:
    content: public SKBitmap Frame { get; protected set; }
    parameters: []
    return:
      type: SkiaSharp.SKBitmap
    content.vb: Public Property Frame As SKBitmap
  overload: DrawnUi.Controls.GifAnimation.Frame*
- uid: DrawnUi.Controls.GifAnimation.SeekFrame(System.Int32)
  commentId: M:DrawnUi.Controls.GifAnimation.SeekFrame(System.Int32)
  id: SeekFrame(System.Int32)
  parent: DrawnUi.Controls.GifAnimation
  langs:
  - csharp
  - vb
  name: SeekFrame(int)
  nameWithType: GifAnimation.SeekFrame(int)
  fullName: DrawnUi.Controls.GifAnimation.SeekFrame(int)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/PlayFrames/GifAnimation.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: SeekFrame
    path: ../src/Maui/DrawnUi/Controls/PlayFrames/GifAnimation.cs
    startLine: 20
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  summary: Select frame. If you pass a negative value the last frame will be set.
  example: []
  syntax:
    content: public void SeekFrame(int frame)
    parameters:
    - id: frame
      type: System.Int32
      description: ''
    content.vb: Public Sub SeekFrame(frame As Integer)
  overload: DrawnUi.Controls.GifAnimation.SeekFrame*
  nameWithType.vb: GifAnimation.SeekFrame(Integer)
  fullName.vb: DrawnUi.Controls.GifAnimation.SeekFrame(Integer)
  name.vb: SeekFrame(Integer)
- uid: DrawnUi.Controls.GifAnimation.GetFrameNumber(System.Double)
  commentId: M:DrawnUi.Controls.GifAnimation.GetFrameNumber(System.Double)
  id: GetFrameNumber(System.Double)
  parent: DrawnUi.Controls.GifAnimation
  langs:
  - csharp
  - vb
  name: GetFrameNumber(double)
  nameWithType: GifAnimation.GetFrameNumber(double)
  fullName: DrawnUi.Controls.GifAnimation.GetFrameNumber(double)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/PlayFrames/GifAnimation.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: GetFrameNumber
    path: ../src/Maui/DrawnUi/Controls/PlayFrames/GifAnimation.cs
    startLine: 41
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: public int GetFrameNumber(double msTime)
    parameters:
    - id: msTime
      type: System.Double
    return:
      type: System.Int32
    content.vb: Public Function GetFrameNumber(msTime As Double) As Integer
  overload: DrawnUi.Controls.GifAnimation.GetFrameNumber*
  nameWithType.vb: GifAnimation.GetFrameNumber(Double)
  fullName.vb: DrawnUi.Controls.GifAnimation.GetFrameNumber(Double)
  name.vb: GetFrameNumber(Double)
- uid: DrawnUi.Controls.GifAnimation.Width
  commentId: P:DrawnUi.Controls.GifAnimation.Width
  id: Width
  parent: DrawnUi.Controls.GifAnimation
  langs:
  - csharp
  - vb
  name: Width
  nameWithType: GifAnimation.Width
  fullName: DrawnUi.Controls.GifAnimation.Width
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/PlayFrames/GifAnimation.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Width
    path: ../src/Maui/DrawnUi/Controls/PlayFrames/GifAnimation.cs
    startLine: 65
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: protected int Width { get; set; }
    parameters: []
    return:
      type: System.Int32
    content.vb: Protected Property Width As Integer
  overload: DrawnUi.Controls.GifAnimation.Width*
- uid: DrawnUi.Controls.GifAnimation.Height
  commentId: P:DrawnUi.Controls.GifAnimation.Height
  id: Height
  parent: DrawnUi.Controls.GifAnimation
  langs:
  - csharp
  - vb
  name: Height
  nameWithType: GifAnimation.Height
  fullName: DrawnUi.Controls.GifAnimation.Height
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/PlayFrames/GifAnimation.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Height
    path: ../src/Maui/DrawnUi/Controls/PlayFrames/GifAnimation.cs
    startLine: 67
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: protected int Height { get; set; }
    parameters: []
    return:
      type: System.Int32
    content.vb: Protected Property Height As Integer
  overload: DrawnUi.Controls.GifAnimation.Height*
- uid: DrawnUi.Controls.GifAnimation.DurationMs
  commentId: P:DrawnUi.Controls.GifAnimation.DurationMs
  id: DurationMs
  parent: DrawnUi.Controls.GifAnimation
  langs:
  - csharp
  - vb
  name: DurationMs
  nameWithType: GifAnimation.DurationMs
  fullName: DrawnUi.Controls.GifAnimation.DurationMs
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/PlayFrames/GifAnimation.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: DurationMs
    path: ../src/Maui/DrawnUi/Controls/PlayFrames/GifAnimation.cs
    startLine: 69
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: public int DurationMs { get; protected set; }
    parameters: []
    return:
      type: System.Int32
    content.vb: Public Property DurationMs As Integer
  overload: DrawnUi.Controls.GifAnimation.DurationMs*
- uid: DrawnUi.Controls.GifAnimation.TotalFrames
  commentId: P:DrawnUi.Controls.GifAnimation.TotalFrames
  id: TotalFrames
  parent: DrawnUi.Controls.GifAnimation
  langs:
  - csharp
  - vb
  name: TotalFrames
  nameWithType: GifAnimation.TotalFrames
  fullName: DrawnUi.Controls.GifAnimation.TotalFrames
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/PlayFrames/GifAnimation.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: TotalFrames
    path: ../src/Maui/DrawnUi/Controls/PlayFrames/GifAnimation.cs
    startLine: 71
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: public int TotalFrames { get; protected set; }
    parameters: []
    return:
      type: System.Int32
    content.vb: Public Property TotalFrames As Integer
  overload: DrawnUi.Controls.GifAnimation.TotalFrames*
- uid: DrawnUi.Controls.GifAnimation.FramesPositionsMs
  commentId: P:DrawnUi.Controls.GifAnimation.FramesPositionsMs
  id: FramesPositionsMs
  parent: DrawnUi.Controls.GifAnimation
  langs:
  - csharp
  - vb
  name: FramesPositionsMs
  nameWithType: GifAnimation.FramesPositionsMs
  fullName: DrawnUi.Controls.GifAnimation.FramesPositionsMs
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/PlayFrames/GifAnimation.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: FramesPositionsMs
    path: ../src/Maui/DrawnUi/Controls/PlayFrames/GifAnimation.cs
    startLine: 73
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: protected int[] FramesPositionsMs { get; set; }
    parameters: []
    return:
      type: System.Int32[]
    content.vb: Protected Property FramesPositionsMs As Integer()
  overload: DrawnUi.Controls.GifAnimation.FramesPositionsMs*
- uid: DrawnUi.Controls.GifAnimation.IsDisposed
  commentId: P:DrawnUi.Controls.GifAnimation.IsDisposed
  id: IsDisposed
  parent: DrawnUi.Controls.GifAnimation
  langs:
  - csharp
  - vb
  name: IsDisposed
  nameWithType: GifAnimation.IsDisposed
  fullName: DrawnUi.Controls.GifAnimation.IsDisposed
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/PlayFrames/GifAnimation.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IsDisposed
    path: ../src/Maui/DrawnUi/Controls/PlayFrames/GifAnimation.cs
    startLine: 75
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: public bool IsDisposed { get; set; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property IsDisposed As Boolean
  overload: DrawnUi.Controls.GifAnimation.IsDisposed*
- uid: DrawnUi.Controls.GifAnimation.Dispose
  commentId: M:DrawnUi.Controls.GifAnimation.Dispose
  id: Dispose
  parent: DrawnUi.Controls.GifAnimation
  langs:
  - csharp
  - vb
  name: Dispose()
  nameWithType: GifAnimation.Dispose()
  fullName: DrawnUi.Controls.GifAnimation.Dispose()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/PlayFrames/GifAnimation.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Dispose
    path: ../src/Maui/DrawnUi/Controls/PlayFrames/GifAnimation.cs
    startLine: 77
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  summary: Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
  example: []
  syntax:
    content: public virtual void Dispose()
    content.vb: Public Overridable Sub Dispose()
  overload: DrawnUi.Controls.GifAnimation.Dispose*
  implements:
  - System.IDisposable.Dispose
- uid: DrawnUi.Controls.GifAnimation.LoadFromStream(System.IO.Stream)
  commentId: M:DrawnUi.Controls.GifAnimation.LoadFromStream(System.IO.Stream)
  id: LoadFromStream(System.IO.Stream)
  parent: DrawnUi.Controls.GifAnimation
  langs:
  - csharp
  - vb
  name: LoadFromStream(Stream)
  nameWithType: GifAnimation.LoadFromStream(Stream)
  fullName: DrawnUi.Controls.GifAnimation.LoadFromStream(System.IO.Stream)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/PlayFrames/GifAnimation.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: LoadFromStream
    path: ../src/Maui/DrawnUi/Controls/PlayFrames/GifAnimation.cs
    startLine: 93
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: public void LoadFromStream(Stream stream)
    parameters:
    - id: stream
      type: System.IO.Stream
    content.vb: Public Sub LoadFromStream(stream As Stream)
  overload: DrawnUi.Controls.GifAnimation.LoadFromStream*
references:
- uid: DrawnUi.Controls
  commentId: N:DrawnUi.Controls
  href: DrawnUi.html
  name: DrawnUi.Controls
  nameWithType: DrawnUi.Controls
  fullName: DrawnUi.Controls
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Controls
    name: Controls
    href: DrawnUi.Controls.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Controls
    name: Controls
    href: DrawnUi.Controls.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.IDisposable
  commentId: T:System.IDisposable
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.idisposable
  name: IDisposable
  nameWithType: IDisposable
  fullName: System.IDisposable
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Controls.GifAnimation.Frames*
  commentId: Overload:DrawnUi.Controls.GifAnimation.Frames
  href: DrawnUi.Controls.GifAnimation.html#DrawnUi_Controls_GifAnimation_Frames
  name: Frames
  nameWithType: GifAnimation.Frames
  fullName: DrawnUi.Controls.GifAnimation.Frames
- uid: SkiaSharp.SKBitmap[]
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.skbitmap
  name: SKBitmap[]
  nameWithType: SKBitmap[]
  fullName: SkiaSharp.SKBitmap[]
  nameWithType.vb: SKBitmap()
  fullName.vb: SkiaSharp.SKBitmap()
  name.vb: SKBitmap()
  spec.csharp:
  - uid: SkiaSharp.SKBitmap
    name: SKBitmap
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skbitmap
  - name: '['
  - name: ']'
  spec.vb:
  - uid: SkiaSharp.SKBitmap
    name: SKBitmap
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skbitmap
  - name: (
  - name: )
- uid: DrawnUi.Controls.GifAnimation.Frame*
  commentId: Overload:DrawnUi.Controls.GifAnimation.Frame
  href: DrawnUi.Controls.GifAnimation.html#DrawnUi_Controls_GifAnimation_Frame
  name: Frame
  nameWithType: GifAnimation.Frame
  fullName: DrawnUi.Controls.GifAnimation.Frame
- uid: SkiaSharp.SKBitmap
  commentId: T:SkiaSharp.SKBitmap
  parent: SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.skbitmap
  name: SKBitmap
  nameWithType: SKBitmap
  fullName: SkiaSharp.SKBitmap
- uid: SkiaSharp
  commentId: N:SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp
  name: SkiaSharp
  nameWithType: SkiaSharp
  fullName: SkiaSharp
- uid: DrawnUi.Controls.GifAnimation.SeekFrame*
  commentId: Overload:DrawnUi.Controls.GifAnimation.SeekFrame
  href: DrawnUi.Controls.GifAnimation.html#DrawnUi_Controls_GifAnimation_SeekFrame_System_Int32_
  name: SeekFrame
  nameWithType: GifAnimation.SeekFrame
  fullName: DrawnUi.Controls.GifAnimation.SeekFrame
- uid: System.Int32
  commentId: T:System.Int32
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.int32
  name: int
  nameWithType: int
  fullName: int
  nameWithType.vb: Integer
  fullName.vb: Integer
  name.vb: Integer
- uid: DrawnUi.Controls.GifAnimation.GetFrameNumber*
  commentId: Overload:DrawnUi.Controls.GifAnimation.GetFrameNumber
  href: DrawnUi.Controls.GifAnimation.html#DrawnUi_Controls_GifAnimation_GetFrameNumber_System_Double_
  name: GetFrameNumber
  nameWithType: GifAnimation.GetFrameNumber
  fullName: DrawnUi.Controls.GifAnimation.GetFrameNumber
- uid: System.Double
  commentId: T:System.Double
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.double
  name: double
  nameWithType: double
  fullName: double
  nameWithType.vb: Double
  fullName.vb: Double
  name.vb: Double
- uid: DrawnUi.Controls.GifAnimation.Width*
  commentId: Overload:DrawnUi.Controls.GifAnimation.Width
  href: DrawnUi.Controls.GifAnimation.html#DrawnUi_Controls_GifAnimation_Width
  name: Width
  nameWithType: GifAnimation.Width
  fullName: DrawnUi.Controls.GifAnimation.Width
- uid: DrawnUi.Controls.GifAnimation.Height*
  commentId: Overload:DrawnUi.Controls.GifAnimation.Height
  href: DrawnUi.Controls.GifAnimation.html#DrawnUi_Controls_GifAnimation_Height
  name: Height
  nameWithType: GifAnimation.Height
  fullName: DrawnUi.Controls.GifAnimation.Height
- uid: DrawnUi.Controls.GifAnimation.DurationMs*
  commentId: Overload:DrawnUi.Controls.GifAnimation.DurationMs
  href: DrawnUi.Controls.GifAnimation.html#DrawnUi_Controls_GifAnimation_DurationMs
  name: DurationMs
  nameWithType: GifAnimation.DurationMs
  fullName: DrawnUi.Controls.GifAnimation.DurationMs
- uid: DrawnUi.Controls.GifAnimation.TotalFrames*
  commentId: Overload:DrawnUi.Controls.GifAnimation.TotalFrames
  href: DrawnUi.Controls.GifAnimation.html#DrawnUi_Controls_GifAnimation_TotalFrames
  name: TotalFrames
  nameWithType: GifAnimation.TotalFrames
  fullName: DrawnUi.Controls.GifAnimation.TotalFrames
- uid: DrawnUi.Controls.GifAnimation.FramesPositionsMs*
  commentId: Overload:DrawnUi.Controls.GifAnimation.FramesPositionsMs
  href: DrawnUi.Controls.GifAnimation.html#DrawnUi_Controls_GifAnimation_FramesPositionsMs
  name: FramesPositionsMs
  nameWithType: GifAnimation.FramesPositionsMs
  fullName: DrawnUi.Controls.GifAnimation.FramesPositionsMs
- uid: System.Int32[]
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.int32
  name: int[]
  nameWithType: int[]
  fullName: int[]
  nameWithType.vb: Integer()
  fullName.vb: Integer()
  name.vb: Integer()
  spec.csharp:
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: '['
  - name: ']'
  spec.vb:
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: (
  - name: )
- uid: DrawnUi.Controls.GifAnimation.IsDisposed*
  commentId: Overload:DrawnUi.Controls.GifAnimation.IsDisposed
  href: DrawnUi.Controls.GifAnimation.html#DrawnUi_Controls_GifAnimation_IsDisposed
  name: IsDisposed
  nameWithType: GifAnimation.IsDisposed
  fullName: DrawnUi.Controls.GifAnimation.IsDisposed
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: DrawnUi.Controls.GifAnimation.Dispose*
  commentId: Overload:DrawnUi.Controls.GifAnimation.Dispose
  href: DrawnUi.Controls.GifAnimation.html#DrawnUi_Controls_GifAnimation_Dispose
  name: Dispose
  nameWithType: GifAnimation.Dispose
  fullName: DrawnUi.Controls.GifAnimation.Dispose
- uid: System.IDisposable.Dispose
  commentId: M:System.IDisposable.Dispose
  parent: System.IDisposable
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.idisposable.dispose
  name: Dispose()
  nameWithType: IDisposable.Dispose()
  fullName: System.IDisposable.Dispose()
  spec.csharp:
  - uid: System.IDisposable.Dispose
    name: Dispose
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.idisposable.dispose
  - name: (
  - name: )
  spec.vb:
  - uid: System.IDisposable.Dispose
    name: Dispose
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.idisposable.dispose
  - name: (
  - name: )
- uid: DrawnUi.Controls.GifAnimation.LoadFromStream*
  commentId: Overload:DrawnUi.Controls.GifAnimation.LoadFromStream
  href: DrawnUi.Controls.GifAnimation.html#DrawnUi_Controls_GifAnimation_LoadFromStream_System_IO_Stream_
  name: LoadFromStream
  nameWithType: GifAnimation.LoadFromStream
  fullName: DrawnUi.Controls.GifAnimation.LoadFromStream
- uid: System.IO.Stream
  commentId: T:System.IO.Stream
  parent: System.IO
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.io.stream
  name: Stream
  nameWithType: Stream
  fullName: System.IO.Stream
- uid: System.IO
  commentId: N:System.IO
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.IO
  nameWithType: System.IO
  fullName: System.IO
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.IO
    name: IO
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.io
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.IO
    name: IO
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.io
