### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.DrawnUiStartupSettings
  commentId: T:DrawnUi.Draw.DrawnUiStartupSettings
  id: DrawnUiStartupSettings
  parent: DrawnU<PERSON>.Draw
  children:
  - DrawnUi.Draw.DrawnUiStartupSettings.DesktopWindow
  - DrawnUi.Draw.DrawnUiStartupSettings.Logger
  - DrawnUi.Draw.DrawnUiStartupSettings.MobileIsFullscreen
  - DrawnUi.Draw.DrawnUiStartupSettings.Startup
  - DrawnUi.Draw.DrawnUiStartupSettings.UseDesktopKeyboard
  langs:
  - csharp
  - vb
  name: DrawnUiStartupSettings
  nameWithType: DrawnUiStartupSettings
  fullName: DrawnUi.Draw.DrawnUiStartupSettings
  type: Class
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/DrawnUiStartupSettings.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: DrawnUiStartupSettings
    path: ../src/Shared/Draw/Internals/Models/DrawnUiStartupSettings.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public class DrawnUiStartupSettings
    content.vb: Public Class DrawnUiStartupSettings
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.DrawnUiStartupSettings.Logger
  commentId: P:DrawnUi.Draw.DrawnUiStartupSettings.Logger
  id: Logger
  parent: DrawnUi.Draw.DrawnUiStartupSettings
  langs:
  - csharp
  - vb
  name: Logger
  nameWithType: DrawnUiStartupSettings.Logger
  fullName: DrawnUi.Draw.DrawnUiStartupSettings.Logger
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/DrawnUiStartupSettings.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Logger
    path: ../src/Shared/Draw/Internals/Models/DrawnUiStartupSettings.cs
    startLine: 9
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Will be used by Super.Log calls
  example: []
  syntax:
    content: public ILogger? Logger { get; set; }
    parameters: []
    return:
      type: Microsoft.Extensions.Logging.ILogger
    content.vb: Public Property Logger As ILogger
  overload: DrawnUi.Draw.DrawnUiStartupSettings.Logger*
- uid: DrawnUi.Draw.DrawnUiStartupSettings.DesktopWindow
  commentId: P:DrawnUi.Draw.DrawnUiStartupSettings.DesktopWindow
  id: DesktopWindow
  parent: DrawnUi.Draw.DrawnUiStartupSettings
  langs:
  - csharp
  - vb
  name: DesktopWindow
  nameWithType: DrawnUiStartupSettings.DesktopWindow
  fullName: DrawnUi.Draw.DrawnUiStartupSettings.DesktopWindow
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/DrawnUiStartupSettings.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: DesktopWindow
    path: ../src/Shared/Draw/Internals/Models/DrawnUiStartupSettings.cs
    startLine: 14
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: 'For desktop: if set will affect the app window at startup.'
  example: []
  syntax:
    content: public WindowParameters? DesktopWindow { get; set; }
    parameters: []
    return:
      type: System.Nullable{DrawnUi.Draw.WindowParameters}
    content.vb: Public Property DesktopWindow As WindowParameters?
  overload: DrawnUi.Draw.DrawnUiStartupSettings.DesktopWindow*
- uid: DrawnUi.Draw.DrawnUiStartupSettings.MobileIsFullscreen
  commentId: P:DrawnUi.Draw.DrawnUiStartupSettings.MobileIsFullscreen
  id: MobileIsFullscreen
  parent: DrawnUi.Draw.DrawnUiStartupSettings
  langs:
  - csharp
  - vb
  name: MobileIsFullscreen
  nameWithType: DrawnUiStartupSettings.MobileIsFullscreen
  fullName: DrawnUi.Draw.DrawnUiStartupSettings.MobileIsFullscreen
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/DrawnUiStartupSettings.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: MobileIsFullscreen
    path: ../src/Shared/Draw/Internals/Models/DrawnUiStartupSettings.cs
    startLine: 19
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Avoid safe insets and remove system ui like status bar etc if supported by platform
  example: []
  syntax:
    content: public bool MobileIsFullscreen { get; set; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property MobileIsFullscreen As Boolean
  overload: DrawnUi.Draw.DrawnUiStartupSettings.MobileIsFullscreen*
- uid: DrawnUi.Draw.DrawnUiStartupSettings.UseDesktopKeyboard
  commentId: P:DrawnUi.Draw.DrawnUiStartupSettings.UseDesktopKeyboard
  id: UseDesktopKeyboard
  parent: DrawnUi.Draw.DrawnUiStartupSettings
  langs:
  - csharp
  - vb
  name: UseDesktopKeyboard
  nameWithType: DrawnUiStartupSettings.UseDesktopKeyboard
  fullName: DrawnUi.Draw.DrawnUiStartupSettings.UseDesktopKeyboard
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/DrawnUiStartupSettings.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: UseDesktopKeyboard
    path: ../src/Shared/Draw/Internals/Models/DrawnUiStartupSettings.cs
    startLine: 24
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Listen to desktop keyboard keys with KeyboardManager. Windows and Catalyst available.
  example: []
  syntax:
    content: public bool UseDesktopKeyboard { get; set; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property UseDesktopKeyboard As Boolean
  overload: DrawnUi.Draw.DrawnUiStartupSettings.UseDesktopKeyboard*
- uid: DrawnUi.Draw.DrawnUiStartupSettings.Startup
  commentId: P:DrawnUi.Draw.DrawnUiStartupSettings.Startup
  id: Startup
  parent: DrawnUi.Draw.DrawnUiStartupSettings
  langs:
  - csharp
  - vb
  name: Startup
  nameWithType: DrawnUiStartupSettings.Startup
  fullName: DrawnUi.Draw.DrawnUiStartupSettings.Startup
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/DrawnUiStartupSettings.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Startup
    path: ../src/Shared/Draw/Internals/Models/DrawnUiStartupSettings.cs
    startLine: 29
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Will be executed after DrawnUI is initialized and MAUI App is created.
  example: []
  syntax:
    content: public Action<IServiceProvider> Startup { get; set; }
    parameters: []
    return:
      type: System.Action{System.IServiceProvider}
    content.vb: Public Property Startup As Action(Of IServiceProvider)
  overload: DrawnUi.Draw.DrawnUiStartupSettings.Startup*
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.DrawnUiStartupSettings.Logger*
  commentId: Overload:DrawnUi.Draw.DrawnUiStartupSettings.Logger
  href: DrawnUi.Draw.DrawnUiStartupSettings.html#DrawnUi_Draw_DrawnUiStartupSettings_Logger
  name: Logger
  nameWithType: DrawnUiStartupSettings.Logger
  fullName: DrawnUi.Draw.DrawnUiStartupSettings.Logger
- uid: Microsoft.Extensions.Logging.ILogger
  commentId: T:Microsoft.Extensions.Logging.ILogger
  parent: Microsoft.Extensions.Logging
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.extensions.logging.ilogger
  name: ILogger
  nameWithType: ILogger
  fullName: Microsoft.Extensions.Logging.ILogger
- uid: Microsoft.Extensions.Logging
  commentId: N:Microsoft.Extensions.Logging
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft
  name: Microsoft.Extensions.Logging
  nameWithType: Microsoft.Extensions.Logging
  fullName: Microsoft.Extensions.Logging
  spec.csharp:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Extensions
    name: Extensions
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.extensions
  - name: .
  - uid: Microsoft.Extensions.Logging
    name: Logging
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.extensions.logging
  spec.vb:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Extensions
    name: Extensions
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.extensions
  - name: .
  - uid: Microsoft.Extensions.Logging
    name: Logging
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.extensions.logging
- uid: DrawnUi.Draw.DrawnUiStartupSettings.DesktopWindow*
  commentId: Overload:DrawnUi.Draw.DrawnUiStartupSettings.DesktopWindow
  href: DrawnUi.Draw.DrawnUiStartupSettings.html#DrawnUi_Draw_DrawnUiStartupSettings_DesktopWindow
  name: DesktopWindow
  nameWithType: DrawnUiStartupSettings.DesktopWindow
  fullName: DrawnUi.Draw.DrawnUiStartupSettings.DesktopWindow
- uid: System.Nullable{DrawnUi.Draw.WindowParameters}
  commentId: T:System.Nullable{DrawnUi.Draw.WindowParameters}
  parent: System
  definition: System.Nullable`1
  href: DrawnUi.Draw.WindowParameters.html
  name: WindowParameters?
  nameWithType: WindowParameters?
  fullName: DrawnUi.Draw.WindowParameters?
  spec.csharp:
  - uid: DrawnUi.Draw.WindowParameters
    name: WindowParameters
    href: DrawnUi.Draw.WindowParameters.html
  - name: '?'
  spec.vb:
  - uid: DrawnUi.Draw.WindowParameters
    name: WindowParameters
    href: DrawnUi.Draw.WindowParameters.html
  - name: '?'
- uid: System.Nullable`1
  commentId: T:System.Nullable`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.nullable-1
  name: Nullable<T>
  nameWithType: Nullable<T>
  fullName: System.Nullable<T>
  nameWithType.vb: Nullable(Of T)
  fullName.vb: System.Nullable(Of T)
  name.vb: Nullable(Of T)
  spec.csharp:
  - uid: System.Nullable`1
    name: Nullable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.nullable-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Nullable`1
    name: Nullable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.nullable-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: DrawnUi.Draw.DrawnUiStartupSettings.MobileIsFullscreen*
  commentId: Overload:DrawnUi.Draw.DrawnUiStartupSettings.MobileIsFullscreen
  href: DrawnUi.Draw.DrawnUiStartupSettings.html#DrawnUi_Draw_DrawnUiStartupSettings_MobileIsFullscreen
  name: MobileIsFullscreen
  nameWithType: DrawnUiStartupSettings.MobileIsFullscreen
  fullName: DrawnUi.Draw.DrawnUiStartupSettings.MobileIsFullscreen
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: DrawnUi.Draw.DrawnUiStartupSettings.UseDesktopKeyboard*
  commentId: Overload:DrawnUi.Draw.DrawnUiStartupSettings.UseDesktopKeyboard
  href: DrawnUi.Draw.DrawnUiStartupSettings.html#DrawnUi_Draw_DrawnUiStartupSettings_UseDesktopKeyboard
  name: UseDesktopKeyboard
  nameWithType: DrawnUiStartupSettings.UseDesktopKeyboard
  fullName: DrawnUi.Draw.DrawnUiStartupSettings.UseDesktopKeyboard
- uid: DrawnUi.Draw.DrawnUiStartupSettings.Startup*
  commentId: Overload:DrawnUi.Draw.DrawnUiStartupSettings.Startup
  href: DrawnUi.Draw.DrawnUiStartupSettings.html#DrawnUi_Draw_DrawnUiStartupSettings_Startup
  name: Startup
  nameWithType: DrawnUiStartupSettings.Startup
  fullName: DrawnUi.Draw.DrawnUiStartupSettings.Startup
- uid: System.Action{System.IServiceProvider}
  commentId: T:System.Action{System.IServiceProvider}
  parent: System
  definition: System.Action`1
  href: https://learn.microsoft.com/dotnet/api/system.action-1
  name: Action<IServiceProvider>
  nameWithType: Action<IServiceProvider>
  fullName: System.Action<System.IServiceProvider>
  nameWithType.vb: Action(Of IServiceProvider)
  fullName.vb: System.Action(Of System.IServiceProvider)
  name.vb: Action(Of IServiceProvider)
  spec.csharp:
  - uid: System.Action`1
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-1
  - name: <
  - uid: System.IServiceProvider
    name: IServiceProvider
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.iserviceprovider
  - name: '>'
  spec.vb:
  - uid: System.Action`1
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-1
  - name: (
  - name: Of
  - name: " "
  - uid: System.IServiceProvider
    name: IServiceProvider
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.iserviceprovider
  - name: )
- uid: System.Action`1
  commentId: T:System.Action`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.action-1
  name: Action<T>
  nameWithType: Action<T>
  fullName: System.Action<T>
  nameWithType.vb: Action(Of T)
  fullName.vb: System.Action(Of T)
  name.vb: Action(Of T)
  spec.csharp:
  - uid: System.Action`1
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Action`1
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
