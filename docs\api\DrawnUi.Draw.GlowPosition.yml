### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.GlowPosition
  commentId: T:DrawnUi.Draw.GlowPosition
  id: GlowPosition
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.GlowPosition.Bottom
  - DrawnUi.Draw.GlowPosition.Top
  langs:
  - csharp
  - vb
  name: GlowPosition
  nameWithType: GlowPosition
  fullName: DrawnUi.Draw.GlowPosition
  type: Enum
  source:
    remote:
      path: src/Shared/Features/Animators/EdgeGlowAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: GlowPosition
    path: ../src/Shared/Features/Animators/EdgeGlowAnimator.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public enum GlowPosition
    content.vb: Public Enum GlowPosition
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.GlowPosition.Top
  commentId: F:DrawnUi.Draw.GlowPosition.Top
  id: Top
  parent: DrawnUi.Draw.GlowPosition
  langs:
  - csharp
  - vb
  name: Top
  nameWithType: GlowPosition.Top
  fullName: DrawnUi.Draw.GlowPosition.Top
  type: Field
  source:
    remote:
      path: src/Shared/Features/Animators/EdgeGlowAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Top
    path: ../src/Shared/Features/Animators/EdgeGlowAnimator.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: Top = 0
    return:
      type: DrawnUi.Draw.GlowPosition
- uid: DrawnUi.Draw.GlowPosition.Bottom
  commentId: F:DrawnUi.Draw.GlowPosition.Bottom
  id: Bottom
  parent: DrawnUi.Draw.GlowPosition
  langs:
  - csharp
  - vb
  name: Bottom
  nameWithType: GlowPosition.Bottom
  fullName: DrawnUi.Draw.GlowPosition.Bottom
  type: Field
  source:
    remote:
      path: src/Shared/Features/Animators/EdgeGlowAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Bottom
    path: ../src/Shared/Features/Animators/EdgeGlowAnimator.cs
    startLine: 5
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: Bottom = 1
    return:
      type: DrawnUi.Draw.GlowPosition
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.GlowPosition
  commentId: T:DrawnUi.Draw.GlowPosition
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.GlowPosition.html
  name: GlowPosition
  nameWithType: GlowPosition
  fullName: DrawnUi.Draw.GlowPosition
