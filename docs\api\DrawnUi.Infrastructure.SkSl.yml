### YamlMime:ManagedReference
items:
- uid: DrawnUi.Infrastructure.SkSl
  commentId: T:DrawnUi.Infrastructure.SkSl
  id: SkSl
  parent: DrawnUi.Infrastructure
  children:
  - DrawnUi.Infrastructure.SkSl.Compile(System.String)
  - DrawnUi.Infrastructure.SkSl.Compile(System.String,System.String,System.Boolean)
  - DrawnUi.Infrastructure.SkSl.CompiledCache
  - DrawnUi.Infrastructure.SkSl.CreateShader(SkiaSharp.SKRuntimeEffect,SkiaSharp.SKRuntimeEffectUniforms,System.Collections.Generic.Dictionary{System.String,SkiaSharp.SKShader})
  - DrawnUi.Infrastructure.SkSl.LoadFromResources(System.String)
  - DrawnUi.Infrastructure.SkSl.LoadFromResourcesAsync(System.String)
  langs:
  - csharp
  - vb
  name: SkSl
  nameWithType: SkSl
  fullName: DrawnUi.Infrastructure.SkSl
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Shaders/SKSL.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: SkSl
    path: ../src/Maui/DrawnUi/Features/Shaders/SKSL.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public static class SkSl
    content.vb: Public Module SkSl
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
- uid: DrawnUi.Infrastructure.SkSl.LoadFromResourcesAsync(System.String)
  commentId: M:DrawnUi.Infrastructure.SkSl.LoadFromResourcesAsync(System.String)
  id: LoadFromResourcesAsync(System.String)
  parent: DrawnUi.Infrastructure.SkSl
  langs:
  - csharp
  - vb
  name: LoadFromResourcesAsync(string)
  nameWithType: SkSl.LoadFromResourcesAsync(string)
  fullName: DrawnUi.Infrastructure.SkSl.LoadFromResourcesAsync(string)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Shaders/SKSL.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: LoadFromResourcesAsync
    path: ../src/Maui/DrawnUi/Features/Shaders/SKSL.cs
    startLine: 7
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public static Task<string> LoadFromResourcesAsync(string fileName)
    parameters:
    - id: fileName
      type: System.String
    return:
      type: System.Threading.Tasks.Task{System.String}
    content.vb: Public Shared Function LoadFromResourcesAsync(fileName As String) As Task(Of String)
  overload: DrawnUi.Infrastructure.SkSl.LoadFromResourcesAsync*
  nameWithType.vb: SkSl.LoadFromResourcesAsync(String)
  fullName.vb: DrawnUi.Infrastructure.SkSl.LoadFromResourcesAsync(String)
  name.vb: LoadFromResourcesAsync(String)
- uid: DrawnUi.Infrastructure.SkSl.LoadFromResources(System.String)
  commentId: M:DrawnUi.Infrastructure.SkSl.LoadFromResources(System.String)
  id: LoadFromResources(System.String)
  parent: DrawnUi.Infrastructure.SkSl
  langs:
  - csharp
  - vb
  name: LoadFromResources(string)
  nameWithType: SkSl.LoadFromResources(string)
  fullName: DrawnUi.Infrastructure.SkSl.LoadFromResources(string)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Shaders/SKSL.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: LoadFromResources
    path: ../src/Maui/DrawnUi/Features/Shaders/SKSL.cs
    startLine: 15
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public static string LoadFromResources(string fileName)
    parameters:
    - id: fileName
      type: System.String
    return:
      type: System.String
    content.vb: Public Shared Function LoadFromResources(fileName As String) As String
  overload: DrawnUi.Infrastructure.SkSl.LoadFromResources*
  nameWithType.vb: SkSl.LoadFromResources(String)
  fullName.vb: DrawnUi.Infrastructure.SkSl.LoadFromResources(String)
  name.vb: LoadFromResources(String)
- uid: DrawnUi.Infrastructure.SkSl.CompiledCache
  commentId: F:DrawnUi.Infrastructure.SkSl.CompiledCache
  id: CompiledCache
  parent: DrawnUi.Infrastructure.SkSl
  langs:
  - csharp
  - vb
  name: CompiledCache
  nameWithType: SkSl.CompiledCache
  fullName: DrawnUi.Infrastructure.SkSl.CompiledCache
  type: Field
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Shaders/SKSL.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: CompiledCache
    path: ../src/Maui/DrawnUi/Features/Shaders/SKSL.cs
    startLine: 23
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public static Dictionary<string, SKRuntimeEffect> CompiledCache
    return:
      type: System.Collections.Generic.Dictionary{System.String,SkiaSharp.SKRuntimeEffect}
    content.vb: Public Shared CompiledCache As Dictionary(Of String, SKRuntimeEffect)
- uid: DrawnUi.Infrastructure.SkSl.Compile(System.String)
  commentId: M:DrawnUi.Infrastructure.SkSl.Compile(System.String)
  id: Compile(System.String)
  parent: DrawnUi.Infrastructure.SkSl
  langs:
  - csharp
  - vb
  name: Compile(string)
  nameWithType: SkSl.Compile(string)
  fullName: DrawnUi.Infrastructure.SkSl.Compile(string)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Shaders/SKSL.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Compile
    path: ../src/Maui/DrawnUi/Features/Shaders/SKSL.cs
    startLine: 30
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  summary: Will compile your SKSL shader code into SKRuntimeEffect.
  example: []
  syntax:
    content: public static SKRuntimeEffect Compile(string shaderCode)
    parameters:
    - id: shaderCode
      type: System.String
      description: ''
    return:
      type: SkiaSharp.SKRuntimeEffect
      description: ''
    content.vb: Public Shared Function Compile(shaderCode As String) As SKRuntimeEffect
  overload: DrawnUi.Infrastructure.SkSl.Compile*
  nameWithType.vb: SkSl.Compile(String)
  fullName.vb: DrawnUi.Infrastructure.SkSl.Compile(String)
  name.vb: Compile(String)
- uid: DrawnUi.Infrastructure.SkSl.Compile(System.String,System.String,System.Boolean)
  commentId: M:DrawnUi.Infrastructure.SkSl.Compile(System.String,System.String,System.Boolean)
  id: Compile(System.String,System.String,System.Boolean)
  parent: DrawnUi.Infrastructure.SkSl
  langs:
  - csharp
  - vb
  name: Compile(string, string, bool)
  nameWithType: SkSl.Compile(string, string, bool)
  fullName: DrawnUi.Infrastructure.SkSl.Compile(string, string, bool)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Shaders/SKSL.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Compile
    path: ../src/Maui/DrawnUi/Features/Shaders/SKSL.cs
    startLine: 42
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  summary: >-
    Will compile your SKSL shader code into SKRuntimeEffect.

    The filename parameter is used for debugging and caching. Do not forget to disable caching if you edit/change shader code at runtime.
  example: []
  syntax:
    content: public static SKRuntimeEffect Compile(string shaderCode, string filename, bool useCache = true)
    parameters:
    - id: shaderCode
      type: System.String
      description: ''
    - id: filename
      type: System.String
      description: ''
    - id: useCache
      type: System.Boolean
    return:
      type: SkiaSharp.SKRuntimeEffect
      description: ''
    content.vb: Public Shared Function Compile(shaderCode As String, filename As String, useCache As Boolean = True) As SKRuntimeEffect
  overload: DrawnUi.Infrastructure.SkSl.Compile*
  nameWithType.vb: SkSl.Compile(String, String, Boolean)
  fullName.vb: DrawnUi.Infrastructure.SkSl.Compile(String, String, Boolean)
  name.vb: Compile(String, String, Boolean)
- uid: DrawnUi.Infrastructure.SkSl.CreateShader(SkiaSharp.SKRuntimeEffect,SkiaSharp.SKRuntimeEffectUniforms,System.Collections.Generic.Dictionary{System.String,SkiaSharp.SKShader})
  commentId: M:DrawnUi.Infrastructure.SkSl.CreateShader(SkiaSharp.SKRuntimeEffect,SkiaSharp.SKRuntimeEffectUniforms,System.Collections.Generic.Dictionary{System.String,SkiaSharp.SKShader})
  id: CreateShader(SkiaSharp.SKRuntimeEffect,SkiaSharp.SKRuntimeEffectUniforms,System.Collections.Generic.Dictionary{System.String,SkiaSharp.SKShader})
  parent: DrawnUi.Infrastructure.SkSl
  langs:
  - csharp
  - vb
  name: CreateShader(SKRuntimeEffect, SKRuntimeEffectUniforms, Dictionary<string, SKShader>)
  nameWithType: SkSl.CreateShader(SKRuntimeEffect, SKRuntimeEffectUniforms, Dictionary<string, SKShader>)
  fullName: DrawnUi.Infrastructure.SkSl.CreateShader(SkiaSharp.SKRuntimeEffect, SkiaSharp.SKRuntimeEffectUniforms, System.Collections.Generic.Dictionary<string, SkiaSharp.SKShader>)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Shaders/SKSL.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: CreateShader
    path: ../src/Maui/DrawnUi/Features/Shaders/SKSL.cs
    startLine: 113
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public static SKShader CreateShader(SKRuntimeEffect compiled, SKRuntimeEffectUniforms uniforms, Dictionary<string, SKShader> textures)
    parameters:
    - id: compiled
      type: SkiaSharp.SKRuntimeEffect
    - id: uniforms
      type: SkiaSharp.SKRuntimeEffectUniforms
    - id: textures
      type: System.Collections.Generic.Dictionary{System.String,SkiaSharp.SKShader}
    return:
      type: SkiaSharp.SKShader
    content.vb: Public Shared Function CreateShader(compiled As SKRuntimeEffect, uniforms As SKRuntimeEffectUniforms, textures As Dictionary(Of String, SKShader)) As SKShader
  overload: DrawnUi.Infrastructure.SkSl.CreateShader*
  nameWithType.vb: SkSl.CreateShader(SKRuntimeEffect, SKRuntimeEffectUniforms, Dictionary(Of String, SKShader))
  fullName.vb: DrawnUi.Infrastructure.SkSl.CreateShader(SkiaSharp.SKRuntimeEffect, SkiaSharp.SKRuntimeEffectUniforms, System.Collections.Generic.Dictionary(Of String, SkiaSharp.SKShader))
  name.vb: CreateShader(SKRuntimeEffect, SKRuntimeEffectUniforms, Dictionary(Of String, SKShader))
references:
- uid: DrawnUi.Infrastructure
  commentId: N:DrawnUi.Infrastructure
  href: DrawnUi.html
  name: DrawnUi.Infrastructure
  nameWithType: DrawnUi.Infrastructure
  fullName: DrawnUi.Infrastructure
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Infrastructure
    name: Infrastructure
    href: DrawnUi.Infrastructure.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Infrastructure
    name: Infrastructure
    href: DrawnUi.Infrastructure.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Infrastructure.SkSl.LoadFromResourcesAsync*
  commentId: Overload:DrawnUi.Infrastructure.SkSl.LoadFromResourcesAsync
  href: DrawnUi.Infrastructure.SkSl.html#DrawnUi_Infrastructure_SkSl_LoadFromResourcesAsync_System_String_
  name: LoadFromResourcesAsync
  nameWithType: SkSl.LoadFromResourcesAsync
  fullName: DrawnUi.Infrastructure.SkSl.LoadFromResourcesAsync
- uid: System.String
  commentId: T:System.String
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.string
  name: string
  nameWithType: string
  fullName: string
  nameWithType.vb: String
  fullName.vb: String
  name.vb: String
- uid: System.Threading.Tasks.Task{System.String}
  commentId: T:System.Threading.Tasks.Task{System.String}
  parent: System.Threading.Tasks
  definition: System.Threading.Tasks.Task`1
  href: https://learn.microsoft.com/dotnet/api/system.threading.tasks.task-1
  name: Task<string>
  nameWithType: Task<string>
  fullName: System.Threading.Tasks.Task<string>
  nameWithType.vb: Task(Of String)
  fullName.vb: System.Threading.Tasks.Task(Of String)
  name.vb: Task(Of String)
  spec.csharp:
  - uid: System.Threading.Tasks.Task`1
    name: Task
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading.tasks.task-1
  - name: <
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: '>'
  spec.vb:
  - uid: System.Threading.Tasks.Task`1
    name: Task
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading.tasks.task-1
  - name: (
  - name: Of
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: System.Threading.Tasks.Task`1
  commentId: T:System.Threading.Tasks.Task`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.threading.tasks.task-1
  name: Task<TResult>
  nameWithType: Task<TResult>
  fullName: System.Threading.Tasks.Task<TResult>
  nameWithType.vb: Task(Of TResult)
  fullName.vb: System.Threading.Tasks.Task(Of TResult)
  name.vb: Task(Of TResult)
  spec.csharp:
  - uid: System.Threading.Tasks.Task`1
    name: Task
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading.tasks.task-1
  - name: <
  - name: TResult
  - name: '>'
  spec.vb:
  - uid: System.Threading.Tasks.Task`1
    name: Task
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading.tasks.task-1
  - name: (
  - name: Of
  - name: " "
  - name: TResult
  - name: )
- uid: System.Threading.Tasks
  commentId: N:System.Threading.Tasks
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Threading.Tasks
  nameWithType: System.Threading.Tasks
  fullName: System.Threading.Tasks
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Threading
    name: Threading
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading
  - name: .
  - uid: System.Threading.Tasks
    name: Tasks
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading.tasks
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Threading
    name: Threading
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading
  - name: .
  - uid: System.Threading.Tasks
    name: Tasks
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading.tasks
- uid: DrawnUi.Infrastructure.SkSl.LoadFromResources*
  commentId: Overload:DrawnUi.Infrastructure.SkSl.LoadFromResources
  href: DrawnUi.Infrastructure.SkSl.html#DrawnUi_Infrastructure_SkSl_LoadFromResources_System_String_
  name: LoadFromResources
  nameWithType: SkSl.LoadFromResources
  fullName: DrawnUi.Infrastructure.SkSl.LoadFromResources
- uid: System.Collections.Generic.Dictionary{System.String,SkiaSharp.SKRuntimeEffect}
  commentId: T:System.Collections.Generic.Dictionary{System.String,SkiaSharp.SKRuntimeEffect}
  parent: System.Collections.Generic
  definition: System.Collections.Generic.Dictionary`2
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2
  name: Dictionary<string, SKRuntimeEffect>
  nameWithType: Dictionary<string, SKRuntimeEffect>
  fullName: System.Collections.Generic.Dictionary<string, SkiaSharp.SKRuntimeEffect>
  nameWithType.vb: Dictionary(Of String, SKRuntimeEffect)
  fullName.vb: System.Collections.Generic.Dictionary(Of String, SkiaSharp.SKRuntimeEffect)
  name.vb: Dictionary(Of String, SKRuntimeEffect)
  spec.csharp:
  - uid: System.Collections.Generic.Dictionary`2
    name: Dictionary
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2
  - name: <
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: SkiaSharp.SKRuntimeEffect
    name: SKRuntimeEffect
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skruntimeeffect
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.Dictionary`2
    name: Dictionary
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2
  - name: (
  - name: Of
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: SkiaSharp.SKRuntimeEffect
    name: SKRuntimeEffect
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skruntimeeffect
  - name: )
- uid: System.Collections.Generic.Dictionary`2
  commentId: T:System.Collections.Generic.Dictionary`2
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2
  name: Dictionary<TKey, TValue>
  nameWithType: Dictionary<TKey, TValue>
  fullName: System.Collections.Generic.Dictionary<TKey, TValue>
  nameWithType.vb: Dictionary(Of TKey, TValue)
  fullName.vb: System.Collections.Generic.Dictionary(Of TKey, TValue)
  name.vb: Dictionary(Of TKey, TValue)
  spec.csharp:
  - uid: System.Collections.Generic.Dictionary`2
    name: Dictionary
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2
  - name: <
  - name: TKey
  - name: ','
  - name: " "
  - name: TValue
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.Dictionary`2
    name: Dictionary
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2
  - name: (
  - name: Of
  - name: " "
  - name: TKey
  - name: ','
  - name: " "
  - name: TValue
  - name: )
- uid: System.Collections.Generic
  commentId: N:System.Collections.Generic
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Collections.Generic
  nameWithType: System.Collections.Generic
  fullName: System.Collections.Generic
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Generic
    name: Generic
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Generic
    name: Generic
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic
- uid: DrawnUi.Infrastructure.SkSl.Compile*
  commentId: Overload:DrawnUi.Infrastructure.SkSl.Compile
  href: DrawnUi.Infrastructure.SkSl.html#DrawnUi_Infrastructure_SkSl_Compile_System_String_
  name: Compile
  nameWithType: SkSl.Compile
  fullName: DrawnUi.Infrastructure.SkSl.Compile
- uid: SkiaSharp.SKRuntimeEffect
  commentId: T:SkiaSharp.SKRuntimeEffect
  parent: SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.skruntimeeffect
  name: SKRuntimeEffect
  nameWithType: SKRuntimeEffect
  fullName: SkiaSharp.SKRuntimeEffect
- uid: SkiaSharp
  commentId: N:SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp
  name: SkiaSharp
  nameWithType: SkiaSharp
  fullName: SkiaSharp
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: DrawnUi.Infrastructure.SkSl.CreateShader*
  commentId: Overload:DrawnUi.Infrastructure.SkSl.CreateShader
  href: DrawnUi.Infrastructure.SkSl.html#DrawnUi_Infrastructure_SkSl_CreateShader_SkiaSharp_SKRuntimeEffect_SkiaSharp_SKRuntimeEffectUniforms_System_Collections_Generic_Dictionary_System_String_SkiaSharp_SKShader__
  name: CreateShader
  nameWithType: SkSl.CreateShader
  fullName: DrawnUi.Infrastructure.SkSl.CreateShader
- uid: SkiaSharp.SKRuntimeEffectUniforms
  commentId: T:SkiaSharp.SKRuntimeEffectUniforms
  parent: SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.skruntimeeffectuniforms
  name: SKRuntimeEffectUniforms
  nameWithType: SKRuntimeEffectUniforms
  fullName: SkiaSharp.SKRuntimeEffectUniforms
- uid: System.Collections.Generic.Dictionary{System.String,SkiaSharp.SKShader}
  commentId: T:System.Collections.Generic.Dictionary{System.String,SkiaSharp.SKShader}
  parent: System.Collections.Generic
  definition: System.Collections.Generic.Dictionary`2
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2
  name: Dictionary<string, SKShader>
  nameWithType: Dictionary<string, SKShader>
  fullName: System.Collections.Generic.Dictionary<string, SkiaSharp.SKShader>
  nameWithType.vb: Dictionary(Of String, SKShader)
  fullName.vb: System.Collections.Generic.Dictionary(Of String, SkiaSharp.SKShader)
  name.vb: Dictionary(Of String, SKShader)
  spec.csharp:
  - uid: System.Collections.Generic.Dictionary`2
    name: Dictionary
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2
  - name: <
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: SkiaSharp.SKShader
    name: SKShader
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skshader
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.Dictionary`2
    name: Dictionary
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2
  - name: (
  - name: Of
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: SkiaSharp.SKShader
    name: SKShader
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skshader
  - name: )
- uid: SkiaSharp.SKShader
  commentId: T:SkiaSharp.SKShader
  parent: SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.skshader
  name: SKShader
  nameWithType: SKShader
  fullName: SkiaSharp.SKShader
