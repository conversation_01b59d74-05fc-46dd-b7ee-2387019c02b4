### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.SvgSpan
  commentId: T:DrawnUi.Draw.SvgSpan
  id: SvgSpan
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.SvgSpan.#ctor
  - DrawnUi.Draw.SvgSpan.Control
  - DrawnUi.Draw.SvgSpan.Dispose
  - DrawnUi.Draw.SvgSpan.Height
  - DrawnUi.Draw.SvgSpan.Source
  - DrawnUi.Draw.SvgSpan.TintColor
  - DrawnUi.Draw.SvgSpan.VerticalAlignement
  - DrawnUi.Draw.SvgSpan.Width
  langs:
  - csharp
  - vb
  name: SvgSpan
  nameWithType: SvgSpan
  fullName: DrawnUi.Draw.SvgSpan
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/SvgSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: SvgSpan
    path: ../src/Maui/DrawnUi/Draw/Text/SvgSpan.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: 'public class SvgSpan : TextSpan, INotifyPropertyChanged, IElementController, IVisualTreeElement, IEffectControlProvider, IToolTipElement, IContextFlyoutElement, IElement, IDisposable, IDrawnTextSpan'
    content.vb: Public Class SvgSpan Inherits TextSpan Implements INotifyPropertyChanged, IElementController, IVisualTreeElement, IEffectControlProvider, IToolTipElement, IContextFlyoutElement, IElement, IDisposable, IDrawnTextSpan
  inheritance:
  - System.Object
  - Microsoft.Maui.Controls.BindableObject
  - Microsoft.Maui.Controls.Element
  - DrawnUi.Draw.TextSpan
  implements:
  - System.ComponentModel.INotifyPropertyChanged
  - Microsoft.Maui.Controls.IElementController
  - Microsoft.Maui.IVisualTreeElement
  - Microsoft.Maui.Controls.IEffectControlProvider
  - Microsoft.Maui.IToolTipElement
  - Microsoft.Maui.IContextFlyoutElement
  - Microsoft.Maui.IElement
  - System.IDisposable
  - DrawnUi.Draw.IDrawnTextSpan
  inheritedMembers:
  - DrawnUi.Draw.TextSpan.TextProperty
  - DrawnUi.Draw.TextSpan.Text
  - DrawnUi.Draw.TextSpan.Default
  - DrawnUi.Draw.TextSpan.DebugString
  - DrawnUi.Draw.TextSpan.RenderingScale
  - DrawnUi.Draw.TextSpan.Glyphs
  - DrawnUi.Draw.TextSpan.Shape
  - DrawnUi.Draw.TextSpan.TextFiltered
  - DrawnUi.Draw.TextSpan.CheckGlyphsCanBeRendered
  - DrawnUi.Draw.TextSpan.SetupPaint(System.Double,SkiaSharp.SKPaint)
  - DrawnUi.Draw.TextSpan.HasSetFont
  - DrawnUi.Draw.TextSpan.HasSetSize
  - DrawnUi.Draw.TextSpan.HasSetColor
  - DrawnUi.Draw.TextSpan.Paint
  - DrawnUi.Draw.TextSpan.TypeFace
  - DrawnUi.Draw.TextSpan.NeedShape
  - DrawnUi.Draw.TextSpan.HasDecorations
  - DrawnUi.Draw.TextSpan.Underline
  - DrawnUi.Draw.TextSpan.UnderlineWidth
  - DrawnUi.Draw.TextSpan.Strikeout
  - DrawnUi.Draw.TextSpan.StrikeoutWidth
  - DrawnUi.Draw.TextSpan.StrikeoutColor
  - DrawnUi.Draw.TextSpan.HasTapHandler
  - DrawnUi.Draw.TextSpan.ForceCaptureInput
  - DrawnUi.Draw.TextSpan.HitIsInside(System.Single,System.Single)
  - DrawnUi.Draw.TextSpan.FireTap
  - DrawnUi.Draw.TextSpan.DrawingOffset
  - DrawnUi.Draw.TextSpan.Tag
  - DrawnUi.Draw.TextSpan.Rects
  - DrawnUi.Draw.TextSpan.CommandTapped
  - DrawnUi.Draw.TextSpan.Tapped
  - DrawnUi.Draw.TextSpan.ParentControl
  - DrawnUi.Draw.TextSpan.OnPropertyChanged(System.String)
  - DrawnUi.Draw.TextSpan._fontAutoSet
  - DrawnUi.Draw.TextSpan.UpdateFont
  - DrawnUi.Draw.TextSpan.FontFamily
  - DrawnUi.Draw.TextSpan.LineSpacing
  - DrawnUi.Draw.TextSpan.LineHeight
  - DrawnUi.Draw.TextSpan.FontWeight
  - DrawnUi.Draw.TextSpan.TextColorProperty
  - DrawnUi.Draw.TextSpan.TextColor
  - DrawnUi.Draw.TextSpan.BackgroundColor
  - DrawnUi.Draw.TextSpan.ParagraphColor
  - DrawnUi.Draw.TextSpan.FontSizeProperty
  - DrawnUi.Draw.TextSpan.FontSize
  - DrawnUi.Draw.TextSpan.IsItalic
  - DrawnUi.Draw.TextSpan.IsBold
  - DrawnUi.Draw.TextSpan.AutoFindFont
  - DrawnUi.Draw.TextSpan.FontDetectedWith
  - Microsoft.Maui.Controls.Element.AutomationIdProperty
  - Microsoft.Maui.Controls.Element.ClassIdProperty
  - Microsoft.Maui.Controls.Element.InsertLogicalChild(System.Int32,Microsoft.Maui.Controls.Element)
  - Microsoft.Maui.Controls.Element.AddLogicalChild(Microsoft.Maui.Controls.Element)
  - Microsoft.Maui.Controls.Element.RemoveLogicalChild(Microsoft.Maui.Controls.Element)
  - Microsoft.Maui.Controls.Element.ClearLogicalChildren
  - Microsoft.Maui.Controls.Element.FindByName(System.String)
  - Microsoft.Maui.Controls.Element.RemoveDynamicResource(Microsoft.Maui.Controls.BindableProperty)
  - Microsoft.Maui.Controls.Element.SetDynamicResource(Microsoft.Maui.Controls.BindableProperty,System.String)
  - Microsoft.Maui.Controls.Element.OnBindingContextChanged
  - Microsoft.Maui.Controls.Element.OnChildAdded(Microsoft.Maui.Controls.Element)
  - Microsoft.Maui.Controls.Element.OnChildRemoved(Microsoft.Maui.Controls.Element,System.Int32)
  - Microsoft.Maui.Controls.Element.OnParentSet
  - Microsoft.Maui.Controls.Element.OnParentChanging(Microsoft.Maui.Controls.ParentChangingEventArgs)
  - Microsoft.Maui.Controls.Element.OnParentChanged
  - Microsoft.Maui.Controls.Element.OnHandlerChanging(Microsoft.Maui.Controls.HandlerChangingEventArgs)
  - Microsoft.Maui.Controls.Element.OnHandlerChanged
  - Microsoft.Maui.Controls.Element.MapAutomationPropertiesIsInAccessibleTree(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
  - Microsoft.Maui.Controls.Element.MapAutomationPropertiesExcludedWithChildren(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
  - Microsoft.Maui.Controls.Element.AutomationId
  - Microsoft.Maui.Controls.Element.ClassId
  - Microsoft.Maui.Controls.Element.Effects
  - Microsoft.Maui.Controls.Element.Id
  - Microsoft.Maui.Controls.Element.StyleId
  - Microsoft.Maui.Controls.Element.Parent
  - Microsoft.Maui.Controls.Element.Handler
  - Microsoft.Maui.Controls.Element.ChildAdded
  - Microsoft.Maui.Controls.Element.ChildRemoved
  - Microsoft.Maui.Controls.Element.DescendantAdded
  - Microsoft.Maui.Controls.Element.DescendantRemoved
  - Microsoft.Maui.Controls.Element.ParentChanging
  - Microsoft.Maui.Controls.Element.ParentChanged
  - Microsoft.Maui.Controls.Element.HandlerChanging
  - Microsoft.Maui.Controls.Element.HandlerChanged
  - Microsoft.Maui.Controls.BindableObject.BindingContextProperty
  - Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindableProperty)
  - Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindablePropertyKey)
  - Microsoft.Maui.Controls.BindableObject.GetValue(Microsoft.Maui.Controls.BindableProperty)
  - Microsoft.Maui.Controls.BindableObject.IsSet(Microsoft.Maui.Controls.BindableProperty)
  - Microsoft.Maui.Controls.BindableObject.RemoveBinding(Microsoft.Maui.Controls.BindableProperty)
  - Microsoft.Maui.Controls.BindableObject.SetBinding(Microsoft.Maui.Controls.BindableProperty,Microsoft.Maui.Controls.BindingBase)
  - Microsoft.Maui.Controls.BindableObject.ApplyBindings
  - Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(System.String)
  - Microsoft.Maui.Controls.BindableObject.UnapplyBindings
  - Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty,System.Object)
  - Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey,System.Object)
  - Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindableProperty)
  - Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindablePropertyKey)
  - Microsoft.Maui.Controls.BindableObject.Dispatcher
  - Microsoft.Maui.Controls.BindableObject.BindingContext
  - Microsoft.Maui.Controls.BindableObject.PropertyChanged
  - Microsoft.Maui.Controls.BindableObject.PropertyChanging
  - Microsoft.Maui.Controls.BindableObject.BindingContextChanged
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - Microsoft.Maui.Controls.Element.DrawnUi.Draw.StaticResourcesExtensions.FindParent``1
  - Microsoft.Maui.Controls.Element.DrawnUi.Extensions.InternalExtensions.FindMauiContext(System.Boolean)
  - Microsoft.Maui.Controls.Element.DrawnUi.Extensions.InternalExtensions.GetParentsPath
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.SvgSpan.#ctor
  commentId: M:DrawnUi.Draw.SvgSpan.#ctor
  id: '#ctor'
  parent: DrawnUi.Draw.SvgSpan
  langs:
  - csharp
  - vb
  name: SvgSpan()
  nameWithType: SvgSpan.SvgSpan()
  fullName: DrawnUi.Draw.SvgSpan.SvgSpan()
  type: Constructor
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/SvgSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Maui/DrawnUi/Draw/Text/SvgSpan.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public SvgSpan()
    content.vb: Public Sub New()
  overload: DrawnUi.Draw.SvgSpan.#ctor*
  nameWithType.vb: SvgSpan.New()
  fullName.vb: DrawnUi.Draw.SvgSpan.New()
  name.vb: New()
- uid: DrawnUi.Draw.SvgSpan.VerticalAlignement
  commentId: P:DrawnUi.Draw.SvgSpan.VerticalAlignement
  id: VerticalAlignement
  parent: DrawnUi.Draw.SvgSpan
  langs:
  - csharp
  - vb
  name: VerticalAlignement
  nameWithType: SvgSpan.VerticalAlignement
  fullName: DrawnUi.Draw.SvgSpan.VerticalAlignement
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/SvgSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: VerticalAlignement
    path: ../src/Maui/DrawnUi/Draw/Text/SvgSpan.cs
    startLine: 11
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  example: []
  syntax:
    content: public DrawImageAlignment VerticalAlignement { get; set; }
    parameters: []
    return:
      type: DrawnUi.Draw.DrawImageAlignment
    content.vb: Public Property VerticalAlignement As DrawImageAlignment
  overload: DrawnUi.Draw.SvgSpan.VerticalAlignement*
  implements:
  - DrawnUi.Draw.IDrawnTextSpan.VerticalAlignement
- uid: DrawnUi.Draw.SvgSpan.Height
  commentId: P:DrawnUi.Draw.SvgSpan.Height
  id: Height
  parent: DrawnUi.Draw.SvgSpan
  langs:
  - csharp
  - vb
  name: Height
  nameWithType: SvgSpan.Height
  fullName: DrawnUi.Draw.SvgSpan.Height
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/SvgSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Height
    path: ../src/Maui/DrawnUi/Draw/Text/SvgSpan.cs
    startLine: 28
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public double Height { get; set; }
    parameters: []
    return:
      type: System.Double
    content.vb: Public Property Height As Double
  overload: DrawnUi.Draw.SvgSpan.Height*
- uid: DrawnUi.Draw.SvgSpan.Width
  commentId: P:DrawnUi.Draw.SvgSpan.Width
  id: Width
  parent: DrawnUi.Draw.SvgSpan
  langs:
  - csharp
  - vb
  name: Width
  nameWithType: SvgSpan.Width
  fullName: DrawnUi.Draw.SvgSpan.Width
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/SvgSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Width
    path: ../src/Maui/DrawnUi/Draw/Text/SvgSpan.cs
    startLine: 45
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public double Width { get; set; }
    parameters: []
    return:
      type: System.Double
    content.vb: Public Property Width As Double
  overload: DrawnUi.Draw.SvgSpan.Width*
- uid: DrawnUi.Draw.SvgSpan.TintColor
  commentId: P:DrawnUi.Draw.SvgSpan.TintColor
  id: TintColor
  parent: DrawnUi.Draw.SvgSpan
  langs:
  - csharp
  - vb
  name: TintColor
  nameWithType: SvgSpan.TintColor
  fullName: DrawnUi.Draw.SvgSpan.TintColor
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/SvgSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: TintColor
    path: ../src/Maui/DrawnUi/Draw/Text/SvgSpan.cs
    startLine: 62
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public Color TintColor { get; set; }
    parameters: []
    return:
      type: Microsoft.Maui.Graphics.Color
    content.vb: Public Property TintColor As Color
  overload: DrawnUi.Draw.SvgSpan.TintColor*
- uid: DrawnUi.Draw.SvgSpan.Source
  commentId: P:DrawnUi.Draw.SvgSpan.Source
  id: Source
  parent: DrawnUi.Draw.SvgSpan
  langs:
  - csharp
  - vb
  name: Source
  nameWithType: SvgSpan.Source
  fullName: DrawnUi.Draw.SvgSpan.Source
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/SvgSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Source
    path: ../src/Maui/DrawnUi/Draw/Text/SvgSpan.cs
    startLine: 79
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public string Source { get; set; }
    parameters: []
    return:
      type: System.String
    content.vb: Public Property Source As String
  overload: DrawnUi.Draw.SvgSpan.Source*
- uid: DrawnUi.Draw.SvgSpan.Control
  commentId: F:DrawnUi.Draw.SvgSpan.Control
  id: Control
  parent: DrawnUi.Draw.SvgSpan
  langs:
  - csharp
  - vb
  name: Control
  nameWithType: SvgSpan.Control
  fullName: DrawnUi.Draw.SvgSpan.Control
  type: Field
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/SvgSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Control
    path: ../src/Maui/DrawnUi/Draw/Text/SvgSpan.cs
    startLine: 95
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: protected SkiaSvg Control
    return:
      type: DrawnUi.Draw.SkiaSvg
    content.vb: Protected Control As SkiaSvg
- uid: DrawnUi.Draw.SvgSpan.Dispose
  commentId: M:DrawnUi.Draw.SvgSpan.Dispose
  id: Dispose
  parent: DrawnUi.Draw.SvgSpan
  langs:
  - csharp
  - vb
  name: Dispose()
  nameWithType: SvgSpan.Dispose()
  fullName: DrawnUi.Draw.SvgSpan.Dispose()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/SvgSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Dispose
    path: ../src/Maui/DrawnUi/Draw/Text/SvgSpan.cs
    startLine: 102
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
  example: []
  syntax:
    content: public override void Dispose()
    content.vb: Public Overrides Sub Dispose()
  overridden: DrawnUi.Draw.TextSpan.Dispose
  overload: DrawnUi.Draw.SvgSpan.Dispose*
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: Microsoft.Maui.Controls.BindableObject
  commentId: T:Microsoft.Maui.Controls.BindableObject
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject
  name: BindableObject
  nameWithType: BindableObject
  fullName: Microsoft.Maui.Controls.BindableObject
- uid: Microsoft.Maui.Controls.Element
  commentId: T:Microsoft.Maui.Controls.Element
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  name: Element
  nameWithType: Element
  fullName: Microsoft.Maui.Controls.Element
- uid: DrawnUi.Draw.TextSpan
  commentId: T:DrawnUi.Draw.TextSpan
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.TextSpan.html
  name: TextSpan
  nameWithType: TextSpan
  fullName: DrawnUi.Draw.TextSpan
- uid: System.ComponentModel.INotifyPropertyChanged
  commentId: T:System.ComponentModel.INotifyPropertyChanged
  parent: System.ComponentModel
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.inotifypropertychanged
  name: INotifyPropertyChanged
  nameWithType: INotifyPropertyChanged
  fullName: System.ComponentModel.INotifyPropertyChanged
- uid: Microsoft.Maui.Controls.IElementController
  commentId: T:Microsoft.Maui.Controls.IElementController
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ielementcontroller
  name: IElementController
  nameWithType: IElementController
  fullName: Microsoft.Maui.Controls.IElementController
- uid: Microsoft.Maui.IVisualTreeElement
  commentId: T:Microsoft.Maui.IVisualTreeElement
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.ivisualtreeelement
  name: IVisualTreeElement
  nameWithType: IVisualTreeElement
  fullName: Microsoft.Maui.IVisualTreeElement
- uid: Microsoft.Maui.Controls.IEffectControlProvider
  commentId: T:Microsoft.Maui.Controls.IEffectControlProvider
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ieffectcontrolprovider
  name: IEffectControlProvider
  nameWithType: IEffectControlProvider
  fullName: Microsoft.Maui.Controls.IEffectControlProvider
- uid: Microsoft.Maui.IToolTipElement
  commentId: T:Microsoft.Maui.IToolTipElement
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.itooltipelement
  name: IToolTipElement
  nameWithType: IToolTipElement
  fullName: Microsoft.Maui.IToolTipElement
- uid: Microsoft.Maui.IContextFlyoutElement
  commentId: T:Microsoft.Maui.IContextFlyoutElement
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.icontextflyoutelement
  name: IContextFlyoutElement
  nameWithType: IContextFlyoutElement
  fullName: Microsoft.Maui.IContextFlyoutElement
- uid: Microsoft.Maui.IElement
  commentId: T:Microsoft.Maui.IElement
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.ielement
  name: IElement
  nameWithType: IElement
  fullName: Microsoft.Maui.IElement
- uid: System.IDisposable
  commentId: T:System.IDisposable
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.idisposable
  name: IDisposable
  nameWithType: IDisposable
  fullName: System.IDisposable
- uid: DrawnUi.Draw.IDrawnTextSpan
  commentId: T:DrawnUi.Draw.IDrawnTextSpan
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.IDrawnTextSpan.html
  name: IDrawnTextSpan
  nameWithType: IDrawnTextSpan
  fullName: DrawnUi.Draw.IDrawnTextSpan
- uid: DrawnUi.Draw.TextSpan.TextProperty
  commentId: F:DrawnUi.Draw.TextSpan.TextProperty
  parent: DrawnUi.Draw.TextSpan
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_TextProperty
  name: TextProperty
  nameWithType: TextSpan.TextProperty
  fullName: DrawnUi.Draw.TextSpan.TextProperty
- uid: DrawnUi.Draw.TextSpan.Text
  commentId: P:DrawnUi.Draw.TextSpan.Text
  parent: DrawnUi.Draw.TextSpan
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_Text
  name: Text
  nameWithType: TextSpan.Text
  fullName: DrawnUi.Draw.TextSpan.Text
- uid: DrawnUi.Draw.TextSpan.Default
  commentId: P:DrawnUi.Draw.TextSpan.Default
  parent: DrawnUi.Draw.TextSpan
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_Default
  name: Default
  nameWithType: TextSpan.Default
  fullName: DrawnUi.Draw.TextSpan.Default
- uid: DrawnUi.Draw.TextSpan.DebugString
  commentId: P:DrawnUi.Draw.TextSpan.DebugString
  parent: DrawnUi.Draw.TextSpan
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_DebugString
  name: DebugString
  nameWithType: TextSpan.DebugString
  fullName: DrawnUi.Draw.TextSpan.DebugString
- uid: DrawnUi.Draw.TextSpan.RenderingScale
  commentId: P:DrawnUi.Draw.TextSpan.RenderingScale
  parent: DrawnUi.Draw.TextSpan
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_RenderingScale
  name: RenderingScale
  nameWithType: TextSpan.RenderingScale
  fullName: DrawnUi.Draw.TextSpan.RenderingScale
- uid: DrawnUi.Draw.TextSpan.Glyphs
  commentId: P:DrawnUi.Draw.TextSpan.Glyphs
  parent: DrawnUi.Draw.TextSpan
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_Glyphs
  name: Glyphs
  nameWithType: TextSpan.Glyphs
  fullName: DrawnUi.Draw.TextSpan.Glyphs
- uid: DrawnUi.Draw.TextSpan.Shape
  commentId: P:DrawnUi.Draw.TextSpan.Shape
  parent: DrawnUi.Draw.TextSpan
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_Shape
  name: Shape
  nameWithType: TextSpan.Shape
  fullName: DrawnUi.Draw.TextSpan.Shape
- uid: DrawnUi.Draw.TextSpan.TextFiltered
  commentId: P:DrawnUi.Draw.TextSpan.TextFiltered
  parent: DrawnUi.Draw.TextSpan
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_TextFiltered
  name: TextFiltered
  nameWithType: TextSpan.TextFiltered
  fullName: DrawnUi.Draw.TextSpan.TextFiltered
- uid: DrawnUi.Draw.TextSpan.CheckGlyphsCanBeRendered
  commentId: M:DrawnUi.Draw.TextSpan.CheckGlyphsCanBeRendered
  parent: DrawnUi.Draw.TextSpan
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_CheckGlyphsCanBeRendered
  name: CheckGlyphsCanBeRendered()
  nameWithType: TextSpan.CheckGlyphsCanBeRendered()
  fullName: DrawnUi.Draw.TextSpan.CheckGlyphsCanBeRendered()
  spec.csharp:
  - uid: DrawnUi.Draw.TextSpan.CheckGlyphsCanBeRendered
    name: CheckGlyphsCanBeRendered
    href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_CheckGlyphsCanBeRendered
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.TextSpan.CheckGlyphsCanBeRendered
    name: CheckGlyphsCanBeRendered
    href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_CheckGlyphsCanBeRendered
  - name: (
  - name: )
- uid: DrawnUi.Draw.TextSpan.SetupPaint(System.Double,SkiaSharp.SKPaint)
  commentId: M:DrawnUi.Draw.TextSpan.SetupPaint(System.Double,SkiaSharp.SKPaint)
  parent: DrawnUi.Draw.TextSpan
  isExternal: true
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_SetupPaint_System_Double_SkiaSharp_SKPaint_
  name: SetupPaint(double, SKPaint)
  nameWithType: TextSpan.SetupPaint(double, SKPaint)
  fullName: DrawnUi.Draw.TextSpan.SetupPaint(double, SkiaSharp.SKPaint)
  nameWithType.vb: TextSpan.SetupPaint(Double, SKPaint)
  fullName.vb: DrawnUi.Draw.TextSpan.SetupPaint(Double, SkiaSharp.SKPaint)
  name.vb: SetupPaint(Double, SKPaint)
  spec.csharp:
  - uid: DrawnUi.Draw.TextSpan.SetupPaint(System.Double,SkiaSharp.SKPaint)
    name: SetupPaint
    href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_SetupPaint_System_Double_SkiaSharp_SKPaint_
  - name: (
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: SkiaSharp.SKPaint
    name: SKPaint
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skpaint
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.TextSpan.SetupPaint(System.Double,SkiaSharp.SKPaint)
    name: SetupPaint
    href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_SetupPaint_System_Double_SkiaSharp_SKPaint_
  - name: (
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: SkiaSharp.SKPaint
    name: SKPaint
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skpaint
  - name: )
- uid: DrawnUi.Draw.TextSpan.HasSetFont
  commentId: P:DrawnUi.Draw.TextSpan.HasSetFont
  parent: DrawnUi.Draw.TextSpan
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_HasSetFont
  name: HasSetFont
  nameWithType: TextSpan.HasSetFont
  fullName: DrawnUi.Draw.TextSpan.HasSetFont
- uid: DrawnUi.Draw.TextSpan.HasSetSize
  commentId: P:DrawnUi.Draw.TextSpan.HasSetSize
  parent: DrawnUi.Draw.TextSpan
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_HasSetSize
  name: HasSetSize
  nameWithType: TextSpan.HasSetSize
  fullName: DrawnUi.Draw.TextSpan.HasSetSize
- uid: DrawnUi.Draw.TextSpan.HasSetColor
  commentId: P:DrawnUi.Draw.TextSpan.HasSetColor
  parent: DrawnUi.Draw.TextSpan
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_HasSetColor
  name: HasSetColor
  nameWithType: TextSpan.HasSetColor
  fullName: DrawnUi.Draw.TextSpan.HasSetColor
- uid: DrawnUi.Draw.TextSpan.Paint
  commentId: P:DrawnUi.Draw.TextSpan.Paint
  parent: DrawnUi.Draw.TextSpan
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_Paint
  name: Paint
  nameWithType: TextSpan.Paint
  fullName: DrawnUi.Draw.TextSpan.Paint
- uid: DrawnUi.Draw.TextSpan.TypeFace
  commentId: P:DrawnUi.Draw.TextSpan.TypeFace
  parent: DrawnUi.Draw.TextSpan
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_TypeFace
  name: TypeFace
  nameWithType: TextSpan.TypeFace
  fullName: DrawnUi.Draw.TextSpan.TypeFace
- uid: DrawnUi.Draw.TextSpan.NeedShape
  commentId: P:DrawnUi.Draw.TextSpan.NeedShape
  parent: DrawnUi.Draw.TextSpan
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_NeedShape
  name: NeedShape
  nameWithType: TextSpan.NeedShape
  fullName: DrawnUi.Draw.TextSpan.NeedShape
- uid: DrawnUi.Draw.TextSpan.HasDecorations
  commentId: P:DrawnUi.Draw.TextSpan.HasDecorations
  parent: DrawnUi.Draw.TextSpan
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_HasDecorations
  name: HasDecorations
  nameWithType: TextSpan.HasDecorations
  fullName: DrawnUi.Draw.TextSpan.HasDecorations
- uid: DrawnUi.Draw.TextSpan.Underline
  commentId: P:DrawnUi.Draw.TextSpan.Underline
  parent: DrawnUi.Draw.TextSpan
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_Underline
  name: Underline
  nameWithType: TextSpan.Underline
  fullName: DrawnUi.Draw.TextSpan.Underline
- uid: DrawnUi.Draw.TextSpan.UnderlineWidth
  commentId: P:DrawnUi.Draw.TextSpan.UnderlineWidth
  parent: DrawnUi.Draw.TextSpan
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_UnderlineWidth
  name: UnderlineWidth
  nameWithType: TextSpan.UnderlineWidth
  fullName: DrawnUi.Draw.TextSpan.UnderlineWidth
- uid: DrawnUi.Draw.TextSpan.Strikeout
  commentId: P:DrawnUi.Draw.TextSpan.Strikeout
  parent: DrawnUi.Draw.TextSpan
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_Strikeout
  name: Strikeout
  nameWithType: TextSpan.Strikeout
  fullName: DrawnUi.Draw.TextSpan.Strikeout
- uid: DrawnUi.Draw.TextSpan.StrikeoutWidth
  commentId: P:DrawnUi.Draw.TextSpan.StrikeoutWidth
  parent: DrawnUi.Draw.TextSpan
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_StrikeoutWidth
  name: StrikeoutWidth
  nameWithType: TextSpan.StrikeoutWidth
  fullName: DrawnUi.Draw.TextSpan.StrikeoutWidth
- uid: DrawnUi.Draw.TextSpan.StrikeoutColor
  commentId: P:DrawnUi.Draw.TextSpan.StrikeoutColor
  parent: DrawnUi.Draw.TextSpan
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_StrikeoutColor
  name: StrikeoutColor
  nameWithType: TextSpan.StrikeoutColor
  fullName: DrawnUi.Draw.TextSpan.StrikeoutColor
- uid: DrawnUi.Draw.TextSpan.HasTapHandler
  commentId: P:DrawnUi.Draw.TextSpan.HasTapHandler
  parent: DrawnUi.Draw.TextSpan
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_HasTapHandler
  name: HasTapHandler
  nameWithType: TextSpan.HasTapHandler
  fullName: DrawnUi.Draw.TextSpan.HasTapHandler
- uid: DrawnUi.Draw.TextSpan.ForceCaptureInput
  commentId: P:DrawnUi.Draw.TextSpan.ForceCaptureInput
  parent: DrawnUi.Draw.TextSpan
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_ForceCaptureInput
  name: ForceCaptureInput
  nameWithType: TextSpan.ForceCaptureInput
  fullName: DrawnUi.Draw.TextSpan.ForceCaptureInput
- uid: DrawnUi.Draw.TextSpan.HitIsInside(System.Single,System.Single)
  commentId: M:DrawnUi.Draw.TextSpan.HitIsInside(System.Single,System.Single)
  parent: DrawnUi.Draw.TextSpan
  isExternal: true
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_HitIsInside_System_Single_System_Single_
  name: HitIsInside(float, float)
  nameWithType: TextSpan.HitIsInside(float, float)
  fullName: DrawnUi.Draw.TextSpan.HitIsInside(float, float)
  nameWithType.vb: TextSpan.HitIsInside(Single, Single)
  fullName.vb: DrawnUi.Draw.TextSpan.HitIsInside(Single, Single)
  name.vb: HitIsInside(Single, Single)
  spec.csharp:
  - uid: DrawnUi.Draw.TextSpan.HitIsInside(System.Single,System.Single)
    name: HitIsInside
    href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_HitIsInside_System_Single_System_Single_
  - name: (
  - uid: System.Single
    name: float
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.single
  - name: ','
  - name: " "
  - uid: System.Single
    name: float
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.single
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.TextSpan.HitIsInside(System.Single,System.Single)
    name: HitIsInside
    href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_HitIsInside_System_Single_System_Single_
  - name: (
  - uid: System.Single
    name: Single
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.single
  - name: ','
  - name: " "
  - uid: System.Single
    name: Single
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.single
  - name: )
- uid: DrawnUi.Draw.TextSpan.FireTap
  commentId: M:DrawnUi.Draw.TextSpan.FireTap
  parent: DrawnUi.Draw.TextSpan
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_FireTap
  name: FireTap()
  nameWithType: TextSpan.FireTap()
  fullName: DrawnUi.Draw.TextSpan.FireTap()
  spec.csharp:
  - uid: DrawnUi.Draw.TextSpan.FireTap
    name: FireTap
    href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_FireTap
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.TextSpan.FireTap
    name: FireTap
    href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_FireTap
  - name: (
  - name: )
- uid: DrawnUi.Draw.TextSpan.DrawingOffset
  commentId: P:DrawnUi.Draw.TextSpan.DrawingOffset
  parent: DrawnUi.Draw.TextSpan
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_DrawingOffset
  name: DrawingOffset
  nameWithType: TextSpan.DrawingOffset
  fullName: DrawnUi.Draw.TextSpan.DrawingOffset
- uid: DrawnUi.Draw.TextSpan.Tag
  commentId: P:DrawnUi.Draw.TextSpan.Tag
  parent: DrawnUi.Draw.TextSpan
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_Tag
  name: Tag
  nameWithType: TextSpan.Tag
  fullName: DrawnUi.Draw.TextSpan.Tag
- uid: DrawnUi.Draw.TextSpan.Rects
  commentId: F:DrawnUi.Draw.TextSpan.Rects
  parent: DrawnUi.Draw.TextSpan
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_Rects
  name: Rects
  nameWithType: TextSpan.Rects
  fullName: DrawnUi.Draw.TextSpan.Rects
- uid: DrawnUi.Draw.TextSpan.CommandTapped
  commentId: P:DrawnUi.Draw.TextSpan.CommandTapped
  parent: DrawnUi.Draw.TextSpan
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_CommandTapped
  name: CommandTapped
  nameWithType: TextSpan.CommandTapped
  fullName: DrawnUi.Draw.TextSpan.CommandTapped
- uid: DrawnUi.Draw.TextSpan.Tapped
  commentId: E:DrawnUi.Draw.TextSpan.Tapped
  parent: DrawnUi.Draw.TextSpan
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_Tapped
  name: Tapped
  nameWithType: TextSpan.Tapped
  fullName: DrawnUi.Draw.TextSpan.Tapped
- uid: DrawnUi.Draw.TextSpan.ParentControl
  commentId: P:DrawnUi.Draw.TextSpan.ParentControl
  parent: DrawnUi.Draw.TextSpan
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_ParentControl
  name: ParentControl
  nameWithType: TextSpan.ParentControl
  fullName: DrawnUi.Draw.TextSpan.ParentControl
- uid: DrawnUi.Draw.TextSpan.OnPropertyChanged(System.String)
  commentId: M:DrawnUi.Draw.TextSpan.OnPropertyChanged(System.String)
  parent: DrawnUi.Draw.TextSpan
  isExternal: true
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_OnPropertyChanged_System_String_
  name: OnPropertyChanged(string)
  nameWithType: TextSpan.OnPropertyChanged(string)
  fullName: DrawnUi.Draw.TextSpan.OnPropertyChanged(string)
  nameWithType.vb: TextSpan.OnPropertyChanged(String)
  fullName.vb: DrawnUi.Draw.TextSpan.OnPropertyChanged(String)
  name.vb: OnPropertyChanged(String)
  spec.csharp:
  - uid: DrawnUi.Draw.TextSpan.OnPropertyChanged(System.String)
    name: OnPropertyChanged
    href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_OnPropertyChanged_System_String_
  - name: (
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.TextSpan.OnPropertyChanged(System.String)
    name: OnPropertyChanged
    href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_OnPropertyChanged_System_String_
  - name: (
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: DrawnUi.Draw.TextSpan._fontAutoSet
  commentId: F:DrawnUi.Draw.TextSpan._fontAutoSet
  parent: DrawnUi.Draw.TextSpan
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan__fontAutoSet
  name: _fontAutoSet
  nameWithType: TextSpan._fontAutoSet
  fullName: DrawnUi.Draw.TextSpan._fontAutoSet
- uid: DrawnUi.Draw.TextSpan.UpdateFont
  commentId: M:DrawnUi.Draw.TextSpan.UpdateFont
  parent: DrawnUi.Draw.TextSpan
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_UpdateFont
  name: UpdateFont()
  nameWithType: TextSpan.UpdateFont()
  fullName: DrawnUi.Draw.TextSpan.UpdateFont()
  spec.csharp:
  - uid: DrawnUi.Draw.TextSpan.UpdateFont
    name: UpdateFont
    href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_UpdateFont
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.TextSpan.UpdateFont
    name: UpdateFont
    href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_UpdateFont
  - name: (
  - name: )
- uid: DrawnUi.Draw.TextSpan.FontFamily
  commentId: P:DrawnUi.Draw.TextSpan.FontFamily
  parent: DrawnUi.Draw.TextSpan
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_FontFamily
  name: FontFamily
  nameWithType: TextSpan.FontFamily
  fullName: DrawnUi.Draw.TextSpan.FontFamily
- uid: DrawnUi.Draw.TextSpan.LineSpacing
  commentId: P:DrawnUi.Draw.TextSpan.LineSpacing
  parent: DrawnUi.Draw.TextSpan
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_LineSpacing
  name: LineSpacing
  nameWithType: TextSpan.LineSpacing
  fullName: DrawnUi.Draw.TextSpan.LineSpacing
- uid: DrawnUi.Draw.TextSpan.LineHeight
  commentId: P:DrawnUi.Draw.TextSpan.LineHeight
  parent: DrawnUi.Draw.TextSpan
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_LineHeight
  name: LineHeight
  nameWithType: TextSpan.LineHeight
  fullName: DrawnUi.Draw.TextSpan.LineHeight
- uid: DrawnUi.Draw.TextSpan.FontWeight
  commentId: P:DrawnUi.Draw.TextSpan.FontWeight
  parent: DrawnUi.Draw.TextSpan
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_FontWeight
  name: FontWeight
  nameWithType: TextSpan.FontWeight
  fullName: DrawnUi.Draw.TextSpan.FontWeight
- uid: DrawnUi.Draw.TextSpan.TextColorProperty
  commentId: F:DrawnUi.Draw.TextSpan.TextColorProperty
  parent: DrawnUi.Draw.TextSpan
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_TextColorProperty
  name: TextColorProperty
  nameWithType: TextSpan.TextColorProperty
  fullName: DrawnUi.Draw.TextSpan.TextColorProperty
- uid: DrawnUi.Draw.TextSpan.TextColor
  commentId: P:DrawnUi.Draw.TextSpan.TextColor
  parent: DrawnUi.Draw.TextSpan
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_TextColor
  name: TextColor
  nameWithType: TextSpan.TextColor
  fullName: DrawnUi.Draw.TextSpan.TextColor
- uid: DrawnUi.Draw.TextSpan.BackgroundColor
  commentId: P:DrawnUi.Draw.TextSpan.BackgroundColor
  parent: DrawnUi.Draw.TextSpan
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_BackgroundColor
  name: BackgroundColor
  nameWithType: TextSpan.BackgroundColor
  fullName: DrawnUi.Draw.TextSpan.BackgroundColor
- uid: DrawnUi.Draw.TextSpan.ParagraphColor
  commentId: P:DrawnUi.Draw.TextSpan.ParagraphColor
  parent: DrawnUi.Draw.TextSpan
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_ParagraphColor
  name: ParagraphColor
  nameWithType: TextSpan.ParagraphColor
  fullName: DrawnUi.Draw.TextSpan.ParagraphColor
- uid: DrawnUi.Draw.TextSpan.FontSizeProperty
  commentId: F:DrawnUi.Draw.TextSpan.FontSizeProperty
  parent: DrawnUi.Draw.TextSpan
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_FontSizeProperty
  name: FontSizeProperty
  nameWithType: TextSpan.FontSizeProperty
  fullName: DrawnUi.Draw.TextSpan.FontSizeProperty
- uid: DrawnUi.Draw.TextSpan.FontSize
  commentId: P:DrawnUi.Draw.TextSpan.FontSize
  parent: DrawnUi.Draw.TextSpan
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_FontSize
  name: FontSize
  nameWithType: TextSpan.FontSize
  fullName: DrawnUi.Draw.TextSpan.FontSize
- uid: DrawnUi.Draw.TextSpan.IsItalic
  commentId: P:DrawnUi.Draw.TextSpan.IsItalic
  parent: DrawnUi.Draw.TextSpan
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_IsItalic
  name: IsItalic
  nameWithType: TextSpan.IsItalic
  fullName: DrawnUi.Draw.TextSpan.IsItalic
- uid: DrawnUi.Draw.TextSpan.IsBold
  commentId: P:DrawnUi.Draw.TextSpan.IsBold
  parent: DrawnUi.Draw.TextSpan
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_IsBold
  name: IsBold
  nameWithType: TextSpan.IsBold
  fullName: DrawnUi.Draw.TextSpan.IsBold
- uid: DrawnUi.Draw.TextSpan.AutoFindFont
  commentId: P:DrawnUi.Draw.TextSpan.AutoFindFont
  parent: DrawnUi.Draw.TextSpan
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_AutoFindFont
  name: AutoFindFont
  nameWithType: TextSpan.AutoFindFont
  fullName: DrawnUi.Draw.TextSpan.AutoFindFont
- uid: DrawnUi.Draw.TextSpan.FontDetectedWith
  commentId: P:DrawnUi.Draw.TextSpan.FontDetectedWith
  parent: DrawnUi.Draw.TextSpan
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_FontDetectedWith
  name: FontDetectedWith
  nameWithType: TextSpan.FontDetectedWith
  fullName: DrawnUi.Draw.TextSpan.FontDetectedWith
- uid: Microsoft.Maui.Controls.Element.AutomationIdProperty
  commentId: F:Microsoft.Maui.Controls.Element.AutomationIdProperty
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.automationidproperty
  name: AutomationIdProperty
  nameWithType: Element.AutomationIdProperty
  fullName: Microsoft.Maui.Controls.Element.AutomationIdProperty
- uid: Microsoft.Maui.Controls.Element.ClassIdProperty
  commentId: F:Microsoft.Maui.Controls.Element.ClassIdProperty
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.classidproperty
  name: ClassIdProperty
  nameWithType: Element.ClassIdProperty
  fullName: Microsoft.Maui.Controls.Element.ClassIdProperty
- uid: Microsoft.Maui.Controls.Element.InsertLogicalChild(System.Int32,Microsoft.Maui.Controls.Element)
  commentId: M:Microsoft.Maui.Controls.Element.InsertLogicalChild(System.Int32,Microsoft.Maui.Controls.Element)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.insertlogicalchild
  name: InsertLogicalChild(int, Element)
  nameWithType: Element.InsertLogicalChild(int, Element)
  fullName: Microsoft.Maui.Controls.Element.InsertLogicalChild(int, Microsoft.Maui.Controls.Element)
  nameWithType.vb: Element.InsertLogicalChild(Integer, Element)
  fullName.vb: Microsoft.Maui.Controls.Element.InsertLogicalChild(Integer, Microsoft.Maui.Controls.Element)
  name.vb: InsertLogicalChild(Integer, Element)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.InsertLogicalChild(System.Int32,Microsoft.Maui.Controls.Element)
    name: InsertLogicalChild
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.insertlogicalchild
  - name: (
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.InsertLogicalChild(System.Int32,Microsoft.Maui.Controls.Element)
    name: InsertLogicalChild
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.insertlogicalchild
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: Microsoft.Maui.Controls.Element.AddLogicalChild(Microsoft.Maui.Controls.Element)
  commentId: M:Microsoft.Maui.Controls.Element.AddLogicalChild(Microsoft.Maui.Controls.Element)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.addlogicalchild
  name: AddLogicalChild(Element)
  nameWithType: Element.AddLogicalChild(Element)
  fullName: Microsoft.Maui.Controls.Element.AddLogicalChild(Microsoft.Maui.Controls.Element)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.AddLogicalChild(Microsoft.Maui.Controls.Element)
    name: AddLogicalChild
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.addlogicalchild
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.AddLogicalChild(Microsoft.Maui.Controls.Element)
    name: AddLogicalChild
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.addlogicalchild
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: Microsoft.Maui.Controls.Element.RemoveLogicalChild(Microsoft.Maui.Controls.Element)
  commentId: M:Microsoft.Maui.Controls.Element.RemoveLogicalChild(Microsoft.Maui.Controls.Element)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.removelogicalchild
  name: RemoveLogicalChild(Element)
  nameWithType: Element.RemoveLogicalChild(Element)
  fullName: Microsoft.Maui.Controls.Element.RemoveLogicalChild(Microsoft.Maui.Controls.Element)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.RemoveLogicalChild(Microsoft.Maui.Controls.Element)
    name: RemoveLogicalChild
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.removelogicalchild
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.RemoveLogicalChild(Microsoft.Maui.Controls.Element)
    name: RemoveLogicalChild
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.removelogicalchild
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: Microsoft.Maui.Controls.Element.ClearLogicalChildren
  commentId: M:Microsoft.Maui.Controls.Element.ClearLogicalChildren
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.clearlogicalchildren
  name: ClearLogicalChildren()
  nameWithType: Element.ClearLogicalChildren()
  fullName: Microsoft.Maui.Controls.Element.ClearLogicalChildren()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.ClearLogicalChildren
    name: ClearLogicalChildren
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.clearlogicalchildren
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.ClearLogicalChildren
    name: ClearLogicalChildren
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.clearlogicalchildren
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.Element.FindByName(System.String)
  commentId: M:Microsoft.Maui.Controls.Element.FindByName(System.String)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.findbyname
  name: FindByName(string)
  nameWithType: Element.FindByName(string)
  fullName: Microsoft.Maui.Controls.Element.FindByName(string)
  nameWithType.vb: Element.FindByName(String)
  fullName.vb: Microsoft.Maui.Controls.Element.FindByName(String)
  name.vb: FindByName(String)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.FindByName(System.String)
    name: FindByName
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.findbyname
  - name: (
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.FindByName(System.String)
    name: FindByName
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.findbyname
  - name: (
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: Microsoft.Maui.Controls.Element.RemoveDynamicResource(Microsoft.Maui.Controls.BindableProperty)
  commentId: M:Microsoft.Maui.Controls.Element.RemoveDynamicResource(Microsoft.Maui.Controls.BindableProperty)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.removedynamicresource
  name: RemoveDynamicResource(BindableProperty)
  nameWithType: Element.RemoveDynamicResource(BindableProperty)
  fullName: Microsoft.Maui.Controls.Element.RemoveDynamicResource(Microsoft.Maui.Controls.BindableProperty)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.RemoveDynamicResource(Microsoft.Maui.Controls.BindableProperty)
    name: RemoveDynamicResource
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.removedynamicresource
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.RemoveDynamicResource(Microsoft.Maui.Controls.BindableProperty)
    name: RemoveDynamicResource
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.removedynamicresource
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
- uid: Microsoft.Maui.Controls.Element.SetDynamicResource(Microsoft.Maui.Controls.BindableProperty,System.String)
  commentId: M:Microsoft.Maui.Controls.Element.SetDynamicResource(Microsoft.Maui.Controls.BindableProperty,System.String)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.setdynamicresource
  name: SetDynamicResource(BindableProperty, string)
  nameWithType: Element.SetDynamicResource(BindableProperty, string)
  fullName: Microsoft.Maui.Controls.Element.SetDynamicResource(Microsoft.Maui.Controls.BindableProperty, string)
  nameWithType.vb: Element.SetDynamicResource(BindableProperty, String)
  fullName.vb: Microsoft.Maui.Controls.Element.SetDynamicResource(Microsoft.Maui.Controls.BindableProperty, String)
  name.vb: SetDynamicResource(BindableProperty, String)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.SetDynamicResource(Microsoft.Maui.Controls.BindableProperty,System.String)
    name: SetDynamicResource
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.setdynamicresource
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.SetDynamicResource(Microsoft.Maui.Controls.BindableProperty,System.String)
    name: SetDynamicResource
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.setdynamicresource
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: Microsoft.Maui.Controls.Element.OnBindingContextChanged
  commentId: M:Microsoft.Maui.Controls.Element.OnBindingContextChanged
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onbindingcontextchanged
  name: OnBindingContextChanged()
  nameWithType: Element.OnBindingContextChanged()
  fullName: Microsoft.Maui.Controls.Element.OnBindingContextChanged()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.OnBindingContextChanged
    name: OnBindingContextChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onbindingcontextchanged
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.OnBindingContextChanged
    name: OnBindingContextChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onbindingcontextchanged
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.Element.OnChildAdded(Microsoft.Maui.Controls.Element)
  commentId: M:Microsoft.Maui.Controls.Element.OnChildAdded(Microsoft.Maui.Controls.Element)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onchildadded
  name: OnChildAdded(Element)
  nameWithType: Element.OnChildAdded(Element)
  fullName: Microsoft.Maui.Controls.Element.OnChildAdded(Microsoft.Maui.Controls.Element)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.OnChildAdded(Microsoft.Maui.Controls.Element)
    name: OnChildAdded
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onchildadded
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.OnChildAdded(Microsoft.Maui.Controls.Element)
    name: OnChildAdded
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onchildadded
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: Microsoft.Maui.Controls.Element.OnChildRemoved(Microsoft.Maui.Controls.Element,System.Int32)
  commentId: M:Microsoft.Maui.Controls.Element.OnChildRemoved(Microsoft.Maui.Controls.Element,System.Int32)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onchildremoved
  name: OnChildRemoved(Element, int)
  nameWithType: Element.OnChildRemoved(Element, int)
  fullName: Microsoft.Maui.Controls.Element.OnChildRemoved(Microsoft.Maui.Controls.Element, int)
  nameWithType.vb: Element.OnChildRemoved(Element, Integer)
  fullName.vb: Microsoft.Maui.Controls.Element.OnChildRemoved(Microsoft.Maui.Controls.Element, Integer)
  name.vb: OnChildRemoved(Element, Integer)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.OnChildRemoved(Microsoft.Maui.Controls.Element,System.Int32)
    name: OnChildRemoved
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onchildremoved
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: ','
  - name: " "
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.OnChildRemoved(Microsoft.Maui.Controls.Element,System.Int32)
    name: OnChildRemoved
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onchildremoved
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: ','
  - name: " "
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: Microsoft.Maui.Controls.Element.OnParentSet
  commentId: M:Microsoft.Maui.Controls.Element.OnParentSet
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentset
  name: OnParentSet()
  nameWithType: Element.OnParentSet()
  fullName: Microsoft.Maui.Controls.Element.OnParentSet()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.OnParentSet
    name: OnParentSet
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentset
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.OnParentSet
    name: OnParentSet
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentset
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.Element.OnParentChanging(Microsoft.Maui.Controls.ParentChangingEventArgs)
  commentId: M:Microsoft.Maui.Controls.Element.OnParentChanging(Microsoft.Maui.Controls.ParentChangingEventArgs)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentchanging
  name: OnParentChanging(ParentChangingEventArgs)
  nameWithType: Element.OnParentChanging(ParentChangingEventArgs)
  fullName: Microsoft.Maui.Controls.Element.OnParentChanging(Microsoft.Maui.Controls.ParentChangingEventArgs)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.OnParentChanging(Microsoft.Maui.Controls.ParentChangingEventArgs)
    name: OnParentChanging
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentchanging
  - name: (
  - uid: Microsoft.Maui.Controls.ParentChangingEventArgs
    name: ParentChangingEventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.parentchangingeventargs
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.OnParentChanging(Microsoft.Maui.Controls.ParentChangingEventArgs)
    name: OnParentChanging
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentchanging
  - name: (
  - uid: Microsoft.Maui.Controls.ParentChangingEventArgs
    name: ParentChangingEventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.parentchangingeventargs
  - name: )
- uid: Microsoft.Maui.Controls.Element.OnParentChanged
  commentId: M:Microsoft.Maui.Controls.Element.OnParentChanged
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentchanged
  name: OnParentChanged()
  nameWithType: Element.OnParentChanged()
  fullName: Microsoft.Maui.Controls.Element.OnParentChanged()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.OnParentChanged
    name: OnParentChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentchanged
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.OnParentChanged
    name: OnParentChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentchanged
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.Element.OnHandlerChanging(Microsoft.Maui.Controls.HandlerChangingEventArgs)
  commentId: M:Microsoft.Maui.Controls.Element.OnHandlerChanging(Microsoft.Maui.Controls.HandlerChangingEventArgs)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onhandlerchanging
  name: OnHandlerChanging(HandlerChangingEventArgs)
  nameWithType: Element.OnHandlerChanging(HandlerChangingEventArgs)
  fullName: Microsoft.Maui.Controls.Element.OnHandlerChanging(Microsoft.Maui.Controls.HandlerChangingEventArgs)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.OnHandlerChanging(Microsoft.Maui.Controls.HandlerChangingEventArgs)
    name: OnHandlerChanging
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onhandlerchanging
  - name: (
  - uid: Microsoft.Maui.Controls.HandlerChangingEventArgs
    name: HandlerChangingEventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.handlerchangingeventargs
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.OnHandlerChanging(Microsoft.Maui.Controls.HandlerChangingEventArgs)
    name: OnHandlerChanging
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onhandlerchanging
  - name: (
  - uid: Microsoft.Maui.Controls.HandlerChangingEventArgs
    name: HandlerChangingEventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.handlerchangingeventargs
  - name: )
- uid: Microsoft.Maui.Controls.Element.OnHandlerChanged
  commentId: M:Microsoft.Maui.Controls.Element.OnHandlerChanged
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onhandlerchanged
  name: OnHandlerChanged()
  nameWithType: Element.OnHandlerChanged()
  fullName: Microsoft.Maui.Controls.Element.OnHandlerChanged()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.OnHandlerChanged
    name: OnHandlerChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onhandlerchanged
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.OnHandlerChanged
    name: OnHandlerChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onhandlerchanged
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.Element.MapAutomationPropertiesIsInAccessibleTree(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
  commentId: M:Microsoft.Maui.Controls.Element.MapAutomationPropertiesIsInAccessibleTree(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.mapautomationpropertiesisinaccessibletree
  name: MapAutomationPropertiesIsInAccessibleTree(IElementHandler, Element)
  nameWithType: Element.MapAutomationPropertiesIsInAccessibleTree(IElementHandler, Element)
  fullName: Microsoft.Maui.Controls.Element.MapAutomationPropertiesIsInAccessibleTree(Microsoft.Maui.IElementHandler, Microsoft.Maui.Controls.Element)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.MapAutomationPropertiesIsInAccessibleTree(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
    name: MapAutomationPropertiesIsInAccessibleTree
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.mapautomationpropertiesisinaccessibletree
  - name: (
  - uid: Microsoft.Maui.IElementHandler
    name: IElementHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.ielementhandler
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.MapAutomationPropertiesIsInAccessibleTree(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
    name: MapAutomationPropertiesIsInAccessibleTree
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.mapautomationpropertiesisinaccessibletree
  - name: (
  - uid: Microsoft.Maui.IElementHandler
    name: IElementHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.ielementhandler
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: Microsoft.Maui.Controls.Element.MapAutomationPropertiesExcludedWithChildren(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
  commentId: M:Microsoft.Maui.Controls.Element.MapAutomationPropertiesExcludedWithChildren(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.mapautomationpropertiesexcludedwithchildren
  name: MapAutomationPropertiesExcludedWithChildren(IElementHandler, Element)
  nameWithType: Element.MapAutomationPropertiesExcludedWithChildren(IElementHandler, Element)
  fullName: Microsoft.Maui.Controls.Element.MapAutomationPropertiesExcludedWithChildren(Microsoft.Maui.IElementHandler, Microsoft.Maui.Controls.Element)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.MapAutomationPropertiesExcludedWithChildren(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
    name: MapAutomationPropertiesExcludedWithChildren
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.mapautomationpropertiesexcludedwithchildren
  - name: (
  - uid: Microsoft.Maui.IElementHandler
    name: IElementHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.ielementhandler
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.MapAutomationPropertiesExcludedWithChildren(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
    name: MapAutomationPropertiesExcludedWithChildren
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.mapautomationpropertiesexcludedwithchildren
  - name: (
  - uid: Microsoft.Maui.IElementHandler
    name: IElementHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.ielementhandler
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: Microsoft.Maui.Controls.Element.AutomationId
  commentId: P:Microsoft.Maui.Controls.Element.AutomationId
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.automationid
  name: AutomationId
  nameWithType: Element.AutomationId
  fullName: Microsoft.Maui.Controls.Element.AutomationId
- uid: Microsoft.Maui.Controls.Element.ClassId
  commentId: P:Microsoft.Maui.Controls.Element.ClassId
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.classid
  name: ClassId
  nameWithType: Element.ClassId
  fullName: Microsoft.Maui.Controls.Element.ClassId
- uid: Microsoft.Maui.Controls.Element.Effects
  commentId: P:Microsoft.Maui.Controls.Element.Effects
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.effects
  name: Effects
  nameWithType: Element.Effects
  fullName: Microsoft.Maui.Controls.Element.Effects
- uid: Microsoft.Maui.Controls.Element.Id
  commentId: P:Microsoft.Maui.Controls.Element.Id
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.id
  name: Id
  nameWithType: Element.Id
  fullName: Microsoft.Maui.Controls.Element.Id
- uid: Microsoft.Maui.Controls.Element.StyleId
  commentId: P:Microsoft.Maui.Controls.Element.StyleId
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.styleid
  name: StyleId
  nameWithType: Element.StyleId
  fullName: Microsoft.Maui.Controls.Element.StyleId
- uid: Microsoft.Maui.Controls.Element.Parent
  commentId: P:Microsoft.Maui.Controls.Element.Parent
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.parent
  name: Parent
  nameWithType: Element.Parent
  fullName: Microsoft.Maui.Controls.Element.Parent
- uid: Microsoft.Maui.Controls.Element.Handler
  commentId: P:Microsoft.Maui.Controls.Element.Handler
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.handler
  name: Handler
  nameWithType: Element.Handler
  fullName: Microsoft.Maui.Controls.Element.Handler
- uid: Microsoft.Maui.Controls.Element.ChildAdded
  commentId: E:Microsoft.Maui.Controls.Element.ChildAdded
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.childadded
  name: ChildAdded
  nameWithType: Element.ChildAdded
  fullName: Microsoft.Maui.Controls.Element.ChildAdded
- uid: Microsoft.Maui.Controls.Element.ChildRemoved
  commentId: E:Microsoft.Maui.Controls.Element.ChildRemoved
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.childremoved
  name: ChildRemoved
  nameWithType: Element.ChildRemoved
  fullName: Microsoft.Maui.Controls.Element.ChildRemoved
- uid: Microsoft.Maui.Controls.Element.DescendantAdded
  commentId: E:Microsoft.Maui.Controls.Element.DescendantAdded
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.descendantadded
  name: DescendantAdded
  nameWithType: Element.DescendantAdded
  fullName: Microsoft.Maui.Controls.Element.DescendantAdded
- uid: Microsoft.Maui.Controls.Element.DescendantRemoved
  commentId: E:Microsoft.Maui.Controls.Element.DescendantRemoved
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.descendantremoved
  name: DescendantRemoved
  nameWithType: Element.DescendantRemoved
  fullName: Microsoft.Maui.Controls.Element.DescendantRemoved
- uid: Microsoft.Maui.Controls.Element.ParentChanging
  commentId: E:Microsoft.Maui.Controls.Element.ParentChanging
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.parentchanging
  name: ParentChanging
  nameWithType: Element.ParentChanging
  fullName: Microsoft.Maui.Controls.Element.ParentChanging
- uid: Microsoft.Maui.Controls.Element.ParentChanged
  commentId: E:Microsoft.Maui.Controls.Element.ParentChanged
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.parentchanged
  name: ParentChanged
  nameWithType: Element.ParentChanged
  fullName: Microsoft.Maui.Controls.Element.ParentChanged
- uid: Microsoft.Maui.Controls.Element.HandlerChanging
  commentId: E:Microsoft.Maui.Controls.Element.HandlerChanging
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.handlerchanging
  name: HandlerChanging
  nameWithType: Element.HandlerChanging
  fullName: Microsoft.Maui.Controls.Element.HandlerChanging
- uid: Microsoft.Maui.Controls.Element.HandlerChanged
  commentId: E:Microsoft.Maui.Controls.Element.HandlerChanged
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.handlerchanged
  name: HandlerChanged
  nameWithType: Element.HandlerChanged
  fullName: Microsoft.Maui.Controls.Element.HandlerChanged
- uid: Microsoft.Maui.Controls.BindableObject.BindingContextProperty
  commentId: F:Microsoft.Maui.Controls.BindableObject.BindingContextProperty
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.bindingcontextproperty
  name: BindingContextProperty
  nameWithType: BindableObject.BindingContextProperty
  fullName: Microsoft.Maui.Controls.BindableObject.BindingContextProperty
- uid: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindableProperty)
  commentId: M:Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindableProperty)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindableproperty)
  name: ClearValue(BindableProperty)
  nameWithType: BindableObject.ClearValue(BindableProperty)
  fullName: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindableProperty)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindableProperty)
    name: ClearValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindableproperty)
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindableProperty)
    name: ClearValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindableproperty)
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindablePropertyKey)
  commentId: M:Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindablePropertyKey)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindablepropertykey)
  name: ClearValue(BindablePropertyKey)
  nameWithType: BindableObject.ClearValue(BindablePropertyKey)
  fullName: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindablePropertyKey)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindablePropertyKey)
    name: ClearValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindablepropertykey)
  - name: (
  - uid: Microsoft.Maui.Controls.BindablePropertyKey
    name: BindablePropertyKey
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindablepropertykey
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindablePropertyKey)
    name: ClearValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindablepropertykey)
  - name: (
  - uid: Microsoft.Maui.Controls.BindablePropertyKey
    name: BindablePropertyKey
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindablepropertykey
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.GetValue(Microsoft.Maui.Controls.BindableProperty)
  commentId: M:Microsoft.Maui.Controls.BindableObject.GetValue(Microsoft.Maui.Controls.BindableProperty)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.getvalue
  name: GetValue(BindableProperty)
  nameWithType: BindableObject.GetValue(BindableProperty)
  fullName: Microsoft.Maui.Controls.BindableObject.GetValue(Microsoft.Maui.Controls.BindableProperty)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.GetValue(Microsoft.Maui.Controls.BindableProperty)
    name: GetValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.getvalue
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.GetValue(Microsoft.Maui.Controls.BindableProperty)
    name: GetValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.getvalue
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.IsSet(Microsoft.Maui.Controls.BindableProperty)
  commentId: M:Microsoft.Maui.Controls.BindableObject.IsSet(Microsoft.Maui.Controls.BindableProperty)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.isset
  name: IsSet(BindableProperty)
  nameWithType: BindableObject.IsSet(BindableProperty)
  fullName: Microsoft.Maui.Controls.BindableObject.IsSet(Microsoft.Maui.Controls.BindableProperty)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.IsSet(Microsoft.Maui.Controls.BindableProperty)
    name: IsSet
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.isset
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.IsSet(Microsoft.Maui.Controls.BindableProperty)
    name: IsSet
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.isset
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.RemoveBinding(Microsoft.Maui.Controls.BindableProperty)
  commentId: M:Microsoft.Maui.Controls.BindableObject.RemoveBinding(Microsoft.Maui.Controls.BindableProperty)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.removebinding
  name: RemoveBinding(BindableProperty)
  nameWithType: BindableObject.RemoveBinding(BindableProperty)
  fullName: Microsoft.Maui.Controls.BindableObject.RemoveBinding(Microsoft.Maui.Controls.BindableProperty)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.RemoveBinding(Microsoft.Maui.Controls.BindableProperty)
    name: RemoveBinding
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.removebinding
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.RemoveBinding(Microsoft.Maui.Controls.BindableProperty)
    name: RemoveBinding
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.removebinding
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.SetBinding(Microsoft.Maui.Controls.BindableProperty,Microsoft.Maui.Controls.BindingBase)
  commentId: M:Microsoft.Maui.Controls.BindableObject.SetBinding(Microsoft.Maui.Controls.BindableProperty,Microsoft.Maui.Controls.BindingBase)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setbinding
  name: SetBinding(BindableProperty, BindingBase)
  nameWithType: BindableObject.SetBinding(BindableProperty, BindingBase)
  fullName: Microsoft.Maui.Controls.BindableObject.SetBinding(Microsoft.Maui.Controls.BindableProperty, Microsoft.Maui.Controls.BindingBase)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.SetBinding(Microsoft.Maui.Controls.BindableProperty,Microsoft.Maui.Controls.BindingBase)
    name: SetBinding
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setbinding
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.BindingBase
    name: BindingBase
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindingbase
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.SetBinding(Microsoft.Maui.Controls.BindableProperty,Microsoft.Maui.Controls.BindingBase)
    name: SetBinding
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setbinding
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.BindingBase
    name: BindingBase
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindingbase
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.ApplyBindings
  commentId: M:Microsoft.Maui.Controls.BindableObject.ApplyBindings
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.applybindings
  name: ApplyBindings()
  nameWithType: BindableObject.ApplyBindings()
  fullName: Microsoft.Maui.Controls.BindableObject.ApplyBindings()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.ApplyBindings
    name: ApplyBindings
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.applybindings
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.ApplyBindings
    name: ApplyBindings
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.applybindings
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(System.String)
  commentId: M:Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(System.String)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.onpropertychanging
  name: OnPropertyChanging(string)
  nameWithType: BindableObject.OnPropertyChanging(string)
  fullName: Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(string)
  nameWithType.vb: BindableObject.OnPropertyChanging(String)
  fullName.vb: Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(String)
  name.vb: OnPropertyChanging(String)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(System.String)
    name: OnPropertyChanging
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.onpropertychanging
  - name: (
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(System.String)
    name: OnPropertyChanging
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.onpropertychanging
  - name: (
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.UnapplyBindings
  commentId: M:Microsoft.Maui.Controls.BindableObject.UnapplyBindings
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.unapplybindings
  name: UnapplyBindings()
  nameWithType: BindableObject.UnapplyBindings()
  fullName: Microsoft.Maui.Controls.BindableObject.UnapplyBindings()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.UnapplyBindings
    name: UnapplyBindings
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.unapplybindings
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.UnapplyBindings
    name: UnapplyBindings
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.unapplybindings
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty,System.Object)
  commentId: M:Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty,System.Object)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindableproperty-system-object)
  name: SetValue(BindableProperty, object)
  nameWithType: BindableObject.SetValue(BindableProperty, object)
  fullName: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty, object)
  nameWithType.vb: BindableObject.SetValue(BindableProperty, Object)
  fullName.vb: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty, Object)
  name.vb: SetValue(BindableProperty, Object)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty,System.Object)
    name: SetValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindableproperty-system-object)
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty,System.Object)
    name: SetValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindableproperty-system-object)
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey,System.Object)
  commentId: M:Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey,System.Object)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindablepropertykey-system-object)
  name: SetValue(BindablePropertyKey, object)
  nameWithType: BindableObject.SetValue(BindablePropertyKey, object)
  fullName: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey, object)
  nameWithType.vb: BindableObject.SetValue(BindablePropertyKey, Object)
  fullName.vb: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey, Object)
  name.vb: SetValue(BindablePropertyKey, Object)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey,System.Object)
    name: SetValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindablepropertykey-system-object)
  - name: (
  - uid: Microsoft.Maui.Controls.BindablePropertyKey
    name: BindablePropertyKey
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindablepropertykey
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey,System.Object)
    name: SetValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindablepropertykey-system-object)
  - name: (
  - uid: Microsoft.Maui.Controls.BindablePropertyKey
    name: BindablePropertyKey
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindablepropertykey
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindableProperty)
  commentId: M:Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindableProperty)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindableproperty)
  name: CoerceValue(BindableProperty)
  nameWithType: BindableObject.CoerceValue(BindableProperty)
  fullName: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindableProperty)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindableProperty)
    name: CoerceValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindableproperty)
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindableProperty)
    name: CoerceValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindableproperty)
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindablePropertyKey)
  commentId: M:Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindablePropertyKey)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindablepropertykey)
  name: CoerceValue(BindablePropertyKey)
  nameWithType: BindableObject.CoerceValue(BindablePropertyKey)
  fullName: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindablePropertyKey)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindablePropertyKey)
    name: CoerceValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindablepropertykey)
  - name: (
  - uid: Microsoft.Maui.Controls.BindablePropertyKey
    name: BindablePropertyKey
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindablepropertykey
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindablePropertyKey)
    name: CoerceValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindablepropertykey)
  - name: (
  - uid: Microsoft.Maui.Controls.BindablePropertyKey
    name: BindablePropertyKey
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindablepropertykey
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.Dispatcher
  commentId: P:Microsoft.Maui.Controls.BindableObject.Dispatcher
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.dispatcher
  name: Dispatcher
  nameWithType: BindableObject.Dispatcher
  fullName: Microsoft.Maui.Controls.BindableObject.Dispatcher
- uid: Microsoft.Maui.Controls.BindableObject.BindingContext
  commentId: P:Microsoft.Maui.Controls.BindableObject.BindingContext
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.bindingcontext
  name: BindingContext
  nameWithType: BindableObject.BindingContext
  fullName: Microsoft.Maui.Controls.BindableObject.BindingContext
- uid: Microsoft.Maui.Controls.BindableObject.PropertyChanged
  commentId: E:Microsoft.Maui.Controls.BindableObject.PropertyChanged
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.propertychanged
  name: PropertyChanged
  nameWithType: BindableObject.PropertyChanged
  fullName: Microsoft.Maui.Controls.BindableObject.PropertyChanged
- uid: Microsoft.Maui.Controls.BindableObject.PropertyChanging
  commentId: E:Microsoft.Maui.Controls.BindableObject.PropertyChanging
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.propertychanging
  name: PropertyChanging
  nameWithType: BindableObject.PropertyChanging
  fullName: Microsoft.Maui.Controls.BindableObject.PropertyChanging
- uid: Microsoft.Maui.Controls.BindableObject.BindingContextChanged
  commentId: E:Microsoft.Maui.Controls.BindableObject.BindingContextChanged
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.bindingcontextchanged
  name: BindingContextChanged
  nameWithType: BindableObject.BindingContextChanged
  fullName: Microsoft.Maui.Controls.BindableObject.BindingContextChanged
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.Element.DrawnUi.Draw.StaticResourcesExtensions.FindParent``1
  commentId: M:DrawnUi.Draw.StaticResourcesExtensions.FindParent``1(Microsoft.Maui.Controls.Element)
  parent: DrawnUi.Draw.StaticResourcesExtensions
  definition: DrawnUi.Draw.StaticResourcesExtensions.FindParent``1(Microsoft.Maui.Controls.Element)
  href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_FindParent__1_Microsoft_Maui_Controls_Element_
  name: FindParent<T>(Element)
  nameWithType: StaticResourcesExtensions.FindParent<T>(Element)
  fullName: DrawnUi.Draw.StaticResourcesExtensions.FindParent<T>(Microsoft.Maui.Controls.Element)
  nameWithType.vb: StaticResourcesExtensions.FindParent(Of T)(Element)
  fullName.vb: DrawnUi.Draw.StaticResourcesExtensions.FindParent(Of T)(Microsoft.Maui.Controls.Element)
  name.vb: FindParent(Of T)(Element)
  spec.csharp:
  - uid: DrawnUi.Draw.StaticResourcesExtensions.FindParent``1(Microsoft.Maui.Controls.Element)
    name: FindParent
    href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_FindParent__1_Microsoft_Maui_Controls_Element_
  - name: <
  - name: T
  - name: '>'
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.StaticResourcesExtensions.FindParent``1(Microsoft.Maui.Controls.Element)
    name: FindParent
    href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_FindParent__1_Microsoft_Maui_Controls_Element_
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: Microsoft.Maui.Controls.Element.DrawnUi.Extensions.InternalExtensions.FindMauiContext(System.Boolean)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element,System.Boolean)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element,System.Boolean)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FindMauiContext_Microsoft_Maui_Controls_Element_System_Boolean_
  name: FindMauiContext(Element, bool)
  nameWithType: InternalExtensions.FindMauiContext(Element, bool)
  fullName: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element, bool)
  nameWithType.vb: InternalExtensions.FindMauiContext(Element, Boolean)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element, Boolean)
  name.vb: FindMauiContext(Element, Boolean)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element,System.Boolean)
    name: FindMauiContext
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FindMauiContext_Microsoft_Maui_Controls_Element_System_Boolean_
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: bool
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element,System.Boolean)
    name: FindMauiContext
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FindMauiContext_Microsoft_Maui_Controls_Element_System_Boolean_
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: Boolean
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
- uid: Microsoft.Maui.Controls.Element.DrawnUi.Extensions.InternalExtensions.GetParentsPath
  commentId: M:DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_GetParentsPath_Microsoft_Maui_Controls_Element_
  name: GetParentsPath(Element)
  nameWithType: InternalExtensions.GetParentsPath(Element)
  fullName: DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
    name: GetParentsPath
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_GetParentsPath_Microsoft_Maui_Controls_Element_
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
    name: GetParentsPath
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_GetParentsPath_Microsoft_Maui_Controls_Element_
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: Microsoft.Maui.Controls
  commentId: N:Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft
  name: Microsoft.Maui.Controls
  nameWithType: Microsoft.Maui.Controls
  fullName: Microsoft.Maui.Controls
  spec.csharp:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Controls
    name: Controls
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls
  spec.vb:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Controls
    name: Controls
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls
- uid: System.ComponentModel
  commentId: N:System.ComponentModel
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.ComponentModel
  nameWithType: System.ComponentModel
  fullName: System.ComponentModel
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.ComponentModel
    name: ComponentModel
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.ComponentModel
    name: ComponentModel
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel
- uid: Microsoft.Maui
  commentId: N:Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft
  name: Microsoft.Maui
  nameWithType: Microsoft.Maui
  fullName: Microsoft.Maui
  spec.csharp:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  spec.vb:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
- uid: DrawnUi.Draw.StaticResourcesExtensions.FindParent``1(Microsoft.Maui.Controls.Element)
  commentId: M:DrawnUi.Draw.StaticResourcesExtensions.FindParent``1(Microsoft.Maui.Controls.Element)
  isExternal: true
  href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_FindParent__1_Microsoft_Maui_Controls_Element_
  name: FindParent<T>(Element)
  nameWithType: StaticResourcesExtensions.FindParent<T>(Element)
  fullName: DrawnUi.Draw.StaticResourcesExtensions.FindParent<T>(Microsoft.Maui.Controls.Element)
  nameWithType.vb: StaticResourcesExtensions.FindParent(Of T)(Element)
  fullName.vb: DrawnUi.Draw.StaticResourcesExtensions.FindParent(Of T)(Microsoft.Maui.Controls.Element)
  name.vb: FindParent(Of T)(Element)
  spec.csharp:
  - uid: DrawnUi.Draw.StaticResourcesExtensions.FindParent``1(Microsoft.Maui.Controls.Element)
    name: FindParent
    href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_FindParent__1_Microsoft_Maui_Controls_Element_
  - name: <
  - name: T
  - name: '>'
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.StaticResourcesExtensions.FindParent``1(Microsoft.Maui.Controls.Element)
    name: FindParent
    href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_FindParent__1_Microsoft_Maui_Controls_Element_
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: DrawnUi.Draw.StaticResourcesExtensions
  commentId: T:DrawnUi.Draw.StaticResourcesExtensions
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.StaticResourcesExtensions.html
  name: StaticResourcesExtensions
  nameWithType: StaticResourcesExtensions
  fullName: DrawnUi.Draw.StaticResourcesExtensions
- uid: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element,System.Boolean)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element,System.Boolean)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FindMauiContext_Microsoft_Maui_Controls_Element_System_Boolean_
  name: FindMauiContext(Element, bool)
  nameWithType: InternalExtensions.FindMauiContext(Element, bool)
  fullName: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element, bool)
  nameWithType.vb: InternalExtensions.FindMauiContext(Element, Boolean)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element, Boolean)
  name.vb: FindMauiContext(Element, Boolean)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element,System.Boolean)
    name: FindMauiContext
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FindMauiContext_Microsoft_Maui_Controls_Element_System_Boolean_
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: bool
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element,System.Boolean)
    name: FindMauiContext
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FindMauiContext_Microsoft_Maui_Controls_Element_System_Boolean_
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: Boolean
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
  commentId: M:DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_GetParentsPath_Microsoft_Maui_Controls_Element_
  name: GetParentsPath(Element)
  nameWithType: InternalExtensions.GetParentsPath(Element)
  fullName: DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
    name: GetParentsPath
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_GetParentsPath_Microsoft_Maui_Controls_Element_
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
    name: GetParentsPath
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_GetParentsPath_Microsoft_Maui_Controls_Element_
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.SvgSpan.#ctor*
  commentId: Overload:DrawnUi.Draw.SvgSpan.#ctor
  href: DrawnUi.Draw.SvgSpan.html#DrawnUi_Draw_SvgSpan__ctor
  name: SvgSpan
  nameWithType: SvgSpan.SvgSpan
  fullName: DrawnUi.Draw.SvgSpan.SvgSpan
  nameWithType.vb: SvgSpan.New
  fullName.vb: DrawnUi.Draw.SvgSpan.New
  name.vb: New
- uid: DrawnUi.Draw.SvgSpan.VerticalAlignement*
  commentId: Overload:DrawnUi.Draw.SvgSpan.VerticalAlignement
  href: DrawnUi.Draw.SvgSpan.html#DrawnUi_Draw_SvgSpan_VerticalAlignement
  name: VerticalAlignement
  nameWithType: SvgSpan.VerticalAlignement
  fullName: DrawnUi.Draw.SvgSpan.VerticalAlignement
- uid: DrawnUi.Draw.IDrawnTextSpan.VerticalAlignement
  commentId: P:DrawnUi.Draw.IDrawnTextSpan.VerticalAlignement
  parent: DrawnUi.Draw.IDrawnTextSpan
  href: DrawnUi.Draw.IDrawnTextSpan.html#DrawnUi_Draw_IDrawnTextSpan_VerticalAlignement
  name: VerticalAlignement
  nameWithType: IDrawnTextSpan.VerticalAlignement
  fullName: DrawnUi.Draw.IDrawnTextSpan.VerticalAlignement
- uid: DrawnUi.Draw.DrawImageAlignment
  commentId: T:DrawnUi.Draw.DrawImageAlignment
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.DrawImageAlignment.html
  name: DrawImageAlignment
  nameWithType: DrawImageAlignment
  fullName: DrawnUi.Draw.DrawImageAlignment
- uid: DrawnUi.Draw.SvgSpan.Height*
  commentId: Overload:DrawnUi.Draw.SvgSpan.Height
  href: DrawnUi.Draw.SvgSpan.html#DrawnUi_Draw_SvgSpan_Height
  name: Height
  nameWithType: SvgSpan.Height
  fullName: DrawnUi.Draw.SvgSpan.Height
- uid: System.Double
  commentId: T:System.Double
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.double
  name: double
  nameWithType: double
  fullName: double
  nameWithType.vb: Double
  fullName.vb: Double
  name.vb: Double
- uid: DrawnUi.Draw.SvgSpan.Width*
  commentId: Overload:DrawnUi.Draw.SvgSpan.Width
  href: DrawnUi.Draw.SvgSpan.html#DrawnUi_Draw_SvgSpan_Width
  name: Width
  nameWithType: SvgSpan.Width
  fullName: DrawnUi.Draw.SvgSpan.Width
- uid: DrawnUi.Draw.SvgSpan.TintColor*
  commentId: Overload:DrawnUi.Draw.SvgSpan.TintColor
  href: DrawnUi.Draw.SvgSpan.html#DrawnUi_Draw_SvgSpan_TintColor
  name: TintColor
  nameWithType: SvgSpan.TintColor
  fullName: DrawnUi.Draw.SvgSpan.TintColor
- uid: Microsoft.Maui.Graphics.Color
  commentId: T:Microsoft.Maui.Graphics.Color
  parent: Microsoft.Maui.Graphics
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.color
  name: Color
  nameWithType: Color
  fullName: Microsoft.Maui.Graphics.Color
- uid: Microsoft.Maui.Graphics
  commentId: N:Microsoft.Maui.Graphics
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft
  name: Microsoft.Maui.Graphics
  nameWithType: Microsoft.Maui.Graphics
  fullName: Microsoft.Maui.Graphics
  spec.csharp:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Graphics
    name: Graphics
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics
  spec.vb:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Graphics
    name: Graphics
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics
- uid: DrawnUi.Draw.SvgSpan.Source*
  commentId: Overload:DrawnUi.Draw.SvgSpan.Source
  href: DrawnUi.Draw.SvgSpan.html#DrawnUi_Draw_SvgSpan_Source
  name: Source
  nameWithType: SvgSpan.Source
  fullName: DrawnUi.Draw.SvgSpan.Source
- uid: System.String
  commentId: T:System.String
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.string
  name: string
  nameWithType: string
  fullName: string
  nameWithType.vb: String
  fullName.vb: String
  name.vb: String
- uid: DrawnUi.Draw.SkiaSvg
  commentId: T:DrawnUi.Draw.SkiaSvg
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaSvg.html
  name: SkiaSvg
  nameWithType: SkiaSvg
  fullName: DrawnUi.Draw.SkiaSvg
- uid: DrawnUi.Draw.TextSpan.Dispose
  commentId: M:DrawnUi.Draw.TextSpan.Dispose
  parent: DrawnUi.Draw.TextSpan
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_Dispose
  name: Dispose()
  nameWithType: TextSpan.Dispose()
  fullName: DrawnUi.Draw.TextSpan.Dispose()
  spec.csharp:
  - uid: DrawnUi.Draw.TextSpan.Dispose
    name: Dispose
    href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_Dispose
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.TextSpan.Dispose
    name: Dispose
    href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_Dispose
  - name: (
  - name: )
- uid: DrawnUi.Draw.SvgSpan.Dispose*
  commentId: Overload:DrawnUi.Draw.SvgSpan.Dispose
  href: DrawnUi.Draw.SvgSpan.html#DrawnUi_Draw_SvgSpan_Dispose
  name: Dispose
  nameWithType: SvgSpan.Dispose
  fullName: DrawnUi.Draw.SvgSpan.Dispose
