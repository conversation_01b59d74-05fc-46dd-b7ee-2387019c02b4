### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.BaseColorFilterEffect
  commentId: T:DrawnUi.Draw.BaseColorFilterEffect
  id: BaseColorFilterEffect
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.BaseColorFilterEffect.CreateFilter(SkiaSharp.SKRect)
  - DrawnUi.Draw.BaseColorFilterEffect.Filter
  - DrawnUi.Draw.BaseColorFilterEffect.OnDisposing
  - DrawnUi.Draw.BaseColorFilterEffect.Update
  langs:
  - csharp
  - vb
  name: BaseColorFilterEffect
  nameWithType: BaseColorFilterEffect
  fullName: DrawnUi.Draw.BaseColorFilterEffect
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Effects/BaseColorFilterEffect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: BaseColorFilterEffect
    path: ../src/Maui/DrawnUi/Features/Effects/BaseColorFilterEffect.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: 'public class BaseColorFilterEffect : SkiaEffect, INotifyPropertyChanged, IDisposable, IColorEffect, ISkiaEffect, ICanBeUpdatedWithContext, ICanBeUpdated'
    content.vb: Public Class BaseColorFilterEffect Inherits SkiaEffect Implements INotifyPropertyChanged, IDisposable, IColorEffect, ISkiaEffect, ICanBeUpdatedWithContext, ICanBeUpdated
  inheritance:
  - System.Object
  - Microsoft.Maui.Controls.BindableObject
  - DrawnUi.Draw.SkiaEffect
  derivedClasses:
  - DrawnUi.Draw.AdjustBrightnessEffect
  - DrawnUi.Draw.AdjustRGBEffect
  - DrawnUi.Draw.ColorPresetEffect
  - DrawnUi.Draw.ContrastEffect
  - DrawnUi.Draw.SaturationEffect
  - DrawnUi.Draw.TintEffect
  - DrawnUi.Draw.TintWithAlphaEffect
  implements:
  - System.ComponentModel.INotifyPropertyChanged
  - System.IDisposable
  - DrawnUi.Draw.IColorEffect
  - DrawnUi.Draw.ISkiaEffect
  - DrawnUi.Draw.ICanBeUpdatedWithContext
  - DrawnUi.Draw.ICanBeUpdated
  inheritedMembers:
  - DrawnUi.Draw.SkiaEffect.Parent
  - DrawnUi.Draw.SkiaEffect.Attach(DrawnUi.Draw.SkiaControl)
  - DrawnUi.Draw.SkiaEffect.Dettach
  - DrawnUi.Draw.SkiaEffect.Dispose
  - DrawnUi.Draw.SkiaEffect.NeedUpdate(Microsoft.Maui.Controls.BindableObject,System.Object,System.Object)
  - DrawnUi.Draw.SkiaEffect.NeedApply
  - Microsoft.Maui.Controls.BindableObject.BindingContextProperty
  - Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindableProperty)
  - Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindablePropertyKey)
  - Microsoft.Maui.Controls.BindableObject.GetValue(Microsoft.Maui.Controls.BindableProperty)
  - Microsoft.Maui.Controls.BindableObject.IsSet(Microsoft.Maui.Controls.BindableProperty)
  - Microsoft.Maui.Controls.BindableObject.RemoveBinding(Microsoft.Maui.Controls.BindableProperty)
  - Microsoft.Maui.Controls.BindableObject.SetBinding(Microsoft.Maui.Controls.BindableProperty,Microsoft.Maui.Controls.BindingBase)
  - Microsoft.Maui.Controls.BindableObject.ApplyBindings
  - Microsoft.Maui.Controls.BindableObject.OnBindingContextChanged
  - Microsoft.Maui.Controls.BindableObject.OnPropertyChanged(System.String)
  - Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(System.String)
  - Microsoft.Maui.Controls.BindableObject.UnapplyBindings
  - Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty,System.Object)
  - Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey,System.Object)
  - Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindableProperty)
  - Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindablePropertyKey)
  - Microsoft.Maui.Controls.BindableObject.Dispatcher
  - Microsoft.Maui.Controls.BindableObject.BindingContext
  - Microsoft.Maui.Controls.BindableObject.PropertyChanged
  - Microsoft.Maui.Controls.BindableObject.PropertyChanging
  - Microsoft.Maui.Controls.BindableObject.BindingContextChanged
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.BaseColorFilterEffect.Filter
  commentId: P:DrawnUi.Draw.BaseColorFilterEffect.Filter
  id: Filter
  parent: DrawnUi.Draw.BaseColorFilterEffect
  langs:
  - csharp
  - vb
  name: Filter
  nameWithType: BaseColorFilterEffect.Filter
  fullName: DrawnUi.Draw.BaseColorFilterEffect.Filter
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Effects/BaseColorFilterEffect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Filter
    path: ../src/Maui/DrawnUi/Features/Effects/BaseColorFilterEffect.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  example: []
  syntax:
    content: public SKColorFilter Filter { get; set; }
    parameters: []
    return:
      type: SkiaSharp.SKColorFilter
    content.vb: Public Property Filter As SKColorFilter
  overload: DrawnUi.Draw.BaseColorFilterEffect.Filter*
  implements:
  - DrawnUi.Draw.IColorEffect.Filter
- uid: DrawnUi.Draw.BaseColorFilterEffect.CreateFilter(SkiaSharp.SKRect)
  commentId: M:DrawnUi.Draw.BaseColorFilterEffect.CreateFilter(SkiaSharp.SKRect)
  id: CreateFilter(SkiaSharp.SKRect)
  parent: DrawnUi.Draw.BaseColorFilterEffect
  langs:
  - csharp
  - vb
  name: CreateFilter(SKRect)
  nameWithType: BaseColorFilterEffect.CreateFilter(SKRect)
  fullName: DrawnUi.Draw.BaseColorFilterEffect.CreateFilter(SkiaSharp.SKRect)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Effects/BaseColorFilterEffect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: CreateFilter
    path: ../src/Maui/DrawnUi/Features/Effects/BaseColorFilterEffect.cs
    startLine: 6
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  example: []
  syntax:
    content: public virtual SKColorFilter CreateFilter(SKRect destination)
    parameters:
    - id: destination
      type: SkiaSharp.SKRect
    return:
      type: SkiaSharp.SKColorFilter
    content.vb: Public Overridable Function CreateFilter(destination As SKRect) As SKColorFilter
  overload: DrawnUi.Draw.BaseColorFilterEffect.CreateFilter*
  implements:
  - DrawnUi.Draw.IColorEffect.CreateFilter(SkiaSharp.SKRect)
- uid: DrawnUi.Draw.BaseColorFilterEffect.Update
  commentId: M:DrawnUi.Draw.BaseColorFilterEffect.Update
  id: Update
  parent: DrawnUi.Draw.BaseColorFilterEffect
  langs:
  - csharp
  - vb
  name: Update()
  nameWithType: BaseColorFilterEffect.Update()
  fullName: DrawnUi.Draw.BaseColorFilterEffect.Update()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Effects/BaseColorFilterEffect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Update
    path: ../src/Maui/DrawnUi/Features/Effects/BaseColorFilterEffect.cs
    startLine: 11
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: You MUST clear any cached resources as soon as this is called. Not on next draw!
  example: []
  syntax:
    content: public override void Update()
    content.vb: Public Overrides Sub Update()
  overridden: DrawnUi.Draw.SkiaEffect.Update
  overload: DrawnUi.Draw.BaseColorFilterEffect.Update*
  implements:
  - DrawnUi.Draw.ICanBeUpdated.Update
- uid: DrawnUi.Draw.BaseColorFilterEffect.OnDisposing
  commentId: M:DrawnUi.Draw.BaseColorFilterEffect.OnDisposing
  id: OnDisposing
  parent: DrawnUi.Draw.BaseColorFilterEffect
  langs:
  - csharp
  - vb
  name: OnDisposing()
  nameWithType: BaseColorFilterEffect.OnDisposing()
  fullName: DrawnUi.Draw.BaseColorFilterEffect.OnDisposing()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Effects/BaseColorFilterEffect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: OnDisposing
    path: ../src/Maui/DrawnUi/Features/Effects/BaseColorFilterEffect.cs
    startLine: 20
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  example: []
  syntax:
    content: protected override void OnDisposing()
    content.vb: Protected Overrides Sub OnDisposing()
  overridden: DrawnUi.Draw.SkiaEffect.OnDisposing
  overload: DrawnUi.Draw.BaseColorFilterEffect.OnDisposing*
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: Microsoft.Maui.Controls.BindableObject
  commentId: T:Microsoft.Maui.Controls.BindableObject
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject
  name: BindableObject
  nameWithType: BindableObject
  fullName: Microsoft.Maui.Controls.BindableObject
- uid: DrawnUi.Draw.SkiaEffect
  commentId: T:DrawnUi.Draw.SkiaEffect
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaEffect.html
  name: SkiaEffect
  nameWithType: SkiaEffect
  fullName: DrawnUi.Draw.SkiaEffect
- uid: System.ComponentModel.INotifyPropertyChanged
  commentId: T:System.ComponentModel.INotifyPropertyChanged
  parent: System.ComponentModel
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.inotifypropertychanged
  name: INotifyPropertyChanged
  nameWithType: INotifyPropertyChanged
  fullName: System.ComponentModel.INotifyPropertyChanged
- uid: System.IDisposable
  commentId: T:System.IDisposable
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.idisposable
  name: IDisposable
  nameWithType: IDisposable
  fullName: System.IDisposable
- uid: DrawnUi.Draw.IColorEffect
  commentId: T:DrawnUi.Draw.IColorEffect
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.IColorEffect.html
  name: IColorEffect
  nameWithType: IColorEffect
  fullName: DrawnUi.Draw.IColorEffect
- uid: DrawnUi.Draw.ISkiaEffect
  commentId: T:DrawnUi.Draw.ISkiaEffect
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ISkiaEffect.html
  name: ISkiaEffect
  nameWithType: ISkiaEffect
  fullName: DrawnUi.Draw.ISkiaEffect
- uid: DrawnUi.Draw.ICanBeUpdatedWithContext
  commentId: T:DrawnUi.Draw.ICanBeUpdatedWithContext
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ICanBeUpdatedWithContext.html
  name: ICanBeUpdatedWithContext
  nameWithType: ICanBeUpdatedWithContext
  fullName: DrawnUi.Draw.ICanBeUpdatedWithContext
- uid: DrawnUi.Draw.ICanBeUpdated
  commentId: T:DrawnUi.Draw.ICanBeUpdated
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ICanBeUpdated.html
  name: ICanBeUpdated
  nameWithType: ICanBeUpdated
  fullName: DrawnUi.Draw.ICanBeUpdated
- uid: DrawnUi.Draw.SkiaEffect.Parent
  commentId: P:DrawnUi.Draw.SkiaEffect.Parent
  parent: DrawnUi.Draw.SkiaEffect
  href: DrawnUi.Draw.SkiaEffect.html#DrawnUi_Draw_SkiaEffect_Parent
  name: Parent
  nameWithType: SkiaEffect.Parent
  fullName: DrawnUi.Draw.SkiaEffect.Parent
- uid: DrawnUi.Draw.SkiaEffect.Attach(DrawnUi.Draw.SkiaControl)
  commentId: M:DrawnUi.Draw.SkiaEffect.Attach(DrawnUi.Draw.SkiaControl)
  parent: DrawnUi.Draw.SkiaEffect
  href: DrawnUi.Draw.SkiaEffect.html#DrawnUi_Draw_SkiaEffect_Attach_DrawnUi_Draw_SkiaControl_
  name: Attach(SkiaControl)
  nameWithType: SkiaEffect.Attach(SkiaControl)
  fullName: DrawnUi.Draw.SkiaEffect.Attach(DrawnUi.Draw.SkiaControl)
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaEffect.Attach(DrawnUi.Draw.SkiaControl)
    name: Attach
    href: DrawnUi.Draw.SkiaEffect.html#DrawnUi_Draw_SkiaEffect_Attach_DrawnUi_Draw_SkiaControl_
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SkiaEffect.Attach(DrawnUi.Draw.SkiaControl)
    name: Attach
    href: DrawnUi.Draw.SkiaEffect.html#DrawnUi_Draw_SkiaEffect_Attach_DrawnUi_Draw_SkiaControl_
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
- uid: DrawnUi.Draw.SkiaEffect.Dettach
  commentId: M:DrawnUi.Draw.SkiaEffect.Dettach
  parent: DrawnUi.Draw.SkiaEffect
  href: DrawnUi.Draw.SkiaEffect.html#DrawnUi_Draw_SkiaEffect_Dettach
  name: Dettach()
  nameWithType: SkiaEffect.Dettach()
  fullName: DrawnUi.Draw.SkiaEffect.Dettach()
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaEffect.Dettach
    name: Dettach
    href: DrawnUi.Draw.SkiaEffect.html#DrawnUi_Draw_SkiaEffect_Dettach
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SkiaEffect.Dettach
    name: Dettach
    href: DrawnUi.Draw.SkiaEffect.html#DrawnUi_Draw_SkiaEffect_Dettach
  - name: (
  - name: )
- uid: DrawnUi.Draw.SkiaEffect.Dispose
  commentId: M:DrawnUi.Draw.SkiaEffect.Dispose
  parent: DrawnUi.Draw.SkiaEffect
  href: DrawnUi.Draw.SkiaEffect.html#DrawnUi_Draw_SkiaEffect_Dispose
  name: Dispose()
  nameWithType: SkiaEffect.Dispose()
  fullName: DrawnUi.Draw.SkiaEffect.Dispose()
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaEffect.Dispose
    name: Dispose
    href: DrawnUi.Draw.SkiaEffect.html#DrawnUi_Draw_SkiaEffect_Dispose
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SkiaEffect.Dispose
    name: Dispose
    href: DrawnUi.Draw.SkiaEffect.html#DrawnUi_Draw_SkiaEffect_Dispose
  - name: (
  - name: )
- uid: DrawnUi.Draw.SkiaEffect.NeedUpdate(Microsoft.Maui.Controls.BindableObject,System.Object,System.Object)
  commentId: M:DrawnUi.Draw.SkiaEffect.NeedUpdate(Microsoft.Maui.Controls.BindableObject,System.Object,System.Object)
  parent: DrawnUi.Draw.SkiaEffect
  isExternal: true
  href: DrawnUi.Draw.SkiaEffect.html#DrawnUi_Draw_SkiaEffect_NeedUpdate_Microsoft_Maui_Controls_BindableObject_System_Object_System_Object_
  name: NeedUpdate(BindableObject, object, object)
  nameWithType: SkiaEffect.NeedUpdate(BindableObject, object, object)
  fullName: DrawnUi.Draw.SkiaEffect.NeedUpdate(Microsoft.Maui.Controls.BindableObject, object, object)
  nameWithType.vb: SkiaEffect.NeedUpdate(BindableObject, Object, Object)
  fullName.vb: DrawnUi.Draw.SkiaEffect.NeedUpdate(Microsoft.Maui.Controls.BindableObject, Object, Object)
  name.vb: NeedUpdate(BindableObject, Object, Object)
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaEffect.NeedUpdate(Microsoft.Maui.Controls.BindableObject,System.Object,System.Object)
    name: NeedUpdate
    href: DrawnUi.Draw.SkiaEffect.html#DrawnUi_Draw_SkiaEffect_NeedUpdate_Microsoft_Maui_Controls_BindableObject_System_Object_System_Object_
  - name: (
  - uid: Microsoft.Maui.Controls.BindableObject
    name: BindableObject
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SkiaEffect.NeedUpdate(Microsoft.Maui.Controls.BindableObject,System.Object,System.Object)
    name: NeedUpdate
    href: DrawnUi.Draw.SkiaEffect.html#DrawnUi_Draw_SkiaEffect_NeedUpdate_Microsoft_Maui_Controls_BindableObject_System_Object_System_Object_
  - name: (
  - uid: Microsoft.Maui.Controls.BindableObject
    name: BindableObject
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Draw.SkiaEffect.NeedApply
  commentId: P:DrawnUi.Draw.SkiaEffect.NeedApply
  parent: DrawnUi.Draw.SkiaEffect
  href: DrawnUi.Draw.SkiaEffect.html#DrawnUi_Draw_SkiaEffect_NeedApply
  name: NeedApply
  nameWithType: SkiaEffect.NeedApply
  fullName: DrawnUi.Draw.SkiaEffect.NeedApply
- uid: Microsoft.Maui.Controls.BindableObject.BindingContextProperty
  commentId: F:Microsoft.Maui.Controls.BindableObject.BindingContextProperty
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.bindingcontextproperty
  name: BindingContextProperty
  nameWithType: BindableObject.BindingContextProperty
  fullName: Microsoft.Maui.Controls.BindableObject.BindingContextProperty
- uid: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindableProperty)
  commentId: M:Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindableProperty)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindableproperty)
  name: ClearValue(BindableProperty)
  nameWithType: BindableObject.ClearValue(BindableProperty)
  fullName: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindableProperty)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindableProperty)
    name: ClearValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindableproperty)
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindableProperty)
    name: ClearValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindableproperty)
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindablePropertyKey)
  commentId: M:Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindablePropertyKey)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindablepropertykey)
  name: ClearValue(BindablePropertyKey)
  nameWithType: BindableObject.ClearValue(BindablePropertyKey)
  fullName: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindablePropertyKey)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindablePropertyKey)
    name: ClearValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindablepropertykey)
  - name: (
  - uid: Microsoft.Maui.Controls.BindablePropertyKey
    name: BindablePropertyKey
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindablepropertykey
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindablePropertyKey)
    name: ClearValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindablepropertykey)
  - name: (
  - uid: Microsoft.Maui.Controls.BindablePropertyKey
    name: BindablePropertyKey
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindablepropertykey
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.GetValue(Microsoft.Maui.Controls.BindableProperty)
  commentId: M:Microsoft.Maui.Controls.BindableObject.GetValue(Microsoft.Maui.Controls.BindableProperty)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.getvalue
  name: GetValue(BindableProperty)
  nameWithType: BindableObject.GetValue(BindableProperty)
  fullName: Microsoft.Maui.Controls.BindableObject.GetValue(Microsoft.Maui.Controls.BindableProperty)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.GetValue(Microsoft.Maui.Controls.BindableProperty)
    name: GetValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.getvalue
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.GetValue(Microsoft.Maui.Controls.BindableProperty)
    name: GetValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.getvalue
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.IsSet(Microsoft.Maui.Controls.BindableProperty)
  commentId: M:Microsoft.Maui.Controls.BindableObject.IsSet(Microsoft.Maui.Controls.BindableProperty)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.isset
  name: IsSet(BindableProperty)
  nameWithType: BindableObject.IsSet(BindableProperty)
  fullName: Microsoft.Maui.Controls.BindableObject.IsSet(Microsoft.Maui.Controls.BindableProperty)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.IsSet(Microsoft.Maui.Controls.BindableProperty)
    name: IsSet
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.isset
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.IsSet(Microsoft.Maui.Controls.BindableProperty)
    name: IsSet
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.isset
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.RemoveBinding(Microsoft.Maui.Controls.BindableProperty)
  commentId: M:Microsoft.Maui.Controls.BindableObject.RemoveBinding(Microsoft.Maui.Controls.BindableProperty)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.removebinding
  name: RemoveBinding(BindableProperty)
  nameWithType: BindableObject.RemoveBinding(BindableProperty)
  fullName: Microsoft.Maui.Controls.BindableObject.RemoveBinding(Microsoft.Maui.Controls.BindableProperty)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.RemoveBinding(Microsoft.Maui.Controls.BindableProperty)
    name: RemoveBinding
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.removebinding
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.RemoveBinding(Microsoft.Maui.Controls.BindableProperty)
    name: RemoveBinding
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.removebinding
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.SetBinding(Microsoft.Maui.Controls.BindableProperty,Microsoft.Maui.Controls.BindingBase)
  commentId: M:Microsoft.Maui.Controls.BindableObject.SetBinding(Microsoft.Maui.Controls.BindableProperty,Microsoft.Maui.Controls.BindingBase)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setbinding
  name: SetBinding(BindableProperty, BindingBase)
  nameWithType: BindableObject.SetBinding(BindableProperty, BindingBase)
  fullName: Microsoft.Maui.Controls.BindableObject.SetBinding(Microsoft.Maui.Controls.BindableProperty, Microsoft.Maui.Controls.BindingBase)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.SetBinding(Microsoft.Maui.Controls.BindableProperty,Microsoft.Maui.Controls.BindingBase)
    name: SetBinding
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setbinding
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.BindingBase
    name: BindingBase
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindingbase
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.SetBinding(Microsoft.Maui.Controls.BindableProperty,Microsoft.Maui.Controls.BindingBase)
    name: SetBinding
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setbinding
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.BindingBase
    name: BindingBase
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindingbase
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.ApplyBindings
  commentId: M:Microsoft.Maui.Controls.BindableObject.ApplyBindings
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.applybindings
  name: ApplyBindings()
  nameWithType: BindableObject.ApplyBindings()
  fullName: Microsoft.Maui.Controls.BindableObject.ApplyBindings()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.ApplyBindings
    name: ApplyBindings
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.applybindings
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.ApplyBindings
    name: ApplyBindings
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.applybindings
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.OnBindingContextChanged
  commentId: M:Microsoft.Maui.Controls.BindableObject.OnBindingContextChanged
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.onbindingcontextchanged
  name: OnBindingContextChanged()
  nameWithType: BindableObject.OnBindingContextChanged()
  fullName: Microsoft.Maui.Controls.BindableObject.OnBindingContextChanged()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.OnBindingContextChanged
    name: OnBindingContextChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.onbindingcontextchanged
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.OnBindingContextChanged
    name: OnBindingContextChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.onbindingcontextchanged
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.OnPropertyChanged(System.String)
  commentId: M:Microsoft.Maui.Controls.BindableObject.OnPropertyChanged(System.String)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.onpropertychanged
  name: OnPropertyChanged(string)
  nameWithType: BindableObject.OnPropertyChanged(string)
  fullName: Microsoft.Maui.Controls.BindableObject.OnPropertyChanged(string)
  nameWithType.vb: BindableObject.OnPropertyChanged(String)
  fullName.vb: Microsoft.Maui.Controls.BindableObject.OnPropertyChanged(String)
  name.vb: OnPropertyChanged(String)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.OnPropertyChanged(System.String)
    name: OnPropertyChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.onpropertychanged
  - name: (
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.OnPropertyChanged(System.String)
    name: OnPropertyChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.onpropertychanged
  - name: (
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(System.String)
  commentId: M:Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(System.String)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.onpropertychanging
  name: OnPropertyChanging(string)
  nameWithType: BindableObject.OnPropertyChanging(string)
  fullName: Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(string)
  nameWithType.vb: BindableObject.OnPropertyChanging(String)
  fullName.vb: Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(String)
  name.vb: OnPropertyChanging(String)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(System.String)
    name: OnPropertyChanging
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.onpropertychanging
  - name: (
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(System.String)
    name: OnPropertyChanging
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.onpropertychanging
  - name: (
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.UnapplyBindings
  commentId: M:Microsoft.Maui.Controls.BindableObject.UnapplyBindings
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.unapplybindings
  name: UnapplyBindings()
  nameWithType: BindableObject.UnapplyBindings()
  fullName: Microsoft.Maui.Controls.BindableObject.UnapplyBindings()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.UnapplyBindings
    name: UnapplyBindings
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.unapplybindings
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.UnapplyBindings
    name: UnapplyBindings
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.unapplybindings
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty,System.Object)
  commentId: M:Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty,System.Object)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindableproperty-system-object)
  name: SetValue(BindableProperty, object)
  nameWithType: BindableObject.SetValue(BindableProperty, object)
  fullName: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty, object)
  nameWithType.vb: BindableObject.SetValue(BindableProperty, Object)
  fullName.vb: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty, Object)
  name.vb: SetValue(BindableProperty, Object)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty,System.Object)
    name: SetValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindableproperty-system-object)
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty,System.Object)
    name: SetValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindableproperty-system-object)
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey,System.Object)
  commentId: M:Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey,System.Object)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindablepropertykey-system-object)
  name: SetValue(BindablePropertyKey, object)
  nameWithType: BindableObject.SetValue(BindablePropertyKey, object)
  fullName: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey, object)
  nameWithType.vb: BindableObject.SetValue(BindablePropertyKey, Object)
  fullName.vb: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey, Object)
  name.vb: SetValue(BindablePropertyKey, Object)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey,System.Object)
    name: SetValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindablepropertykey-system-object)
  - name: (
  - uid: Microsoft.Maui.Controls.BindablePropertyKey
    name: BindablePropertyKey
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindablepropertykey
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey,System.Object)
    name: SetValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindablepropertykey-system-object)
  - name: (
  - uid: Microsoft.Maui.Controls.BindablePropertyKey
    name: BindablePropertyKey
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindablepropertykey
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindableProperty)
  commentId: M:Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindableProperty)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindableproperty)
  name: CoerceValue(BindableProperty)
  nameWithType: BindableObject.CoerceValue(BindableProperty)
  fullName: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindableProperty)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindableProperty)
    name: CoerceValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindableproperty)
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindableProperty)
    name: CoerceValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindableproperty)
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindablePropertyKey)
  commentId: M:Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindablePropertyKey)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindablepropertykey)
  name: CoerceValue(BindablePropertyKey)
  nameWithType: BindableObject.CoerceValue(BindablePropertyKey)
  fullName: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindablePropertyKey)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindablePropertyKey)
    name: CoerceValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindablepropertykey)
  - name: (
  - uid: Microsoft.Maui.Controls.BindablePropertyKey
    name: BindablePropertyKey
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindablepropertykey
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindablePropertyKey)
    name: CoerceValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindablepropertykey)
  - name: (
  - uid: Microsoft.Maui.Controls.BindablePropertyKey
    name: BindablePropertyKey
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindablepropertykey
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.Dispatcher
  commentId: P:Microsoft.Maui.Controls.BindableObject.Dispatcher
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.dispatcher
  name: Dispatcher
  nameWithType: BindableObject.Dispatcher
  fullName: Microsoft.Maui.Controls.BindableObject.Dispatcher
- uid: Microsoft.Maui.Controls.BindableObject.BindingContext
  commentId: P:Microsoft.Maui.Controls.BindableObject.BindingContext
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.bindingcontext
  name: BindingContext
  nameWithType: BindableObject.BindingContext
  fullName: Microsoft.Maui.Controls.BindableObject.BindingContext
- uid: Microsoft.Maui.Controls.BindableObject.PropertyChanged
  commentId: E:Microsoft.Maui.Controls.BindableObject.PropertyChanged
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.propertychanged
  name: PropertyChanged
  nameWithType: BindableObject.PropertyChanged
  fullName: Microsoft.Maui.Controls.BindableObject.PropertyChanged
- uid: Microsoft.Maui.Controls.BindableObject.PropertyChanging
  commentId: E:Microsoft.Maui.Controls.BindableObject.PropertyChanging
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.propertychanging
  name: PropertyChanging
  nameWithType: BindableObject.PropertyChanging
  fullName: Microsoft.Maui.Controls.BindableObject.PropertyChanging
- uid: Microsoft.Maui.Controls.BindableObject.BindingContextChanged
  commentId: E:Microsoft.Maui.Controls.BindableObject.BindingContextChanged
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.bindingcontextchanged
  name: BindingContextChanged
  nameWithType: BindableObject.BindingContextChanged
  fullName: Microsoft.Maui.Controls.BindableObject.BindingContextChanged
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: Microsoft.Maui.Controls
  commentId: N:Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft
  name: Microsoft.Maui.Controls
  nameWithType: Microsoft.Maui.Controls
  fullName: Microsoft.Maui.Controls
  spec.csharp:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Controls
    name: Controls
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls
  spec.vb:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Controls
    name: Controls
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls
- uid: System.ComponentModel
  commentId: N:System.ComponentModel
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.ComponentModel
  nameWithType: System.ComponentModel
  fullName: System.ComponentModel
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.ComponentModel
    name: ComponentModel
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.ComponentModel
    name: ComponentModel
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.BaseColorFilterEffect.Filter*
  commentId: Overload:DrawnUi.Draw.BaseColorFilterEffect.Filter
  href: DrawnUi.Draw.BaseColorFilterEffect.html#DrawnUi_Draw_BaseColorFilterEffect_Filter
  name: Filter
  nameWithType: BaseColorFilterEffect.Filter
  fullName: DrawnUi.Draw.BaseColorFilterEffect.Filter
- uid: DrawnUi.Draw.IColorEffect.Filter
  commentId: P:DrawnUi.Draw.IColorEffect.Filter
  parent: DrawnUi.Draw.IColorEffect
  href: DrawnUi.Draw.IColorEffect.html#DrawnUi_Draw_IColorEffect_Filter
  name: Filter
  nameWithType: IColorEffect.Filter
  fullName: DrawnUi.Draw.IColorEffect.Filter
- uid: SkiaSharp.SKColorFilter
  commentId: T:SkiaSharp.SKColorFilter
  parent: SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.skcolorfilter
  name: SKColorFilter
  nameWithType: SKColorFilter
  fullName: SkiaSharp.SKColorFilter
- uid: SkiaSharp
  commentId: N:SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp
  name: SkiaSharp
  nameWithType: SkiaSharp
  fullName: SkiaSharp
- uid: DrawnUi.Draw.BaseColorFilterEffect.CreateFilter*
  commentId: Overload:DrawnUi.Draw.BaseColorFilterEffect.CreateFilter
  href: DrawnUi.Draw.BaseColorFilterEffect.html#DrawnUi_Draw_BaseColorFilterEffect_CreateFilter_SkiaSharp_SKRect_
  name: CreateFilter
  nameWithType: BaseColorFilterEffect.CreateFilter
  fullName: DrawnUi.Draw.BaseColorFilterEffect.CreateFilter
- uid: DrawnUi.Draw.IColorEffect.CreateFilter(SkiaSharp.SKRect)
  commentId: M:DrawnUi.Draw.IColorEffect.CreateFilter(SkiaSharp.SKRect)
  parent: DrawnUi.Draw.IColorEffect
  isExternal: true
  href: DrawnUi.Draw.IColorEffect.html#DrawnUi_Draw_IColorEffect_CreateFilter_SkiaSharp_SKRect_
  name: CreateFilter(SKRect)
  nameWithType: IColorEffect.CreateFilter(SKRect)
  fullName: DrawnUi.Draw.IColorEffect.CreateFilter(SkiaSharp.SKRect)
  spec.csharp:
  - uid: DrawnUi.Draw.IColorEffect.CreateFilter(SkiaSharp.SKRect)
    name: CreateFilter
    href: DrawnUi.Draw.IColorEffect.html#DrawnUi_Draw_IColorEffect_CreateFilter_SkiaSharp_SKRect_
  - name: (
  - uid: SkiaSharp.SKRect
    name: SKRect
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skrect
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.IColorEffect.CreateFilter(SkiaSharp.SKRect)
    name: CreateFilter
    href: DrawnUi.Draw.IColorEffect.html#DrawnUi_Draw_IColorEffect_CreateFilter_SkiaSharp_SKRect_
  - name: (
  - uid: SkiaSharp.SKRect
    name: SKRect
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skrect
  - name: )
- uid: SkiaSharp.SKRect
  commentId: T:SkiaSharp.SKRect
  parent: SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.skrect
  name: SKRect
  nameWithType: SKRect
  fullName: SkiaSharp.SKRect
- uid: DrawnUi.Draw.SkiaEffect.Update
  commentId: M:DrawnUi.Draw.SkiaEffect.Update
  parent: DrawnUi.Draw.SkiaEffect
  href: DrawnUi.Draw.SkiaEffect.html#DrawnUi_Draw_SkiaEffect_Update
  name: Update()
  nameWithType: SkiaEffect.Update()
  fullName: DrawnUi.Draw.SkiaEffect.Update()
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaEffect.Update
    name: Update
    href: DrawnUi.Draw.SkiaEffect.html#DrawnUi_Draw_SkiaEffect_Update
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SkiaEffect.Update
    name: Update
    href: DrawnUi.Draw.SkiaEffect.html#DrawnUi_Draw_SkiaEffect_Update
  - name: (
  - name: )
- uid: DrawnUi.Draw.BaseColorFilterEffect.Update*
  commentId: Overload:DrawnUi.Draw.BaseColorFilterEffect.Update
  href: DrawnUi.Draw.BaseColorFilterEffect.html#DrawnUi_Draw_BaseColorFilterEffect_Update
  name: Update
  nameWithType: BaseColorFilterEffect.Update
  fullName: DrawnUi.Draw.BaseColorFilterEffect.Update
- uid: DrawnUi.Draw.ICanBeUpdated.Update
  commentId: M:DrawnUi.Draw.ICanBeUpdated.Update
  parent: DrawnUi.Draw.ICanBeUpdated
  href: DrawnUi.Draw.ICanBeUpdated.html#DrawnUi_Draw_ICanBeUpdated_Update
  name: Update()
  nameWithType: ICanBeUpdated.Update()
  fullName: DrawnUi.Draw.ICanBeUpdated.Update()
  spec.csharp:
  - uid: DrawnUi.Draw.ICanBeUpdated.Update
    name: Update
    href: DrawnUi.Draw.ICanBeUpdated.html#DrawnUi_Draw_ICanBeUpdated_Update
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.ICanBeUpdated.Update
    name: Update
    href: DrawnUi.Draw.ICanBeUpdated.html#DrawnUi_Draw_ICanBeUpdated_Update
  - name: (
  - name: )
- uid: DrawnUi.Draw.SkiaEffect.OnDisposing
  commentId: M:DrawnUi.Draw.SkiaEffect.OnDisposing
  parent: DrawnUi.Draw.SkiaEffect
  href: DrawnUi.Draw.SkiaEffect.html#DrawnUi_Draw_SkiaEffect_OnDisposing
  name: OnDisposing()
  nameWithType: SkiaEffect.OnDisposing()
  fullName: DrawnUi.Draw.SkiaEffect.OnDisposing()
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaEffect.OnDisposing
    name: OnDisposing
    href: DrawnUi.Draw.SkiaEffect.html#DrawnUi_Draw_SkiaEffect_OnDisposing
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SkiaEffect.OnDisposing
    name: OnDisposing
    href: DrawnUi.Draw.SkiaEffect.html#DrawnUi_Draw_SkiaEffect_OnDisposing
  - name: (
  - name: )
- uid: DrawnUi.Draw.BaseColorFilterEffect.OnDisposing*
  commentId: Overload:DrawnUi.Draw.BaseColorFilterEffect.OnDisposing
  href: DrawnUi.Draw.BaseColorFilterEffect.html#DrawnUi_Draw_BaseColorFilterEffect_OnDisposing
  name: OnDisposing
  nameWithType: BaseColorFilterEffect.OnDisposing
  fullName: DrawnUi.Draw.BaseColorFilterEffect.OnDisposing
