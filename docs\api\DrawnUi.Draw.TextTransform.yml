### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.TextTransform
  commentId: T:DrawnUi.Draw.TextTransform
  id: TextTransform
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.TextTransform.Lowercase
  - DrawnUi.Draw.TextTransform.None
  - DrawnUi.Draw.TextTransform.Phrasecase
  - DrawnUi.Draw.TextTransform.Titlecase
  - DrawnUi.Draw.TextTransform.Uppercase
  langs:
  - csharp
  - vb
  name: TextTransform
  nameWithType: TextTransform
  fullName: DrawnUi.Draw.TextTransform
  type: Enum
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/TextTransform.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: TextTransform
    path: ../src/Shared/Draw/Internals/Enums/TextTransform.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public enum TextTransform
    content.vb: Public Enum TextTransform
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.TextTransform.None
  commentId: F:DrawnUi.Draw.TextTransform.None
  id: None
  parent: DrawnUi.Draw.TextTransform
  langs:
  - csharp
  - vb
  name: None
  nameWithType: TextTransform.None
  fullName: DrawnUi.Draw.TextTransform.None
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/TextTransform.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: None
    path: ../src/Shared/Draw/Internals/Enums/TextTransform.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: None = 0
    return:
      type: DrawnUi.Draw.TextTransform
- uid: DrawnUi.Draw.TextTransform.Lowercase
  commentId: F:DrawnUi.Draw.TextTransform.Lowercase
  id: Lowercase
  parent: DrawnUi.Draw.TextTransform
  langs:
  - csharp
  - vb
  name: Lowercase
  nameWithType: TextTransform.Lowercase
  fullName: DrawnUi.Draw.TextTransform.Lowercase
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/TextTransform.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Lowercase
    path: ../src/Shared/Draw/Internals/Enums/TextTransform.cs
    startLine: 6
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: Lowercase = 1
    return:
      type: DrawnUi.Draw.TextTransform
- uid: DrawnUi.Draw.TextTransform.Uppercase
  commentId: F:DrawnUi.Draw.TextTransform.Uppercase
  id: Uppercase
  parent: DrawnUi.Draw.TextTransform
  langs:
  - csharp
  - vb
  name: Uppercase
  nameWithType: TextTransform.Uppercase
  fullName: DrawnUi.Draw.TextTransform.Uppercase
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/TextTransform.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Uppercase
    path: ../src/Shared/Draw/Internals/Enums/TextTransform.cs
    startLine: 8
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: Uppercase = 2
    return:
      type: DrawnUi.Draw.TextTransform
- uid: DrawnUi.Draw.TextTransform.Titlecase
  commentId: F:DrawnUi.Draw.TextTransform.Titlecase
  id: Titlecase
  parent: DrawnUi.Draw.TextTransform
  langs:
  - csharp
  - vb
  name: Titlecase
  nameWithType: TextTransform.Titlecase
  fullName: DrawnUi.Draw.TextTransform.Titlecase
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/TextTransform.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Titlecase
    path: ../src/Shared/Draw/Internals/Enums/TextTransform.cs
    startLine: 13
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Every word starts with a capital letter, others do not change
  example: []
  syntax:
    content: Titlecase = 3
    return:
      type: DrawnUi.Draw.TextTransform
- uid: DrawnUi.Draw.TextTransform.Phrasecase
  commentId: F:DrawnUi.Draw.TextTransform.Phrasecase
  id: Phrasecase
  parent: DrawnUi.Draw.TextTransform
  langs:
  - csharp
  - vb
  name: Phrasecase
  nameWithType: TextTransform.Phrasecase
  fullName: DrawnUi.Draw.TextTransform.Phrasecase
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/TextTransform.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Phrasecase
    path: ../src/Shared/Draw/Internals/Enums/TextTransform.cs
    startLine: 18
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: First word starts with a capital letter, others do not change
  example: []
  syntax:
    content: Phrasecase = 4
    return:
      type: DrawnUi.Draw.TextTransform
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.TextTransform
  commentId: T:DrawnUi.Draw.TextTransform
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.TextTransform.html
  name: TextTransform
  nameWithType: TextTransform
  fullName: DrawnUi.Draw.TextTransform
