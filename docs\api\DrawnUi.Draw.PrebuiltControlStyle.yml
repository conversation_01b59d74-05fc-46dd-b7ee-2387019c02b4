### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.PrebuiltControlStyle
  commentId: T:DrawnUi.Draw.PrebuiltControlStyle
  id: PrebuiltControlStyle
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.PrebuiltControlStyle.Cupertino
  - DrawnUi.Draw.PrebuiltControlStyle.Material
  - DrawnUi.Draw.PrebuiltControlStyle.Platform
  - DrawnUi.Draw.PrebuiltControlStyle.Unset
  - DrawnUi.Draw.PrebuiltControlStyle.Windows
  langs:
  - csharp
  - vb
  name: PrebuiltControlStyle
  nameWithType: PrebuiltControlStyle
  fullName: DrawnUi.Draw.PrebuiltControlStyle
  type: Enum
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/Switches/PrebuiltControlStyle.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: PrebuiltControlStyle
    path: ../src/Maui/DrawnUi/Controls/Switches/PrebuiltControlStyle.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public enum PrebuiltControlStyle
    content.vb: Public Enum PrebuiltControlStyle
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.PrebuiltControlStyle.Unset
  commentId: F:DrawnUi.Draw.PrebuiltControlStyle.Unset
  id: Unset
  parent: DrawnUi.Draw.PrebuiltControlStyle
  langs:
  - csharp
  - vb
  name: Unset
  nameWithType: PrebuiltControlStyle.Unset
  fullName: DrawnUi.Draw.PrebuiltControlStyle.Unset
  type: Field
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/Switches/PrebuiltControlStyle.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Unset
    path: ../src/Maui/DrawnUi/Controls/Switches/PrebuiltControlStyle.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: Unset = 0
    return:
      type: DrawnUi.Draw.PrebuiltControlStyle
- uid: DrawnUi.Draw.PrebuiltControlStyle.Platform
  commentId: F:DrawnUi.Draw.PrebuiltControlStyle.Platform
  id: Platform
  parent: DrawnUi.Draw.PrebuiltControlStyle
  langs:
  - csharp
  - vb
  name: Platform
  nameWithType: PrebuiltControlStyle.Platform
  fullName: DrawnUi.Draw.PrebuiltControlStyle.Platform
  type: Field
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/Switches/PrebuiltControlStyle.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Platform
    path: ../src/Maui/DrawnUi/Controls/Switches/PrebuiltControlStyle.cs
    startLine: 9
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Will select the style upon the current platform
  example: []
  syntax:
    content: Platform = 1
    return:
      type: DrawnUi.Draw.PrebuiltControlStyle
- uid: DrawnUi.Draw.PrebuiltControlStyle.Cupertino
  commentId: F:DrawnUi.Draw.PrebuiltControlStyle.Cupertino
  id: Cupertino
  parent: DrawnUi.Draw.PrebuiltControlStyle
  langs:
  - csharp
  - vb
  name: Cupertino
  nameWithType: PrebuiltControlStyle.Cupertino
  fullName: DrawnUi.Draw.PrebuiltControlStyle.Cupertino
  type: Field
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/Switches/PrebuiltControlStyle.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Cupertino
    path: ../src/Maui/DrawnUi/Controls/Switches/PrebuiltControlStyle.cs
    startLine: 14
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Apple iOS style
  example: []
  syntax:
    content: Cupertino = 2
    return:
      type: DrawnUi.Draw.PrebuiltControlStyle
- uid: DrawnUi.Draw.PrebuiltControlStyle.Material
  commentId: F:DrawnUi.Draw.PrebuiltControlStyle.Material
  id: Material
  parent: DrawnUi.Draw.PrebuiltControlStyle
  langs:
  - csharp
  - vb
  name: Material
  nameWithType: PrebuiltControlStyle.Material
  fullName: DrawnUi.Draw.PrebuiltControlStyle.Material
  type: Field
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/Switches/PrebuiltControlStyle.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Material
    path: ../src/Maui/DrawnUi/Controls/Switches/PrebuiltControlStyle.cs
    startLine: 19
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Google Android tyle
  example: []
  syntax:
    content: Material = 3
    return:
      type: DrawnUi.Draw.PrebuiltControlStyle
- uid: DrawnUi.Draw.PrebuiltControlStyle.Windows
  commentId: F:DrawnUi.Draw.PrebuiltControlStyle.Windows
  id: Windows
  parent: DrawnUi.Draw.PrebuiltControlStyle
  langs:
  - csharp
  - vb
  name: Windows
  nameWithType: PrebuiltControlStyle.Windows
  fullName: DrawnUi.Draw.PrebuiltControlStyle.Windows
  type: Field
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/Switches/PrebuiltControlStyle.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Windows
    path: ../src/Maui/DrawnUi/Controls/Switches/PrebuiltControlStyle.cs
    startLine: 24
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Windows style
  example: []
  syntax:
    content: Windows = 4
    return:
      type: DrawnUi.Draw.PrebuiltControlStyle
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.PrebuiltControlStyle
  commentId: T:DrawnUi.Draw.PrebuiltControlStyle
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.PrebuiltControlStyle.html
  name: PrebuiltControlStyle
  nameWithType: PrebuiltControlStyle
  fullName: DrawnUi.Draw.PrebuiltControlStyle
