### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.ViewportScrollType
  commentId: T:DrawnUi.Draw.ViewportScrollType
  id: ViewportScrollType
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.ViewportScrollType.None
  - DrawnUi.Draw.ViewportScrollType.Pannable
  - DrawnUi.Draw.ViewportScrollType.Scrollable
  langs:
  - csharp
  - vb
  name: ViewportScrollType
  nameWithType: ViewportScrollType
  fullName: DrawnUi.Draw.ViewportScrollType
  type: Enum
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/ViewportScrollType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ViewportScrollType
    path: ../src/Shared/Draw/Internals/Enums/ViewportScrollType.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public enum ViewportScrollType
    content.vb: Public Enum ViewportScrollType
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.ViewportScrollType.None
  commentId: F:DrawnUi.Draw.ViewportScrollType.None
  id: None
  parent: DrawnUi.Draw.ViewportScrollType
  langs:
  - csharp
  - vb
  name: None
  nameWithType: ViewportScrollType.None
  fullName: DrawnUi.Draw.ViewportScrollType.None
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/ViewportScrollType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: None
    path: ../src/Shared/Draw/Internals/Enums/ViewportScrollType.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: None = 0
    return:
      type: DrawnUi.Draw.ViewportScrollType
- uid: DrawnUi.Draw.ViewportScrollType.Pannable
  commentId: F:DrawnUi.Draw.ViewportScrollType.Pannable
  id: Pannable
  parent: DrawnUi.Draw.ViewportScrollType
  langs:
  - csharp
  - vb
  name: Pannable
  nameWithType: ViewportScrollType.Pannable
  fullName: DrawnUi.Draw.ViewportScrollType.Pannable
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/ViewportScrollType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Pannable
    path: ../src/Shared/Draw/Internals/Enums/ViewportScrollType.cs
    startLine: 5
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: Pannable = 1
    return:
      type: DrawnUi.Draw.ViewportScrollType
- uid: DrawnUi.Draw.ViewportScrollType.Scrollable
  commentId: F:DrawnUi.Draw.ViewportScrollType.Scrollable
  id: Scrollable
  parent: DrawnUi.Draw.ViewportScrollType
  langs:
  - csharp
  - vb
  name: Scrollable
  nameWithType: ViewportScrollType.Scrollable
  fullName: DrawnUi.Draw.ViewportScrollType.Scrollable
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/ViewportScrollType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Scrollable
    path: ../src/Shared/Draw/Internals/Enums/ViewportScrollType.cs
    startLine: 6
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: Scrollable = 2
    return:
      type: DrawnUi.Draw.ViewportScrollType
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.ViewportScrollType
  commentId: T:DrawnUi.Draw.ViewportScrollType
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ViewportScrollType.html
  name: ViewportScrollType
  nameWithType: ViewportScrollType
  fullName: DrawnUi.Draw.ViewportScrollType
