### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.RangeF
  commentId: T:DrawnUi.Draw.RangeF
  id: RangeF
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.RangeF.#ctor(System.Single,System.Single)
  - DrawnUi.Draw.RangeF.Delta
  - DrawnUi.Draw.RangeF.End
  - DrawnUi.Draw.RangeF.Length
  - DrawnUi.Draw.RangeF.Start
  langs:
  - csharp
  - vb
  name: RangeF
  nameWithType: RangeF
  fullName: DrawnUi.Draw.RangeF
  type: Struct
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ScaledSize.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: RangeF
    path: ../src/Shared/Draw/Internals/Models/ScaledSize.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public struct RangeF
    content.vb: Public Structure RangeF
  inheritedMembers:
  - System.ValueType.Equals(System.Object)
  - System.ValueType.GetHashCode
  - System.ValueType.ToString
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetType
  - System.Object.ReferenceEquals(System.Object,System.Object)
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.RangeF.Start
  commentId: P:DrawnUi.Draw.RangeF.Start
  id: Start
  parent: DrawnUi.Draw.RangeF
  langs:
  - csharp
  - vb
  name: Start
  nameWithType: RangeF.Start
  fullName: DrawnUi.Draw.RangeF.Start
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ScaledSize.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Start
    path: ../src/Shared/Draw/Internals/Models/ScaledSize.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public float Start { readonly get; set; }
    parameters: []
    return:
      type: System.Single
    content.vb: Public Property Start As Single
  overload: DrawnUi.Draw.RangeF.Start*
- uid: DrawnUi.Draw.RangeF.End
  commentId: P:DrawnUi.Draw.RangeF.End
  id: End
  parent: DrawnUi.Draw.RangeF
  langs:
  - csharp
  - vb
  name: End
  nameWithType: RangeF.End
  fullName: DrawnUi.Draw.RangeF.End
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ScaledSize.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: End
    path: ../src/Shared/Draw/Internals/Models/ScaledSize.cs
    startLine: 5
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public float End { readonly get; set; }
    parameters: []
    return:
      type: System.Single
    content.vb: Public Property [End] As Single
  overload: DrawnUi.Draw.RangeF.End*
- uid: DrawnUi.Draw.RangeF.#ctor(System.Single,System.Single)
  commentId: M:DrawnUi.Draw.RangeF.#ctor(System.Single,System.Single)
  id: '#ctor(System.Single,System.Single)'
  parent: DrawnUi.Draw.RangeF
  langs:
  - csharp
  - vb
  name: RangeF(float, float)
  nameWithType: RangeF.RangeF(float, float)
  fullName: DrawnUi.Draw.RangeF.RangeF(float, float)
  type: Constructor
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ScaledSize.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Shared/Draw/Internals/Models/ScaledSize.cs
    startLine: 7
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public RangeF(float start, float end)
    parameters:
    - id: start
      type: System.Single
    - id: end
      type: System.Single
    content.vb: Public Sub New(start As Single, [end] As Single)
  overload: DrawnUi.Draw.RangeF.#ctor*
  nameWithType.vb: RangeF.New(Single, Single)
  fullName.vb: DrawnUi.Draw.RangeF.New(Single, Single)
  name.vb: New(Single, Single)
- uid: DrawnUi.Draw.RangeF.Length
  commentId: P:DrawnUi.Draw.RangeF.Length
  id: Length
  parent: DrawnUi.Draw.RangeF
  langs:
  - csharp
  - vb
  name: Length
  nameWithType: RangeF.Length
  fullName: DrawnUi.Draw.RangeF.Length
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ScaledSize.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Length
    path: ../src/Shared/Draw/Internals/Models/ScaledSize.cs
    startLine: 13
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public float Length { get; }
    parameters: []
    return:
      type: System.Single
    content.vb: Public ReadOnly Property Length As Single
  overload: DrawnUi.Draw.RangeF.Length*
- uid: DrawnUi.Draw.RangeF.Delta
  commentId: P:DrawnUi.Draw.RangeF.Delta
  id: Delta
  parent: DrawnUi.Draw.RangeF
  langs:
  - csharp
  - vb
  name: Delta
  nameWithType: RangeF.Delta
  fullName: DrawnUi.Draw.RangeF.Delta
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ScaledSize.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Delta
    path: ../src/Shared/Draw/Internals/Models/ScaledSize.cs
    startLine: 15
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public float Delta { get; }
    parameters: []
    return:
      type: System.Single
    content.vb: Public ReadOnly Property Delta As Single
  overload: DrawnUi.Draw.RangeF.Delta*
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.ValueType.Equals(System.Object)
  commentId: M:System.ValueType.Equals(System.Object)
  parent: System.ValueType
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype.equals
  name: Equals(object)
  nameWithType: ValueType.Equals(object)
  fullName: System.ValueType.Equals(object)
  nameWithType.vb: ValueType.Equals(Object)
  fullName.vb: System.ValueType.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.ValueType.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.equals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.ValueType.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.equals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.ValueType.GetHashCode
  commentId: M:System.ValueType.GetHashCode
  parent: System.ValueType
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype.gethashcode
  name: GetHashCode()
  nameWithType: ValueType.GetHashCode()
  fullName: System.ValueType.GetHashCode()
  spec.csharp:
  - uid: System.ValueType.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.ValueType.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.gethashcode
  - name: (
  - name: )
- uid: System.ValueType.ToString
  commentId: M:System.ValueType.ToString
  parent: System.ValueType
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype.tostring
  name: ToString()
  nameWithType: ValueType.ToString()
  fullName: System.ValueType.ToString()
  spec.csharp:
  - uid: System.ValueType.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.ValueType.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.tostring
  - name: (
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.ValueType
  commentId: T:System.ValueType
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype
  name: ValueType
  nameWithType: ValueType
  fullName: System.ValueType
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.RangeF.Start*
  commentId: Overload:DrawnUi.Draw.RangeF.Start
  href: DrawnUi.Draw.RangeF.html#DrawnUi_Draw_RangeF_Start
  name: Start
  nameWithType: RangeF.Start
  fullName: DrawnUi.Draw.RangeF.Start
- uid: System.Single
  commentId: T:System.Single
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.single
  name: float
  nameWithType: float
  fullName: float
  nameWithType.vb: Single
  fullName.vb: Single
  name.vb: Single
- uid: DrawnUi.Draw.RangeF.End*
  commentId: Overload:DrawnUi.Draw.RangeF.End
  href: DrawnUi.Draw.RangeF.html#DrawnUi_Draw_RangeF_End
  name: End
  nameWithType: RangeF.End
  fullName: DrawnUi.Draw.RangeF.End
- uid: DrawnUi.Draw.RangeF.#ctor*
  commentId: Overload:DrawnUi.Draw.RangeF.#ctor
  href: DrawnUi.Draw.RangeF.html#DrawnUi_Draw_RangeF__ctor_System_Single_System_Single_
  name: RangeF
  nameWithType: RangeF.RangeF
  fullName: DrawnUi.Draw.RangeF.RangeF
  nameWithType.vb: RangeF.New
  fullName.vb: DrawnUi.Draw.RangeF.New
  name.vb: New
- uid: DrawnUi.Draw.RangeF.Length*
  commentId: Overload:DrawnUi.Draw.RangeF.Length
  href: DrawnUi.Draw.RangeF.html#DrawnUi_Draw_RangeF_Length
  name: Length
  nameWithType: RangeF.Length
  fullName: DrawnUi.Draw.RangeF.Length
- uid: DrawnUi.Draw.RangeF.Delta*
  commentId: Overload:DrawnUi.Draw.RangeF.Delta
  href: DrawnUi.Draw.RangeF.html#DrawnUi_Draw_RangeF_Delta
  name: Delta
  nameWithType: RangeF.Delta
  fullName: DrawnUi.Draw.RangeF.Delta
