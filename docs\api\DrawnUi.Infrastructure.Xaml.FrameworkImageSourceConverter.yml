### YamlMime:ManagedReference
items:
- uid: DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter
  commentId: T:DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter
  id: FrameworkImageSourceConverter
  parent: DrawnUi.Infrastructure.Xaml
  children:
  - DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)
  - DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)
  - DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.ConvertToString(System.Object)
  - DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.FromInvariantString(System.String)
  - DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.FromStream(System.Func{System.IO.Stream},System.String,System.String)
  - DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.StreamFromResourceUrl(System.String,System.Reflection.Assembly)
  langs:
  - csharp
  - vb
  name: FrameworkImageSourceConverter
  nameWithType: FrameworkImageSourceConverter
  fullName: DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Xaml/FrameworkImageSourceConverter.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: FrameworkImageSourceConverter
    path: ../src/Maui/DrawnUi/Internals/Xaml/FrameworkImageSourceConverter.cs
    startLine: 7
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure.Xaml
  syntax:
    content: 'public class FrameworkImageSourceConverter : TypeConverter'
    content.vb: Public Class FrameworkImageSourceConverter Inherits TypeConverter
  inheritance:
  - System.Object
  - System.ComponentModel.TypeConverter
  inheritedMembers:
  - System.ComponentModel.TypeConverter.CanConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Type)
  - System.ComponentModel.TypeConverter.CanConvertFrom(System.Type)
  - System.ComponentModel.TypeConverter.CanConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Type)
  - System.ComponentModel.TypeConverter.CanConvertTo(System.Type)
  - System.ComponentModel.TypeConverter.ConvertFrom(System.Object)
  - System.ComponentModel.TypeConverter.ConvertFromInvariantString(System.ComponentModel.ITypeDescriptorContext,System.String)
  - System.ComponentModel.TypeConverter.ConvertFromInvariantString(System.String)
  - System.ComponentModel.TypeConverter.ConvertFromString(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.String)
  - System.ComponentModel.TypeConverter.ConvertFromString(System.ComponentModel.ITypeDescriptorContext,System.String)
  - System.ComponentModel.TypeConverter.ConvertFromString(System.String)
  - System.ComponentModel.TypeConverter.ConvertTo(System.Object,System.Type)
  - System.ComponentModel.TypeConverter.ConvertToInvariantString(System.ComponentModel.ITypeDescriptorContext,System.Object)
  - System.ComponentModel.TypeConverter.ConvertToInvariantString(System.Object)
  - System.ComponentModel.TypeConverter.ConvertToString(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)
  - System.ComponentModel.TypeConverter.ConvertToString(System.ComponentModel.ITypeDescriptorContext,System.Object)
  - System.ComponentModel.TypeConverter.CreateInstance(System.Collections.IDictionary)
  - System.ComponentModel.TypeConverter.CreateInstance(System.ComponentModel.ITypeDescriptorContext,System.Collections.IDictionary)
  - System.ComponentModel.TypeConverter.GetConvertFromException(System.Object)
  - System.ComponentModel.TypeConverter.GetConvertToException(System.Object,System.Type)
  - System.ComponentModel.TypeConverter.GetCreateInstanceSupported
  - System.ComponentModel.TypeConverter.GetCreateInstanceSupported(System.ComponentModel.ITypeDescriptorContext)
  - System.ComponentModel.TypeConverter.GetProperties(System.ComponentModel.ITypeDescriptorContext,System.Object)
  - System.ComponentModel.TypeConverter.GetProperties(System.ComponentModel.ITypeDescriptorContext,System.Object,System.Attribute[])
  - System.ComponentModel.TypeConverter.GetProperties(System.Object)
  - System.ComponentModel.TypeConverter.GetPropertiesSupported
  - System.ComponentModel.TypeConverter.GetPropertiesSupported(System.ComponentModel.ITypeDescriptorContext)
  - System.ComponentModel.TypeConverter.GetStandardValues
  - System.ComponentModel.TypeConverter.GetStandardValues(System.ComponentModel.ITypeDescriptorContext)
  - System.ComponentModel.TypeConverter.GetStandardValuesExclusive
  - System.ComponentModel.TypeConverter.GetStandardValuesExclusive(System.ComponentModel.ITypeDescriptorContext)
  - System.ComponentModel.TypeConverter.GetStandardValuesSupported
  - System.ComponentModel.TypeConverter.GetStandardValuesSupported(System.ComponentModel.ITypeDescriptorContext)
  - System.ComponentModel.TypeConverter.IsValid(System.ComponentModel.ITypeDescriptorContext,System.Object)
  - System.ComponentModel.TypeConverter.IsValid(System.Object)
  - System.ComponentModel.TypeConverter.SortProperties(System.ComponentModel.PropertyDescriptorCollection,System.String[])
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.FromStream(System.Func{System.IO.Stream},System.String,System.String)
  commentId: M:DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.FromStream(System.Func{System.IO.Stream},System.String,System.String)
  id: FromStream(System.Func{System.IO.Stream},System.String,System.String)
  parent: DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter
  langs:
  - csharp
  - vb
  name: FromStream(Func<Stream>, string, string)
  nameWithType: FrameworkImageSourceConverter.FromStream(Func<Stream>, string, string)
  fullName: DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.FromStream(System.Func<System.IO.Stream>, string, string)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Xaml/FrameworkImageSourceConverter.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: FromStream
    path: ../src/Maui/DrawnUi/Internals/Xaml/FrameworkImageSourceConverter.cs
    startLine: 10
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure.Xaml
  syntax:
    content: public static ImageSource FromStream(Func<Stream> stream, string url, string resource)
    parameters:
    - id: stream
      type: System.Func{System.IO.Stream}
    - id: url
      type: System.String
    - id: resource
      type: System.String
    return:
      type: Microsoft.Maui.Controls.ImageSource
    content.vb: Public Shared Function FromStream(stream As Func(Of Stream), url As String, resource As String) As ImageSource
  overload: DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.FromStream*
  nameWithType.vb: FrameworkImageSourceConverter.FromStream(Func(Of Stream), String, String)
  fullName.vb: DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.FromStream(System.Func(Of System.IO.Stream), String, String)
  name.vb: FromStream(Func(Of Stream), String, String)
- uid: DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.StreamFromResourceUrl(System.String,System.Reflection.Assembly)
  commentId: M:DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.StreamFromResourceUrl(System.String,System.Reflection.Assembly)
  id: StreamFromResourceUrl(System.String,System.Reflection.Assembly)
  parent: DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter
  langs:
  - csharp
  - vb
  name: StreamFromResourceUrl(string, Assembly)
  nameWithType: FrameworkImageSourceConverter.StreamFromResourceUrl(string, Assembly)
  fullName: DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.StreamFromResourceUrl(string, System.Reflection.Assembly)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Xaml/FrameworkImageSourceConverter.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: StreamFromResourceUrl
    path: ../src/Maui/DrawnUi/Internals/Xaml/FrameworkImageSourceConverter.cs
    startLine: 20
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure.Xaml
  syntax:
    content: public static ImageSource StreamFromResourceUrl(string url, Assembly assembly = null)
    parameters:
    - id: url
      type: System.String
    - id: assembly
      type: System.Reflection.Assembly
    return:
      type: Microsoft.Maui.Controls.ImageSource
    content.vb: Public Shared Function StreamFromResourceUrl(url As String, assembly As Assembly = Nothing) As ImageSource
  overload: DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.StreamFromResourceUrl*
  nameWithType.vb: FrameworkImageSourceConverter.StreamFromResourceUrl(String, Assembly)
  fullName.vb: DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.StreamFromResourceUrl(String, System.Reflection.Assembly)
  name.vb: StreamFromResourceUrl(String, Assembly)
- uid: DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.FromInvariantString(System.String)
  commentId: M:DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.FromInvariantString(System.String)
  id: FromInvariantString(System.String)
  parent: DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter
  langs:
  - csharp
  - vb
  name: FromInvariantString(string)
  nameWithType: FrameworkImageSourceConverter.FromInvariantString(string)
  fullName: DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.FromInvariantString(string)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Xaml/FrameworkImageSourceConverter.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: FromInvariantString
    path: ../src/Maui/DrawnUi/Internals/Xaml/FrameworkImageSourceConverter.cs
    startLine: 46
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure.Xaml
  syntax:
    content: public static ImageSource FromInvariantString(string value)
    parameters:
    - id: value
      type: System.String
    return:
      type: Microsoft.Maui.Controls.ImageSource
    content.vb: Public Shared Function FromInvariantString(value As String) As ImageSource
  overload: DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.FromInvariantString*
  nameWithType.vb: FrameworkImageSourceConverter.FromInvariantString(String)
  fullName.vb: DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.FromInvariantString(String)
  name.vb: FromInvariantString(String)
- uid: DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)
  commentId: M:DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)
  id: ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)
  parent: DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter
  langs:
  - csharp
  - vb
  name: ConvertFrom(ITypeDescriptorContext, CultureInfo, object)
  nameWithType: FrameworkImageSourceConverter.ConvertFrom(ITypeDescriptorContext, CultureInfo, object)
  fullName: DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext, System.Globalization.CultureInfo, object)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Xaml/FrameworkImageSourceConverter.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ConvertFrom
    path: ../src/Maui/DrawnUi/Internals/Xaml/FrameworkImageSourceConverter.cs
    startLine: 80
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure.Xaml
  summary: Converts the given object to the type of this converter, using the specified context and culture information.
  example: []
  syntax:
    content: public override object ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, object value)
    parameters:
    - id: context
      type: System.ComponentModel.ITypeDescriptorContext
      description: An <xref href="System.ComponentModel.ITypeDescriptorContext" data-throw-if-not-resolved="false"></xref> that provides a format context.
    - id: culture
      type: System.Globalization.CultureInfo
      description: The <xref href="System.Globalization.CultureInfo" data-throw-if-not-resolved="false"></xref> to use as the current culture.
    - id: value
      type: System.Object
      description: The <xref href="System.Object" data-throw-if-not-resolved="false"></xref> to convert.
    return:
      type: System.Object
      description: An <xref href="System.Object" data-throw-if-not-resolved="false"></xref> that represents the converted value.
    content.vb: Public Overrides Function ConvertFrom(context As ITypeDescriptorContext, culture As CultureInfo, value As Object) As Object
  overridden: System.ComponentModel.TypeConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)
  overload: DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.ConvertFrom*
  exceptions:
  - type: System.NotSupportedException
    commentId: T:System.NotSupportedException
    description: The conversion cannot be performed.
  nameWithType.vb: FrameworkImageSourceConverter.ConvertFrom(ITypeDescriptorContext, CultureInfo, Object)
  fullName.vb: DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext, System.Globalization.CultureInfo, Object)
  name.vb: ConvertFrom(ITypeDescriptorContext, CultureInfo, Object)
- uid: DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)
  commentId: M:DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)
  id: ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)
  parent: DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter
  langs:
  - csharp
  - vb
  name: ConvertTo(ITypeDescriptorContext, CultureInfo, object, Type)
  nameWithType: FrameworkImageSourceConverter.ConvertTo(ITypeDescriptorContext, CultureInfo, object, Type)
  fullName: DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext, System.Globalization.CultureInfo, object, System.Type)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Xaml/FrameworkImageSourceConverter.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ConvertTo
    path: ../src/Maui/DrawnUi/Internals/Xaml/FrameworkImageSourceConverter.cs
    startLine: 94
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure.Xaml
  summary: Converts the given value object to the specified type, using the specified context and culture information.
  example: []
  syntax:
    content: public override object ConvertTo(ITypeDescriptorContext context, CultureInfo culture, object value, Type destinationType)
    parameters:
    - id: context
      type: System.ComponentModel.ITypeDescriptorContext
      description: An <xref href="System.ComponentModel.ITypeDescriptorContext" data-throw-if-not-resolved="false"></xref> that provides a format context.
    - id: culture
      type: System.Globalization.CultureInfo
      description: A <xref href="System.Globalization.CultureInfo" data-throw-if-not-resolved="false"></xref>. If <a href="https://learn.microsoft.com/dotnet/csharp/language-reference/keywords/null">null</a> is passed, the current culture is assumed.
    - id: value
      type: System.Object
      description: The <xref href="System.Object" data-throw-if-not-resolved="false"></xref> to convert.
    - id: destinationType
      type: System.Type
      description: The <xref href="System.Type" data-throw-if-not-resolved="false"></xref> to convert the <code class="paramref">value</code> parameter to.
    return:
      type: System.Object
      description: An <xref href="System.Object" data-throw-if-not-resolved="false"></xref> that represents the converted value.
    content.vb: Public Overrides Function ConvertTo(context As ITypeDescriptorContext, culture As CultureInfo, value As Object, destinationType As Type) As Object
  overridden: System.ComponentModel.TypeConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)
  overload: DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.ConvertTo*
  exceptions:
  - type: System.ArgumentNullException
    commentId: T:System.ArgumentNullException
    description: The <code class="paramref">destinationType</code> parameter is <a href="https://learn.microsoft.com/dotnet/csharp/language-reference/keywords/null">null</a>.
  - type: System.NotSupportedException
    commentId: T:System.NotSupportedException
    description: The conversion cannot be performed.
  nameWithType.vb: FrameworkImageSourceConverter.ConvertTo(ITypeDescriptorContext, CultureInfo, Object, Type)
  fullName.vb: DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext, System.Globalization.CultureInfo, Object, System.Type)
  name.vb: ConvertTo(ITypeDescriptorContext, CultureInfo, Object, Type)
- uid: DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.ConvertToString(System.Object)
  commentId: M:DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.ConvertToString(System.Object)
  id: ConvertToString(System.Object)
  parent: DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter
  langs:
  - csharp
  - vb
  name: ConvertToString(object)
  nameWithType: FrameworkImageSourceConverter.ConvertToString(object)
  fullName: DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.ConvertToString(object)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Xaml/FrameworkImageSourceConverter.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ConvertToString
    path: ../src/Maui/DrawnUi/Internals/Xaml/FrameworkImageSourceConverter.cs
    startLine: 100
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure.Xaml
  syntax:
    content: public static string ConvertToString(object value)
    parameters:
    - id: value
      type: System.Object
    return:
      type: System.String
    content.vb: Public Shared Function ConvertToString(value As Object) As String
  overload: DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.ConvertToString*
  nameWithType.vb: FrameworkImageSourceConverter.ConvertToString(Object)
  fullName.vb: DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.ConvertToString(Object)
  name.vb: ConvertToString(Object)
references:
- uid: DrawnUi.Infrastructure.Xaml
  commentId: N:DrawnUi.Infrastructure.Xaml
  href: DrawnUi.html
  name: DrawnUi.Infrastructure.Xaml
  nameWithType: DrawnUi.Infrastructure.Xaml
  fullName: DrawnUi.Infrastructure.Xaml
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Infrastructure
    name: Infrastructure
    href: DrawnUi.Infrastructure.html
  - name: .
  - uid: DrawnUi.Infrastructure.Xaml
    name: Xaml
    href: DrawnUi.Infrastructure.Xaml.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Infrastructure
    name: Infrastructure
    href: DrawnUi.Infrastructure.html
  - name: .
  - uid: DrawnUi.Infrastructure.Xaml
    name: Xaml
    href: DrawnUi.Infrastructure.Xaml.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.ComponentModel.TypeConverter
  commentId: T:System.ComponentModel.TypeConverter
  parent: System.ComponentModel
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter
  name: TypeConverter
  nameWithType: TypeConverter
  fullName: System.ComponentModel.TypeConverter
- uid: System.ComponentModel.TypeConverter.CanConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Type)
  commentId: M:System.ComponentModel.TypeConverter.CanConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Type)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.canconvertfrom#system-componentmodel-typeconverter-canconvertfrom(system-componentmodel-itypedescriptorcontext-system-type)
  name: CanConvertFrom(ITypeDescriptorContext, Type)
  nameWithType: TypeConverter.CanConvertFrom(ITypeDescriptorContext, Type)
  fullName: System.ComponentModel.TypeConverter.CanConvertFrom(System.ComponentModel.ITypeDescriptorContext, System.Type)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.CanConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Type)
    name: CanConvertFrom
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.canconvertfrom#system-componentmodel-typeconverter-canconvertfrom(system-componentmodel-itypedescriptorcontext-system-type)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.CanConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Type)
    name: CanConvertFrom
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.canconvertfrom#system-componentmodel-typeconverter-canconvertfrom(system-componentmodel-itypedescriptorcontext-system-type)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
- uid: System.ComponentModel.TypeConverter.CanConvertFrom(System.Type)
  commentId: M:System.ComponentModel.TypeConverter.CanConvertFrom(System.Type)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.canconvertfrom#system-componentmodel-typeconverter-canconvertfrom(system-type)
  name: CanConvertFrom(Type)
  nameWithType: TypeConverter.CanConvertFrom(Type)
  fullName: System.ComponentModel.TypeConverter.CanConvertFrom(System.Type)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.CanConvertFrom(System.Type)
    name: CanConvertFrom
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.canconvertfrom#system-componentmodel-typeconverter-canconvertfrom(system-type)
  - name: (
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.CanConvertFrom(System.Type)
    name: CanConvertFrom
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.canconvertfrom#system-componentmodel-typeconverter-canconvertfrom(system-type)
  - name: (
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
- uid: System.ComponentModel.TypeConverter.CanConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Type)
  commentId: M:System.ComponentModel.TypeConverter.CanConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Type)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.canconvertto#system-componentmodel-typeconverter-canconvertto(system-componentmodel-itypedescriptorcontext-system-type)
  name: CanConvertTo(ITypeDescriptorContext, Type)
  nameWithType: TypeConverter.CanConvertTo(ITypeDescriptorContext, Type)
  fullName: System.ComponentModel.TypeConverter.CanConvertTo(System.ComponentModel.ITypeDescriptorContext, System.Type)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.CanConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Type)
    name: CanConvertTo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.canconvertto#system-componentmodel-typeconverter-canconvertto(system-componentmodel-itypedescriptorcontext-system-type)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.CanConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Type)
    name: CanConvertTo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.canconvertto#system-componentmodel-typeconverter-canconvertto(system-componentmodel-itypedescriptorcontext-system-type)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
- uid: System.ComponentModel.TypeConverter.CanConvertTo(System.Type)
  commentId: M:System.ComponentModel.TypeConverter.CanConvertTo(System.Type)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.canconvertto#system-componentmodel-typeconverter-canconvertto(system-type)
  name: CanConvertTo(Type)
  nameWithType: TypeConverter.CanConvertTo(Type)
  fullName: System.ComponentModel.TypeConverter.CanConvertTo(System.Type)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.CanConvertTo(System.Type)
    name: CanConvertTo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.canconvertto#system-componentmodel-typeconverter-canconvertto(system-type)
  - name: (
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.CanConvertTo(System.Type)
    name: CanConvertTo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.canconvertto#system-componentmodel-typeconverter-canconvertto(system-type)
  - name: (
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
- uid: System.ComponentModel.TypeConverter.ConvertFrom(System.Object)
  commentId: M:System.ComponentModel.TypeConverter.ConvertFrom(System.Object)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertfrom#system-componentmodel-typeconverter-convertfrom(system-object)
  name: ConvertFrom(object)
  nameWithType: TypeConverter.ConvertFrom(object)
  fullName: System.ComponentModel.TypeConverter.ConvertFrom(object)
  nameWithType.vb: TypeConverter.ConvertFrom(Object)
  fullName.vb: System.ComponentModel.TypeConverter.ConvertFrom(Object)
  name.vb: ConvertFrom(Object)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.ConvertFrom(System.Object)
    name: ConvertFrom
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertfrom#system-componentmodel-typeconverter-convertfrom(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.ConvertFrom(System.Object)
    name: ConvertFrom
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertfrom#system-componentmodel-typeconverter-convertfrom(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.ComponentModel.TypeConverter.ConvertFromInvariantString(System.ComponentModel.ITypeDescriptorContext,System.String)
  commentId: M:System.ComponentModel.TypeConverter.ConvertFromInvariantString(System.ComponentModel.ITypeDescriptorContext,System.String)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertfrominvariantstring#system-componentmodel-typeconverter-convertfrominvariantstring(system-componentmodel-itypedescriptorcontext-system-string)
  name: ConvertFromInvariantString(ITypeDescriptorContext, string)
  nameWithType: TypeConverter.ConvertFromInvariantString(ITypeDescriptorContext, string)
  fullName: System.ComponentModel.TypeConverter.ConvertFromInvariantString(System.ComponentModel.ITypeDescriptorContext, string)
  nameWithType.vb: TypeConverter.ConvertFromInvariantString(ITypeDescriptorContext, String)
  fullName.vb: System.ComponentModel.TypeConverter.ConvertFromInvariantString(System.ComponentModel.ITypeDescriptorContext, String)
  name.vb: ConvertFromInvariantString(ITypeDescriptorContext, String)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.ConvertFromInvariantString(System.ComponentModel.ITypeDescriptorContext,System.String)
    name: ConvertFromInvariantString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertfrominvariantstring#system-componentmodel-typeconverter-convertfrominvariantstring(system-componentmodel-itypedescriptorcontext-system-string)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.ConvertFromInvariantString(System.ComponentModel.ITypeDescriptorContext,System.String)
    name: ConvertFromInvariantString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertfrominvariantstring#system-componentmodel-typeconverter-convertfrominvariantstring(system-componentmodel-itypedescriptorcontext-system-string)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: System.ComponentModel.TypeConverter.ConvertFromInvariantString(System.String)
  commentId: M:System.ComponentModel.TypeConverter.ConvertFromInvariantString(System.String)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertfrominvariantstring#system-componentmodel-typeconverter-convertfrominvariantstring(system-string)
  name: ConvertFromInvariantString(string)
  nameWithType: TypeConverter.ConvertFromInvariantString(string)
  fullName: System.ComponentModel.TypeConverter.ConvertFromInvariantString(string)
  nameWithType.vb: TypeConverter.ConvertFromInvariantString(String)
  fullName.vb: System.ComponentModel.TypeConverter.ConvertFromInvariantString(String)
  name.vb: ConvertFromInvariantString(String)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.ConvertFromInvariantString(System.String)
    name: ConvertFromInvariantString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertfrominvariantstring#system-componentmodel-typeconverter-convertfrominvariantstring(system-string)
  - name: (
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.ConvertFromInvariantString(System.String)
    name: ConvertFromInvariantString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertfrominvariantstring#system-componentmodel-typeconverter-convertfrominvariantstring(system-string)
  - name: (
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: System.ComponentModel.TypeConverter.ConvertFromString(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.String)
  commentId: M:System.ComponentModel.TypeConverter.ConvertFromString(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.String)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertfromstring#system-componentmodel-typeconverter-convertfromstring(system-componentmodel-itypedescriptorcontext-system-globalization-cultureinfo-system-string)
  name: ConvertFromString(ITypeDescriptorContext, CultureInfo, string)
  nameWithType: TypeConverter.ConvertFromString(ITypeDescriptorContext, CultureInfo, string)
  fullName: System.ComponentModel.TypeConverter.ConvertFromString(System.ComponentModel.ITypeDescriptorContext, System.Globalization.CultureInfo, string)
  nameWithType.vb: TypeConverter.ConvertFromString(ITypeDescriptorContext, CultureInfo, String)
  fullName.vb: System.ComponentModel.TypeConverter.ConvertFromString(System.ComponentModel.ITypeDescriptorContext, System.Globalization.CultureInfo, String)
  name.vb: ConvertFromString(ITypeDescriptorContext, CultureInfo, String)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.ConvertFromString(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.String)
    name: ConvertFromString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertfromstring#system-componentmodel-typeconverter-convertfromstring(system-componentmodel-itypedescriptorcontext-system-globalization-cultureinfo-system-string)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.Globalization.CultureInfo
    name: CultureInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.globalization.cultureinfo
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.ConvertFromString(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.String)
    name: ConvertFromString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertfromstring#system-componentmodel-typeconverter-convertfromstring(system-componentmodel-itypedescriptorcontext-system-globalization-cultureinfo-system-string)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.Globalization.CultureInfo
    name: CultureInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.globalization.cultureinfo
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: System.ComponentModel.TypeConverter.ConvertFromString(System.ComponentModel.ITypeDescriptorContext,System.String)
  commentId: M:System.ComponentModel.TypeConverter.ConvertFromString(System.ComponentModel.ITypeDescriptorContext,System.String)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertfromstring#system-componentmodel-typeconverter-convertfromstring(system-componentmodel-itypedescriptorcontext-system-string)
  name: ConvertFromString(ITypeDescriptorContext, string)
  nameWithType: TypeConverter.ConvertFromString(ITypeDescriptorContext, string)
  fullName: System.ComponentModel.TypeConverter.ConvertFromString(System.ComponentModel.ITypeDescriptorContext, string)
  nameWithType.vb: TypeConverter.ConvertFromString(ITypeDescriptorContext, String)
  fullName.vb: System.ComponentModel.TypeConverter.ConvertFromString(System.ComponentModel.ITypeDescriptorContext, String)
  name.vb: ConvertFromString(ITypeDescriptorContext, String)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.ConvertFromString(System.ComponentModel.ITypeDescriptorContext,System.String)
    name: ConvertFromString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertfromstring#system-componentmodel-typeconverter-convertfromstring(system-componentmodel-itypedescriptorcontext-system-string)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.ConvertFromString(System.ComponentModel.ITypeDescriptorContext,System.String)
    name: ConvertFromString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertfromstring#system-componentmodel-typeconverter-convertfromstring(system-componentmodel-itypedescriptorcontext-system-string)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: System.ComponentModel.TypeConverter.ConvertFromString(System.String)
  commentId: M:System.ComponentModel.TypeConverter.ConvertFromString(System.String)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertfromstring#system-componentmodel-typeconverter-convertfromstring(system-string)
  name: ConvertFromString(string)
  nameWithType: TypeConverter.ConvertFromString(string)
  fullName: System.ComponentModel.TypeConverter.ConvertFromString(string)
  nameWithType.vb: TypeConverter.ConvertFromString(String)
  fullName.vb: System.ComponentModel.TypeConverter.ConvertFromString(String)
  name.vb: ConvertFromString(String)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.ConvertFromString(System.String)
    name: ConvertFromString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertfromstring#system-componentmodel-typeconverter-convertfromstring(system-string)
  - name: (
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.ConvertFromString(System.String)
    name: ConvertFromString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertfromstring#system-componentmodel-typeconverter-convertfromstring(system-string)
  - name: (
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: System.ComponentModel.TypeConverter.ConvertTo(System.Object,System.Type)
  commentId: M:System.ComponentModel.TypeConverter.ConvertTo(System.Object,System.Type)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertto#system-componentmodel-typeconverter-convertto(system-object-system-type)
  name: ConvertTo(object, Type)
  nameWithType: TypeConverter.ConvertTo(object, Type)
  fullName: System.ComponentModel.TypeConverter.ConvertTo(object, System.Type)
  nameWithType.vb: TypeConverter.ConvertTo(Object, Type)
  fullName.vb: System.ComponentModel.TypeConverter.ConvertTo(Object, System.Type)
  name.vb: ConvertTo(Object, Type)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.ConvertTo(System.Object,System.Type)
    name: ConvertTo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertto#system-componentmodel-typeconverter-convertto(system-object-system-type)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.ConvertTo(System.Object,System.Type)
    name: ConvertTo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertto#system-componentmodel-typeconverter-convertto(system-object-system-type)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
- uid: System.ComponentModel.TypeConverter.ConvertToInvariantString(System.ComponentModel.ITypeDescriptorContext,System.Object)
  commentId: M:System.ComponentModel.TypeConverter.ConvertToInvariantString(System.ComponentModel.ITypeDescriptorContext,System.Object)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.converttoinvariantstring#system-componentmodel-typeconverter-converttoinvariantstring(system-componentmodel-itypedescriptorcontext-system-object)
  name: ConvertToInvariantString(ITypeDescriptorContext, object)
  nameWithType: TypeConverter.ConvertToInvariantString(ITypeDescriptorContext, object)
  fullName: System.ComponentModel.TypeConverter.ConvertToInvariantString(System.ComponentModel.ITypeDescriptorContext, object)
  nameWithType.vb: TypeConverter.ConvertToInvariantString(ITypeDescriptorContext, Object)
  fullName.vb: System.ComponentModel.TypeConverter.ConvertToInvariantString(System.ComponentModel.ITypeDescriptorContext, Object)
  name.vb: ConvertToInvariantString(ITypeDescriptorContext, Object)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.ConvertToInvariantString(System.ComponentModel.ITypeDescriptorContext,System.Object)
    name: ConvertToInvariantString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.converttoinvariantstring#system-componentmodel-typeconverter-converttoinvariantstring(system-componentmodel-itypedescriptorcontext-system-object)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.ConvertToInvariantString(System.ComponentModel.ITypeDescriptorContext,System.Object)
    name: ConvertToInvariantString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.converttoinvariantstring#system-componentmodel-typeconverter-converttoinvariantstring(system-componentmodel-itypedescriptorcontext-system-object)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.ComponentModel.TypeConverter.ConvertToInvariantString(System.Object)
  commentId: M:System.ComponentModel.TypeConverter.ConvertToInvariantString(System.Object)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.converttoinvariantstring#system-componentmodel-typeconverter-converttoinvariantstring(system-object)
  name: ConvertToInvariantString(object)
  nameWithType: TypeConverter.ConvertToInvariantString(object)
  fullName: System.ComponentModel.TypeConverter.ConvertToInvariantString(object)
  nameWithType.vb: TypeConverter.ConvertToInvariantString(Object)
  fullName.vb: System.ComponentModel.TypeConverter.ConvertToInvariantString(Object)
  name.vb: ConvertToInvariantString(Object)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.ConvertToInvariantString(System.Object)
    name: ConvertToInvariantString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.converttoinvariantstring#system-componentmodel-typeconverter-converttoinvariantstring(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.ConvertToInvariantString(System.Object)
    name: ConvertToInvariantString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.converttoinvariantstring#system-componentmodel-typeconverter-converttoinvariantstring(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.ComponentModel.TypeConverter.ConvertToString(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)
  commentId: M:System.ComponentModel.TypeConverter.ConvertToString(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.converttostring#system-componentmodel-typeconverter-converttostring(system-componentmodel-itypedescriptorcontext-system-globalization-cultureinfo-system-object)
  name: ConvertToString(ITypeDescriptorContext, CultureInfo, object)
  nameWithType: TypeConverter.ConvertToString(ITypeDescriptorContext, CultureInfo, object)
  fullName: System.ComponentModel.TypeConverter.ConvertToString(System.ComponentModel.ITypeDescriptorContext, System.Globalization.CultureInfo, object)
  nameWithType.vb: TypeConverter.ConvertToString(ITypeDescriptorContext, CultureInfo, Object)
  fullName.vb: System.ComponentModel.TypeConverter.ConvertToString(System.ComponentModel.ITypeDescriptorContext, System.Globalization.CultureInfo, Object)
  name.vb: ConvertToString(ITypeDescriptorContext, CultureInfo, Object)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.ConvertToString(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)
    name: ConvertToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.converttostring#system-componentmodel-typeconverter-converttostring(system-componentmodel-itypedescriptorcontext-system-globalization-cultureinfo-system-object)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.Globalization.CultureInfo
    name: CultureInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.globalization.cultureinfo
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.ConvertToString(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)
    name: ConvertToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.converttostring#system-componentmodel-typeconverter-converttostring(system-componentmodel-itypedescriptorcontext-system-globalization-cultureinfo-system-object)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.Globalization.CultureInfo
    name: CultureInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.globalization.cultureinfo
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.ComponentModel.TypeConverter.ConvertToString(System.ComponentModel.ITypeDescriptorContext,System.Object)
  commentId: M:System.ComponentModel.TypeConverter.ConvertToString(System.ComponentModel.ITypeDescriptorContext,System.Object)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.converttostring#system-componentmodel-typeconverter-converttostring(system-componentmodel-itypedescriptorcontext-system-object)
  name: ConvertToString(ITypeDescriptorContext, object)
  nameWithType: TypeConverter.ConvertToString(ITypeDescriptorContext, object)
  fullName: System.ComponentModel.TypeConverter.ConvertToString(System.ComponentModel.ITypeDescriptorContext, object)
  nameWithType.vb: TypeConverter.ConvertToString(ITypeDescriptorContext, Object)
  fullName.vb: System.ComponentModel.TypeConverter.ConvertToString(System.ComponentModel.ITypeDescriptorContext, Object)
  name.vb: ConvertToString(ITypeDescriptorContext, Object)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.ConvertToString(System.ComponentModel.ITypeDescriptorContext,System.Object)
    name: ConvertToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.converttostring#system-componentmodel-typeconverter-converttostring(system-componentmodel-itypedescriptorcontext-system-object)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.ConvertToString(System.ComponentModel.ITypeDescriptorContext,System.Object)
    name: ConvertToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.converttostring#system-componentmodel-typeconverter-converttostring(system-componentmodel-itypedescriptorcontext-system-object)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.ComponentModel.TypeConverter.CreateInstance(System.Collections.IDictionary)
  commentId: M:System.ComponentModel.TypeConverter.CreateInstance(System.Collections.IDictionary)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.createinstance#system-componentmodel-typeconverter-createinstance(system-collections-idictionary)
  name: CreateInstance(IDictionary)
  nameWithType: TypeConverter.CreateInstance(IDictionary)
  fullName: System.ComponentModel.TypeConverter.CreateInstance(System.Collections.IDictionary)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.CreateInstance(System.Collections.IDictionary)
    name: CreateInstance
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.createinstance#system-componentmodel-typeconverter-createinstance(system-collections-idictionary)
  - name: (
  - uid: System.Collections.IDictionary
    name: IDictionary
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.idictionary
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.CreateInstance(System.Collections.IDictionary)
    name: CreateInstance
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.createinstance#system-componentmodel-typeconverter-createinstance(system-collections-idictionary)
  - name: (
  - uid: System.Collections.IDictionary
    name: IDictionary
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.idictionary
  - name: )
- uid: System.ComponentModel.TypeConverter.CreateInstance(System.ComponentModel.ITypeDescriptorContext,System.Collections.IDictionary)
  commentId: M:System.ComponentModel.TypeConverter.CreateInstance(System.ComponentModel.ITypeDescriptorContext,System.Collections.IDictionary)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.createinstance#system-componentmodel-typeconverter-createinstance(system-componentmodel-itypedescriptorcontext-system-collections-idictionary)
  name: CreateInstance(ITypeDescriptorContext, IDictionary)
  nameWithType: TypeConverter.CreateInstance(ITypeDescriptorContext, IDictionary)
  fullName: System.ComponentModel.TypeConverter.CreateInstance(System.ComponentModel.ITypeDescriptorContext, System.Collections.IDictionary)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.CreateInstance(System.ComponentModel.ITypeDescriptorContext,System.Collections.IDictionary)
    name: CreateInstance
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.createinstance#system-componentmodel-typeconverter-createinstance(system-componentmodel-itypedescriptorcontext-system-collections-idictionary)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.Collections.IDictionary
    name: IDictionary
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.idictionary
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.CreateInstance(System.ComponentModel.ITypeDescriptorContext,System.Collections.IDictionary)
    name: CreateInstance
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.createinstance#system-componentmodel-typeconverter-createinstance(system-componentmodel-itypedescriptorcontext-system-collections-idictionary)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.Collections.IDictionary
    name: IDictionary
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.idictionary
  - name: )
- uid: System.ComponentModel.TypeConverter.GetConvertFromException(System.Object)
  commentId: M:System.ComponentModel.TypeConverter.GetConvertFromException(System.Object)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getconvertfromexception
  name: GetConvertFromException(object)
  nameWithType: TypeConverter.GetConvertFromException(object)
  fullName: System.ComponentModel.TypeConverter.GetConvertFromException(object)
  nameWithType.vb: TypeConverter.GetConvertFromException(Object)
  fullName.vb: System.ComponentModel.TypeConverter.GetConvertFromException(Object)
  name.vb: GetConvertFromException(Object)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.GetConvertFromException(System.Object)
    name: GetConvertFromException
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getconvertfromexception
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.GetConvertFromException(System.Object)
    name: GetConvertFromException
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getconvertfromexception
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.ComponentModel.TypeConverter.GetConvertToException(System.Object,System.Type)
  commentId: M:System.ComponentModel.TypeConverter.GetConvertToException(System.Object,System.Type)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getconverttoexception
  name: GetConvertToException(object, Type)
  nameWithType: TypeConverter.GetConvertToException(object, Type)
  fullName: System.ComponentModel.TypeConverter.GetConvertToException(object, System.Type)
  nameWithType.vb: TypeConverter.GetConvertToException(Object, Type)
  fullName.vb: System.ComponentModel.TypeConverter.GetConvertToException(Object, System.Type)
  name.vb: GetConvertToException(Object, Type)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.GetConvertToException(System.Object,System.Type)
    name: GetConvertToException
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getconverttoexception
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.GetConvertToException(System.Object,System.Type)
    name: GetConvertToException
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getconverttoexception
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
- uid: System.ComponentModel.TypeConverter.GetCreateInstanceSupported
  commentId: M:System.ComponentModel.TypeConverter.GetCreateInstanceSupported
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getcreateinstancesupported#system-componentmodel-typeconverter-getcreateinstancesupported
  name: GetCreateInstanceSupported()
  nameWithType: TypeConverter.GetCreateInstanceSupported()
  fullName: System.ComponentModel.TypeConverter.GetCreateInstanceSupported()
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.GetCreateInstanceSupported
    name: GetCreateInstanceSupported
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getcreateinstancesupported#system-componentmodel-typeconverter-getcreateinstancesupported
  - name: (
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.GetCreateInstanceSupported
    name: GetCreateInstanceSupported
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getcreateinstancesupported#system-componentmodel-typeconverter-getcreateinstancesupported
  - name: (
  - name: )
- uid: System.ComponentModel.TypeConverter.GetCreateInstanceSupported(System.ComponentModel.ITypeDescriptorContext)
  commentId: M:System.ComponentModel.TypeConverter.GetCreateInstanceSupported(System.ComponentModel.ITypeDescriptorContext)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getcreateinstancesupported#system-componentmodel-typeconverter-getcreateinstancesupported(system-componentmodel-itypedescriptorcontext)
  name: GetCreateInstanceSupported(ITypeDescriptorContext)
  nameWithType: TypeConverter.GetCreateInstanceSupported(ITypeDescriptorContext)
  fullName: System.ComponentModel.TypeConverter.GetCreateInstanceSupported(System.ComponentModel.ITypeDescriptorContext)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.GetCreateInstanceSupported(System.ComponentModel.ITypeDescriptorContext)
    name: GetCreateInstanceSupported
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getcreateinstancesupported#system-componentmodel-typeconverter-getcreateinstancesupported(system-componentmodel-itypedescriptorcontext)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.GetCreateInstanceSupported(System.ComponentModel.ITypeDescriptorContext)
    name: GetCreateInstanceSupported
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getcreateinstancesupported#system-componentmodel-typeconverter-getcreateinstancesupported(system-componentmodel-itypedescriptorcontext)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: )
- uid: System.ComponentModel.TypeConverter.GetProperties(System.ComponentModel.ITypeDescriptorContext,System.Object)
  commentId: M:System.ComponentModel.TypeConverter.GetProperties(System.ComponentModel.ITypeDescriptorContext,System.Object)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getproperties#system-componentmodel-typeconverter-getproperties(system-componentmodel-itypedescriptorcontext-system-object)
  name: GetProperties(ITypeDescriptorContext, object)
  nameWithType: TypeConverter.GetProperties(ITypeDescriptorContext, object)
  fullName: System.ComponentModel.TypeConverter.GetProperties(System.ComponentModel.ITypeDescriptorContext, object)
  nameWithType.vb: TypeConverter.GetProperties(ITypeDescriptorContext, Object)
  fullName.vb: System.ComponentModel.TypeConverter.GetProperties(System.ComponentModel.ITypeDescriptorContext, Object)
  name.vb: GetProperties(ITypeDescriptorContext, Object)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.GetProperties(System.ComponentModel.ITypeDescriptorContext,System.Object)
    name: GetProperties
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getproperties#system-componentmodel-typeconverter-getproperties(system-componentmodel-itypedescriptorcontext-system-object)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.GetProperties(System.ComponentModel.ITypeDescriptorContext,System.Object)
    name: GetProperties
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getproperties#system-componentmodel-typeconverter-getproperties(system-componentmodel-itypedescriptorcontext-system-object)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.ComponentModel.TypeConverter.GetProperties(System.ComponentModel.ITypeDescriptorContext,System.Object,System.Attribute[])
  commentId: M:System.ComponentModel.TypeConverter.GetProperties(System.ComponentModel.ITypeDescriptorContext,System.Object,System.Attribute[])
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getproperties#system-componentmodel-typeconverter-getproperties(system-componentmodel-itypedescriptorcontext-system-object-system-attribute())
  name: GetProperties(ITypeDescriptorContext, object, Attribute[])
  nameWithType: TypeConverter.GetProperties(ITypeDescriptorContext, object, Attribute[])
  fullName: System.ComponentModel.TypeConverter.GetProperties(System.ComponentModel.ITypeDescriptorContext, object, System.Attribute[])
  nameWithType.vb: TypeConverter.GetProperties(ITypeDescriptorContext, Object, Attribute())
  fullName.vb: System.ComponentModel.TypeConverter.GetProperties(System.ComponentModel.ITypeDescriptorContext, Object, System.Attribute())
  name.vb: GetProperties(ITypeDescriptorContext, Object, Attribute())
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.GetProperties(System.ComponentModel.ITypeDescriptorContext,System.Object,System.Attribute[])
    name: GetProperties
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getproperties#system-componentmodel-typeconverter-getproperties(system-componentmodel-itypedescriptorcontext-system-object-system-attribute())
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Attribute
    name: Attribute
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute
  - name: '['
  - name: ']'
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.GetProperties(System.ComponentModel.ITypeDescriptorContext,System.Object,System.Attribute[])
    name: GetProperties
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getproperties#system-componentmodel-typeconverter-getproperties(system-componentmodel-itypedescriptorcontext-system-object-system-attribute())
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Attribute
    name: Attribute
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute
  - name: (
  - name: )
  - name: )
- uid: System.ComponentModel.TypeConverter.GetProperties(System.Object)
  commentId: M:System.ComponentModel.TypeConverter.GetProperties(System.Object)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getproperties#system-componentmodel-typeconverter-getproperties(system-object)
  name: GetProperties(object)
  nameWithType: TypeConverter.GetProperties(object)
  fullName: System.ComponentModel.TypeConverter.GetProperties(object)
  nameWithType.vb: TypeConverter.GetProperties(Object)
  fullName.vb: System.ComponentModel.TypeConverter.GetProperties(Object)
  name.vb: GetProperties(Object)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.GetProperties(System.Object)
    name: GetProperties
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getproperties#system-componentmodel-typeconverter-getproperties(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.GetProperties(System.Object)
    name: GetProperties
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getproperties#system-componentmodel-typeconverter-getproperties(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.ComponentModel.TypeConverter.GetPropertiesSupported
  commentId: M:System.ComponentModel.TypeConverter.GetPropertiesSupported
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getpropertiessupported#system-componentmodel-typeconverter-getpropertiessupported
  name: GetPropertiesSupported()
  nameWithType: TypeConverter.GetPropertiesSupported()
  fullName: System.ComponentModel.TypeConverter.GetPropertiesSupported()
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.GetPropertiesSupported
    name: GetPropertiesSupported
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getpropertiessupported#system-componentmodel-typeconverter-getpropertiessupported
  - name: (
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.GetPropertiesSupported
    name: GetPropertiesSupported
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getpropertiessupported#system-componentmodel-typeconverter-getpropertiessupported
  - name: (
  - name: )
- uid: System.ComponentModel.TypeConverter.GetPropertiesSupported(System.ComponentModel.ITypeDescriptorContext)
  commentId: M:System.ComponentModel.TypeConverter.GetPropertiesSupported(System.ComponentModel.ITypeDescriptorContext)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getpropertiessupported#system-componentmodel-typeconverter-getpropertiessupported(system-componentmodel-itypedescriptorcontext)
  name: GetPropertiesSupported(ITypeDescriptorContext)
  nameWithType: TypeConverter.GetPropertiesSupported(ITypeDescriptorContext)
  fullName: System.ComponentModel.TypeConverter.GetPropertiesSupported(System.ComponentModel.ITypeDescriptorContext)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.GetPropertiesSupported(System.ComponentModel.ITypeDescriptorContext)
    name: GetPropertiesSupported
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getpropertiessupported#system-componentmodel-typeconverter-getpropertiessupported(system-componentmodel-itypedescriptorcontext)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.GetPropertiesSupported(System.ComponentModel.ITypeDescriptorContext)
    name: GetPropertiesSupported
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getpropertiessupported#system-componentmodel-typeconverter-getpropertiessupported(system-componentmodel-itypedescriptorcontext)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: )
- uid: System.ComponentModel.TypeConverter.GetStandardValues
  commentId: M:System.ComponentModel.TypeConverter.GetStandardValues
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getstandardvalues#system-componentmodel-typeconverter-getstandardvalues
  name: GetStandardValues()
  nameWithType: TypeConverter.GetStandardValues()
  fullName: System.ComponentModel.TypeConverter.GetStandardValues()
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.GetStandardValues
    name: GetStandardValues
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getstandardvalues#system-componentmodel-typeconverter-getstandardvalues
  - name: (
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.GetStandardValues
    name: GetStandardValues
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getstandardvalues#system-componentmodel-typeconverter-getstandardvalues
  - name: (
  - name: )
- uid: System.ComponentModel.TypeConverter.GetStandardValues(System.ComponentModel.ITypeDescriptorContext)
  commentId: M:System.ComponentModel.TypeConverter.GetStandardValues(System.ComponentModel.ITypeDescriptorContext)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getstandardvalues#system-componentmodel-typeconverter-getstandardvalues(system-componentmodel-itypedescriptorcontext)
  name: GetStandardValues(ITypeDescriptorContext)
  nameWithType: TypeConverter.GetStandardValues(ITypeDescriptorContext)
  fullName: System.ComponentModel.TypeConverter.GetStandardValues(System.ComponentModel.ITypeDescriptorContext)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.GetStandardValues(System.ComponentModel.ITypeDescriptorContext)
    name: GetStandardValues
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getstandardvalues#system-componentmodel-typeconverter-getstandardvalues(system-componentmodel-itypedescriptorcontext)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.GetStandardValues(System.ComponentModel.ITypeDescriptorContext)
    name: GetStandardValues
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getstandardvalues#system-componentmodel-typeconverter-getstandardvalues(system-componentmodel-itypedescriptorcontext)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: )
- uid: System.ComponentModel.TypeConverter.GetStandardValuesExclusive
  commentId: M:System.ComponentModel.TypeConverter.GetStandardValuesExclusive
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getstandardvaluesexclusive#system-componentmodel-typeconverter-getstandardvaluesexclusive
  name: GetStandardValuesExclusive()
  nameWithType: TypeConverter.GetStandardValuesExclusive()
  fullName: System.ComponentModel.TypeConverter.GetStandardValuesExclusive()
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.GetStandardValuesExclusive
    name: GetStandardValuesExclusive
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getstandardvaluesexclusive#system-componentmodel-typeconverter-getstandardvaluesexclusive
  - name: (
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.GetStandardValuesExclusive
    name: GetStandardValuesExclusive
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getstandardvaluesexclusive#system-componentmodel-typeconverter-getstandardvaluesexclusive
  - name: (
  - name: )
- uid: System.ComponentModel.TypeConverter.GetStandardValuesExclusive(System.ComponentModel.ITypeDescriptorContext)
  commentId: M:System.ComponentModel.TypeConverter.GetStandardValuesExclusive(System.ComponentModel.ITypeDescriptorContext)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getstandardvaluesexclusive#system-componentmodel-typeconverter-getstandardvaluesexclusive(system-componentmodel-itypedescriptorcontext)
  name: GetStandardValuesExclusive(ITypeDescriptorContext)
  nameWithType: TypeConverter.GetStandardValuesExclusive(ITypeDescriptorContext)
  fullName: System.ComponentModel.TypeConverter.GetStandardValuesExclusive(System.ComponentModel.ITypeDescriptorContext)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.GetStandardValuesExclusive(System.ComponentModel.ITypeDescriptorContext)
    name: GetStandardValuesExclusive
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getstandardvaluesexclusive#system-componentmodel-typeconverter-getstandardvaluesexclusive(system-componentmodel-itypedescriptorcontext)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.GetStandardValuesExclusive(System.ComponentModel.ITypeDescriptorContext)
    name: GetStandardValuesExclusive
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getstandardvaluesexclusive#system-componentmodel-typeconverter-getstandardvaluesexclusive(system-componentmodel-itypedescriptorcontext)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: )
- uid: System.ComponentModel.TypeConverter.GetStandardValuesSupported
  commentId: M:System.ComponentModel.TypeConverter.GetStandardValuesSupported
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getstandardvaluessupported#system-componentmodel-typeconverter-getstandardvaluessupported
  name: GetStandardValuesSupported()
  nameWithType: TypeConverter.GetStandardValuesSupported()
  fullName: System.ComponentModel.TypeConverter.GetStandardValuesSupported()
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.GetStandardValuesSupported
    name: GetStandardValuesSupported
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getstandardvaluessupported#system-componentmodel-typeconverter-getstandardvaluessupported
  - name: (
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.GetStandardValuesSupported
    name: GetStandardValuesSupported
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getstandardvaluessupported#system-componentmodel-typeconverter-getstandardvaluessupported
  - name: (
  - name: )
- uid: System.ComponentModel.TypeConverter.GetStandardValuesSupported(System.ComponentModel.ITypeDescriptorContext)
  commentId: M:System.ComponentModel.TypeConverter.GetStandardValuesSupported(System.ComponentModel.ITypeDescriptorContext)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getstandardvaluessupported#system-componentmodel-typeconverter-getstandardvaluessupported(system-componentmodel-itypedescriptorcontext)
  name: GetStandardValuesSupported(ITypeDescriptorContext)
  nameWithType: TypeConverter.GetStandardValuesSupported(ITypeDescriptorContext)
  fullName: System.ComponentModel.TypeConverter.GetStandardValuesSupported(System.ComponentModel.ITypeDescriptorContext)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.GetStandardValuesSupported(System.ComponentModel.ITypeDescriptorContext)
    name: GetStandardValuesSupported
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getstandardvaluessupported#system-componentmodel-typeconverter-getstandardvaluessupported(system-componentmodel-itypedescriptorcontext)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.GetStandardValuesSupported(System.ComponentModel.ITypeDescriptorContext)
    name: GetStandardValuesSupported
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getstandardvaluessupported#system-componentmodel-typeconverter-getstandardvaluessupported(system-componentmodel-itypedescriptorcontext)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: )
- uid: System.ComponentModel.TypeConverter.IsValid(System.ComponentModel.ITypeDescriptorContext,System.Object)
  commentId: M:System.ComponentModel.TypeConverter.IsValid(System.ComponentModel.ITypeDescriptorContext,System.Object)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.isvalid#system-componentmodel-typeconverter-isvalid(system-componentmodel-itypedescriptorcontext-system-object)
  name: IsValid(ITypeDescriptorContext, object)
  nameWithType: TypeConverter.IsValid(ITypeDescriptorContext, object)
  fullName: System.ComponentModel.TypeConverter.IsValid(System.ComponentModel.ITypeDescriptorContext, object)
  nameWithType.vb: TypeConverter.IsValid(ITypeDescriptorContext, Object)
  fullName.vb: System.ComponentModel.TypeConverter.IsValid(System.ComponentModel.ITypeDescriptorContext, Object)
  name.vb: IsValid(ITypeDescriptorContext, Object)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.IsValid(System.ComponentModel.ITypeDescriptorContext,System.Object)
    name: IsValid
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.isvalid#system-componentmodel-typeconverter-isvalid(system-componentmodel-itypedescriptorcontext-system-object)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.IsValid(System.ComponentModel.ITypeDescriptorContext,System.Object)
    name: IsValid
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.isvalid#system-componentmodel-typeconverter-isvalid(system-componentmodel-itypedescriptorcontext-system-object)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.ComponentModel.TypeConverter.IsValid(System.Object)
  commentId: M:System.ComponentModel.TypeConverter.IsValid(System.Object)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.isvalid#system-componentmodel-typeconverter-isvalid(system-object)
  name: IsValid(object)
  nameWithType: TypeConverter.IsValid(object)
  fullName: System.ComponentModel.TypeConverter.IsValid(object)
  nameWithType.vb: TypeConverter.IsValid(Object)
  fullName.vb: System.ComponentModel.TypeConverter.IsValid(Object)
  name.vb: IsValid(Object)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.IsValid(System.Object)
    name: IsValid
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.isvalid#system-componentmodel-typeconverter-isvalid(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.IsValid(System.Object)
    name: IsValid
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.isvalid#system-componentmodel-typeconverter-isvalid(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.ComponentModel.TypeConverter.SortProperties(System.ComponentModel.PropertyDescriptorCollection,System.String[])
  commentId: M:System.ComponentModel.TypeConverter.SortProperties(System.ComponentModel.PropertyDescriptorCollection,System.String[])
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.sortproperties
  name: SortProperties(PropertyDescriptorCollection, string[])
  nameWithType: TypeConverter.SortProperties(PropertyDescriptorCollection, string[])
  fullName: System.ComponentModel.TypeConverter.SortProperties(System.ComponentModel.PropertyDescriptorCollection, string[])
  nameWithType.vb: TypeConverter.SortProperties(PropertyDescriptorCollection, String())
  fullName.vb: System.ComponentModel.TypeConverter.SortProperties(System.ComponentModel.PropertyDescriptorCollection, String())
  name.vb: SortProperties(PropertyDescriptorCollection, String())
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.SortProperties(System.ComponentModel.PropertyDescriptorCollection,System.String[])
    name: SortProperties
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.sortproperties
  - name: (
  - uid: System.ComponentModel.PropertyDescriptorCollection
    name: PropertyDescriptorCollection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.propertydescriptorcollection
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: '['
  - name: ']'
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.SortProperties(System.ComponentModel.PropertyDescriptorCollection,System.String[])
    name: SortProperties
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.sortproperties
  - name: (
  - uid: System.ComponentModel.PropertyDescriptorCollection
    name: PropertyDescriptorCollection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.propertydescriptorcollection
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: (
  - name: )
  - name: )
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: System.ComponentModel
  commentId: N:System.ComponentModel
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.ComponentModel
  nameWithType: System.ComponentModel
  fullName: System.ComponentModel
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.ComponentModel
    name: ComponentModel
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.ComponentModel
    name: ComponentModel
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.FromStream*
  commentId: Overload:DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.FromStream
  href: DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.html#DrawnUi_Infrastructure_Xaml_FrameworkImageSourceConverter_FromStream_System_Func_System_IO_Stream__System_String_System_String_
  name: FromStream
  nameWithType: FrameworkImageSourceConverter.FromStream
  fullName: DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.FromStream
- uid: System.Func{System.IO.Stream}
  commentId: T:System.Func{System.IO.Stream}
  parent: System
  definition: System.Func`1
  href: https://learn.microsoft.com/dotnet/api/system.func-1
  name: Func<Stream>
  nameWithType: Func<Stream>
  fullName: System.Func<System.IO.Stream>
  nameWithType.vb: Func(Of Stream)
  fullName.vb: System.Func(Of System.IO.Stream)
  name.vb: Func(Of Stream)
  spec.csharp:
  - uid: System.Func`1
    name: Func
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.func-1
  - name: <
  - uid: System.IO.Stream
    name: Stream
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.io.stream
  - name: '>'
  spec.vb:
  - uid: System.Func`1
    name: Func
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.func-1
  - name: (
  - name: Of
  - name: " "
  - uid: System.IO.Stream
    name: Stream
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.io.stream
  - name: )
- uid: System.String
  commentId: T:System.String
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.string
  name: string
  nameWithType: string
  fullName: string
  nameWithType.vb: String
  fullName.vb: String
  name.vb: String
- uid: Microsoft.Maui.Controls.ImageSource
  commentId: T:Microsoft.Maui.Controls.ImageSource
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.imagesource
  name: ImageSource
  nameWithType: ImageSource
  fullName: Microsoft.Maui.Controls.ImageSource
- uid: System.Func`1
  commentId: T:System.Func`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.func-1
  name: Func<TResult>
  nameWithType: Func<TResult>
  fullName: System.Func<TResult>
  nameWithType.vb: Func(Of TResult)
  fullName.vb: System.Func(Of TResult)
  name.vb: Func(Of TResult)
  spec.csharp:
  - uid: System.Func`1
    name: Func
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.func-1
  - name: <
  - name: TResult
  - name: '>'
  spec.vb:
  - uid: System.Func`1
    name: Func
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.func-1
  - name: (
  - name: Of
  - name: " "
  - name: TResult
  - name: )
- uid: Microsoft.Maui.Controls
  commentId: N:Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft
  name: Microsoft.Maui.Controls
  nameWithType: Microsoft.Maui.Controls
  fullName: Microsoft.Maui.Controls
  spec.csharp:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Controls
    name: Controls
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls
  spec.vb:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Controls
    name: Controls
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls
- uid: DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.StreamFromResourceUrl*
  commentId: Overload:DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.StreamFromResourceUrl
  href: DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.html#DrawnUi_Infrastructure_Xaml_FrameworkImageSourceConverter_StreamFromResourceUrl_System_String_System_Reflection_Assembly_
  name: StreamFromResourceUrl
  nameWithType: FrameworkImageSourceConverter.StreamFromResourceUrl
  fullName: DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.StreamFromResourceUrl
- uid: System.Reflection.Assembly
  commentId: T:System.Reflection.Assembly
  parent: System.Reflection
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.reflection.assembly
  name: Assembly
  nameWithType: Assembly
  fullName: System.Reflection.Assembly
- uid: System.Reflection
  commentId: N:System.Reflection
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Reflection
  nameWithType: System.Reflection
  fullName: System.Reflection
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Reflection
    name: Reflection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Reflection
    name: Reflection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection
- uid: DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.FromInvariantString*
  commentId: Overload:DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.FromInvariantString
  href: DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.html#DrawnUi_Infrastructure_Xaml_FrameworkImageSourceConverter_FromInvariantString_System_String_
  name: FromInvariantString
  nameWithType: FrameworkImageSourceConverter.FromInvariantString
  fullName: DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.FromInvariantString
- uid: System.ComponentModel.ITypeDescriptorContext
  commentId: T:System.ComponentModel.ITypeDescriptorContext
  parent: System.ComponentModel
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  name: ITypeDescriptorContext
  nameWithType: ITypeDescriptorContext
  fullName: System.ComponentModel.ITypeDescriptorContext
- uid: System.Globalization.CultureInfo
  commentId: T:System.Globalization.CultureInfo
  parent: System.Globalization
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.globalization.cultureinfo
  name: CultureInfo
  nameWithType: CultureInfo
  fullName: System.Globalization.CultureInfo
- uid: System.NotSupportedException
  commentId: T:System.NotSupportedException
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.notsupportedexception
  name: NotSupportedException
  nameWithType: NotSupportedException
  fullName: System.NotSupportedException
- uid: System.ComponentModel.TypeConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)
  commentId: M:System.ComponentModel.TypeConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertfrom#system-componentmodel-typeconverter-convertfrom(system-componentmodel-itypedescriptorcontext-system-globalization-cultureinfo-system-object)
  name: ConvertFrom(ITypeDescriptorContext, CultureInfo, object)
  nameWithType: TypeConverter.ConvertFrom(ITypeDescriptorContext, CultureInfo, object)
  fullName: System.ComponentModel.TypeConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext, System.Globalization.CultureInfo, object)
  nameWithType.vb: TypeConverter.ConvertFrom(ITypeDescriptorContext, CultureInfo, Object)
  fullName.vb: System.ComponentModel.TypeConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext, System.Globalization.CultureInfo, Object)
  name.vb: ConvertFrom(ITypeDescriptorContext, CultureInfo, Object)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)
    name: ConvertFrom
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertfrom#system-componentmodel-typeconverter-convertfrom(system-componentmodel-itypedescriptorcontext-system-globalization-cultureinfo-system-object)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.Globalization.CultureInfo
    name: CultureInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.globalization.cultureinfo
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)
    name: ConvertFrom
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertfrom#system-componentmodel-typeconverter-convertfrom(system-componentmodel-itypedescriptorcontext-system-globalization-cultureinfo-system-object)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.Globalization.CultureInfo
    name: CultureInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.globalization.cultureinfo
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.ConvertFrom*
  commentId: Overload:DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.ConvertFrom
  href: DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.html#DrawnUi_Infrastructure_Xaml_FrameworkImageSourceConverter_ConvertFrom_System_ComponentModel_ITypeDescriptorContext_System_Globalization_CultureInfo_System_Object_
  name: ConvertFrom
  nameWithType: FrameworkImageSourceConverter.ConvertFrom
  fullName: DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.ConvertFrom
- uid: System.Globalization
  commentId: N:System.Globalization
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Globalization
  nameWithType: System.Globalization
  fullName: System.Globalization
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Globalization
    name: Globalization
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.globalization
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Globalization
    name: Globalization
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.globalization
- uid: System.Type
  commentId: T:System.Type
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.type
  name: Type
  nameWithType: Type
  fullName: System.Type
- uid: System.ArgumentNullException
  commentId: T:System.ArgumentNullException
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.argumentnullexception
  name: ArgumentNullException
  nameWithType: ArgumentNullException
  fullName: System.ArgumentNullException
- uid: System.ComponentModel.TypeConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)
  commentId: M:System.ComponentModel.TypeConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertto#system-componentmodel-typeconverter-convertto(system-componentmodel-itypedescriptorcontext-system-globalization-cultureinfo-system-object-system-type)
  name: ConvertTo(ITypeDescriptorContext, CultureInfo, object, Type)
  nameWithType: TypeConverter.ConvertTo(ITypeDescriptorContext, CultureInfo, object, Type)
  fullName: System.ComponentModel.TypeConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext, System.Globalization.CultureInfo, object, System.Type)
  nameWithType.vb: TypeConverter.ConvertTo(ITypeDescriptorContext, CultureInfo, Object, Type)
  fullName.vb: System.ComponentModel.TypeConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext, System.Globalization.CultureInfo, Object, System.Type)
  name.vb: ConvertTo(ITypeDescriptorContext, CultureInfo, Object, Type)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)
    name: ConvertTo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertto#system-componentmodel-typeconverter-convertto(system-componentmodel-itypedescriptorcontext-system-globalization-cultureinfo-system-object-system-type)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.Globalization.CultureInfo
    name: CultureInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.globalization.cultureinfo
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)
    name: ConvertTo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertto#system-componentmodel-typeconverter-convertto(system-componentmodel-itypedescriptorcontext-system-globalization-cultureinfo-system-object-system-type)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.Globalization.CultureInfo
    name: CultureInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.globalization.cultureinfo
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
- uid: DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.ConvertTo*
  commentId: Overload:DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.ConvertTo
  href: DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.html#DrawnUi_Infrastructure_Xaml_FrameworkImageSourceConverter_ConvertTo_System_ComponentModel_ITypeDescriptorContext_System_Globalization_CultureInfo_System_Object_System_Type_
  name: ConvertTo
  nameWithType: FrameworkImageSourceConverter.ConvertTo
  fullName: DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.ConvertTo
- uid: DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.ConvertToString*
  commentId: Overload:DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.ConvertToString
  href: DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.html#DrawnUi_Infrastructure_Xaml_FrameworkImageSourceConverter_ConvertToString_System_Object_
  name: ConvertToString
  nameWithType: FrameworkImageSourceConverter.ConvertToString
  fullName: DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.ConvertToString
