### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.LayoutType
  commentId: T:DrawnUi.Draw.LayoutType
  id: LayoutType
  parent: DrawnU<PERSON>.Draw
  children:
  - DrawnUi.Draw.LayoutType.Absolute
  - DrawnUi.Draw.LayoutType.Column
  - DrawnUi.Draw.LayoutType.Grid
  - DrawnUi.Draw.LayoutType.Row
  - DrawnUi.Draw.LayoutType.Wrap
  langs:
  - csharp
  - vb
  name: LayoutType
  nameWithType: LayoutType
  fullName: DrawnUi.Draw.LayoutType
  type: Enum
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/LayoutType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: LayoutType
    path: ../src/Shared/Draw/Internals/Enums/LayoutType.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public enum LayoutType
    content.vb: Public Enum LayoutType
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.LayoutType.Absolute
  commentId: F:DrawnUi.Draw.LayoutType.Absolute
  id: Absolute
  parent: DrawnUi.Draw.LayoutType
  langs:
  - csharp
  - vb
  name: Absolute
  nameWithType: LayoutType.Absolute
  fullName: DrawnUi.Draw.LayoutType.Absolute
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/LayoutType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Absolute
    path: ../src/Shared/Draw/Internals/Enums/LayoutType.cs
    startLine: 7
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Fastest rendering
  example: []
  syntax:
    content: Absolute = 0
    return:
      type: DrawnUi.Draw.LayoutType
- uid: DrawnUi.Draw.LayoutType.Column
  commentId: F:DrawnUi.Draw.LayoutType.Column
  id: Column
  parent: DrawnUi.Draw.LayoutType
  langs:
  - csharp
  - vb
  name: Column
  nameWithType: LayoutType.Column
  fullName: DrawnUi.Draw.LayoutType.Column
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/LayoutType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Column
    path: ../src/Shared/Draw/Internals/Enums/LayoutType.cs
    startLine: 12
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Vertical stack
  example: []
  syntax:
    content: Column = 1
    return:
      type: DrawnUi.Draw.LayoutType
- uid: DrawnUi.Draw.LayoutType.Row
  commentId: F:DrawnUi.Draw.LayoutType.Row
  id: Row
  parent: DrawnUi.Draw.LayoutType
  langs:
  - csharp
  - vb
  name: Row
  nameWithType: LayoutType.Row
  fullName: DrawnUi.Draw.LayoutType.Row
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/LayoutType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Row
    path: ../src/Shared/Draw/Internals/Enums/LayoutType.cs
    startLine: 17
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Horizontal stack
  example: []
  syntax:
    content: Row = 2
    return:
      type: DrawnUi.Draw.LayoutType
- uid: DrawnUi.Draw.LayoutType.Wrap
  commentId: F:DrawnUi.Draw.LayoutType.Wrap
  id: Wrap
  parent: DrawnUi.Draw.LayoutType
  langs:
  - csharp
  - vb
  name: Wrap
  nameWithType: LayoutType.Wrap
  fullName: DrawnUi.Draw.LayoutType.Wrap
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/LayoutType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Wrap
    path: ../src/Shared/Draw/Internals/Enums/LayoutType.cs
    startLine: 22
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Think of wrap panel
  example: []
  syntax:
    content: Wrap = 3
    return:
      type: DrawnUi.Draw.LayoutType
- uid: DrawnUi.Draw.LayoutType.Grid
  commentId: F:DrawnUi.Draw.LayoutType.Grid
  id: Grid
  parent: DrawnUi.Draw.LayoutType
  langs:
  - csharp
  - vb
  name: Grid
  nameWithType: LayoutType.Grid
  fullName: DrawnUi.Draw.LayoutType.Grid
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/LayoutType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Grid
    path: ../src/Shared/Draw/Internals/Enums/LayoutType.cs
    startLine: 27
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Use usual grid properties like Grid.Stack, ColumnSpacing etc
  example: []
  syntax:
    content: Grid = 4
    return:
      type: DrawnUi.Draw.LayoutType
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.LayoutType
  commentId: T:DrawnUi.Draw.LayoutType
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.LayoutType.html
  name: LayoutType
  nameWithType: LayoutType
  fullName: DrawnUi.Draw.LayoutType
