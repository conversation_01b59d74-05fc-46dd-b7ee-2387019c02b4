### YamlMime:ManagedReference
items:
- uid: DrawnUi.Models.DefinitionInfo
  commentId: T:DrawnUi.Models.DefinitionInfo
  id: DefinitionInfo
  parent: DrawnUi.Models
  children:
  - DrawnUi.Models.DefinitionInfo.#ctor(Microsoft.Maui.GridLength)
  - DrawnUi.Models.DefinitionInfo.GridLength
  - DrawnUi.Models.DefinitionInfo.IsAbsolute
  - DrawnUi.Models.DefinitionInfo.IsAuto
  - DrawnUi.Models.DefinitionInfo.IsStar
  - DrawnUi.Models.DefinitionInfo.Size
  - DrawnUi.Models.DefinitionInfo.Update(System.Double)
  langs:
  - csharp
  - vb
  name: DefinitionInfo
  nameWithType: DefinitionInfo
  fullName: DrawnUi.Models.DefinitionInfo
  type: Class
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/DefinitionInfo.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: DefinitionInfo
    path: ../src/Shared/Draw/Internals/Models/DefinitionInfo.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Models
  syntax:
    content: public class DefinitionInfo
    content.vb: Public Class DefinitionInfo
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Models.DefinitionInfo.Size
  commentId: P:DrawnUi.Models.DefinitionInfo.Size
  id: Size
  parent: DrawnUi.Models.DefinitionInfo
  langs:
  - csharp
  - vb
  name: Size
  nameWithType: DefinitionInfo.Size
  fullName: DrawnUi.Models.DefinitionInfo.Size
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/DefinitionInfo.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Size
    path: ../src/Shared/Draw/Internals/Models/DefinitionInfo.cs
    startLine: 11
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Models
  syntax:
    content: public double Size { get; set; }
    parameters: []
    return:
      type: System.Double
    content.vb: Public Property Size As Double
  overload: DrawnUi.Models.DefinitionInfo.Size*
- uid: DrawnUi.Models.DefinitionInfo.Update(System.Double)
  commentId: M:DrawnUi.Models.DefinitionInfo.Update(System.Double)
  id: Update(System.Double)
  parent: DrawnUi.Models.DefinitionInfo
  langs:
  - csharp
  - vb
  name: Update(double)
  nameWithType: DefinitionInfo.Update(double)
  fullName: DrawnUi.Models.DefinitionInfo.Update(double)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/DefinitionInfo.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Update
    path: ../src/Shared/Draw/Internals/Models/DefinitionInfo.cs
    startLine: 13
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Models
  syntax:
    content: public void Update(double size)
    parameters:
    - id: size
      type: System.Double
    content.vb: Public Sub Update(size As Double)
  overload: DrawnUi.Models.DefinitionInfo.Update*
  nameWithType.vb: DefinitionInfo.Update(Double)
  fullName.vb: DrawnUi.Models.DefinitionInfo.Update(Double)
  name.vb: Update(Double)
- uid: DrawnUi.Models.DefinitionInfo.IsAuto
  commentId: P:DrawnUi.Models.DefinitionInfo.IsAuto
  id: IsAuto
  parent: DrawnUi.Models.DefinitionInfo
  langs:
  - csharp
  - vb
  name: IsAuto
  nameWithType: DefinitionInfo.IsAuto
  fullName: DrawnUi.Models.DefinitionInfo.IsAuto
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/DefinitionInfo.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IsAuto
    path: ../src/Shared/Draw/Internals/Models/DefinitionInfo.cs
    startLine: 21
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Models
  syntax:
    content: public bool IsAuto { get; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public ReadOnly Property IsAuto As Boolean
  overload: DrawnUi.Models.DefinitionInfo.IsAuto*
- uid: DrawnUi.Models.DefinitionInfo.IsStar
  commentId: P:DrawnUi.Models.DefinitionInfo.IsStar
  id: IsStar
  parent: DrawnUi.Models.DefinitionInfo
  langs:
  - csharp
  - vb
  name: IsStar
  nameWithType: DefinitionInfo.IsStar
  fullName: DrawnUi.Models.DefinitionInfo.IsStar
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/DefinitionInfo.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IsStar
    path: ../src/Shared/Draw/Internals/Models/DefinitionInfo.cs
    startLine: 22
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Models
  syntax:
    content: public bool IsStar { get; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public ReadOnly Property IsStar As Boolean
  overload: DrawnUi.Models.DefinitionInfo.IsStar*
- uid: DrawnUi.Models.DefinitionInfo.IsAbsolute
  commentId: P:DrawnUi.Models.DefinitionInfo.IsAbsolute
  id: IsAbsolute
  parent: DrawnUi.Models.DefinitionInfo
  langs:
  - csharp
  - vb
  name: IsAbsolute
  nameWithType: DefinitionInfo.IsAbsolute
  fullName: DrawnUi.Models.DefinitionInfo.IsAbsolute
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/DefinitionInfo.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IsAbsolute
    path: ../src/Shared/Draw/Internals/Models/DefinitionInfo.cs
    startLine: 23
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Models
  syntax:
    content: public bool IsAbsolute { get; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public ReadOnly Property IsAbsolute As Boolean
  overload: DrawnUi.Models.DefinitionInfo.IsAbsolute*
- uid: DrawnUi.Models.DefinitionInfo.GridLength
  commentId: P:DrawnUi.Models.DefinitionInfo.GridLength
  id: GridLength
  parent: DrawnUi.Models.DefinitionInfo
  langs:
  - csharp
  - vb
  name: GridLength
  nameWithType: DefinitionInfo.GridLength
  fullName: DrawnUi.Models.DefinitionInfo.GridLength
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/DefinitionInfo.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: GridLength
    path: ../src/Shared/Draw/Internals/Models/DefinitionInfo.cs
    startLine: 25
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Models
  syntax:
    content: public GridLength GridLength { get; }
    parameters: []
    return:
      type: Microsoft.Maui.GridLength
    content.vb: Public ReadOnly Property GridLength As GridLength
  overload: DrawnUi.Models.DefinitionInfo.GridLength*
- uid: DrawnUi.Models.DefinitionInfo.#ctor(Microsoft.Maui.GridLength)
  commentId: M:DrawnUi.Models.DefinitionInfo.#ctor(Microsoft.Maui.GridLength)
  id: '#ctor(Microsoft.Maui.GridLength)'
  parent: DrawnUi.Models.DefinitionInfo
  langs:
  - csharp
  - vb
  name: DefinitionInfo(GridLength)
  nameWithType: DefinitionInfo.DefinitionInfo(GridLength)
  fullName: DrawnUi.Models.DefinitionInfo.DefinitionInfo(Microsoft.Maui.GridLength)
  type: Constructor
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/DefinitionInfo.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Shared/Draw/Internals/Models/DefinitionInfo.cs
    startLine: 27
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Models
  syntax:
    content: public DefinitionInfo(GridLength gridLength)
    parameters:
    - id: gridLength
      type: Microsoft.Maui.GridLength
    content.vb: Public Sub New(gridLength As GridLength)
  overload: DrawnUi.Models.DefinitionInfo.#ctor*
  nameWithType.vb: DefinitionInfo.New(GridLength)
  fullName.vb: DrawnUi.Models.DefinitionInfo.New(Microsoft.Maui.GridLength)
  name.vb: New(GridLength)
references:
- uid: DrawnUi.Models
  commentId: N:DrawnUi.Models
  href: DrawnUi.html
  name: DrawnUi.Models
  nameWithType: DrawnUi.Models
  fullName: DrawnUi.Models
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Models
    name: Models
    href: DrawnUi.Models.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Models
    name: Models
    href: DrawnUi.Models.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Models.DefinitionInfo.Size*
  commentId: Overload:DrawnUi.Models.DefinitionInfo.Size
  href: DrawnUi.Models.DefinitionInfo.html#DrawnUi_Models_DefinitionInfo_Size
  name: Size
  nameWithType: DefinitionInfo.Size
  fullName: DrawnUi.Models.DefinitionInfo.Size
- uid: System.Double
  commentId: T:System.Double
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.double
  name: double
  nameWithType: double
  fullName: double
  nameWithType.vb: Double
  fullName.vb: Double
  name.vb: Double
- uid: DrawnUi.Models.DefinitionInfo.Update*
  commentId: Overload:DrawnUi.Models.DefinitionInfo.Update
  href: DrawnUi.Models.DefinitionInfo.html#DrawnUi_Models_DefinitionInfo_Update_System_Double_
  name: Update
  nameWithType: DefinitionInfo.Update
  fullName: DrawnUi.Models.DefinitionInfo.Update
- uid: DrawnUi.Models.DefinitionInfo.IsAuto*
  commentId: Overload:DrawnUi.Models.DefinitionInfo.IsAuto
  href: DrawnUi.Models.DefinitionInfo.html#DrawnUi_Models_DefinitionInfo_IsAuto
  name: IsAuto
  nameWithType: DefinitionInfo.IsAuto
  fullName: DrawnUi.Models.DefinitionInfo.IsAuto
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: DrawnUi.Models.DefinitionInfo.IsStar*
  commentId: Overload:DrawnUi.Models.DefinitionInfo.IsStar
  href: DrawnUi.Models.DefinitionInfo.html#DrawnUi_Models_DefinitionInfo_IsStar
  name: IsStar
  nameWithType: DefinitionInfo.IsStar
  fullName: DrawnUi.Models.DefinitionInfo.IsStar
- uid: DrawnUi.Models.DefinitionInfo.IsAbsolute*
  commentId: Overload:DrawnUi.Models.DefinitionInfo.IsAbsolute
  href: DrawnUi.Models.DefinitionInfo.html#DrawnUi_Models_DefinitionInfo_IsAbsolute
  name: IsAbsolute
  nameWithType: DefinitionInfo.IsAbsolute
  fullName: DrawnUi.Models.DefinitionInfo.IsAbsolute
- uid: DrawnUi.Models.DefinitionInfo.GridLength*
  commentId: Overload:DrawnUi.Models.DefinitionInfo.GridLength
  href: DrawnUi.Models.DefinitionInfo.html#DrawnUi_Models_DefinitionInfo_GridLength
  name: GridLength
  nameWithType: DefinitionInfo.GridLength
  fullName: DrawnUi.Models.DefinitionInfo.GridLength
- uid: Microsoft.Maui.GridLength
  commentId: T:Microsoft.Maui.GridLength
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.gridlength
  name: GridLength
  nameWithType: GridLength
  fullName: Microsoft.Maui.GridLength
- uid: Microsoft.Maui
  commentId: N:Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft
  name: Microsoft.Maui
  nameWithType: Microsoft.Maui
  fullName: Microsoft.Maui
  spec.csharp:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  spec.vb:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
- uid: DrawnUi.Models.DefinitionInfo.#ctor*
  commentId: Overload:DrawnUi.Models.DefinitionInfo.#ctor
  href: DrawnUi.Models.DefinitionInfo.html#DrawnUi_Models_DefinitionInfo__ctor_Microsoft_Maui_GridLength_
  name: DefinitionInfo
  nameWithType: DefinitionInfo.DefinitionInfo
  fullName: DrawnUi.Models.DefinitionInfo.DefinitionInfo
  nameWithType.vb: DefinitionInfo.New
  fullName.vb: DrawnUi.Models.DefinitionInfo.New
  name.vb: New
