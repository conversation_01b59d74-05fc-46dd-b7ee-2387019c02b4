### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.SkiaPoint
  commentId: T:DrawnUi.Draw.SkiaPoint
  id: SkiaPoint
  parent: DrawnU<PERSON>.Draw
  children:
  - DrawnUi.Draw.SkiaPoint.#ctor
  - DrawnUi.Draw.SkiaPoint.#ctor(System.Double,System.Double)
  - DrawnUi.Draw.SkiaPoint.X
  - DrawnUi.Draw.SkiaPoint.XProperty
  - DrawnUi.Draw.SkiaPoint.Y
  - DrawnUi.Draw.SkiaPoint.YProperty
  langs:
  - csharp
  - vb
  name: SkiaPoint
  nameWithType: SkiaPoint
  fullName: DrawnUi.Draw.SkiaPoint
  type: Class
  source:
    remote:
      path: src/Shared/Draw/SkiaPoint.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: SkiaPoint
    path: ../src/Shared/Draw/SkiaPoint.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: 'public class SkiaPoint : BindableObject, INotifyPropertyChanged'
    content.vb: Public Class SkiaPoint Inherits BindableObject Implements INotifyPropertyChanged
  inheritance:
  - System.Object
  - Microsoft.Maui.Controls.BindableObject
  implements:
  - System.ComponentModel.INotifyPropertyChanged
  inheritedMembers:
  - Microsoft.Maui.Controls.BindableObject.BindingContextProperty
  - Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindableProperty)
  - Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindablePropertyKey)
  - Microsoft.Maui.Controls.BindableObject.GetValue(Microsoft.Maui.Controls.BindableProperty)
  - Microsoft.Maui.Controls.BindableObject.IsSet(Microsoft.Maui.Controls.BindableProperty)
  - Microsoft.Maui.Controls.BindableObject.RemoveBinding(Microsoft.Maui.Controls.BindableProperty)
  - Microsoft.Maui.Controls.BindableObject.SetBinding(Microsoft.Maui.Controls.BindableProperty,Microsoft.Maui.Controls.BindingBase)
  - Microsoft.Maui.Controls.BindableObject.ApplyBindings
  - Microsoft.Maui.Controls.BindableObject.OnBindingContextChanged
  - Microsoft.Maui.Controls.BindableObject.OnPropertyChanged(System.String)
  - Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(System.String)
  - Microsoft.Maui.Controls.BindableObject.UnapplyBindings
  - Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty,System.Object)
  - Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey,System.Object)
  - Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindableProperty)
  - Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindablePropertyKey)
  - Microsoft.Maui.Controls.BindableObject.Dispatcher
  - Microsoft.Maui.Controls.BindableObject.BindingContext
  - Microsoft.Maui.Controls.BindableObject.PropertyChanged
  - Microsoft.Maui.Controls.BindableObject.PropertyChanging
  - Microsoft.Maui.Controls.BindableObject.BindingContextChanged
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.SkiaPoint.#ctor
  commentId: M:DrawnUi.Draw.SkiaPoint.#ctor
  id: '#ctor'
  parent: DrawnUi.Draw.SkiaPoint
  langs:
  - csharp
  - vb
  name: SkiaPoint()
  nameWithType: SkiaPoint.SkiaPoint()
  fullName: DrawnUi.Draw.SkiaPoint.SkiaPoint()
  type: Constructor
  source:
    remote:
      path: src/Shared/Draw/SkiaPoint.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Shared/Draw/SkiaPoint.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public SkiaPoint()
    content.vb: Public Sub New()
  overload: DrawnUi.Draw.SkiaPoint.#ctor*
  nameWithType.vb: SkiaPoint.New()
  fullName.vb: DrawnUi.Draw.SkiaPoint.New()
  name.vb: New()
- uid: DrawnUi.Draw.SkiaPoint.#ctor(System.Double,System.Double)
  commentId: M:DrawnUi.Draw.SkiaPoint.#ctor(System.Double,System.Double)
  id: '#ctor(System.Double,System.Double)'
  parent: DrawnUi.Draw.SkiaPoint
  langs:
  - csharp
  - vb
  name: SkiaPoint(double, double)
  nameWithType: SkiaPoint.SkiaPoint(double, double)
  fullName: DrawnUi.Draw.SkiaPoint.SkiaPoint(double, double)
  type: Constructor
  source:
    remote:
      path: src/Shared/Draw/SkiaPoint.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Shared/Draw/SkiaPoint.cs
    startLine: 9
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public SkiaPoint(double x, double y)
    parameters:
    - id: x
      type: System.Double
    - id: y
      type: System.Double
    content.vb: Public Sub New(x As Double, y As Double)
  overload: DrawnUi.Draw.SkiaPoint.#ctor*
  nameWithType.vb: SkiaPoint.New(Double, Double)
  fullName.vb: DrawnUi.Draw.SkiaPoint.New(Double, Double)
  name.vb: New(Double, Double)
- uid: DrawnUi.Draw.SkiaPoint.XProperty
  commentId: F:DrawnUi.Draw.SkiaPoint.XProperty
  id: XProperty
  parent: DrawnUi.Draw.SkiaPoint
  langs:
  - csharp
  - vb
  name: XProperty
  nameWithType: SkiaPoint.XProperty
  fullName: DrawnUi.Draw.SkiaPoint.XProperty
  type: Field
  source:
    remote:
      path: src/Shared/Draw/SkiaPoint.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: XProperty
    path: ../src/Shared/Draw/SkiaPoint.cs
    startLine: 15
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static readonly BindableProperty XProperty
    return:
      type: Microsoft.Maui.Controls.BindableProperty
    content.vb: Public Shared ReadOnly XProperty As BindableProperty
- uid: DrawnUi.Draw.SkiaPoint.X
  commentId: P:DrawnUi.Draw.SkiaPoint.X
  id: X
  parent: DrawnUi.Draw.SkiaPoint
  langs:
  - csharp
  - vb
  name: X
  nameWithType: SkiaPoint.X
  fullName: DrawnUi.Draw.SkiaPoint.X
  type: Property
  source:
    remote:
      path: src/Shared/Draw/SkiaPoint.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: X
    path: ../src/Shared/Draw/SkiaPoint.cs
    startLine: 22
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public double X { get; set; }
    parameters: []
    return:
      type: System.Double
    content.vb: Public Property X As Double
  overload: DrawnUi.Draw.SkiaPoint.X*
- uid: DrawnUi.Draw.SkiaPoint.YProperty
  commentId: F:DrawnUi.Draw.SkiaPoint.YProperty
  id: YProperty
  parent: DrawnUi.Draw.SkiaPoint
  langs:
  - csharp
  - vb
  name: YProperty
  nameWithType: SkiaPoint.YProperty
  fullName: DrawnUi.Draw.SkiaPoint.YProperty
  type: Field
  source:
    remote:
      path: src/Shared/Draw/SkiaPoint.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: YProperty
    path: ../src/Shared/Draw/SkiaPoint.cs
    startLine: 28
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static readonly BindableProperty YProperty
    return:
      type: Microsoft.Maui.Controls.BindableProperty
    content.vb: Public Shared ReadOnly YProperty As BindableProperty
- uid: DrawnUi.Draw.SkiaPoint.Y
  commentId: P:DrawnUi.Draw.SkiaPoint.Y
  id: Y
  parent: DrawnUi.Draw.SkiaPoint
  langs:
  - csharp
  - vb
  name: Y
  nameWithType: SkiaPoint.Y
  fullName: DrawnUi.Draw.SkiaPoint.Y
  type: Property
  source:
    remote:
      path: src/Shared/Draw/SkiaPoint.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Y
    path: ../src/Shared/Draw/SkiaPoint.cs
    startLine: 35
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public double Y { get; set; }
    parameters: []
    return:
      type: System.Double
    content.vb: Public Property Y As Double
  overload: DrawnUi.Draw.SkiaPoint.Y*
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: Microsoft.Maui.Controls.BindableObject
  commentId: T:Microsoft.Maui.Controls.BindableObject
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject
  name: BindableObject
  nameWithType: BindableObject
  fullName: Microsoft.Maui.Controls.BindableObject
- uid: System.ComponentModel.INotifyPropertyChanged
  commentId: T:System.ComponentModel.INotifyPropertyChanged
  parent: System.ComponentModel
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.inotifypropertychanged
  name: INotifyPropertyChanged
  nameWithType: INotifyPropertyChanged
  fullName: System.ComponentModel.INotifyPropertyChanged
- uid: Microsoft.Maui.Controls.BindableObject.BindingContextProperty
  commentId: F:Microsoft.Maui.Controls.BindableObject.BindingContextProperty
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.bindingcontextproperty
  name: BindingContextProperty
  nameWithType: BindableObject.BindingContextProperty
  fullName: Microsoft.Maui.Controls.BindableObject.BindingContextProperty
- uid: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindableProperty)
  commentId: M:Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindableProperty)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindableproperty)
  name: ClearValue(BindableProperty)
  nameWithType: BindableObject.ClearValue(BindableProperty)
  fullName: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindableProperty)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindableProperty)
    name: ClearValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindableproperty)
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindableProperty)
    name: ClearValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindableproperty)
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindablePropertyKey)
  commentId: M:Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindablePropertyKey)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindablepropertykey)
  name: ClearValue(BindablePropertyKey)
  nameWithType: BindableObject.ClearValue(BindablePropertyKey)
  fullName: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindablePropertyKey)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindablePropertyKey)
    name: ClearValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindablepropertykey)
  - name: (
  - uid: Microsoft.Maui.Controls.BindablePropertyKey
    name: BindablePropertyKey
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindablepropertykey
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindablePropertyKey)
    name: ClearValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindablepropertykey)
  - name: (
  - uid: Microsoft.Maui.Controls.BindablePropertyKey
    name: BindablePropertyKey
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindablepropertykey
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.GetValue(Microsoft.Maui.Controls.BindableProperty)
  commentId: M:Microsoft.Maui.Controls.BindableObject.GetValue(Microsoft.Maui.Controls.BindableProperty)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.getvalue
  name: GetValue(BindableProperty)
  nameWithType: BindableObject.GetValue(BindableProperty)
  fullName: Microsoft.Maui.Controls.BindableObject.GetValue(Microsoft.Maui.Controls.BindableProperty)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.GetValue(Microsoft.Maui.Controls.BindableProperty)
    name: GetValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.getvalue
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.GetValue(Microsoft.Maui.Controls.BindableProperty)
    name: GetValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.getvalue
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.IsSet(Microsoft.Maui.Controls.BindableProperty)
  commentId: M:Microsoft.Maui.Controls.BindableObject.IsSet(Microsoft.Maui.Controls.BindableProperty)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.isset
  name: IsSet(BindableProperty)
  nameWithType: BindableObject.IsSet(BindableProperty)
  fullName: Microsoft.Maui.Controls.BindableObject.IsSet(Microsoft.Maui.Controls.BindableProperty)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.IsSet(Microsoft.Maui.Controls.BindableProperty)
    name: IsSet
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.isset
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.IsSet(Microsoft.Maui.Controls.BindableProperty)
    name: IsSet
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.isset
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.RemoveBinding(Microsoft.Maui.Controls.BindableProperty)
  commentId: M:Microsoft.Maui.Controls.BindableObject.RemoveBinding(Microsoft.Maui.Controls.BindableProperty)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.removebinding
  name: RemoveBinding(BindableProperty)
  nameWithType: BindableObject.RemoveBinding(BindableProperty)
  fullName: Microsoft.Maui.Controls.BindableObject.RemoveBinding(Microsoft.Maui.Controls.BindableProperty)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.RemoveBinding(Microsoft.Maui.Controls.BindableProperty)
    name: RemoveBinding
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.removebinding
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.RemoveBinding(Microsoft.Maui.Controls.BindableProperty)
    name: RemoveBinding
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.removebinding
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.SetBinding(Microsoft.Maui.Controls.BindableProperty,Microsoft.Maui.Controls.BindingBase)
  commentId: M:Microsoft.Maui.Controls.BindableObject.SetBinding(Microsoft.Maui.Controls.BindableProperty,Microsoft.Maui.Controls.BindingBase)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setbinding
  name: SetBinding(BindableProperty, BindingBase)
  nameWithType: BindableObject.SetBinding(BindableProperty, BindingBase)
  fullName: Microsoft.Maui.Controls.BindableObject.SetBinding(Microsoft.Maui.Controls.BindableProperty, Microsoft.Maui.Controls.BindingBase)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.SetBinding(Microsoft.Maui.Controls.BindableProperty,Microsoft.Maui.Controls.BindingBase)
    name: SetBinding
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setbinding
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.BindingBase
    name: BindingBase
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindingbase
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.SetBinding(Microsoft.Maui.Controls.BindableProperty,Microsoft.Maui.Controls.BindingBase)
    name: SetBinding
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setbinding
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.BindingBase
    name: BindingBase
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindingbase
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.ApplyBindings
  commentId: M:Microsoft.Maui.Controls.BindableObject.ApplyBindings
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.applybindings
  name: ApplyBindings()
  nameWithType: BindableObject.ApplyBindings()
  fullName: Microsoft.Maui.Controls.BindableObject.ApplyBindings()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.ApplyBindings
    name: ApplyBindings
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.applybindings
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.ApplyBindings
    name: ApplyBindings
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.applybindings
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.OnBindingContextChanged
  commentId: M:Microsoft.Maui.Controls.BindableObject.OnBindingContextChanged
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.onbindingcontextchanged
  name: OnBindingContextChanged()
  nameWithType: BindableObject.OnBindingContextChanged()
  fullName: Microsoft.Maui.Controls.BindableObject.OnBindingContextChanged()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.OnBindingContextChanged
    name: OnBindingContextChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.onbindingcontextchanged
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.OnBindingContextChanged
    name: OnBindingContextChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.onbindingcontextchanged
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.OnPropertyChanged(System.String)
  commentId: M:Microsoft.Maui.Controls.BindableObject.OnPropertyChanged(System.String)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.onpropertychanged
  name: OnPropertyChanged(string)
  nameWithType: BindableObject.OnPropertyChanged(string)
  fullName: Microsoft.Maui.Controls.BindableObject.OnPropertyChanged(string)
  nameWithType.vb: BindableObject.OnPropertyChanged(String)
  fullName.vb: Microsoft.Maui.Controls.BindableObject.OnPropertyChanged(String)
  name.vb: OnPropertyChanged(String)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.OnPropertyChanged(System.String)
    name: OnPropertyChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.onpropertychanged
  - name: (
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.OnPropertyChanged(System.String)
    name: OnPropertyChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.onpropertychanged
  - name: (
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(System.String)
  commentId: M:Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(System.String)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.onpropertychanging
  name: OnPropertyChanging(string)
  nameWithType: BindableObject.OnPropertyChanging(string)
  fullName: Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(string)
  nameWithType.vb: BindableObject.OnPropertyChanging(String)
  fullName.vb: Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(String)
  name.vb: OnPropertyChanging(String)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(System.String)
    name: OnPropertyChanging
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.onpropertychanging
  - name: (
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(System.String)
    name: OnPropertyChanging
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.onpropertychanging
  - name: (
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.UnapplyBindings
  commentId: M:Microsoft.Maui.Controls.BindableObject.UnapplyBindings
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.unapplybindings
  name: UnapplyBindings()
  nameWithType: BindableObject.UnapplyBindings()
  fullName: Microsoft.Maui.Controls.BindableObject.UnapplyBindings()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.UnapplyBindings
    name: UnapplyBindings
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.unapplybindings
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.UnapplyBindings
    name: UnapplyBindings
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.unapplybindings
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty,System.Object)
  commentId: M:Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty,System.Object)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindableproperty-system-object)
  name: SetValue(BindableProperty, object)
  nameWithType: BindableObject.SetValue(BindableProperty, object)
  fullName: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty, object)
  nameWithType.vb: BindableObject.SetValue(BindableProperty, Object)
  fullName.vb: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty, Object)
  name.vb: SetValue(BindableProperty, Object)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty,System.Object)
    name: SetValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindableproperty-system-object)
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty,System.Object)
    name: SetValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindableproperty-system-object)
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey,System.Object)
  commentId: M:Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey,System.Object)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindablepropertykey-system-object)
  name: SetValue(BindablePropertyKey, object)
  nameWithType: BindableObject.SetValue(BindablePropertyKey, object)
  fullName: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey, object)
  nameWithType.vb: BindableObject.SetValue(BindablePropertyKey, Object)
  fullName.vb: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey, Object)
  name.vb: SetValue(BindablePropertyKey, Object)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey,System.Object)
    name: SetValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindablepropertykey-system-object)
  - name: (
  - uid: Microsoft.Maui.Controls.BindablePropertyKey
    name: BindablePropertyKey
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindablepropertykey
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey,System.Object)
    name: SetValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindablepropertykey-system-object)
  - name: (
  - uid: Microsoft.Maui.Controls.BindablePropertyKey
    name: BindablePropertyKey
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindablepropertykey
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindableProperty)
  commentId: M:Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindableProperty)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindableproperty)
  name: CoerceValue(BindableProperty)
  nameWithType: BindableObject.CoerceValue(BindableProperty)
  fullName: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindableProperty)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindableProperty)
    name: CoerceValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindableproperty)
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindableProperty)
    name: CoerceValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindableproperty)
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindablePropertyKey)
  commentId: M:Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindablePropertyKey)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindablepropertykey)
  name: CoerceValue(BindablePropertyKey)
  nameWithType: BindableObject.CoerceValue(BindablePropertyKey)
  fullName: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindablePropertyKey)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindablePropertyKey)
    name: CoerceValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindablepropertykey)
  - name: (
  - uid: Microsoft.Maui.Controls.BindablePropertyKey
    name: BindablePropertyKey
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindablepropertykey
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindablePropertyKey)
    name: CoerceValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindablepropertykey)
  - name: (
  - uid: Microsoft.Maui.Controls.BindablePropertyKey
    name: BindablePropertyKey
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindablepropertykey
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.Dispatcher
  commentId: P:Microsoft.Maui.Controls.BindableObject.Dispatcher
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.dispatcher
  name: Dispatcher
  nameWithType: BindableObject.Dispatcher
  fullName: Microsoft.Maui.Controls.BindableObject.Dispatcher
- uid: Microsoft.Maui.Controls.BindableObject.BindingContext
  commentId: P:Microsoft.Maui.Controls.BindableObject.BindingContext
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.bindingcontext
  name: BindingContext
  nameWithType: BindableObject.BindingContext
  fullName: Microsoft.Maui.Controls.BindableObject.BindingContext
- uid: Microsoft.Maui.Controls.BindableObject.PropertyChanged
  commentId: E:Microsoft.Maui.Controls.BindableObject.PropertyChanged
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.propertychanged
  name: PropertyChanged
  nameWithType: BindableObject.PropertyChanged
  fullName: Microsoft.Maui.Controls.BindableObject.PropertyChanged
- uid: Microsoft.Maui.Controls.BindableObject.PropertyChanging
  commentId: E:Microsoft.Maui.Controls.BindableObject.PropertyChanging
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.propertychanging
  name: PropertyChanging
  nameWithType: BindableObject.PropertyChanging
  fullName: Microsoft.Maui.Controls.BindableObject.PropertyChanging
- uid: Microsoft.Maui.Controls.BindableObject.BindingContextChanged
  commentId: E:Microsoft.Maui.Controls.BindableObject.BindingContextChanged
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.bindingcontextchanged
  name: BindingContextChanged
  nameWithType: BindableObject.BindingContextChanged
  fullName: Microsoft.Maui.Controls.BindableObject.BindingContextChanged
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: Microsoft.Maui.Controls
  commentId: N:Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft
  name: Microsoft.Maui.Controls
  nameWithType: Microsoft.Maui.Controls
  fullName: Microsoft.Maui.Controls
  spec.csharp:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Controls
    name: Controls
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls
  spec.vb:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Controls
    name: Controls
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls
- uid: System.ComponentModel
  commentId: N:System.ComponentModel
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.ComponentModel
  nameWithType: System.ComponentModel
  fullName: System.ComponentModel
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.ComponentModel
    name: ComponentModel
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.ComponentModel
    name: ComponentModel
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.SkiaPoint.#ctor*
  commentId: Overload:DrawnUi.Draw.SkiaPoint.#ctor
  href: DrawnUi.Draw.SkiaPoint.html#DrawnUi_Draw_SkiaPoint__ctor
  name: SkiaPoint
  nameWithType: SkiaPoint.SkiaPoint
  fullName: DrawnUi.Draw.SkiaPoint.SkiaPoint
  nameWithType.vb: SkiaPoint.New
  fullName.vb: DrawnUi.Draw.SkiaPoint.New
  name.vb: New
- uid: System.Double
  commentId: T:System.Double
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.double
  name: double
  nameWithType: double
  fullName: double
  nameWithType.vb: Double
  fullName.vb: Double
  name.vb: Double
- uid: Microsoft.Maui.Controls.BindableProperty
  commentId: T:Microsoft.Maui.Controls.BindableProperty
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  name: BindableProperty
  nameWithType: BindableProperty
  fullName: Microsoft.Maui.Controls.BindableProperty
- uid: DrawnUi.Draw.SkiaPoint.X*
  commentId: Overload:DrawnUi.Draw.SkiaPoint.X
  href: DrawnUi.Draw.SkiaPoint.html#DrawnUi_Draw_SkiaPoint_X
  name: X
  nameWithType: SkiaPoint.X
  fullName: DrawnUi.Draw.SkiaPoint.X
- uid: DrawnUi.Draw.SkiaPoint.Y*
  commentId: Overload:DrawnUi.Draw.SkiaPoint.Y
  href: DrawnUi.Draw.SkiaPoint.html#DrawnUi_Draw_SkiaPoint_Y
  name: Y
  nameWithType: SkiaPoint.Y
  fullName: DrawnUi.Draw.SkiaPoint.Y
