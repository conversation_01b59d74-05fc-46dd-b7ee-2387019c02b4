### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.FontWeight
  commentId: T:DrawnUi.Draw.FontWeight
  id: FontWeight
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.FontWeight.Black
  - DrawnUi.Draw.FontWeight.Bold
  - DrawnUi.Draw.FontWeight.ExtraBold
  - DrawnUi.Draw.FontWeight.ExtraLight
  - DrawnUi.Draw.FontWeight.Light
  - DrawnUi.Draw.FontWeight.Medium
  - DrawnUi.Draw.FontWeight.Regular
  - DrawnUi.Draw.FontWeight.SemiBold
  - DrawnUi.Draw.FontWeight.Thin
  langs:
  - csharp
  - vb
  name: FontWeight
  nameWithType: FontWeight
  fullName: DrawnUi.Draw.FontWeight
  type: Enum
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/FontWeight.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: FontWeight
    path: ../src/Shared/Draw/Internals/Enums/FontWeight.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public enum FontWeight
    content.vb: Public Enum FontWeight
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.FontWeight.Thin
  commentId: F:DrawnUi.Draw.FontWeight.Thin
  id: Thin
  parent: DrawnUi.Draw.FontWeight
  langs:
  - csharp
  - vb
  name: Thin
  nameWithType: FontWeight.Thin
  fullName: DrawnUi.Draw.FontWeight.Thin
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/FontWeight.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Thin
    path: ../src/Shared/Draw/Internals/Enums/FontWeight.cs
    startLine: 7
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: The thin
  example: []
  syntax:
    content: Thin = 100
    return:
      type: DrawnUi.Draw.FontWeight
- uid: DrawnUi.Draw.FontWeight.ExtraLight
  commentId: F:DrawnUi.Draw.FontWeight.ExtraLight
  id: ExtraLight
  parent: DrawnUi.Draw.FontWeight
  langs:
  - csharp
  - vb
  name: ExtraLight
  nameWithType: FontWeight.ExtraLight
  fullName: DrawnUi.Draw.FontWeight.ExtraLight
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/FontWeight.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ExtraLight
    path: ../src/Shared/Draw/Internals/Enums/FontWeight.cs
    startLine: 12
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Also known as Ultra Light
  example: []
  syntax:
    content: ExtraLight = 200
    return:
      type: DrawnUi.Draw.FontWeight
- uid: DrawnUi.Draw.FontWeight.Light
  commentId: F:DrawnUi.Draw.FontWeight.Light
  id: Light
  parent: DrawnUi.Draw.FontWeight
  langs:
  - csharp
  - vb
  name: Light
  nameWithType: FontWeight.Light
  fullName: DrawnUi.Draw.FontWeight.Light
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/FontWeight.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Light
    path: ../src/Shared/Draw/Internals/Enums/FontWeight.cs
    startLine: 17
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: The light
  example: []
  syntax:
    content: Light = 300
    return:
      type: DrawnUi.Draw.FontWeight
- uid: DrawnUi.Draw.FontWeight.Regular
  commentId: F:DrawnUi.Draw.FontWeight.Regular
  id: Regular
  parent: DrawnUi.Draw.FontWeight
  langs:
  - csharp
  - vb
  name: Regular
  nameWithType: FontWeight.Regular
  fullName: DrawnUi.Draw.FontWeight.Regular
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/FontWeight.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Regular
    path: ../src/Shared/Draw/Internals/Enums/FontWeight.cs
    startLine: 22
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Also known as Normal
  example: []
  syntax:
    content: Regular = 400
    return:
      type: DrawnUi.Draw.FontWeight
- uid: DrawnUi.Draw.FontWeight.Medium
  commentId: F:DrawnUi.Draw.FontWeight.Medium
  id: Medium
  parent: DrawnUi.Draw.FontWeight
  langs:
  - csharp
  - vb
  name: Medium
  nameWithType: FontWeight.Medium
  fullName: DrawnUi.Draw.FontWeight.Medium
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/FontWeight.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Medium
    path: ../src/Shared/Draw/Internals/Enums/FontWeight.cs
    startLine: 27
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: The medium
  example: []
  syntax:
    content: Medium = 500
    return:
      type: DrawnUi.Draw.FontWeight
- uid: DrawnUi.Draw.FontWeight.SemiBold
  commentId: F:DrawnUi.Draw.FontWeight.SemiBold
  id: SemiBold
  parent: DrawnUi.Draw.FontWeight
  langs:
  - csharp
  - vb
  name: SemiBold
  nameWithType: FontWeight.SemiBold
  fullName: DrawnUi.Draw.FontWeight.SemiBold
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/FontWeight.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: SemiBold
    path: ../src/Shared/Draw/Internals/Enums/FontWeight.cs
    startLine: 32
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: The semi bold
  example: []
  syntax:
    content: SemiBold = 600
    return:
      type: DrawnUi.Draw.FontWeight
- uid: DrawnUi.Draw.FontWeight.Bold
  commentId: F:DrawnUi.Draw.FontWeight.Bold
  id: Bold
  parent: DrawnUi.Draw.FontWeight
  langs:
  - csharp
  - vb
  name: Bold
  nameWithType: FontWeight.Bold
  fullName: DrawnUi.Draw.FontWeight.Bold
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/FontWeight.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Bold
    path: ../src/Shared/Draw/Internals/Enums/FontWeight.cs
    startLine: 37
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: The bold
  example: []
  syntax:
    content: Bold = 700
    return:
      type: DrawnUi.Draw.FontWeight
- uid: DrawnUi.Draw.FontWeight.ExtraBold
  commentId: F:DrawnUi.Draw.FontWeight.ExtraBold
  id: ExtraBold
  parent: DrawnUi.Draw.FontWeight
  langs:
  - csharp
  - vb
  name: ExtraBold
  nameWithType: FontWeight.ExtraBold
  fullName: DrawnUi.Draw.FontWeight.ExtraBold
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/FontWeight.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ExtraBold
    path: ../src/Shared/Draw/Internals/Enums/FontWeight.cs
    startLine: 42
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Also known as Heavy or UltraBold
  example: []
  syntax:
    content: ExtraBold = 800
    return:
      type: DrawnUi.Draw.FontWeight
- uid: DrawnUi.Draw.FontWeight.Black
  commentId: F:DrawnUi.Draw.FontWeight.Black
  id: Black
  parent: DrawnUi.Draw.FontWeight
  langs:
  - csharp
  - vb
  name: Black
  nameWithType: FontWeight.Black
  fullName: DrawnUi.Draw.FontWeight.Black
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/FontWeight.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Black
    path: ../src/Shared/Draw/Internals/Enums/FontWeight.cs
    startLine: 47
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: The black
  example: []
  syntax:
    content: Black = 900
    return:
      type: DrawnUi.Draw.FontWeight
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.FontWeight
  commentId: T:DrawnUi.Draw.FontWeight
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.FontWeight.html
  name: FontWeight
  nameWithType: FontWeight
  fullName: DrawnUi.Draw.FontWeight
