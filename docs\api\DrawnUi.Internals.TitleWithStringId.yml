### YamlMime:ManagedReference
items:
- uid: DrawnUi.Internals.TitleWithStringId
  commentId: T:DrawnUi.Internals.TitleWithStringId
  id: TitleWithStringId
  parent: DrawnUi.Internals
  children:
  - DrawnUi.Internals.TitleWithStringId.Id
  - DrawnUi.Internals.TitleWithStringId.Title
  langs:
  - csharp
  - vb
  name: TitleWithStringId
  nameWithType: TitleWithStringId
  fullName: DrawnUi.Internals.TitleWithStringId
  type: Class
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/TitleWithStringId.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: TitleWithStringId
    path: ../src/Shared/Draw/Internals/Models/TitleWithStringId.cs
    startLine: 8
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Internals
  syntax:
    content: 'public class TitleWithStringId : IHasTitleWithId, IHasStringTitle, IHasStringId'
    content.vb: Public Class TitleWithStringId Implements IHasTitleWithId, IHasStringTitle, IHasStringId
  inheritance:
  - System.Object
  derivedClasses:
  - DrawnUi.Internals.SelectableAction
  implements:
  - AppoMobi.Specials.IHasTitleWithId
  - AppoMobi.Specials.IHasStringTitle
  - AppoMobi.Specials.IHasStringId
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Internals.TitleWithStringId.Id
  commentId: P:DrawnUi.Internals.TitleWithStringId.Id
  id: Id
  parent: DrawnUi.Internals.TitleWithStringId
  langs:
  - csharp
  - vb
  name: Id
  nameWithType: TitleWithStringId.Id
  fullName: DrawnUi.Internals.TitleWithStringId.Id
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/TitleWithStringId.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Id
    path: ../src/Shared/Draw/Internals/Models/TitleWithStringId.cs
    startLine: 10
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Internals
  example: []
  syntax:
    content: public string Id { get; set; }
    parameters: []
    return:
      type: System.String
    content.vb: Public Property Id As String
  overload: DrawnUi.Internals.TitleWithStringId.Id*
  implements:
  - AppoMobi.Specials.IHasStringId.Id
- uid: DrawnUi.Internals.TitleWithStringId.Title
  commentId: P:DrawnUi.Internals.TitleWithStringId.Title
  id: Title
  parent: DrawnUi.Internals.TitleWithStringId
  langs:
  - csharp
  - vb
  name: Title
  nameWithType: TitleWithStringId.Title
  fullName: DrawnUi.Internals.TitleWithStringId.Title
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/TitleWithStringId.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Title
    path: ../src/Shared/Draw/Internals/Models/TitleWithStringId.cs
    startLine: 11
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Internals
  example: []
  syntax:
    content: public string Title { get; set; }
    parameters: []
    return:
      type: System.String
    content.vb: Public Property Title As String
  overload: DrawnUi.Internals.TitleWithStringId.Title*
  implements:
  - AppoMobi.Specials.IHasStringTitle.Title
references:
- uid: DrawnUi.Internals
  commentId: N:DrawnUi.Internals
  href: DrawnUi.html
  name: DrawnUi.Internals
  nameWithType: DrawnUi.Internals
  fullName: DrawnUi.Internals
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Internals
    name: Internals
    href: DrawnUi.Internals.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Internals
    name: Internals
    href: DrawnUi.Internals.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: AppoMobi.Specials.IHasTitleWithId
  commentId: T:AppoMobi.Specials.IHasTitleWithId
  parent: AppoMobi.Specials
  isExternal: true
  name: IHasTitleWithId
  nameWithType: IHasTitleWithId
  fullName: AppoMobi.Specials.IHasTitleWithId
- uid: AppoMobi.Specials.IHasStringTitle
  commentId: T:AppoMobi.Specials.IHasStringTitle
  parent: AppoMobi.Specials
  isExternal: true
  name: IHasStringTitle
  nameWithType: IHasStringTitle
  fullName: AppoMobi.Specials.IHasStringTitle
- uid: AppoMobi.Specials.IHasStringId
  commentId: T:AppoMobi.Specials.IHasStringId
  parent: AppoMobi.Specials
  isExternal: true
  name: IHasStringId
  nameWithType: IHasStringId
  fullName: AppoMobi.Specials.IHasStringId
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: AppoMobi.Specials
  commentId: N:AppoMobi.Specials
  isExternal: true
  name: AppoMobi.Specials
  nameWithType: AppoMobi.Specials
  fullName: AppoMobi.Specials
  spec.csharp:
  - uid: AppoMobi
    name: AppoMobi
    isExternal: true
  - name: .
  - uid: AppoMobi.Specials
    name: Specials
    isExternal: true
  spec.vb:
  - uid: AppoMobi
    name: AppoMobi
    isExternal: true
  - name: .
  - uid: AppoMobi.Specials
    name: Specials
    isExternal: true
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Internals.TitleWithStringId.Id*
  commentId: Overload:DrawnUi.Internals.TitleWithStringId.Id
  href: DrawnUi.Internals.TitleWithStringId.html#DrawnUi_Internals_TitleWithStringId_Id
  name: Id
  nameWithType: TitleWithStringId.Id
  fullName: DrawnUi.Internals.TitleWithStringId.Id
- uid: AppoMobi.Specials.IHasStringId.Id
  commentId: P:AppoMobi.Specials.IHasStringId.Id
  parent: AppoMobi.Specials.IHasStringId
  isExternal: true
  name: Id
  nameWithType: IHasStringId.Id
  fullName: AppoMobi.Specials.IHasStringId.Id
- uid: System.String
  commentId: T:System.String
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.string
  name: string
  nameWithType: string
  fullName: string
  nameWithType.vb: String
  fullName.vb: String
  name.vb: String
- uid: DrawnUi.Internals.TitleWithStringId.Title*
  commentId: Overload:DrawnUi.Internals.TitleWithStringId.Title
  href: DrawnUi.Internals.TitleWithStringId.html#DrawnUi_Internals_TitleWithStringId_Title
  name: Title
  nameWithType: TitleWithStringId.Title
  fullName: DrawnUi.Internals.TitleWithStringId.Title
- uid: AppoMobi.Specials.IHasStringTitle.Title
  commentId: P:AppoMobi.Specials.IHasStringTitle.Title
  parent: AppoMobi.Specials.IHasStringTitle
  isExternal: true
  name: Title
  nameWithType: IHasStringTitle.Title
  fullName: AppoMobi.Specials.IHasStringTitle.Title
