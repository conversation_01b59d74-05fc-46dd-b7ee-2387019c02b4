### YamlMime:ManagedReference
items:
- uid: DrawnUi.Controls.SkiaShell.NavigationLayer`1
  commentId: T:DrawnUi.Controls.SkiaShell.NavigationLayer`1
  id: SkiaShell.NavigationLayer`1
  parent: DrawnUi.Controls
  children:
  - DrawnUi.Controls.SkiaShell.NavigationLayer`1.#ctor(DrawnUi.Controls.SkiaShell,System.Boolean)
  - DrawnUi.Controls.SkiaShell.NavigationLayer`1.Close(System.Boolean)
  - DrawnUi.Controls.SkiaShell.NavigationLayer`1.Close(`0,System.Boolean)
  - DrawnUi.Controls.SkiaShell.NavigationLayer`1.CloseAll
  - DrawnUi.Controls.SkiaShell.NavigationLayer`1.NavigationStack
  - DrawnUi.Controls.SkiaShell.NavigationLayer`1.OnOpened(`0)
  - DrawnUi.Controls.SkiaShell.NavigationLayer`1.Open(`0,System.Boolean)
  - DrawnUi.Controls.SkiaShell.NavigationLayer`1._shell
  langs:
  - csharp
  - vb
  name: SkiaShell.NavigationLayer<T>
  nameWithType: SkiaShell.NavigationLayer<T>
  fullName: DrawnUi.Controls.SkiaShell.NavigationLayer<T>
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/Navigation/SkiaShell.NavigationLayer.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: NavigationLayer
    path: ../src/Maui/DrawnUi/Controls/Navigation/SkiaShell.NavigationLayer.cs
    startLine: 6
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: 'public class SkiaShell.NavigationLayer<T> where T : SkiaControl'
    typeParameters:
    - id: T
    content.vb: Public Class SkiaShell.NavigationLayer(Of T As SkiaControl)
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  nameWithType.vb: SkiaShell.NavigationLayer(Of T)
  fullName.vb: DrawnUi.Controls.SkiaShell.NavigationLayer(Of T)
  name.vb: SkiaShell.NavigationLayer(Of T)
- uid: DrawnUi.Controls.SkiaShell.NavigationLayer`1.#ctor(DrawnUi.Controls.SkiaShell,System.Boolean)
  commentId: M:DrawnUi.Controls.SkiaShell.NavigationLayer`1.#ctor(DrawnUi.Controls.SkiaShell,System.Boolean)
  id: '#ctor(DrawnUi.Controls.SkiaShell,System.Boolean)'
  parent: DrawnUi.Controls.SkiaShell.NavigationLayer`1
  langs:
  - csharp
  - vb
  name: NavigationLayer(SkiaShell, bool)
  nameWithType: SkiaShell.NavigationLayer<T>.NavigationLayer(SkiaShell, bool)
  fullName: DrawnUi.Controls.SkiaShell.NavigationLayer<T>.NavigationLayer(DrawnUi.Controls.SkiaShell, bool)
  type: Constructor
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/Navigation/SkiaShell.NavigationLayer.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Maui/DrawnUi/Controls/Navigation/SkiaShell.NavigationLayer.cs
    startLine: 14
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  summary: if isModel is true than will try to freeze background before showing. otherwise will be just an overlay like toast etc.
  example: []
  syntax:
    content: public NavigationLayer(SkiaShell shell, bool freezeLayout)
    parameters:
    - id: shell
      type: DrawnUi.Controls.SkiaShell
      description: ''
    - id: freezeLayout
      type: System.Boolean
    content.vb: Public Sub New(shell As SkiaShell, freezeLayout As Boolean)
  overload: DrawnUi.Controls.SkiaShell.NavigationLayer`1.#ctor*
  nameWithType.vb: SkiaShell.NavigationLayer(Of T).New(SkiaShell, Boolean)
  fullName.vb: DrawnUi.Controls.SkiaShell.NavigationLayer(Of T).New(DrawnUi.Controls.SkiaShell, Boolean)
  name.vb: New(SkiaShell, Boolean)
- uid: DrawnUi.Controls.SkiaShell.NavigationLayer`1.NavigationStack
  commentId: F:DrawnUi.Controls.SkiaShell.NavigationLayer`1.NavigationStack
  id: NavigationStack
  parent: DrawnUi.Controls.SkiaShell.NavigationLayer`1
  langs:
  - csharp
  - vb
  name: NavigationStack
  nameWithType: SkiaShell.NavigationLayer<T>.NavigationStack
  fullName: DrawnUi.Controls.SkiaShell.NavigationLayer<T>.NavigationStack
  type: Field
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/Navigation/SkiaShell.NavigationLayer.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: NavigationStack
    path: ../src/Maui/DrawnUi/Controls/Navigation/SkiaShell.NavigationLayer.cs
    startLine: 20
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: public ObservableCollection<T> NavigationStack
    return:
      type: System.Collections.ObjectModel.ObservableCollection{{T}}
    content.vb: Public NavigationStack As ObservableCollection(Of T)
  nameWithType.vb: SkiaShell.NavigationLayer(Of T).NavigationStack
  fullName.vb: DrawnUi.Controls.SkiaShell.NavigationLayer(Of T).NavigationStack
- uid: DrawnUi.Controls.SkiaShell.NavigationLayer`1._shell
  commentId: F:DrawnUi.Controls.SkiaShell.NavigationLayer`1._shell
  id: _shell
  parent: DrawnUi.Controls.SkiaShell.NavigationLayer`1
  langs:
  - csharp
  - vb
  name: _shell
  nameWithType: SkiaShell.NavigationLayer<T>._shell
  fullName: DrawnUi.Controls.SkiaShell.NavigationLayer<T>._shell
  type: Field
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/Navigation/SkiaShell.NavigationLayer.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: _shell
    path: ../src/Maui/DrawnUi/Controls/Navigation/SkiaShell.NavigationLayer.cs
    startLine: 22
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: protected readonly SkiaShell _shell
    return:
      type: DrawnUi.Controls.SkiaShell
    content.vb: Protected ReadOnly _shell As SkiaShell
  nameWithType.vb: SkiaShell.NavigationLayer(Of T)._shell
  fullName.vb: DrawnUi.Controls.SkiaShell.NavigationLayer(Of T)._shell
- uid: DrawnUi.Controls.SkiaShell.NavigationLayer`1.Open(`0,System.Boolean)
  commentId: M:DrawnUi.Controls.SkiaShell.NavigationLayer`1.Open(`0,System.Boolean)
  id: Open(`0,System.Boolean)
  parent: DrawnUi.Controls.SkiaShell.NavigationLayer`1
  langs:
  - csharp
  - vb
  name: Open(T, bool)
  nameWithType: SkiaShell.NavigationLayer<T>.Open(T, bool)
  fullName: DrawnUi.Controls.SkiaShell.NavigationLayer<T>.Open(T, bool)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/Navigation/SkiaShell.NavigationLayer.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Open
    path: ../src/Maui/DrawnUi/Controls/Navigation/SkiaShell.NavigationLayer.cs
    startLine: 26
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: public virtual Task Open(T control, bool animated)
    parameters:
    - id: control
      type: '{T}'
    - id: animated
      type: System.Boolean
    return:
      type: System.Threading.Tasks.Task
    content.vb: Public Overridable Function Open(control As T, animated As Boolean) As Task
  overload: DrawnUi.Controls.SkiaShell.NavigationLayer`1.Open*
  nameWithType.vb: SkiaShell.NavigationLayer(Of T).Open(T, Boolean)
  fullName.vb: DrawnUi.Controls.SkiaShell.NavigationLayer(Of T).Open(T, Boolean)
  name.vb: Open(T, Boolean)
- uid: DrawnUi.Controls.SkiaShell.NavigationLayer`1.OnOpened(`0)
  commentId: M:DrawnUi.Controls.SkiaShell.NavigationLayer`1.OnOpened(`0)
  id: OnOpened(`0)
  parent: DrawnUi.Controls.SkiaShell.NavigationLayer`1
  langs:
  - csharp
  - vb
  name: OnOpened(T)
  nameWithType: SkiaShell.NavigationLayer<T>.OnOpened(T)
  fullName: DrawnUi.Controls.SkiaShell.NavigationLayer<T>.OnOpened(T)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/Navigation/SkiaShell.NavigationLayer.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: OnOpened
    path: ../src/Maui/DrawnUi/Controls/Navigation/SkiaShell.NavigationLayer.cs
    startLine: 52
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: public virtual void OnOpened(T control)
    parameters:
    - id: control
      type: '{T}'
    content.vb: Public Overridable Sub OnOpened(control As T)
  overload: DrawnUi.Controls.SkiaShell.NavigationLayer`1.OnOpened*
  nameWithType.vb: SkiaShell.NavigationLayer(Of T).OnOpened(T)
  fullName.vb: DrawnUi.Controls.SkiaShell.NavigationLayer(Of T).OnOpened(T)
- uid: DrawnUi.Controls.SkiaShell.NavigationLayer`1.Close(`0,System.Boolean)
  commentId: M:DrawnUi.Controls.SkiaShell.NavigationLayer`1.Close(`0,System.Boolean)
  id: Close(`0,System.Boolean)
  parent: DrawnUi.Controls.SkiaShell.NavigationLayer`1
  langs:
  - csharp
  - vb
  name: Close(T, bool)
  nameWithType: SkiaShell.NavigationLayer<T>.Close(T, bool)
  fullName: DrawnUi.Controls.SkiaShell.NavigationLayer<T>.Close(T, bool)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/Navigation/SkiaShell.NavigationLayer.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Close
    path: ../src/Maui/DrawnUi/Controls/Navigation/SkiaShell.NavigationLayer.cs
    startLine: 60
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: public Task Close(T control, bool animated)
    parameters:
    - id: control
      type: '{T}'
    - id: animated
      type: System.Boolean
    return:
      type: System.Threading.Tasks.Task
    content.vb: Public Function Close(control As T, animated As Boolean) As Task
  overload: DrawnUi.Controls.SkiaShell.NavigationLayer`1.Close*
  nameWithType.vb: SkiaShell.NavigationLayer(Of T).Close(T, Boolean)
  fullName.vb: DrawnUi.Controls.SkiaShell.NavigationLayer(Of T).Close(T, Boolean)
  name.vb: Close(T, Boolean)
- uid: DrawnUi.Controls.SkiaShell.NavigationLayer`1.Close(System.Boolean)
  commentId: M:DrawnUi.Controls.SkiaShell.NavigationLayer`1.Close(System.Boolean)
  id: Close(System.Boolean)
  parent: DrawnUi.Controls.SkiaShell.NavigationLayer`1
  langs:
  - csharp
  - vb
  name: Close(bool)
  nameWithType: SkiaShell.NavigationLayer<T>.Close(bool)
  fullName: DrawnUi.Controls.SkiaShell.NavigationLayer<T>.Close(bool)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/Navigation/SkiaShell.NavigationLayer.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Close
    path: ../src/Maui/DrawnUi/Controls/Navigation/SkiaShell.NavigationLayer.cs
    startLine: 90
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: public Task Close(bool animated)
    parameters:
    - id: animated
      type: System.Boolean
    return:
      type: System.Threading.Tasks.Task
    content.vb: Public Function Close(animated As Boolean) As Task
  overload: DrawnUi.Controls.SkiaShell.NavigationLayer`1.Close*
  nameWithType.vb: SkiaShell.NavigationLayer(Of T).Close(Boolean)
  fullName.vb: DrawnUi.Controls.SkiaShell.NavigationLayer(Of T).Close(Boolean)
  name.vb: Close(Boolean)
- uid: DrawnUi.Controls.SkiaShell.NavigationLayer`1.CloseAll
  commentId: M:DrawnUi.Controls.SkiaShell.NavigationLayer`1.CloseAll
  id: CloseAll
  parent: DrawnUi.Controls.SkiaShell.NavigationLayer`1
  langs:
  - csharp
  - vb
  name: CloseAll()
  nameWithType: SkiaShell.NavigationLayer<T>.CloseAll()
  fullName: DrawnUi.Controls.SkiaShell.NavigationLayer<T>.CloseAll()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/Navigation/SkiaShell.NavigationLayer.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: CloseAll
    path: ../src/Maui/DrawnUi/Controls/Navigation/SkiaShell.NavigationLayer.cs
    startLine: 99
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: public Task CloseAll()
    return:
      type: System.Threading.Tasks.Task
    content.vb: Public Function CloseAll() As Task
  overload: DrawnUi.Controls.SkiaShell.NavigationLayer`1.CloseAll*
  nameWithType.vb: SkiaShell.NavigationLayer(Of T).CloseAll()
  fullName.vb: DrawnUi.Controls.SkiaShell.NavigationLayer(Of T).CloseAll()
references:
- uid: DrawnUi.Controls
  commentId: N:DrawnUi.Controls
  href: DrawnUi.html
  name: DrawnUi.Controls
  nameWithType: DrawnUi.Controls
  fullName: DrawnUi.Controls
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Controls
    name: Controls
    href: DrawnUi.Controls.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Controls
    name: Controls
    href: DrawnUi.Controls.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Controls.SkiaShell.NavigationLayer`1.#ctor*
  commentId: Overload:DrawnUi.Controls.SkiaShell.NavigationLayer`1.#ctor
  href: DrawnUi.Controls.SkiaShell.NavigationLayer-1.html#DrawnUi_Controls_SkiaShell_NavigationLayer_1__ctor_DrawnUi_Controls_SkiaShell_System_Boolean_
  name: NavigationLayer
  nameWithType: SkiaShell.NavigationLayer<T>.NavigationLayer
  fullName: DrawnUi.Controls.SkiaShell.NavigationLayer<T>.NavigationLayer
  nameWithType.vb: SkiaShell.NavigationLayer(Of T).New
  fullName.vb: DrawnUi.Controls.SkiaShell.NavigationLayer(Of T).New
  name.vb: New
- uid: DrawnUi.Controls.SkiaShell
  commentId: T:DrawnUi.Controls.SkiaShell
  parent: DrawnUi.Controls
  href: DrawnUi.Controls.SkiaShell.html
  name: SkiaShell
  nameWithType: SkiaShell
  fullName: DrawnUi.Controls.SkiaShell
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: System.Collections.ObjectModel.ObservableCollection{{T}}
  commentId: T:System.Collections.ObjectModel.ObservableCollection{`0}
  parent: System.Collections.ObjectModel
  definition: System.Collections.ObjectModel.ObservableCollection`1
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1
  name: ObservableCollection<T>
  nameWithType: ObservableCollection<T>
  fullName: System.Collections.ObjectModel.ObservableCollection<T>
  nameWithType.vb: ObservableCollection(Of T)
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of T)
  name.vb: ObservableCollection(Of T)
  spec.csharp:
  - uid: System.Collections.ObjectModel.ObservableCollection`1
    name: ObservableCollection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Collections.ObjectModel.ObservableCollection`1
    name: ObservableCollection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: System.Collections.ObjectModel.ObservableCollection`1
  commentId: T:System.Collections.ObjectModel.ObservableCollection`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1
  name: ObservableCollection<T>
  nameWithType: ObservableCollection<T>
  fullName: System.Collections.ObjectModel.ObservableCollection<T>
  nameWithType.vb: ObservableCollection(Of T)
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of T)
  name.vb: ObservableCollection(Of T)
  spec.csharp:
  - uid: System.Collections.ObjectModel.ObservableCollection`1
    name: ObservableCollection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Collections.ObjectModel.ObservableCollection`1
    name: ObservableCollection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: System.Collections.ObjectModel
  commentId: N:System.Collections.ObjectModel
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Collections.ObjectModel
  nameWithType: System.Collections.ObjectModel
  fullName: System.Collections.ObjectModel
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.ObjectModel
    name: ObjectModel
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.ObjectModel
    name: ObjectModel
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel
- uid: DrawnUi.Controls.SkiaShell.NavigationLayer`1.Open*
  commentId: Overload:DrawnUi.Controls.SkiaShell.NavigationLayer`1.Open
  href: DrawnUi.Controls.SkiaShell.NavigationLayer-1.html#DrawnUi_Controls_SkiaShell_NavigationLayer_1_Open__0_System_Boolean_
  name: Open
  nameWithType: SkiaShell.NavigationLayer<T>.Open
  fullName: DrawnUi.Controls.SkiaShell.NavigationLayer<T>.Open
  nameWithType.vb: SkiaShell.NavigationLayer(Of T).Open
  fullName.vb: DrawnUi.Controls.SkiaShell.NavigationLayer(Of T).Open
- uid: '{T}'
  commentId: '!:T'
  definition: T
  name: T
  nameWithType: T
  fullName: T
- uid: System.Threading.Tasks.Task
  commentId: T:System.Threading.Tasks.Task
  parent: System.Threading.Tasks
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.threading.tasks.task
  name: Task
  nameWithType: Task
  fullName: System.Threading.Tasks.Task
- uid: T
  name: T
  nameWithType: T
  fullName: T
- uid: System.Threading.Tasks
  commentId: N:System.Threading.Tasks
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Threading.Tasks
  nameWithType: System.Threading.Tasks
  fullName: System.Threading.Tasks
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Threading
    name: Threading
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading
  - name: .
  - uid: System.Threading.Tasks
    name: Tasks
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading.tasks
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Threading
    name: Threading
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading
  - name: .
  - uid: System.Threading.Tasks
    name: Tasks
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading.tasks
- uid: DrawnUi.Controls.SkiaShell.NavigationLayer`1.OnOpened*
  commentId: Overload:DrawnUi.Controls.SkiaShell.NavigationLayer`1.OnOpened
  href: DrawnUi.Controls.SkiaShell.NavigationLayer-1.html#DrawnUi_Controls_SkiaShell_NavigationLayer_1_OnOpened__0_
  name: OnOpened
  nameWithType: SkiaShell.NavigationLayer<T>.OnOpened
  fullName: DrawnUi.Controls.SkiaShell.NavigationLayer<T>.OnOpened
  nameWithType.vb: SkiaShell.NavigationLayer(Of T).OnOpened
  fullName.vb: DrawnUi.Controls.SkiaShell.NavigationLayer(Of T).OnOpened
- uid: DrawnUi.Controls.SkiaShell.NavigationLayer`1.Close*
  commentId: Overload:DrawnUi.Controls.SkiaShell.NavigationLayer`1.Close
  href: DrawnUi.Controls.SkiaShell.NavigationLayer-1.html#DrawnUi_Controls_SkiaShell_NavigationLayer_1_Close__0_System_Boolean_
  name: Close
  nameWithType: SkiaShell.NavigationLayer<T>.Close
  fullName: DrawnUi.Controls.SkiaShell.NavigationLayer<T>.Close
  nameWithType.vb: SkiaShell.NavigationLayer(Of T).Close
  fullName.vb: DrawnUi.Controls.SkiaShell.NavigationLayer(Of T).Close
- uid: DrawnUi.Controls.SkiaShell.NavigationLayer`1.CloseAll*
  commentId: Overload:DrawnUi.Controls.SkiaShell.NavigationLayer`1.CloseAll
  href: DrawnUi.Controls.SkiaShell.NavigationLayer-1.html#DrawnUi_Controls_SkiaShell_NavigationLayer_1_CloseAll
  name: CloseAll
  nameWithType: SkiaShell.NavigationLayer<T>.CloseAll
  fullName: DrawnUi.Controls.SkiaShell.NavigationLayer<T>.CloseAll
  nameWithType.vb: SkiaShell.NavigationLayer(Of T).CloseAll
  fullName.vb: DrawnUi.Controls.SkiaShell.NavigationLayer(Of T).CloseAll
