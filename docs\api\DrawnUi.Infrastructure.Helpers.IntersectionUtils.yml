### YamlMime:ManagedReference
items:
- uid: DrawnUi.Infrastructure.Helpers.IntersectionUtils
  commentId: T:DrawnUi.Infrastructure.Helpers.IntersectionUtils
  id: IntersectionUtils
  parent: DrawnUi.Infrastructure.Helpers
  children:
  - DrawnUi.Infrastructure.Helpers.IntersectionUtils.Clamped(System.Numerics.Vector2,SkiaSharp.SKRect)
  - DrawnUi.Infrastructure.Helpers.IntersectionUtils.GetIntersection(SkiaSharp.SKRect,System.ValueTuple{System.Numerics.Vector2,System.Numerics.Vector2})
  - DrawnUi.Infrastructure.Helpers.IntersectionUtils.GetIntersection(System.ValueTuple{System.Numerics.Vector2,System.Numerics.Vector2},System.ValueTuple{System.Numerics.Vector2,System.Numerics.Vector2})
  - DrawnUi.Infrastructure.Helpers.IntersectionUtils.IntersectWith(SkiaSharp.SKRect,SkiaSharp.SKRect)
  langs:
  - csharp
  - vb
  name: IntersectionUtils
  nameWithType: IntersectionUtils
  fullName: DrawnUi.Infrastructure.Helpers.IntersectionUtils
  type: Class
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/IntersectionUtils.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IntersectionUtils
    path: ../src/Shared/Draw/Internals/Helpers/IntersectionUtils.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure.Helpers
  syntax:
    content: public static class IntersectionUtils
    content.vb: Public Module IntersectionUtils
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
- uid: DrawnUi.Infrastructure.Helpers.IntersectionUtils.IntersectWith(SkiaSharp.SKRect,SkiaSharp.SKRect)
  commentId: M:DrawnUi.Infrastructure.Helpers.IntersectionUtils.IntersectWith(SkiaSharp.SKRect,SkiaSharp.SKRect)
  id: IntersectWith(SkiaSharp.SKRect,SkiaSharp.SKRect)
  isExtensionMethod: true
  parent: DrawnUi.Infrastructure.Helpers.IntersectionUtils
  langs:
  - csharp
  - vb
  name: IntersectWith(SKRect, SKRect)
  nameWithType: IntersectionUtils.IntersectWith(SKRect, SKRect)
  fullName: DrawnUi.Infrastructure.Helpers.IntersectionUtils.IntersectWith(SkiaSharp.SKRect, SkiaSharp.SKRect)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/IntersectionUtils.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IntersectWith
    path: ../src/Shared/Draw/Internals/Helpers/IntersectionUtils.cs
    startLine: 7
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure.Helpers
  syntax:
    content: public static SKRect IntersectWith(this SKRect r1, SKRect r2)
    parameters:
    - id: r1
      type: SkiaSharp.SKRect
    - id: r2
      type: SkiaSharp.SKRect
    return:
      type: SkiaSharp.SKRect
    content.vb: Public Shared Function IntersectWith(r1 As SKRect, r2 As SKRect) As SKRect
  overload: DrawnUi.Infrastructure.Helpers.IntersectionUtils.IntersectWith*
- uid: DrawnUi.Infrastructure.Helpers.IntersectionUtils.Clamped(System.Numerics.Vector2,SkiaSharp.SKRect)
  commentId: M:DrawnUi.Infrastructure.Helpers.IntersectionUtils.Clamped(System.Numerics.Vector2,SkiaSharp.SKRect)
  id: Clamped(System.Numerics.Vector2,SkiaSharp.SKRect)
  isExtensionMethod: true
  parent: DrawnUi.Infrastructure.Helpers.IntersectionUtils
  langs:
  - csharp
  - vb
  name: Clamped(Vector2, SKRect)
  nameWithType: IntersectionUtils.Clamped(Vector2, SKRect)
  fullName: DrawnUi.Infrastructure.Helpers.IntersectionUtils.Clamped(System.Numerics.Vector2, SkiaSharp.SKRect)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/IntersectionUtils.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Clamped
    path: ../src/Shared/Draw/Internals/Helpers/IntersectionUtils.cs
    startLine: 21
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure.Helpers
  syntax:
    content: public static Vector2 Clamped(this Vector2 point, SKRect rect)
    parameters:
    - id: point
      type: System.Numerics.Vector2
    - id: rect
      type: SkiaSharp.SKRect
    return:
      type: System.Numerics.Vector2
    content.vb: Public Shared Function Clamped(point As Vector2, rect As SKRect) As Vector2
  overload: DrawnUi.Infrastructure.Helpers.IntersectionUtils.Clamped*
- uid: DrawnUi.Infrastructure.Helpers.IntersectionUtils.GetIntersection(System.ValueTuple{System.Numerics.Vector2,System.Numerics.Vector2},System.ValueTuple{System.Numerics.Vector2,System.Numerics.Vector2})
  commentId: M:DrawnUi.Infrastructure.Helpers.IntersectionUtils.GetIntersection(System.ValueTuple{System.Numerics.Vector2,System.Numerics.Vector2},System.ValueTuple{System.Numerics.Vector2,System.Numerics.Vector2})
  id: GetIntersection(System.ValueTuple{System.Numerics.Vector2,System.Numerics.Vector2},System.ValueTuple{System.Numerics.Vector2,System.Numerics.Vector2})
  parent: DrawnUi.Infrastructure.Helpers.IntersectionUtils
  langs:
  - csharp
  - vb
  name: GetIntersection((Vector2, Vector2), (Vector2, Vector2))
  nameWithType: IntersectionUtils.GetIntersection((Vector2, Vector2), (Vector2, Vector2))
  fullName: DrawnUi.Infrastructure.Helpers.IntersectionUtils.GetIntersection((System.Numerics.Vector2, System.Numerics.Vector2), (System.Numerics.Vector2, System.Numerics.Vector2))
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/IntersectionUtils.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: GetIntersection
    path: ../src/Shared/Draw/Internals/Helpers/IntersectionUtils.cs
    startLine: 28
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure.Helpers
  syntax:
    content: public static Vector2? GetIntersection((Vector2, Vector2) segment1, (Vector2, Vector2) segment2)
    parameters:
    - id: segment1
      type: System.ValueTuple{System.Numerics.Vector2,System.Numerics.Vector2}
    - id: segment2
      type: System.ValueTuple{System.Numerics.Vector2,System.Numerics.Vector2}
    return:
      type: System.Nullable{System.Numerics.Vector2}
    content.vb: Public Shared Function GetIntersection(segment1 As (Vector2, Vector2), segment2 As (Vector2, Vector2)) As Vector2?
  overload: DrawnUi.Infrastructure.Helpers.IntersectionUtils.GetIntersection*
- uid: DrawnUi.Infrastructure.Helpers.IntersectionUtils.GetIntersection(SkiaSharp.SKRect,System.ValueTuple{System.Numerics.Vector2,System.Numerics.Vector2})
  commentId: M:DrawnUi.Infrastructure.Helpers.IntersectionUtils.GetIntersection(SkiaSharp.SKRect,System.ValueTuple{System.Numerics.Vector2,System.Numerics.Vector2})
  id: GetIntersection(SkiaSharp.SKRect,System.ValueTuple{System.Numerics.Vector2,System.Numerics.Vector2})
  parent: DrawnUi.Infrastructure.Helpers.IntersectionUtils
  langs:
  - csharp
  - vb
  name: GetIntersection(SKRect, (Vector2, Vector2))
  nameWithType: IntersectionUtils.GetIntersection(SKRect, (Vector2, Vector2))
  fullName: DrawnUi.Infrastructure.Helpers.IntersectionUtils.GetIntersection(SkiaSharp.SKRect, (System.Numerics.Vector2, System.Numerics.Vector2))
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/IntersectionUtils.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: GetIntersection
    path: ../src/Shared/Draw/Internals/Helpers/IntersectionUtils.cs
    startLine: 57
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure.Helpers
  syntax:
    content: public static Vector2? GetIntersection(SKRect rect, (Vector2, Vector2) segment)
    parameters:
    - id: rect
      type: SkiaSharp.SKRect
    - id: segment
      type: System.ValueTuple{System.Numerics.Vector2,System.Numerics.Vector2}
    return:
      type: System.Nullable{System.Numerics.Vector2}
    content.vb: Public Shared Function GetIntersection(rect As SKRect, segment As (Vector2, Vector2)) As Vector2?
  overload: DrawnUi.Infrastructure.Helpers.IntersectionUtils.GetIntersection*
references:
- uid: DrawnUi.Infrastructure.Helpers
  commentId: N:DrawnUi.Infrastructure.Helpers
  href: DrawnUi.html
  name: DrawnUi.Infrastructure.Helpers
  nameWithType: DrawnUi.Infrastructure.Helpers
  fullName: DrawnUi.Infrastructure.Helpers
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Infrastructure
    name: Infrastructure
    href: DrawnUi.Infrastructure.html
  - name: .
  - uid: DrawnUi.Infrastructure.Helpers
    name: Helpers
    href: DrawnUi.Infrastructure.Helpers.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Infrastructure
    name: Infrastructure
    href: DrawnUi.Infrastructure.html
  - name: .
  - uid: DrawnUi.Infrastructure.Helpers
    name: Helpers
    href: DrawnUi.Infrastructure.Helpers.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Infrastructure.Helpers.IntersectionUtils.IntersectWith*
  commentId: Overload:DrawnUi.Infrastructure.Helpers.IntersectionUtils.IntersectWith
  href: DrawnUi.Infrastructure.Helpers.IntersectionUtils.html#DrawnUi_Infrastructure_Helpers_IntersectionUtils_IntersectWith_SkiaSharp_SKRect_SkiaSharp_SKRect_
  name: IntersectWith
  nameWithType: IntersectionUtils.IntersectWith
  fullName: DrawnUi.Infrastructure.Helpers.IntersectionUtils.IntersectWith
- uid: SkiaSharp.SKRect
  commentId: T:SkiaSharp.SKRect
  parent: SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.skrect
  name: SKRect
  nameWithType: SKRect
  fullName: SkiaSharp.SKRect
- uid: SkiaSharp
  commentId: N:SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp
  name: SkiaSharp
  nameWithType: SkiaSharp
  fullName: SkiaSharp
- uid: DrawnUi.Infrastructure.Helpers.IntersectionUtils.Clamped*
  commentId: Overload:DrawnUi.Infrastructure.Helpers.IntersectionUtils.Clamped
  href: DrawnUi.Infrastructure.Helpers.IntersectionUtils.html#DrawnUi_Infrastructure_Helpers_IntersectionUtils_Clamped_System_Numerics_Vector2_SkiaSharp_SKRect_
  name: Clamped
  nameWithType: IntersectionUtils.Clamped
  fullName: DrawnUi.Infrastructure.Helpers.IntersectionUtils.Clamped
- uid: System.Numerics.Vector2
  commentId: T:System.Numerics.Vector2
  parent: System.Numerics
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.numerics.vector2
  name: Vector2
  nameWithType: Vector2
  fullName: System.Numerics.Vector2
- uid: System.Numerics
  commentId: N:System.Numerics
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Numerics
  nameWithType: System.Numerics
  fullName: System.Numerics
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Numerics
    name: Numerics
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.numerics
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Numerics
    name: Numerics
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.numerics
- uid: DrawnUi.Infrastructure.Helpers.IntersectionUtils.GetIntersection*
  commentId: Overload:DrawnUi.Infrastructure.Helpers.IntersectionUtils.GetIntersection
  href: DrawnUi.Infrastructure.Helpers.IntersectionUtils.html#DrawnUi_Infrastructure_Helpers_IntersectionUtils_GetIntersection_System_ValueTuple_System_Numerics_Vector2_System_Numerics_Vector2__System_ValueTuple_System_Numerics_Vector2_System_Numerics_Vector2__
  name: GetIntersection
  nameWithType: IntersectionUtils.GetIntersection
  fullName: DrawnUi.Infrastructure.Helpers.IntersectionUtils.GetIntersection
- uid: System.ValueTuple{System.Numerics.Vector2,System.Numerics.Vector2}
  commentId: T:System.ValueTuple{System.Numerics.Vector2,System.Numerics.Vector2}
  parent: System
  definition: System.ValueTuple`2
  href: https://learn.microsoft.com/dotnet/api/system.numerics.vector2
  name: (Vector2, Vector2)
  nameWithType: (Vector2, Vector2)
  fullName: (System.Numerics.Vector2, System.Numerics.Vector2)
  spec.csharp:
  - name: (
  - uid: System.Numerics.Vector2
    name: Vector2
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.numerics.vector2
  - name: ','
  - name: " "
  - uid: System.Numerics.Vector2
    name: Vector2
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.numerics.vector2
  - name: )
  spec.vb:
  - name: (
  - uid: System.Numerics.Vector2
    name: Vector2
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.numerics.vector2
  - name: ','
  - name: " "
  - uid: System.Numerics.Vector2
    name: Vector2
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.numerics.vector2
  - name: )
- uid: System.Nullable{System.Numerics.Vector2}
  commentId: T:System.Nullable{System.Numerics.Vector2}
  parent: System
  definition: System.Nullable`1
  href: https://learn.microsoft.com/dotnet/api/system.numerics.vector2
  name: Vector2?
  nameWithType: Vector2?
  fullName: System.Numerics.Vector2?
  spec.csharp:
  - uid: System.Numerics.Vector2
    name: Vector2
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.numerics.vector2
  - name: '?'
  spec.vb:
  - uid: System.Numerics.Vector2
    name: Vector2
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.numerics.vector2
  - name: '?'
- uid: System.ValueTuple`2
  commentId: T:System.ValueTuple`2
  name: (T1, T2)
  nameWithType: (T1, T2)
  fullName: (T1, T2)
  spec.csharp:
  - name: (
  - name: T1
  - name: ','
  - name: " "
  - name: T2
  - name: )
  spec.vb:
  - name: (
  - name: T1
  - name: ','
  - name: " "
  - name: T2
  - name: )
- uid: System.Nullable`1
  commentId: T:System.Nullable`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.nullable-1
  name: Nullable<T>
  nameWithType: Nullable<T>
  fullName: System.Nullable<T>
  nameWithType.vb: Nullable(Of T)
  fullName.vb: System.Nullable(Of T)
  name.vb: Nullable(Of T)
  spec.csharp:
  - uid: System.Nullable`1
    name: Nullable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.nullable-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Nullable`1
    name: Nullable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.nullable-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
