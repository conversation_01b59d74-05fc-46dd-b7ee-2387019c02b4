### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.RenderLabel
  commentId: T:DrawnUi.Draw.RenderLabel
  id: RenderLabel
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.RenderLabel.Draw
  - DrawnUi.Draw.RenderLabel.Paint
  - DrawnUi.Draw.RenderLabel.Rect
  - DrawnUi.Draw.RenderLabel.Text
  langs:
  - csharp
  - vb
  name: RenderLabel
  nameWithType: RenderLabel
  fullName: DrawnUi.Draw.RenderLabel
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: RenderLabel
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
    startLine: 157
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: 'public class RenderLabel : RenderObject'
    content.vb: Public Class RenderLabel Inherits RenderObject
  inheritance:
  - System.Object
  - DrawnUi.Draw.RenderObject
  inheritedMembers:
  - DrawnUi.Draw.RenderObject.Dispose
  - DrawnUi.Draw.RenderObject.ShouldClipAntialiased
  - DrawnUi.Draw.RenderObject.ClippingPath
  - DrawnUi.Draw.RenderObject.Cache
  - DrawnUi.Draw.RenderObject.IsDistorted
  - DrawnUi.Draw.RenderObject.WillClipBounds
  - DrawnUi.Draw.RenderObject.EffectPostRenderer
  - DrawnUi.Draw.RenderObject.DelegateDrawCache
  - DrawnUi.Draw.RenderObject.DrawRenderObject(DrawnUi.Draw.DrawingContext,DrawnUi.Draw.CachedObject)
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.RenderLabel.Text
  commentId: P:DrawnUi.Draw.RenderLabel.Text
  id: Text
  parent: DrawnUi.Draw.RenderLabel
  langs:
  - csharp
  - vb
  name: Text
  nameWithType: RenderLabel.Text
  fullName: DrawnUi.Draw.RenderLabel.Text
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Text
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
    startLine: 159
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public string Text { get; set; }
    parameters: []
    return:
      type: System.String
    content.vb: Public Property Text As String
  overload: DrawnUi.Draw.RenderLabel.Text*
- uid: DrawnUi.Draw.RenderLabel.Rect
  commentId: P:DrawnUi.Draw.RenderLabel.Rect
  id: Rect
  parent: DrawnUi.Draw.RenderLabel
  langs:
  - csharp
  - vb
  name: Rect
  nameWithType: RenderLabel.Rect
  fullName: DrawnUi.Draw.RenderLabel.Rect
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Rect
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
    startLine: 160
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public SKRect Rect { get; set; }
    parameters: []
    return:
      type: SkiaSharp.SKRect
    content.vb: Public Property Rect As SKRect
  overload: DrawnUi.Draw.RenderLabel.Rect*
- uid: DrawnUi.Draw.RenderLabel.Paint
  commentId: P:DrawnUi.Draw.RenderLabel.Paint
  id: Paint
  parent: DrawnUi.Draw.RenderLabel
  langs:
  - csharp
  - vb
  name: Paint
  nameWithType: RenderLabel.Paint
  fullName: DrawnUi.Draw.RenderLabel.Paint
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Paint
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
    startLine: 161
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public SKPaint Paint { get; set; }
    parameters: []
    return:
      type: SkiaSharp.SKPaint
    content.vb: Public Property Paint As SKPaint
  overload: DrawnUi.Draw.RenderLabel.Paint*
- uid: DrawnUi.Draw.RenderLabel.Draw
  commentId: M:DrawnUi.Draw.RenderLabel.Draw
  id: Draw
  parent: DrawnUi.Draw.RenderLabel
  langs:
  - csharp
  - vb
  name: Draw()
  nameWithType: RenderLabel.Draw()
  fullName: DrawnUi.Draw.RenderLabel.Draw()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Draw
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
    startLine: 163
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public void Draw()
    content.vb: Public Sub Draw()
  overload: DrawnUi.Draw.RenderLabel.Draw*
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: DrawnUi.Draw.RenderObject
  commentId: T:DrawnUi.Draw.RenderObject
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.RenderObject.html
  name: RenderObject
  nameWithType: RenderObject
  fullName: DrawnUi.Draw.RenderObject
- uid: DrawnUi.Draw.RenderObject.Dispose
  commentId: M:DrawnUi.Draw.RenderObject.Dispose
  parent: DrawnUi.Draw.RenderObject
  href: DrawnUi.Draw.RenderObject.html#DrawnUi_Draw_RenderObject_Dispose
  name: Dispose()
  nameWithType: RenderObject.Dispose()
  fullName: DrawnUi.Draw.RenderObject.Dispose()
  spec.csharp:
  - uid: DrawnUi.Draw.RenderObject.Dispose
    name: Dispose
    href: DrawnUi.Draw.RenderObject.html#DrawnUi_Draw_RenderObject_Dispose
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.RenderObject.Dispose
    name: Dispose
    href: DrawnUi.Draw.RenderObject.html#DrawnUi_Draw_RenderObject_Dispose
  - name: (
  - name: )
- uid: DrawnUi.Draw.RenderObject.ShouldClipAntialiased
  commentId: P:DrawnUi.Draw.RenderObject.ShouldClipAntialiased
  parent: DrawnUi.Draw.RenderObject
  href: DrawnUi.Draw.RenderObject.html#DrawnUi_Draw_RenderObject_ShouldClipAntialiased
  name: ShouldClipAntialiased
  nameWithType: RenderObject.ShouldClipAntialiased
  fullName: DrawnUi.Draw.RenderObject.ShouldClipAntialiased
- uid: DrawnUi.Draw.RenderObject.ClippingPath
  commentId: P:DrawnUi.Draw.RenderObject.ClippingPath
  parent: DrawnUi.Draw.RenderObject
  href: DrawnUi.Draw.RenderObject.html#DrawnUi_Draw_RenderObject_ClippingPath
  name: ClippingPath
  nameWithType: RenderObject.ClippingPath
  fullName: DrawnUi.Draw.RenderObject.ClippingPath
- uid: DrawnUi.Draw.RenderObject.Cache
  commentId: P:DrawnUi.Draw.RenderObject.Cache
  parent: DrawnUi.Draw.RenderObject
  href: DrawnUi.Draw.RenderObject.html#DrawnUi_Draw_RenderObject_Cache
  name: Cache
  nameWithType: RenderObject.Cache
  fullName: DrawnUi.Draw.RenderObject.Cache
- uid: DrawnUi.Draw.RenderObject.IsDistorted
  commentId: P:DrawnUi.Draw.RenderObject.IsDistorted
  parent: DrawnUi.Draw.RenderObject
  href: DrawnUi.Draw.RenderObject.html#DrawnUi_Draw_RenderObject_IsDistorted
  name: IsDistorted
  nameWithType: RenderObject.IsDistorted
  fullName: DrawnUi.Draw.RenderObject.IsDistorted
- uid: DrawnUi.Draw.RenderObject.WillClipBounds
  commentId: P:DrawnUi.Draw.RenderObject.WillClipBounds
  parent: DrawnUi.Draw.RenderObject
  href: DrawnUi.Draw.RenderObject.html#DrawnUi_Draw_RenderObject_WillClipBounds
  name: WillClipBounds
  nameWithType: RenderObject.WillClipBounds
  fullName: DrawnUi.Draw.RenderObject.WillClipBounds
- uid: DrawnUi.Draw.RenderObject.EffectPostRenderer
  commentId: P:DrawnUi.Draw.RenderObject.EffectPostRenderer
  parent: DrawnUi.Draw.RenderObject
  href: DrawnUi.Draw.RenderObject.html#DrawnUi_Draw_RenderObject_EffectPostRenderer
  name: EffectPostRenderer
  nameWithType: RenderObject.EffectPostRenderer
  fullName: DrawnUi.Draw.RenderObject.EffectPostRenderer
- uid: DrawnUi.Draw.RenderObject.DelegateDrawCache
  commentId: P:DrawnUi.Draw.RenderObject.DelegateDrawCache
  parent: DrawnUi.Draw.RenderObject
  href: DrawnUi.Draw.RenderObject.html#DrawnUi_Draw_RenderObject_DelegateDrawCache
  name: DelegateDrawCache
  nameWithType: RenderObject.DelegateDrawCache
  fullName: DrawnUi.Draw.RenderObject.DelegateDrawCache
- uid: DrawnUi.Draw.RenderObject.DrawRenderObject(DrawnUi.Draw.DrawingContext,DrawnUi.Draw.CachedObject)
  commentId: M:DrawnUi.Draw.RenderObject.DrawRenderObject(DrawnUi.Draw.DrawingContext,DrawnUi.Draw.CachedObject)
  parent: DrawnUi.Draw.RenderObject
  href: DrawnUi.Draw.RenderObject.html#DrawnUi_Draw_RenderObject_DrawRenderObject_DrawnUi_Draw_DrawingContext_DrawnUi_Draw_CachedObject_
  name: DrawRenderObject(DrawingContext, CachedObject)
  nameWithType: RenderObject.DrawRenderObject(DrawingContext, CachedObject)
  fullName: DrawnUi.Draw.RenderObject.DrawRenderObject(DrawnUi.Draw.DrawingContext, DrawnUi.Draw.CachedObject)
  spec.csharp:
  - uid: DrawnUi.Draw.RenderObject.DrawRenderObject(DrawnUi.Draw.DrawingContext,DrawnUi.Draw.CachedObject)
    name: DrawRenderObject
    href: DrawnUi.Draw.RenderObject.html#DrawnUi_Draw_RenderObject_DrawRenderObject_DrawnUi_Draw_DrawingContext_DrawnUi_Draw_CachedObject_
  - name: (
  - uid: DrawnUi.Draw.DrawingContext
    name: DrawingContext
    href: DrawnUi.Draw.DrawingContext.html
  - name: ','
  - name: " "
  - uid: DrawnUi.Draw.CachedObject
    name: CachedObject
    href: DrawnUi.Draw.CachedObject.html
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.RenderObject.DrawRenderObject(DrawnUi.Draw.DrawingContext,DrawnUi.Draw.CachedObject)
    name: DrawRenderObject
    href: DrawnUi.Draw.RenderObject.html#DrawnUi_Draw_RenderObject_DrawRenderObject_DrawnUi_Draw_DrawingContext_DrawnUi_Draw_CachedObject_
  - name: (
  - uid: DrawnUi.Draw.DrawingContext
    name: DrawingContext
    href: DrawnUi.Draw.DrawingContext.html
  - name: ','
  - name: " "
  - uid: DrawnUi.Draw.CachedObject
    name: CachedObject
    href: DrawnUi.Draw.CachedObject.html
  - name: )
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.RenderLabel.Text*
  commentId: Overload:DrawnUi.Draw.RenderLabel.Text
  href: DrawnUi.Draw.RenderLabel.html#DrawnUi_Draw_RenderLabel_Text
  name: Text
  nameWithType: RenderLabel.Text
  fullName: DrawnUi.Draw.RenderLabel.Text
- uid: System.String
  commentId: T:System.String
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.string
  name: string
  nameWithType: string
  fullName: string
  nameWithType.vb: String
  fullName.vb: String
  name.vb: String
- uid: DrawnUi.Draw.RenderLabel.Rect*
  commentId: Overload:DrawnUi.Draw.RenderLabel.Rect
  href: DrawnUi.Draw.RenderLabel.html#DrawnUi_Draw_RenderLabel_Rect
  name: Rect
  nameWithType: RenderLabel.Rect
  fullName: DrawnUi.Draw.RenderLabel.Rect
- uid: SkiaSharp.SKRect
  commentId: T:SkiaSharp.SKRect
  parent: SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.skrect
  name: SKRect
  nameWithType: SKRect
  fullName: SkiaSharp.SKRect
- uid: SkiaSharp
  commentId: N:SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp
  name: SkiaSharp
  nameWithType: SkiaSharp
  fullName: SkiaSharp
- uid: DrawnUi.Draw.RenderLabel.Paint*
  commentId: Overload:DrawnUi.Draw.RenderLabel.Paint
  href: DrawnUi.Draw.RenderLabel.html#DrawnUi_Draw_RenderLabel_Paint
  name: Paint
  nameWithType: RenderLabel.Paint
  fullName: DrawnUi.Draw.RenderLabel.Paint
- uid: SkiaSharp.SKPaint
  commentId: T:SkiaSharp.SKPaint
  parent: SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.skpaint
  name: SKPaint
  nameWithType: SKPaint
  fullName: SkiaSharp.SKPaint
- uid: DrawnUi.Draw.RenderLabel.Draw*
  commentId: Overload:DrawnUi.Draw.RenderLabel.Draw
  href: DrawnUi.Draw.RenderLabel.html#DrawnUi_Draw_RenderLabel_Draw
  name: Draw
  nameWithType: RenderLabel.Draw
  fullName: DrawnUi.Draw.RenderLabel.Draw
