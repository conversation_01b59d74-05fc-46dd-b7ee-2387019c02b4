### YamlMime:ManagedReference
items:
- uid: DrawnUi.Infrastructure.Models.BitmapLoadedEventArgs
  commentId: T:DrawnUi.Infrastructure.Models.BitmapLoadedEventArgs
  id: BitmapLoadedEventArgs
  parent: DrawnUi.Infrastructure.Models
  children:
  - DrawnUi.Infrastructure.Models.BitmapLoadedEventArgs.#ctor(System.String,SkiaSharp.SKBitmap)
  - DrawnUi.Infrastructure.Models.BitmapLoadedEventArgs.Bitmap
  langs:
  - csharp
  - vb
  name: BitmapLoadedEventArgs
  nameWithType: BitmapLoadedEventArgs
  fullName: DrawnUi.Infrastructure.Models.BitmapLoadedEventArgs
  type: Class
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ContentLoadedEventArgs.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: BitmapLoadedEventArgs
    path: ../src/Shared/Draw/Internals/Models/ContentLoadedEventArgs.cs
    startLine: 3
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure.Models
  syntax:
    content: 'public class BitmapLoadedEventArgs : ContentLoadedEventArgs'
    content.vb: Public Class BitmapLoadedEventArgs Inherits ContentLoadedEventArgs
  inheritance:
  - System.Object
  - System.EventArgs
  - DrawnUi.Infrastructure.Models.ContentLoadedEventArgs
  inheritedMembers:
  - DrawnUi.Infrastructure.Models.ContentLoadedEventArgs.Content
  - System.EventArgs.Empty
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Infrastructure.Models.BitmapLoadedEventArgs.#ctor(System.String,SkiaSharp.SKBitmap)
  commentId: M:DrawnUi.Infrastructure.Models.BitmapLoadedEventArgs.#ctor(System.String,SkiaSharp.SKBitmap)
  id: '#ctor(System.String,SkiaSharp.SKBitmap)'
  parent: DrawnUi.Infrastructure.Models.BitmapLoadedEventArgs
  langs:
  - csharp
  - vb
  name: BitmapLoadedEventArgs(string, SKBitmap)
  nameWithType: BitmapLoadedEventArgs.BitmapLoadedEventArgs(string, SKBitmap)
  fullName: DrawnUi.Infrastructure.Models.BitmapLoadedEventArgs.BitmapLoadedEventArgs(string, SkiaSharp.SKBitmap)
  type: Constructor
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ContentLoadedEventArgs.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Shared/Draw/Internals/Models/ContentLoadedEventArgs.cs
    startLine: 5
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure.Models
  syntax:
    content: public BitmapLoadedEventArgs(string content, SKBitmap bitmap)
    parameters:
    - id: content
      type: System.String
    - id: bitmap
      type: SkiaSharp.SKBitmap
    content.vb: Public Sub New(content As String, bitmap As SKBitmap)
  overload: DrawnUi.Infrastructure.Models.BitmapLoadedEventArgs.#ctor*
  nameWithType.vb: BitmapLoadedEventArgs.New(String, SKBitmap)
  fullName.vb: DrawnUi.Infrastructure.Models.BitmapLoadedEventArgs.New(String, SkiaSharp.SKBitmap)
  name.vb: New(String, SKBitmap)
- uid: DrawnUi.Infrastructure.Models.BitmapLoadedEventArgs.Bitmap
  commentId: P:DrawnUi.Infrastructure.Models.BitmapLoadedEventArgs.Bitmap
  id: Bitmap
  parent: DrawnUi.Infrastructure.Models.BitmapLoadedEventArgs
  langs:
  - csharp
  - vb
  name: Bitmap
  nameWithType: BitmapLoadedEventArgs.Bitmap
  fullName: DrawnUi.Infrastructure.Models.BitmapLoadedEventArgs.Bitmap
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ContentLoadedEventArgs.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Bitmap
    path: ../src/Shared/Draw/Internals/Models/ContentLoadedEventArgs.cs
    startLine: 9
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure.Models
  syntax:
    content: public SKBitmap Bitmap { get; }
    parameters: []
    return:
      type: SkiaSharp.SKBitmap
    content.vb: Public Property Bitmap As SKBitmap
  overload: DrawnUi.Infrastructure.Models.BitmapLoadedEventArgs.Bitmap*
references:
- uid: DrawnUi.Infrastructure.Models
  commentId: N:DrawnUi.Infrastructure.Models
  href: DrawnUi.html
  name: DrawnUi.Infrastructure.Models
  nameWithType: DrawnUi.Infrastructure.Models
  fullName: DrawnUi.Infrastructure.Models
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Infrastructure
    name: Infrastructure
    href: DrawnUi.Infrastructure.html
  - name: .
  - uid: DrawnUi.Infrastructure.Models
    name: Models
    href: DrawnUi.Infrastructure.Models.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Infrastructure
    name: Infrastructure
    href: DrawnUi.Infrastructure.html
  - name: .
  - uid: DrawnUi.Infrastructure.Models
    name: Models
    href: DrawnUi.Infrastructure.Models.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.EventArgs
  commentId: T:System.EventArgs
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.eventargs
  name: EventArgs
  nameWithType: EventArgs
  fullName: System.EventArgs
- uid: DrawnUi.Infrastructure.Models.ContentLoadedEventArgs
  commentId: T:DrawnUi.Infrastructure.Models.ContentLoadedEventArgs
  parent: DrawnUi.Infrastructure.Models
  href: DrawnUi.Infrastructure.Models.ContentLoadedEventArgs.html
  name: ContentLoadedEventArgs
  nameWithType: ContentLoadedEventArgs
  fullName: DrawnUi.Infrastructure.Models.ContentLoadedEventArgs
- uid: DrawnUi.Infrastructure.Models.ContentLoadedEventArgs.Content
  commentId: P:DrawnUi.Infrastructure.Models.ContentLoadedEventArgs.Content
  parent: DrawnUi.Infrastructure.Models.ContentLoadedEventArgs
  href: DrawnUi.Infrastructure.Models.ContentLoadedEventArgs.html#DrawnUi_Infrastructure_Models_ContentLoadedEventArgs_Content
  name: Content
  nameWithType: ContentLoadedEventArgs.Content
  fullName: DrawnUi.Infrastructure.Models.ContentLoadedEventArgs.Content
- uid: System.EventArgs.Empty
  commentId: F:System.EventArgs.Empty
  parent: System.EventArgs
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.eventargs.empty
  name: Empty
  nameWithType: EventArgs.Empty
  fullName: System.EventArgs.Empty
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Infrastructure.Models.BitmapLoadedEventArgs.#ctor*
  commentId: Overload:DrawnUi.Infrastructure.Models.BitmapLoadedEventArgs.#ctor
  href: DrawnUi.Infrastructure.Models.BitmapLoadedEventArgs.html#DrawnUi_Infrastructure_Models_BitmapLoadedEventArgs__ctor_System_String_SkiaSharp_SKBitmap_
  name: BitmapLoadedEventArgs
  nameWithType: BitmapLoadedEventArgs.BitmapLoadedEventArgs
  fullName: DrawnUi.Infrastructure.Models.BitmapLoadedEventArgs.BitmapLoadedEventArgs
  nameWithType.vb: BitmapLoadedEventArgs.New
  fullName.vb: DrawnUi.Infrastructure.Models.BitmapLoadedEventArgs.New
  name.vb: New
- uid: System.String
  commentId: T:System.String
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.string
  name: string
  nameWithType: string
  fullName: string
  nameWithType.vb: String
  fullName.vb: String
  name.vb: String
- uid: SkiaSharp.SKBitmap
  commentId: T:SkiaSharp.SKBitmap
  parent: SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.skbitmap
  name: SKBitmap
  nameWithType: SKBitmap
  fullName: SkiaSharp.SKBitmap
- uid: SkiaSharp
  commentId: N:SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp
  name: SkiaSharp
  nameWithType: SkiaSharp
  fullName: SkiaSharp
- uid: DrawnUi.Infrastructure.Models.BitmapLoadedEventArgs.Bitmap*
  commentId: Overload:DrawnUi.Infrastructure.Models.BitmapLoadedEventArgs.Bitmap
  href: DrawnUi.Infrastructure.Models.BitmapLoadedEventArgs.html#DrawnUi_Infrastructure_Models_BitmapLoadedEventArgs_Bitmap
  name: Bitmap
  nameWithType: BitmapLoadedEventArgs.Bitmap
  fullName: DrawnUi.Infrastructure.Models.BitmapLoadedEventArgs.Bitmap
