### YamlMime:ManagedReference
items:
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce
  commentId: T:DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce
  id: VelocitySkiaAnimator.DragForce
  parent: DrawnUi.Animate.Animators
  children:
  - DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.GetInitialVelocity(System.Single,System.Single,System.Single)
  - DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.getAcceleration(System.Single,System.Single)
  - DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.getFrictionScalar
  - DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.isAtEquilibrium(System.Double,System.Single)
  - DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.setFrictionScalar(System.Single)
  - DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.setValueThreshold(System.Single)
  - DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.updateValueAndVelocity(System.Double,System.Single,System.Int64,System.Int64)
  langs:
  - csharp
  - vb
  name: VelocitySkiaAnimator.DragForce
  nameWithType: VelocitySkiaAnimator.DragForce
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce
  type: Class
  source:
    remote:
      path: src/Shared/Features/Animators/VelocitySkiaAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: DragForce
    path: ../src/Shared/Features/Animators/VelocitySkiaAnimator.cs
    startLine: 317
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Animate.Animators
  syntax:
    content: public class VelocitySkiaAnimator.DragForce
    content.vb: Public Class VelocitySkiaAnimator.DragForce
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.setFrictionScalar(System.Single)
  commentId: M:DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.setFrictionScalar(System.Single)
  id: setFrictionScalar(System.Single)
  parent: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce
  langs:
  - csharp
  - vb
  name: setFrictionScalar(float)
  nameWithType: VelocitySkiaAnimator.DragForce.setFrictionScalar(float)
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.setFrictionScalar(float)
  type: Method
  source:
    remote:
      path: src/Shared/Features/Animators/VelocitySkiaAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: setFrictionScalar
    path: ../src/Shared/Features/Animators/VelocitySkiaAnimator.cs
    startLine: 333
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Animate.Animators
  syntax:
    content: public void setFrictionScalar(float frictionScalar)
    parameters:
    - id: frictionScalar
      type: System.Single
    content.vb: Public Sub setFrictionScalar(frictionScalar As Single)
  overload: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.setFrictionScalar*
  nameWithType.vb: VelocitySkiaAnimator.DragForce.setFrictionScalar(Single)
  fullName.vb: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.setFrictionScalar(Single)
  name.vb: setFrictionScalar(Single)
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.getFrictionScalar
  commentId: M:DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.getFrictionScalar
  id: getFrictionScalar
  parent: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce
  langs:
  - csharp
  - vb
  name: getFrictionScalar()
  nameWithType: VelocitySkiaAnimator.DragForce.getFrictionScalar()
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.getFrictionScalar()
  type: Method
  source:
    remote:
      path: src/Shared/Features/Animators/VelocitySkiaAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: getFrictionScalar
    path: ../src/Shared/Features/Animators/VelocitySkiaAnimator.cs
    startLine: 338
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Animate.Animators
  syntax:
    content: public float getFrictionScalar()
    return:
      type: System.Single
    content.vb: Public Function getFrictionScalar() As Single
  overload: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.getFrictionScalar*
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.GetInitialVelocity(System.Single,System.Single,System.Single)
  commentId: M:DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.GetInitialVelocity(System.Single,System.Single,System.Single)
  id: GetInitialVelocity(System.Single,System.Single,System.Single)
  parent: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce
  langs:
  - csharp
  - vb
  name: GetInitialVelocity(float, float, float)
  nameWithType: VelocitySkiaAnimator.DragForce.GetInitialVelocity(float, float, float)
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.GetInitialVelocity(float, float, float)
  type: Method
  source:
    remote:
      path: src/Shared/Features/Animators/VelocitySkiaAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: GetInitialVelocity
    path: ../src/Shared/Features/Animators/VelocitySkiaAnimator.cs
    startLine: 360
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Animate.Animators
  summary: inverse of updateValueAndVelocity
  example: []
  syntax:
    content: public float GetInitialVelocity(float initialPosition, float finalPosition, float durationTime)
    parameters:
    - id: initialPosition
      type: System.Single
      description: ''
    - id: finalPosition
      type: System.Single
      description: ''
    - id: durationTime
      type: System.Single
      description: ''
    return:
      type: System.Single
      description: ''
    content.vb: Public Function GetInitialVelocity(initialPosition As Single, finalPosition As Single, durationTime As Single) As Single
  overload: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.GetInitialVelocity*
  nameWithType.vb: VelocitySkiaAnimator.DragForce.GetInitialVelocity(Single, Single, Single)
  fullName.vb: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.GetInitialVelocity(Single, Single, Single)
  name.vb: GetInitialVelocity(Single, Single, Single)
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.updateValueAndVelocity(System.Double,System.Single,System.Int64,System.Int64)
  commentId: M:DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.updateValueAndVelocity(System.Double,System.Single,System.Int64,System.Int64)
  id: updateValueAndVelocity(System.Double,System.Single,System.Int64,System.Int64)
  parent: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce
  langs:
  - csharp
  - vb
  name: updateValueAndVelocity(double, float, long, long)
  nameWithType: VelocitySkiaAnimator.DragForce.updateValueAndVelocity(double, float, long, long)
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.updateValueAndVelocity(double, float, long, long)
  type: Method
  source:
    remote:
      path: src/Shared/Features/Animators/VelocitySkiaAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: updateValueAndVelocity
    path: ../src/Shared/Features/Animators/VelocitySkiaAnimator.cs
    startLine: 366
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Animate.Animators
  syntax:
    content: public VelocitySkiaAnimator.MassState updateValueAndVelocity(double value, float velocity, long deltaT, long deltaFromStart)
    parameters:
    - id: value
      type: System.Double
    - id: velocity
      type: System.Single
    - id: deltaT
      type: System.Int64
    - id: deltaFromStart
      type: System.Int64
    return:
      type: DrawnUi.Animate.Animators.VelocitySkiaAnimator.MassState
    content.vb: Public Function updateValueAndVelocity(value As Double, velocity As Single, deltaT As Long, deltaFromStart As Long) As VelocitySkiaAnimator.MassState
  overload: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.updateValueAndVelocity*
  nameWithType.vb: VelocitySkiaAnimator.DragForce.updateValueAndVelocity(Double, Single, Long, Long)
  fullName.vb: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.updateValueAndVelocity(Double, Single, Long, Long)
  name.vb: updateValueAndVelocity(Double, Single, Long, Long)
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.getAcceleration(System.Single,System.Single)
  commentId: M:DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.getAcceleration(System.Single,System.Single)
  id: getAcceleration(System.Single,System.Single)
  parent: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce
  langs:
  - csharp
  - vb
  name: getAcceleration(float, float)
  nameWithType: VelocitySkiaAnimator.DragForce.getAcceleration(float, float)
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.getAcceleration(float, float)
  type: Method
  source:
    remote:
      path: src/Shared/Features/Animators/VelocitySkiaAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: getAcceleration
    path: ../src/Shared/Features/Animators/VelocitySkiaAnimator.cs
    startLine: 381
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Animate.Animators
  syntax:
    content: public float getAcceleration(float position, float velocity)
    parameters:
    - id: position
      type: System.Single
    - id: velocity
      type: System.Single
    return:
      type: System.Single
    content.vb: Public Function getAcceleration(position As Single, velocity As Single) As Single
  overload: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.getAcceleration*
  nameWithType.vb: VelocitySkiaAnimator.DragForce.getAcceleration(Single, Single)
  fullName.vb: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.getAcceleration(Single, Single)
  name.vb: getAcceleration(Single, Single)
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.isAtEquilibrium(System.Double,System.Single)
  commentId: M:DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.isAtEquilibrium(System.Double,System.Single)
  id: isAtEquilibrium(System.Double,System.Single)
  parent: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce
  langs:
  - csharp
  - vb
  name: isAtEquilibrium(double, float)
  nameWithType: VelocitySkiaAnimator.DragForce.isAtEquilibrium(double, float)
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.isAtEquilibrium(double, float)
  type: Method
  source:
    remote:
      path: src/Shared/Features/Animators/VelocitySkiaAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: isAtEquilibrium
    path: ../src/Shared/Features/Animators/VelocitySkiaAnimator.cs
    startLine: 387
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Animate.Animators
  syntax:
    content: public bool isAtEquilibrium(double value, float velocity)
    parameters:
    - id: value
      type: System.Double
    - id: velocity
      type: System.Single
    return:
      type: System.Boolean
    content.vb: Public Function isAtEquilibrium(value As Double, velocity As Single) As Boolean
  overload: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.isAtEquilibrium*
  nameWithType.vb: VelocitySkiaAnimator.DragForce.isAtEquilibrium(Double, Single)
  fullName.vb: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.isAtEquilibrium(Double, Single)
  name.vb: isAtEquilibrium(Double, Single)
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.setValueThreshold(System.Single)
  commentId: M:DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.setValueThreshold(System.Single)
  id: setValueThreshold(System.Single)
  parent: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce
  langs:
  - csharp
  - vb
  name: setValueThreshold(float)
  nameWithType: VelocitySkiaAnimator.DragForce.setValueThreshold(float)
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.setValueThreshold(float)
  type: Method
  source:
    remote:
      path: src/Shared/Features/Animators/VelocitySkiaAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: setValueThreshold
    path: ../src/Shared/Features/Animators/VelocitySkiaAnimator.cs
    startLine: 392
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Animate.Animators
  syntax:
    content: public void setValueThreshold(float threshold)
    parameters:
    - id: threshold
      type: System.Single
    content.vb: Public Sub setValueThreshold(threshold As Single)
  overload: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.setValueThreshold*
  nameWithType.vb: VelocitySkiaAnimator.DragForce.setValueThreshold(Single)
  fullName.vb: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.setValueThreshold(Single)
  name.vb: setValueThreshold(Single)
references:
- uid: DrawnUi.Animate.Animators
  commentId: N:DrawnUi.Animate.Animators
  href: DrawnUi.html
  name: DrawnUi.Animate.Animators
  nameWithType: DrawnUi.Animate.Animators
  fullName: DrawnUi.Animate.Animators
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Animate
    name: Animate
    href: DrawnUi.Animate.html
  - name: .
  - uid: DrawnUi.Animate.Animators
    name: Animators
    href: DrawnUi.Animate.Animators.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Animate
    name: Animate
    href: DrawnUi.Animate.html
  - name: .
  - uid: DrawnUi.Animate.Animators
    name: Animators
    href: DrawnUi.Animate.Animators.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.setFrictionScalar*
  commentId: Overload:DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.setFrictionScalar
  href: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.html#DrawnUi_Animate_Animators_VelocitySkiaAnimator_DragForce_setFrictionScalar_System_Single_
  name: setFrictionScalar
  nameWithType: VelocitySkiaAnimator.DragForce.setFrictionScalar
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.setFrictionScalar
- uid: System.Single
  commentId: T:System.Single
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.single
  name: float
  nameWithType: float
  fullName: float
  nameWithType.vb: Single
  fullName.vb: Single
  name.vb: Single
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.getFrictionScalar*
  commentId: Overload:DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.getFrictionScalar
  href: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.html#DrawnUi_Animate_Animators_VelocitySkiaAnimator_DragForce_getFrictionScalar
  name: getFrictionScalar
  nameWithType: VelocitySkiaAnimator.DragForce.getFrictionScalar
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.getFrictionScalar
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.GetInitialVelocity*
  commentId: Overload:DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.GetInitialVelocity
  href: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.html#DrawnUi_Animate_Animators_VelocitySkiaAnimator_DragForce_GetInitialVelocity_System_Single_System_Single_System_Single_
  name: GetInitialVelocity
  nameWithType: VelocitySkiaAnimator.DragForce.GetInitialVelocity
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.GetInitialVelocity
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.updateValueAndVelocity*
  commentId: Overload:DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.updateValueAndVelocity
  href: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.html#DrawnUi_Animate_Animators_VelocitySkiaAnimator_DragForce_updateValueAndVelocity_System_Double_System_Single_System_Int64_System_Int64_
  name: updateValueAndVelocity
  nameWithType: VelocitySkiaAnimator.DragForce.updateValueAndVelocity
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.updateValueAndVelocity
- uid: System.Double
  commentId: T:System.Double
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.double
  name: double
  nameWithType: double
  fullName: double
  nameWithType.vb: Double
  fullName.vb: Double
  name.vb: Double
- uid: System.Int64
  commentId: T:System.Int64
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.int64
  name: long
  nameWithType: long
  fullName: long
  nameWithType.vb: Long
  fullName.vb: Long
  name.vb: Long
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.MassState
  commentId: T:DrawnUi.Animate.Animators.VelocitySkiaAnimator.MassState
  parent: DrawnUi.Animate.Animators
  href: DrawnUi.Animate.Animators.VelocitySkiaAnimator.html
  name: VelocitySkiaAnimator.MassState
  nameWithType: VelocitySkiaAnimator.MassState
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.MassState
  spec.csharp:
  - uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator
    name: VelocitySkiaAnimator
    href: DrawnUi.Animate.Animators.VelocitySkiaAnimator.html
  - name: .
  - uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.MassState
    name: MassState
    href: DrawnUi.Animate.Animators.VelocitySkiaAnimator.MassState.html
  spec.vb:
  - uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator
    name: VelocitySkiaAnimator
    href: DrawnUi.Animate.Animators.VelocitySkiaAnimator.html
  - name: .
  - uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.MassState
    name: MassState
    href: DrawnUi.Animate.Animators.VelocitySkiaAnimator.MassState.html
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.getAcceleration*
  commentId: Overload:DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.getAcceleration
  href: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.html#DrawnUi_Animate_Animators_VelocitySkiaAnimator_DragForce_getAcceleration_System_Single_System_Single_
  name: getAcceleration
  nameWithType: VelocitySkiaAnimator.DragForce.getAcceleration
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.getAcceleration
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.isAtEquilibrium*
  commentId: Overload:DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.isAtEquilibrium
  href: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.html#DrawnUi_Animate_Animators_VelocitySkiaAnimator_DragForce_isAtEquilibrium_System_Double_System_Single_
  name: isAtEquilibrium
  nameWithType: VelocitySkiaAnimator.DragForce.isAtEquilibrium
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.isAtEquilibrium
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.setValueThreshold*
  commentId: Overload:DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.setValueThreshold
  href: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.html#DrawnUi_Animate_Animators_VelocitySkiaAnimator_DragForce_setValueThreshold_System_Single_
  name: setValueThreshold
  nameWithType: VelocitySkiaAnimator.DragForce.setValueThreshold
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.setValueThreshold
