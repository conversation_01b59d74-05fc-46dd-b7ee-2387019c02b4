### YamlMime:ManagedReference
items:
- uid: DrawnUi.Models.RestartingTimer`1
  commentId: T:DrawnUi.Models.RestartingTimer`1
  id: RestartingTimer`1
  parent: DrawnUi.Models
  children:
  - DrawnUi.Models.RestartingTimer`1.#ctor(System.TimeSpan,System.Action{`0})
  - DrawnUi.Models.RestartingTimer`1.#ctor(System.UInt32,System.Action{`0})
  - DrawnUi.Models.RestartingTimer`1.Context
  - DrawnUi.Models.RestartingTimer`1.Dispose
  - DrawnUi.Models.RestartingTimer`1.IsRunning
  - DrawnUi.Models.RestartingTimer`1.Kick(`0)
  - DrawnUi.Models.RestartingTimer`1.Restart(`0)
  - DrawnUi.Models.RestartingTimer`1.Start(`0)
  - DrawnUi.Models.RestartingTimer`1.Stop
  - DrawnUi.Models.RestartingTimer`1.disposed
  langs:
  - csharp
  - vb
  name: RestartingTimer<T>
  nameWithType: RestartingTimer<T>
  fullName: DrawnUi.Models.RestartingTimer<T>
  type: Class
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/RestartingTimer.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: RestartingTimer
    path: ../src/Shared/Draw/Internals/Helpers/RestartingTimer.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Models
  syntax:
    content: public class RestartingTimer<T>
    typeParameters:
    - id: T
    content.vb: Public Class RestartingTimer(Of T)
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  nameWithType.vb: RestartingTimer(Of T)
  fullName.vb: DrawnUi.Models.RestartingTimer(Of T)
  name.vb: RestartingTimer(Of T)
- uid: DrawnUi.Models.RestartingTimer`1.IsRunning
  commentId: P:DrawnUi.Models.RestartingTimer`1.IsRunning
  id: IsRunning
  parent: DrawnUi.Models.RestartingTimer`1
  langs:
  - csharp
  - vb
  name: IsRunning
  nameWithType: RestartingTimer<T>.IsRunning
  fullName: DrawnUi.Models.RestartingTimer<T>.IsRunning
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/RestartingTimer.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IsRunning
    path: ../src/Shared/Draw/Internals/Helpers/RestartingTimer.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Models
  syntax:
    content: public bool IsRunning { get; protected set; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property IsRunning As Boolean
  overload: DrawnUi.Models.RestartingTimer`1.IsRunning*
  nameWithType.vb: RestartingTimer(Of T).IsRunning
  fullName.vb: DrawnUi.Models.RestartingTimer(Of T).IsRunning
- uid: DrawnUi.Models.RestartingTimer`1.Context
  commentId: P:DrawnUi.Models.RestartingTimer`1.Context
  id: Context
  parent: DrawnUi.Models.RestartingTimer`1
  langs:
  - csharp
  - vb
  name: Context
  nameWithType: RestartingTimer<T>.Context
  fullName: DrawnUi.Models.RestartingTimer<T>.Context
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/RestartingTimer.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Context
    path: ../src/Shared/Draw/Internals/Helpers/RestartingTimer.cs
    startLine: 6
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Models
  syntax:
    content: public T Context { get; protected set; }
    parameters: []
    return:
      type: '{T}'
    content.vb: Public Property Context As T
  overload: DrawnUi.Models.RestartingTimer`1.Context*
  nameWithType.vb: RestartingTimer(Of T).Context
  fullName.vb: DrawnUi.Models.RestartingTimer(Of T).Context
- uid: DrawnUi.Models.RestartingTimer`1.Kick(`0)
  commentId: M:DrawnUi.Models.RestartingTimer`1.Kick(`0)
  id: Kick(`0)
  parent: DrawnUi.Models.RestartingTimer`1
  langs:
  - csharp
  - vb
  name: Kick(T)
  nameWithType: RestartingTimer<T>.Kick(T)
  fullName: DrawnUi.Models.RestartingTimer<T>.Kick(T)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/RestartingTimer.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Kick
    path: ../src/Shared/Draw/Internals/Helpers/RestartingTimer.cs
    startLine: 7
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Models
  syntax:
    content: public void Kick(T param)
    parameters:
    - id: param
      type: '{T}'
    content.vb: Public Sub Kick(param As T)
  overload: DrawnUi.Models.RestartingTimer`1.Kick*
  nameWithType.vb: RestartingTimer(Of T).Kick(T)
  fullName.vb: DrawnUi.Models.RestartingTimer(Of T).Kick(T)
- uid: DrawnUi.Models.RestartingTimer`1.#ctor(System.UInt32,System.Action{`0})
  commentId: M:DrawnUi.Models.RestartingTimer`1.#ctor(System.UInt32,System.Action{`0})
  id: '#ctor(System.UInt32,System.Action{`0})'
  parent: DrawnUi.Models.RestartingTimer`1
  langs:
  - csharp
  - vb
  name: RestartingTimer(uint, Action<T>)
  nameWithType: RestartingTimer<T>.RestartingTimer(uint, Action<T>)
  fullName: DrawnUi.Models.RestartingTimer<T>.RestartingTimer(uint, System.Action<T>)
  type: Constructor
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/RestartingTimer.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Shared/Draw/Internals/Helpers/RestartingTimer.cs
    startLine: 25
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Models
  syntax:
    content: public RestartingTimer(uint ms, Action<T> callback)
    parameters:
    - id: ms
      type: System.UInt32
    - id: callback
      type: System.Action{{T}}
    content.vb: Public Sub New(ms As UInteger, callback As Action(Of T))
  overload: DrawnUi.Models.RestartingTimer`1.#ctor*
  nameWithType.vb: RestartingTimer(Of T).New(UInteger, Action(Of T))
  fullName.vb: DrawnUi.Models.RestartingTimer(Of T).New(UInteger, System.Action(Of T))
  name.vb: New(UInteger, Action(Of T))
- uid: DrawnUi.Models.RestartingTimer`1.#ctor(System.TimeSpan,System.Action{`0})
  commentId: M:DrawnUi.Models.RestartingTimer`1.#ctor(System.TimeSpan,System.Action{`0})
  id: '#ctor(System.TimeSpan,System.Action{`0})'
  parent: DrawnUi.Models.RestartingTimer`1
  langs:
  - csharp
  - vb
  name: RestartingTimer(TimeSpan, Action<T>)
  nameWithType: RestartingTimer<T>.RestartingTimer(TimeSpan, Action<T>)
  fullName: DrawnUi.Models.RestartingTimer<T>.RestartingTimer(System.TimeSpan, System.Action<T>)
  type: Constructor
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/RestartingTimer.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Shared/Draw/Internals/Helpers/RestartingTimer.cs
    startLine: 31
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Models
  syntax:
    content: public RestartingTimer(TimeSpan timespan, Action<T> callback)
    parameters:
    - id: timespan
      type: System.TimeSpan
    - id: callback
      type: System.Action{{T}}
    content.vb: Public Sub New(timespan As TimeSpan, callback As Action(Of T))
  overload: DrawnUi.Models.RestartingTimer`1.#ctor*
  nameWithType.vb: RestartingTimer(Of T).New(TimeSpan, Action(Of T))
  fullName.vb: DrawnUi.Models.RestartingTimer(Of T).New(System.TimeSpan, System.Action(Of T))
  name.vb: New(TimeSpan, Action(Of T))
- uid: DrawnUi.Models.RestartingTimer`1.Restart(`0)
  commentId: M:DrawnUi.Models.RestartingTimer`1.Restart(`0)
  id: Restart(`0)
  parent: DrawnUi.Models.RestartingTimer`1
  langs:
  - csharp
  - vb
  name: Restart(T)
  nameWithType: RestartingTimer<T>.Restart(T)
  fullName: DrawnUi.Models.RestartingTimer<T>.Restart(T)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/RestartingTimer.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Restart
    path: ../src/Shared/Draw/Internals/Helpers/RestartingTimer.cs
    startLine: 40
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Models
  syntax:
    content: public void Restart(T param)
    parameters:
    - id: param
      type: '{T}'
    content.vb: Public Sub Restart(param As T)
  overload: DrawnUi.Models.RestartingTimer`1.Restart*
  nameWithType.vb: RestartingTimer(Of T).Restart(T)
  fullName.vb: DrawnUi.Models.RestartingTimer(Of T).Restart(T)
- uid: DrawnUi.Models.RestartingTimer`1.Start(`0)
  commentId: M:DrawnUi.Models.RestartingTimer`1.Start(`0)
  id: Start(`0)
  parent: DrawnUi.Models.RestartingTimer`1
  langs:
  - csharp
  - vb
  name: Start(T)
  nameWithType: RestartingTimer<T>.Start(T)
  fullName: DrawnUi.Models.RestartingTimer<T>.Start(T)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/RestartingTimer.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Start
    path: ../src/Shared/Draw/Internals/Helpers/RestartingTimer.cs
    startLine: 46
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Models
  syntax:
    content: public void Start(T param)
    parameters:
    - id: param
      type: '{T}'
    content.vb: Public Sub Start(param As T)
  overload: DrawnUi.Models.RestartingTimer`1.Start*
  nameWithType.vb: RestartingTimer(Of T).Start(T)
  fullName.vb: DrawnUi.Models.RestartingTimer(Of T).Start(T)
- uid: DrawnUi.Models.RestartingTimer`1.Stop
  commentId: M:DrawnUi.Models.RestartingTimer`1.Stop
  id: Stop
  parent: DrawnUi.Models.RestartingTimer`1
  langs:
  - csharp
  - vb
  name: Stop()
  nameWithType: RestartingTimer<T>.Stop()
  fullName: DrawnUi.Models.RestartingTimer<T>.Stop()
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/RestartingTimer.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Stop
    path: ../src/Shared/Draw/Internals/Helpers/RestartingTimer.cs
    startLine: 62
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Models
  syntax:
    content: public void Stop()
    content.vb: Public Sub [Stop]()
  overload: DrawnUi.Models.RestartingTimer`1.Stop*
  nameWithType.vb: RestartingTimer(Of T).Stop()
  fullName.vb: DrawnUi.Models.RestartingTimer(Of T).Stop()
- uid: DrawnUi.Models.RestartingTimer`1.disposed
  commentId: F:DrawnUi.Models.RestartingTimer`1.disposed
  id: disposed
  parent: DrawnUi.Models.RestartingTimer`1
  langs:
  - csharp
  - vb
  name: disposed
  nameWithType: RestartingTimer<T>.disposed
  fullName: DrawnUi.Models.RestartingTimer<T>.disposed
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/RestartingTimer.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: disposed
    path: ../src/Shared/Draw/Internals/Helpers/RestartingTimer.cs
    startLine: 67
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Models
  syntax:
    content: protected bool disposed
    return:
      type: System.Boolean
    content.vb: Protected disposed As Boolean
  nameWithType.vb: RestartingTimer(Of T).disposed
  fullName.vb: DrawnUi.Models.RestartingTimer(Of T).disposed
- uid: DrawnUi.Models.RestartingTimer`1.Dispose
  commentId: M:DrawnUi.Models.RestartingTimer`1.Dispose
  id: Dispose
  parent: DrawnUi.Models.RestartingTimer`1
  langs:
  - csharp
  - vb
  name: Dispose()
  nameWithType: RestartingTimer<T>.Dispose()
  fullName: DrawnUi.Models.RestartingTimer<T>.Dispose()
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/RestartingTimer.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Dispose
    path: ../src/Shared/Draw/Internals/Helpers/RestartingTimer.cs
    startLine: 68
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Models
  syntax:
    content: public void Dispose()
    content.vb: Public Sub Dispose()
  overload: DrawnUi.Models.RestartingTimer`1.Dispose*
  nameWithType.vb: RestartingTimer(Of T).Dispose()
  fullName.vb: DrawnUi.Models.RestartingTimer(Of T).Dispose()
references:
- uid: DrawnUi.Models
  commentId: N:DrawnUi.Models
  href: DrawnUi.html
  name: DrawnUi.Models
  nameWithType: DrawnUi.Models
  fullName: DrawnUi.Models
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Models
    name: Models
    href: DrawnUi.Models.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Models
    name: Models
    href: DrawnUi.Models.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Models.RestartingTimer`1.IsRunning*
  commentId: Overload:DrawnUi.Models.RestartingTimer`1.IsRunning
  href: DrawnUi.Models.RestartingTimer-1.html#DrawnUi_Models_RestartingTimer_1_IsRunning
  name: IsRunning
  nameWithType: RestartingTimer<T>.IsRunning
  fullName: DrawnUi.Models.RestartingTimer<T>.IsRunning
  nameWithType.vb: RestartingTimer(Of T).IsRunning
  fullName.vb: DrawnUi.Models.RestartingTimer(Of T).IsRunning
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: DrawnUi.Models.RestartingTimer`1.Context*
  commentId: Overload:DrawnUi.Models.RestartingTimer`1.Context
  href: DrawnUi.Models.RestartingTimer-1.html#DrawnUi_Models_RestartingTimer_1_Context
  name: Context
  nameWithType: RestartingTimer<T>.Context
  fullName: DrawnUi.Models.RestartingTimer<T>.Context
  nameWithType.vb: RestartingTimer(Of T).Context
  fullName.vb: DrawnUi.Models.RestartingTimer(Of T).Context
- uid: '{T}'
  commentId: '!:T'
  definition: T
  name: T
  nameWithType: T
  fullName: T
- uid: T
  name: T
  nameWithType: T
  fullName: T
- uid: DrawnUi.Models.RestartingTimer`1.Kick*
  commentId: Overload:DrawnUi.Models.RestartingTimer`1.Kick
  href: DrawnUi.Models.RestartingTimer-1.html#DrawnUi_Models_RestartingTimer_1_Kick__0_
  name: Kick
  nameWithType: RestartingTimer<T>.Kick
  fullName: DrawnUi.Models.RestartingTimer<T>.Kick
  nameWithType.vb: RestartingTimer(Of T).Kick
  fullName.vb: DrawnUi.Models.RestartingTimer(Of T).Kick
- uid: DrawnUi.Models.RestartingTimer`1.#ctor*
  commentId: Overload:DrawnUi.Models.RestartingTimer`1.#ctor
  href: DrawnUi.Models.RestartingTimer-1.html#DrawnUi_Models_RestartingTimer_1__ctor_System_UInt32_System_Action__0__
  name: RestartingTimer
  nameWithType: RestartingTimer<T>.RestartingTimer
  fullName: DrawnUi.Models.RestartingTimer<T>.RestartingTimer
  nameWithType.vb: RestartingTimer(Of T).New
  fullName.vb: DrawnUi.Models.RestartingTimer(Of T).New
  name.vb: New
- uid: System.UInt32
  commentId: T:System.UInt32
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.uint32
  name: uint
  nameWithType: uint
  fullName: uint
  nameWithType.vb: UInteger
  fullName.vb: UInteger
  name.vb: UInteger
- uid: System.Action{{T}}
  commentId: T:System.Action{``0}
  parent: System
  definition: System.Action`1
  href: https://learn.microsoft.com/dotnet/api/system.action-1
  name: Action<T>
  nameWithType: Action<T>
  fullName: System.Action<T>
  nameWithType.vb: Action(Of T)
  fullName.vb: System.Action(Of T)
  name.vb: Action(Of T)
  spec.csharp:
  - uid: System.Action`1
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Action`1
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: System.Action`1
  commentId: T:System.Action`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.action-1
  name: Action<T>
  nameWithType: Action<T>
  fullName: System.Action<T>
  nameWithType.vb: Action(Of T)
  fullName.vb: System.Action(Of T)
  name.vb: Action(Of T)
  spec.csharp:
  - uid: System.Action`1
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Action`1
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: System.TimeSpan
  commentId: T:System.TimeSpan
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.timespan
  name: TimeSpan
  nameWithType: TimeSpan
  fullName: System.TimeSpan
- uid: DrawnUi.Models.RestartingTimer`1.Restart*
  commentId: Overload:DrawnUi.Models.RestartingTimer`1.Restart
  href: DrawnUi.Models.RestartingTimer-1.html#DrawnUi_Models_RestartingTimer_1_Restart__0_
  name: Restart
  nameWithType: RestartingTimer<T>.Restart
  fullName: DrawnUi.Models.RestartingTimer<T>.Restart
  nameWithType.vb: RestartingTimer(Of T).Restart
  fullName.vb: DrawnUi.Models.RestartingTimer(Of T).Restart
- uid: DrawnUi.Models.RestartingTimer`1.Start*
  commentId: Overload:DrawnUi.Models.RestartingTimer`1.Start
  href: DrawnUi.Models.RestartingTimer-1.html#DrawnUi_Models_RestartingTimer_1_Start__0_
  name: Start
  nameWithType: RestartingTimer<T>.Start
  fullName: DrawnUi.Models.RestartingTimer<T>.Start
  nameWithType.vb: RestartingTimer(Of T).Start
  fullName.vb: DrawnUi.Models.RestartingTimer(Of T).Start
- uid: DrawnUi.Models.RestartingTimer`1.Stop*
  commentId: Overload:DrawnUi.Models.RestartingTimer`1.Stop
  href: DrawnUi.Models.RestartingTimer-1.html#DrawnUi_Models_RestartingTimer_1_Stop
  name: Stop
  nameWithType: RestartingTimer<T>.Stop
  fullName: DrawnUi.Models.RestartingTimer<T>.Stop
  nameWithType.vb: RestartingTimer(Of T).Stop
  fullName.vb: DrawnUi.Models.RestartingTimer(Of T).Stop
- uid: DrawnUi.Models.RestartingTimer`1.Dispose*
  commentId: Overload:DrawnUi.Models.RestartingTimer`1.Dispose
  href: DrawnUi.Models.RestartingTimer-1.html#DrawnUi_Models_RestartingTimer_1_Dispose
  name: Dispose
  nameWithType: RestartingTimer<T>.Dispose
  fullName: DrawnUi.Models.RestartingTimer<T>.Dispose
  nameWithType.vb: RestartingTimer(Of T).Dispose
  fullName.vb: DrawnUi.Models.RestartingTimer(Of T).Dispose
