### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.ContainsPointResult
  commentId: T:DrawnUi.Draw.ContainsPointResult
  id: ContainsPointResult
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.ContainsPointResult.Index
  - DrawnUi.Draw.ContainsPointResult.NotFound
  - DrawnUi.Draw.ContainsPointResult.Unmodified
  langs:
  - csharp
  - vb
  name: ContainsPointResult
  nameWithType: ContainsPointResult
  fullName: DrawnUi.Draw.ContainsPointResult
  type: Class
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ContainsPointResult.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ContainsPointResult
    path: ../src/Shared/Draw/Internals/Models/ContainsPointResult.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: 'public class ContainsPointResult : PointIsInsideResult'
    content.vb: Public Class ContainsPointResult Inherits PointIsInsideResult
  inheritance:
  - System.Object
  - DrawnUi.Draw.PointIsInsideResult
  inheritedMembers:
  - DrawnUi.Draw.PointIsInsideResult.IsInside
  - DrawnUi.Draw.PointIsInsideResult.Point
  - DrawnUi.Draw.PointIsInsideResult.Area
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.ContainsPointResult.Index
  commentId: P:DrawnUi.Draw.ContainsPointResult.Index
  id: Index
  parent: DrawnUi.Draw.ContainsPointResult
  langs:
  - csharp
  - vb
  name: Index
  nameWithType: ContainsPointResult.Index
  fullName: DrawnUi.Draw.ContainsPointResult.Index
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ContainsPointResult.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Index
    path: ../src/Shared/Draw/Internals/Models/ContainsPointResult.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public int Index { get; set; }
    parameters: []
    return:
      type: System.Int32
    content.vb: Public Property Index As Integer
  overload: DrawnUi.Draw.ContainsPointResult.Index*
- uid: DrawnUi.Draw.ContainsPointResult.Unmodified
  commentId: P:DrawnUi.Draw.ContainsPointResult.Unmodified
  id: Unmodified
  parent: DrawnUi.Draw.ContainsPointResult
  langs:
  - csharp
  - vb
  name: Unmodified
  nameWithType: ContainsPointResult.Unmodified
  fullName: DrawnUi.Draw.ContainsPointResult.Unmodified
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ContainsPointResult.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Unmodified
    path: ../src/Shared/Draw/Internals/Models/ContainsPointResult.cs
    startLine: 7
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public SKPoint Unmodified { get; set; }
    parameters: []
    return:
      type: SkiaSharp.SKPoint
    content.vb: Public Property Unmodified As SKPoint
  overload: DrawnUi.Draw.ContainsPointResult.Unmodified*
- uid: DrawnUi.Draw.ContainsPointResult.NotFound
  commentId: M:DrawnUi.Draw.ContainsPointResult.NotFound
  id: NotFound
  parent: DrawnUi.Draw.ContainsPointResult
  langs:
  - csharp
  - vb
  name: NotFound()
  nameWithType: ContainsPointResult.NotFound()
  fullName: DrawnUi.Draw.ContainsPointResult.NotFound()
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ContainsPointResult.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: NotFound
    path: ../src/Shared/Draw/Internals/Models/ContainsPointResult.cs
    startLine: 9
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static ContainsPointResult NotFound()
    return:
      type: DrawnUi.Draw.ContainsPointResult
    content.vb: Public Shared Function NotFound() As ContainsPointResult
  overload: DrawnUi.Draw.ContainsPointResult.NotFound*
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: DrawnUi.Draw.PointIsInsideResult
  commentId: T:DrawnUi.Draw.PointIsInsideResult
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.PointIsInsideResult.html
  name: PointIsInsideResult
  nameWithType: PointIsInsideResult
  fullName: DrawnUi.Draw.PointIsInsideResult
- uid: DrawnUi.Draw.PointIsInsideResult.IsInside
  commentId: P:DrawnUi.Draw.PointIsInsideResult.IsInside
  parent: DrawnUi.Draw.PointIsInsideResult
  href: DrawnUi.Draw.PointIsInsideResult.html#DrawnUi_Draw_PointIsInsideResult_IsInside
  name: IsInside
  nameWithType: PointIsInsideResult.IsInside
  fullName: DrawnUi.Draw.PointIsInsideResult.IsInside
- uid: DrawnUi.Draw.PointIsInsideResult.Point
  commentId: P:DrawnUi.Draw.PointIsInsideResult.Point
  parent: DrawnUi.Draw.PointIsInsideResult
  href: DrawnUi.Draw.PointIsInsideResult.html#DrawnUi_Draw_PointIsInsideResult_Point
  name: Point
  nameWithType: PointIsInsideResult.Point
  fullName: DrawnUi.Draw.PointIsInsideResult.Point
- uid: DrawnUi.Draw.PointIsInsideResult.Area
  commentId: P:DrawnUi.Draw.PointIsInsideResult.Area
  parent: DrawnUi.Draw.PointIsInsideResult
  href: DrawnUi.Draw.PointIsInsideResult.html#DrawnUi_Draw_PointIsInsideResult_Area
  name: Area
  nameWithType: PointIsInsideResult.Area
  fullName: DrawnUi.Draw.PointIsInsideResult.Area
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.ContainsPointResult.Index*
  commentId: Overload:DrawnUi.Draw.ContainsPointResult.Index
  href: DrawnUi.Draw.ContainsPointResult.html#DrawnUi_Draw_ContainsPointResult_Index
  name: Index
  nameWithType: ContainsPointResult.Index
  fullName: DrawnUi.Draw.ContainsPointResult.Index
- uid: System.Int32
  commentId: T:System.Int32
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.int32
  name: int
  nameWithType: int
  fullName: int
  nameWithType.vb: Integer
  fullName.vb: Integer
  name.vb: Integer
- uid: DrawnUi.Draw.ContainsPointResult.Unmodified*
  commentId: Overload:DrawnUi.Draw.ContainsPointResult.Unmodified
  href: DrawnUi.Draw.ContainsPointResult.html#DrawnUi_Draw_ContainsPointResult_Unmodified
  name: Unmodified
  nameWithType: ContainsPointResult.Unmodified
  fullName: DrawnUi.Draw.ContainsPointResult.Unmodified
- uid: SkiaSharp.SKPoint
  commentId: T:SkiaSharp.SKPoint
  parent: SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.skpoint
  name: SKPoint
  nameWithType: SKPoint
  fullName: SkiaSharp.SKPoint
- uid: SkiaSharp
  commentId: N:SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp
  name: SkiaSharp
  nameWithType: SkiaSharp
  fullName: SkiaSharp
- uid: DrawnUi.Draw.ContainsPointResult.NotFound*
  commentId: Overload:DrawnUi.Draw.ContainsPointResult.NotFound
  href: DrawnUi.Draw.ContainsPointResult.html#DrawnUi_Draw_ContainsPointResult_NotFound
  name: NotFound
  nameWithType: ContainsPointResult.NotFound
  fullName: DrawnUi.Draw.ContainsPointResult.NotFound
- uid: DrawnUi.Draw.ContainsPointResult
  commentId: T:DrawnUi.Draw.ContainsPointResult
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ContainsPointResult.html
  name: ContainsPointResult
  nameWithType: ContainsPointResult
  fullName: DrawnUi.Draw.ContainsPointResult
