### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.UnderdampedSpringTimingParameters
  commentId: T:DrawnUi.Draw.UnderdampedSpringTimingParameters
  id: UnderdampedSpringTimingParameters
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.UnderdampedSpringTimingParameters.#ctor(DrawnUi.Infrastructure.Spring,System.Single,System.Single,System.Single)
  - DrawnUi.Draw.UnderdampedSpringTimingParameters.AmplitudeAt(System.Single)
  - DrawnUi.Draw.UnderdampedSpringTimingParameters.DurationSecs
  - DrawnUi.Draw.UnderdampedSpringTimingParameters.ValueAt(System.Single)
  langs:
  - csharp
  - vb
  name: UnderdampedSpringTimingParameters
  nameWithType: UnderdampedSpringTimingParameters
  fullName: DrawnUi.Draw.UnderdampedSpringTimingParameters
  type: Class
  source:
    remote:
      path: src/Shared/Features/Animations/Parameters/SpringTimingParameters.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: UnderdampedSpringTimingParameters
    path: ../src/Shared/Features/Animations/Parameters/SpringTimingParameters.cs
    startLine: 112
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: 'public class UnderdampedSpringTimingParameters : IDampingTimingParameters, ITimingParameters'
    content.vb: Public Class UnderdampedSpringTimingParameters Implements IDampingTimingParameters, ITimingParameters
  inheritance:
  - System.Object
  implements:
  - DrawnUi.Draw.IDampingTimingParameters
  - DrawnUi.Draw.ITimingParameters
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.UnderdampedSpringTimingParameters.#ctor(DrawnUi.Infrastructure.Spring,System.Single,System.Single,System.Single)
  commentId: M:DrawnUi.Draw.UnderdampedSpringTimingParameters.#ctor(DrawnUi.Infrastructure.Spring,System.Single,System.Single,System.Single)
  id: '#ctor(DrawnUi.Infrastructure.Spring,System.Single,System.Single,System.Single)'
  parent: DrawnUi.Draw.UnderdampedSpringTimingParameters
  langs:
  - csharp
  - vb
  name: UnderdampedSpringTimingParameters(Spring, float, float, float)
  nameWithType: UnderdampedSpringTimingParameters.UnderdampedSpringTimingParameters(Spring, float, float, float)
  fullName: DrawnUi.Draw.UnderdampedSpringTimingParameters.UnderdampedSpringTimingParameters(DrawnUi.Infrastructure.Spring, float, float, float)
  type: Constructor
  source:
    remote:
      path: src/Shared/Features/Animations/Parameters/SpringTimingParameters.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Shared/Features/Animations/Parameters/SpringTimingParameters.cs
    startLine: 119
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public UnderdampedSpringTimingParameters(Spring spring, float displacement, float initialVelocity, float threshold)
    parameters:
    - id: spring
      type: DrawnUi.Infrastructure.Spring
    - id: displacement
      type: System.Single
    - id: initialVelocity
      type: System.Single
    - id: threshold
      type: System.Single
    content.vb: Public Sub New(spring As Spring, displacement As Single, initialVelocity As Single, threshold As Single)
  overload: DrawnUi.Draw.UnderdampedSpringTimingParameters.#ctor*
  nameWithType.vb: UnderdampedSpringTimingParameters.New(Spring, Single, Single, Single)
  fullName.vb: DrawnUi.Draw.UnderdampedSpringTimingParameters.New(DrawnUi.Infrastructure.Spring, Single, Single, Single)
  name.vb: New(Spring, Single, Single, Single)
- uid: DrawnUi.Draw.UnderdampedSpringTimingParameters.DurationSecs
  commentId: P:DrawnUi.Draw.UnderdampedSpringTimingParameters.DurationSecs
  id: DurationSecs
  parent: DrawnUi.Draw.UnderdampedSpringTimingParameters
  langs:
  - csharp
  - vb
  name: DurationSecs
  nameWithType: UnderdampedSpringTimingParameters.DurationSecs
  fullName: DrawnUi.Draw.UnderdampedSpringTimingParameters.DurationSecs
  type: Property
  source:
    remote:
      path: src/Shared/Features/Animations/Parameters/SpringTimingParameters.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: DurationSecs
    path: ../src/Shared/Features/Animations/Parameters/SpringTimingParameters.cs
    startLine: 127
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  example: []
  syntax:
    content: public float DurationSecs { get; }
    parameters: []
    return:
      type: System.Single
    content.vb: Public ReadOnly Property DurationSecs As Single
  overload: DrawnUi.Draw.UnderdampedSpringTimingParameters.DurationSecs*
  implements:
  - DrawnUi.Draw.ITimingParameters.DurationSecs
- uid: DrawnUi.Draw.UnderdampedSpringTimingParameters.ValueAt(System.Single)
  commentId: M:DrawnUi.Draw.UnderdampedSpringTimingParameters.ValueAt(System.Single)
  id: ValueAt(System.Single)
  parent: DrawnUi.Draw.UnderdampedSpringTimingParameters
  langs:
  - csharp
  - vb
  name: ValueAt(float)
  nameWithType: UnderdampedSpringTimingParameters.ValueAt(float)
  fullName: DrawnUi.Draw.UnderdampedSpringTimingParameters.ValueAt(float)
  type: Method
  source:
    remote:
      path: src/Shared/Features/Animations/Parameters/SpringTimingParameters.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ValueAt
    path: ../src/Shared/Features/Animations/Parameters/SpringTimingParameters.cs
    startLine: 140
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  example: []
  syntax:
    content: public float ValueAt(float offsetSecs)
    parameters:
    - id: offsetSecs
      type: System.Single
    return:
      type: System.Single
    content.vb: Public Function ValueAt(offsetSecs As Single) As Single
  overload: DrawnUi.Draw.UnderdampedSpringTimingParameters.ValueAt*
  implements:
  - DrawnUi.Draw.ITimingParameters.ValueAt(System.Single)
  nameWithType.vb: UnderdampedSpringTimingParameters.ValueAt(Single)
  fullName.vb: DrawnUi.Draw.UnderdampedSpringTimingParameters.ValueAt(Single)
  name.vb: ValueAt(Single)
- uid: DrawnUi.Draw.UnderdampedSpringTimingParameters.AmplitudeAt(System.Single)
  commentId: M:DrawnUi.Draw.UnderdampedSpringTimingParameters.AmplitudeAt(System.Single)
  id: AmplitudeAt(System.Single)
  parent: DrawnUi.Draw.UnderdampedSpringTimingParameters
  langs:
  - csharp
  - vb
  name: AmplitudeAt(float)
  nameWithType: UnderdampedSpringTimingParameters.AmplitudeAt(float)
  fullName: DrawnUi.Draw.UnderdampedSpringTimingParameters.AmplitudeAt(float)
  type: Method
  source:
    remote:
      path: src/Shared/Features/Animations/Parameters/SpringTimingParameters.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: AmplitudeAt
    path: ../src/Shared/Features/Animations/Parameters/SpringTimingParameters.cs
    startLine: 148
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  example: []
  syntax:
    content: public float AmplitudeAt(float offsetSecs)
    parameters:
    - id: offsetSecs
      type: System.Single
    return:
      type: System.Single
    content.vb: Public Function AmplitudeAt(offsetSecs As Single) As Single
  overload: DrawnUi.Draw.UnderdampedSpringTimingParameters.AmplitudeAt*
  implements:
  - DrawnUi.Draw.IDampingTimingParameters.AmplitudeAt(System.Single)
  nameWithType.vb: UnderdampedSpringTimingParameters.AmplitudeAt(Single)
  fullName.vb: DrawnUi.Draw.UnderdampedSpringTimingParameters.AmplitudeAt(Single)
  name.vb: AmplitudeAt(Single)
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: DrawnUi.Draw.IDampingTimingParameters
  commentId: T:DrawnUi.Draw.IDampingTimingParameters
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.IDampingTimingParameters.html
  name: IDampingTimingParameters
  nameWithType: IDampingTimingParameters
  fullName: DrawnUi.Draw.IDampingTimingParameters
- uid: DrawnUi.Draw.ITimingParameters
  commentId: T:DrawnUi.Draw.ITimingParameters
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ITimingParameters.html
  name: ITimingParameters
  nameWithType: ITimingParameters
  fullName: DrawnUi.Draw.ITimingParameters
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.UnderdampedSpringTimingParameters.#ctor*
  commentId: Overload:DrawnUi.Draw.UnderdampedSpringTimingParameters.#ctor
  href: DrawnUi.Draw.UnderdampedSpringTimingParameters.html#DrawnUi_Draw_UnderdampedSpringTimingParameters__ctor_DrawnUi_Infrastructure_Spring_System_Single_System_Single_System_Single_
  name: UnderdampedSpringTimingParameters
  nameWithType: UnderdampedSpringTimingParameters.UnderdampedSpringTimingParameters
  fullName: DrawnUi.Draw.UnderdampedSpringTimingParameters.UnderdampedSpringTimingParameters
  nameWithType.vb: UnderdampedSpringTimingParameters.New
  fullName.vb: DrawnUi.Draw.UnderdampedSpringTimingParameters.New
  name.vb: New
- uid: DrawnUi.Infrastructure.Spring
  commentId: T:DrawnUi.Infrastructure.Spring
  parent: DrawnUi.Infrastructure
  href: DrawnUi.Infrastructure.Spring.html
  name: Spring
  nameWithType: Spring
  fullName: DrawnUi.Infrastructure.Spring
- uid: System.Single
  commentId: T:System.Single
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.single
  name: float
  nameWithType: float
  fullName: float
  nameWithType.vb: Single
  fullName.vb: Single
  name.vb: Single
- uid: DrawnUi.Infrastructure
  commentId: N:DrawnUi.Infrastructure
  href: DrawnUi.html
  name: DrawnUi.Infrastructure
  nameWithType: DrawnUi.Infrastructure
  fullName: DrawnUi.Infrastructure
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Infrastructure
    name: Infrastructure
    href: DrawnUi.Infrastructure.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Infrastructure
    name: Infrastructure
    href: DrawnUi.Infrastructure.html
- uid: DrawnUi.Draw.UnderdampedSpringTimingParameters.DurationSecs*
  commentId: Overload:DrawnUi.Draw.UnderdampedSpringTimingParameters.DurationSecs
  href: DrawnUi.Draw.UnderdampedSpringTimingParameters.html#DrawnUi_Draw_UnderdampedSpringTimingParameters_DurationSecs
  name: DurationSecs
  nameWithType: UnderdampedSpringTimingParameters.DurationSecs
  fullName: DrawnUi.Draw.UnderdampedSpringTimingParameters.DurationSecs
- uid: DrawnUi.Draw.ITimingParameters.DurationSecs
  commentId: P:DrawnUi.Draw.ITimingParameters.DurationSecs
  parent: DrawnUi.Draw.ITimingParameters
  href: DrawnUi.Draw.ITimingParameters.html#DrawnUi_Draw_ITimingParameters_DurationSecs
  name: DurationSecs
  nameWithType: ITimingParameters.DurationSecs
  fullName: DrawnUi.Draw.ITimingParameters.DurationSecs
- uid: DrawnUi.Draw.UnderdampedSpringTimingParameters.ValueAt*
  commentId: Overload:DrawnUi.Draw.UnderdampedSpringTimingParameters.ValueAt
  href: DrawnUi.Draw.UnderdampedSpringTimingParameters.html#DrawnUi_Draw_UnderdampedSpringTimingParameters_ValueAt_System_Single_
  name: ValueAt
  nameWithType: UnderdampedSpringTimingParameters.ValueAt
  fullName: DrawnUi.Draw.UnderdampedSpringTimingParameters.ValueAt
- uid: DrawnUi.Draw.ITimingParameters.ValueAt(System.Single)
  commentId: M:DrawnUi.Draw.ITimingParameters.ValueAt(System.Single)
  parent: DrawnUi.Draw.ITimingParameters
  isExternal: true
  href: DrawnUi.Draw.ITimingParameters.html#DrawnUi_Draw_ITimingParameters_ValueAt_System_Single_
  name: ValueAt(float)
  nameWithType: ITimingParameters.ValueAt(float)
  fullName: DrawnUi.Draw.ITimingParameters.ValueAt(float)
  nameWithType.vb: ITimingParameters.ValueAt(Single)
  fullName.vb: DrawnUi.Draw.ITimingParameters.ValueAt(Single)
  name.vb: ValueAt(Single)
  spec.csharp:
  - uid: DrawnUi.Draw.ITimingParameters.ValueAt(System.Single)
    name: ValueAt
    href: DrawnUi.Draw.ITimingParameters.html#DrawnUi_Draw_ITimingParameters_ValueAt_System_Single_
  - name: (
  - uid: System.Single
    name: float
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.single
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.ITimingParameters.ValueAt(System.Single)
    name: ValueAt
    href: DrawnUi.Draw.ITimingParameters.html#DrawnUi_Draw_ITimingParameters_ValueAt_System_Single_
  - name: (
  - uid: System.Single
    name: Single
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.single
  - name: )
- uid: DrawnUi.Draw.UnderdampedSpringTimingParameters.AmplitudeAt*
  commentId: Overload:DrawnUi.Draw.UnderdampedSpringTimingParameters.AmplitudeAt
  href: DrawnUi.Draw.UnderdampedSpringTimingParameters.html#DrawnUi_Draw_UnderdampedSpringTimingParameters_AmplitudeAt_System_Single_
  name: AmplitudeAt
  nameWithType: UnderdampedSpringTimingParameters.AmplitudeAt
  fullName: DrawnUi.Draw.UnderdampedSpringTimingParameters.AmplitudeAt
- uid: DrawnUi.Draw.IDampingTimingParameters.AmplitudeAt(System.Single)
  commentId: M:DrawnUi.Draw.IDampingTimingParameters.AmplitudeAt(System.Single)
  parent: DrawnUi.Draw.IDampingTimingParameters
  isExternal: true
  href: DrawnUi.Draw.IDampingTimingParameters.html#DrawnUi_Draw_IDampingTimingParameters_AmplitudeAt_System_Single_
  name: AmplitudeAt(float)
  nameWithType: IDampingTimingParameters.AmplitudeAt(float)
  fullName: DrawnUi.Draw.IDampingTimingParameters.AmplitudeAt(float)
  nameWithType.vb: IDampingTimingParameters.AmplitudeAt(Single)
  fullName.vb: DrawnUi.Draw.IDampingTimingParameters.AmplitudeAt(Single)
  name.vb: AmplitudeAt(Single)
  spec.csharp:
  - uid: DrawnUi.Draw.IDampingTimingParameters.AmplitudeAt(System.Single)
    name: AmplitudeAt
    href: DrawnUi.Draw.IDampingTimingParameters.html#DrawnUi_Draw_IDampingTimingParameters_AmplitudeAt_System_Single_
  - name: (
  - uid: System.Single
    name: float
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.single
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.IDampingTimingParameters.AmplitudeAt(System.Single)
    name: AmplitudeAt
    href: DrawnUi.Draw.IDampingTimingParameters.html#DrawnUi_Draw_IDampingTimingParameters_AmplitudeAt_System_Single_
  - name: (
  - uid: System.Single
    name: Single
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.single
  - name: )
