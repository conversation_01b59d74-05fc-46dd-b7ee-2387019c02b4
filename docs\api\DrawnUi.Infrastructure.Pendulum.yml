### YamlMime:ManagedReference
items:
- uid: DrawnUi.Infrastructure.Pendulum
  commentId: T:DrawnUi.Infrastructure.Pendulum
  id: Pendulum
  parent: DrawnUi.Infrastructure
  children:
  - DrawnUi.Infrastructure.Pendulum.#ctor
  - DrawnUi.Infrastructure.Pendulum.AirResistance
  - DrawnUi.Infrastructure.Pendulum.AngularVelocity
  - DrawnUi.Infrastructure.Pendulum.Gravity
  - DrawnUi.Infrastructure.Pendulum.Reset
  - DrawnUi.Infrastructure.Pendulum.RodLength
  - DrawnUi.Infrastructure.Pendulum.SetAmplitude(System.Double)
  - DrawnUi.Infrastructure.Pendulum.Update(System.Double)
  - DrawnUi.Infrastructure.Pendulum.accelerationVector
  - DrawnUi.Infrastructure.Pendulum.angle
  - DrawnUi.Infrastructure.Pendulum.angularAcceleration
  - DrawnUi.Infrastructure.Pendulum.getAccelerationVector
  - DrawnUi.Infrastructure.Pendulum.getAirResistence
  - DrawnUi.Infrastructure.Pendulum.getAngle
  - DrawnUi.Infrastructure.Pendulum.getAngularAcceleration
  - DrawnUi.Infrastructure.Pendulum.getAngularVelocity
  - DrawnUi.Infrastructure.Pendulum.getInitialAngle
  - DrawnUi.Infrastructure.Pendulum.getInitialVelocity
  - DrawnUi.Infrastructure.Pendulum.getVelocityVector
  - DrawnUi.Infrastructure.Pendulum.getWireVector
  - DrawnUi.Infrastructure.Pendulum.setAirResistance(System.Double)
  - DrawnUi.Infrastructure.Pendulum.setInitialAngle(System.Double)
  - DrawnUi.Infrastructure.Pendulum.setInitialVelocity(System.Double)
  - DrawnUi.Infrastructure.Pendulum.velocityVector
  - DrawnUi.Infrastructure.Pendulum.wireVector
  langs:
  - csharp
  - vb
  name: Pendulum
  nameWithType: Pendulum
  fullName: DrawnUi.Infrastructure.Pendulum
  type: Class
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/Pendulum.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Pendulum
    path: ../src/Shared/Draw/Internals/Helpers/Pendulum.cs
    startLine: 41
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public class Pendulum
    content.vb: Public Class Pendulum
  inheritance:
  - System.Object
  derivedClasses:
  - DrawnUi.Infrastructure.PerpetualPendulum
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Infrastructure.Pendulum.wireVector
  commentId: F:DrawnUi.Infrastructure.Pendulum.wireVector
  id: wireVector
  parent: DrawnUi.Infrastructure.Pendulum
  langs:
  - csharp
  - vb
  name: wireVector
  nameWithType: Pendulum.wireVector
  fullName: DrawnUi.Infrastructure.Pendulum.wireVector
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/Pendulum.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: wireVector
    path: ../src/Shared/Draw/Internals/Helpers/Pendulum.cs
    startLine: 43
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: protected Vector wireVector
    return:
      type: DrawnUi.Infrastructure.Vector
    content.vb: Protected wireVector As Vector
- uid: DrawnUi.Infrastructure.Pendulum.accelerationVector
  commentId: F:DrawnUi.Infrastructure.Pendulum.accelerationVector
  id: accelerationVector
  parent: DrawnUi.Infrastructure.Pendulum
  langs:
  - csharp
  - vb
  name: accelerationVector
  nameWithType: Pendulum.accelerationVector
  fullName: DrawnUi.Infrastructure.Pendulum.accelerationVector
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/Pendulum.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: accelerationVector
    path: ../src/Shared/Draw/Internals/Helpers/Pendulum.cs
    startLine: 44
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: protected Vector accelerationVector
    return:
      type: DrawnUi.Infrastructure.Vector
    content.vb: Protected accelerationVector As Vector
- uid: DrawnUi.Infrastructure.Pendulum.velocityVector
  commentId: F:DrawnUi.Infrastructure.Pendulum.velocityVector
  id: velocityVector
  parent: DrawnUi.Infrastructure.Pendulum
  langs:
  - csharp
  - vb
  name: velocityVector
  nameWithType: Pendulum.velocityVector
  fullName: DrawnUi.Infrastructure.Pendulum.velocityVector
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/Pendulum.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: velocityVector
    path: ../src/Shared/Draw/Internals/Helpers/Pendulum.cs
    startLine: 45
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: protected Vector velocityVector
    return:
      type: DrawnUi.Infrastructure.Vector
    content.vb: Protected velocityVector As Vector
- uid: DrawnUi.Infrastructure.Pendulum.angularAcceleration
  commentId: F:DrawnUi.Infrastructure.Pendulum.angularAcceleration
  id: angularAcceleration
  parent: DrawnUi.Infrastructure.Pendulum
  langs:
  - csharp
  - vb
  name: angularAcceleration
  nameWithType: Pendulum.angularAcceleration
  fullName: DrawnUi.Infrastructure.Pendulum.angularAcceleration
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/Pendulum.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: angularAcceleration
    path: ../src/Shared/Draw/Internals/Helpers/Pendulum.cs
    startLine: 47
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: protected double angularAcceleration
    return:
      type: System.Double
    content.vb: Protected angularAcceleration As Double
- uid: DrawnUi.Infrastructure.Pendulum.AngularVelocity
  commentId: P:DrawnUi.Infrastructure.Pendulum.AngularVelocity
  id: AngularVelocity
  parent: DrawnUi.Infrastructure.Pendulum
  langs:
  - csharp
  - vb
  name: AngularVelocity
  nameWithType: Pendulum.AngularVelocity
  fullName: DrawnUi.Infrastructure.Pendulum.AngularVelocity
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/Pendulum.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: AngularVelocity
    path: ../src/Shared/Draw/Internals/Helpers/Pendulum.cs
    startLine: 48
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public double AngularVelocity { get; protected set; }
    parameters: []
    return:
      type: System.Double
    content.vb: Public Property AngularVelocity As Double
  overload: DrawnUi.Infrastructure.Pendulum.AngularVelocity*
- uid: DrawnUi.Infrastructure.Pendulum.RodLength
  commentId: P:DrawnUi.Infrastructure.Pendulum.RodLength
  id: RodLength
  parent: DrawnUi.Infrastructure.Pendulum
  langs:
  - csharp
  - vb
  name: RodLength
  nameWithType: Pendulum.RodLength
  fullName: DrawnUi.Infrastructure.Pendulum.RodLength
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/Pendulum.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: RodLength
    path: ../src/Shared/Draw/Internals/Helpers/Pendulum.cs
    startLine: 50
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public double RodLength { get; set; }
    parameters: []
    return:
      type: System.Double
    content.vb: Public Property RodLength As Double
  overload: DrawnUi.Infrastructure.Pendulum.RodLength*
- uid: DrawnUi.Infrastructure.Pendulum.AirResistance
  commentId: P:DrawnUi.Infrastructure.Pendulum.AirResistance
  id: AirResistance
  parent: DrawnUi.Infrastructure.Pendulum
  langs:
  - csharp
  - vb
  name: AirResistance
  nameWithType: Pendulum.AirResistance
  fullName: DrawnUi.Infrastructure.Pendulum.AirResistance
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/Pendulum.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: AirResistance
    path: ../src/Shared/Draw/Internals/Helpers/Pendulum.cs
    startLine: 68
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public double AirResistance { get; set; }
    parameters: []
    return:
      type: System.Double
    content.vb: Public Property AirResistance As Double
  overload: DrawnUi.Infrastructure.Pendulum.AirResistance*
- uid: DrawnUi.Infrastructure.Pendulum.Gravity
  commentId: P:DrawnUi.Infrastructure.Pendulum.Gravity
  id: Gravity
  parent: DrawnUi.Infrastructure.Pendulum
  langs:
  - csharp
  - vb
  name: Gravity
  nameWithType: Pendulum.Gravity
  fullName: DrawnUi.Infrastructure.Pendulum.Gravity
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/Pendulum.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Gravity
    path: ../src/Shared/Draw/Internals/Helpers/Pendulum.cs
    startLine: 70
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public double Gravity { get; set; }
    parameters: []
    return:
      type: System.Double
    content.vb: Public Property Gravity As Double
  overload: DrawnUi.Infrastructure.Pendulum.Gravity*
- uid: DrawnUi.Infrastructure.Pendulum.angle
  commentId: F:DrawnUi.Infrastructure.Pendulum.angle
  id: angle
  parent: DrawnUi.Infrastructure.Pendulum
  langs:
  - csharp
  - vb
  name: angle
  nameWithType: Pendulum.angle
  fullName: DrawnUi.Infrastructure.Pendulum.angle
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/Pendulum.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: angle
    path: ../src/Shared/Draw/Internals/Helpers/Pendulum.cs
    startLine: 72
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: protected double angle
    return:
      type: System.Double
    content.vb: Protected angle As Double
- uid: DrawnUi.Infrastructure.Pendulum.#ctor
  commentId: M:DrawnUi.Infrastructure.Pendulum.#ctor
  id: '#ctor'
  parent: DrawnUi.Infrastructure.Pendulum
  langs:
  - csharp
  - vb
  name: Pendulum()
  nameWithType: Pendulum.Pendulum()
  fullName: DrawnUi.Infrastructure.Pendulum.Pendulum()
  type: Constructor
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/Pendulum.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Shared/Draw/Internals/Helpers/Pendulum.cs
    startLine: 81
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public Pendulum()
    content.vb: Public Sub New()
  overload: DrawnUi.Infrastructure.Pendulum.#ctor*
  nameWithType.vb: Pendulum.New()
  fullName.vb: DrawnUi.Infrastructure.Pendulum.New()
  name.vb: New()
- uid: DrawnUi.Infrastructure.Pendulum.Update(System.Double)
  commentId: M:DrawnUi.Infrastructure.Pendulum.Update(System.Double)
  id: Update(System.Double)
  parent: DrawnUi.Infrastructure.Pendulum
  langs:
  - csharp
  - vb
  name: Update(double)
  nameWithType: Pendulum.Update(double)
  fullName: DrawnUi.Infrastructure.Pendulum.Update(double)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/Pendulum.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Update
    path: ../src/Shared/Draw/Internals/Helpers/Pendulum.cs
    startLine: 90
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public virtual void Update(double timeStep)
    parameters:
    - id: timeStep
      type: System.Double
    content.vb: Public Overridable Sub Update(timeStep As Double)
  overload: DrawnUi.Infrastructure.Pendulum.Update*
  nameWithType.vb: Pendulum.Update(Double)
  fullName.vb: DrawnUi.Infrastructure.Pendulum.Update(Double)
  name.vb: Update(Double)
- uid: DrawnUi.Infrastructure.Pendulum.Reset
  commentId: M:DrawnUi.Infrastructure.Pendulum.Reset
  id: Reset
  parent: DrawnUi.Infrastructure.Pendulum
  langs:
  - csharp
  - vb
  name: Reset()
  nameWithType: Pendulum.Reset()
  fullName: DrawnUi.Infrastructure.Pendulum.Reset()
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/Pendulum.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Reset
    path: ../src/Shared/Draw/Internals/Helpers/Pendulum.cs
    startLine: 133
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public void Reset()
    content.vb: Public Sub Reset()
  overload: DrawnUi.Infrastructure.Pendulum.Reset*
- uid: DrawnUi.Infrastructure.Pendulum.SetAmplitude(System.Double)
  commentId: M:DrawnUi.Infrastructure.Pendulum.SetAmplitude(System.Double)
  id: SetAmplitude(System.Double)
  parent: DrawnUi.Infrastructure.Pendulum
  langs:
  - csharp
  - vb
  name: SetAmplitude(double)
  nameWithType: Pendulum.SetAmplitude(double)
  fullName: DrawnUi.Infrastructure.Pendulum.SetAmplitude(double)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/Pendulum.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: SetAmplitude
    path: ../src/Shared/Draw/Internals/Helpers/Pendulum.cs
    startLine: 140
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public void SetAmplitude(double value)
    parameters:
    - id: value
      type: System.Double
    content.vb: Public Sub SetAmplitude(value As Double)
  overload: DrawnUi.Infrastructure.Pendulum.SetAmplitude*
  nameWithType.vb: Pendulum.SetAmplitude(Double)
  fullName.vb: DrawnUi.Infrastructure.Pendulum.SetAmplitude(Double)
  name.vb: SetAmplitude(Double)
- uid: DrawnUi.Infrastructure.Pendulum.getInitialAngle
  commentId: M:DrawnUi.Infrastructure.Pendulum.getInitialAngle
  id: getInitialAngle
  parent: DrawnUi.Infrastructure.Pendulum
  langs:
  - csharp
  - vb
  name: getInitialAngle()
  nameWithType: Pendulum.getInitialAngle()
  fullName: DrawnUi.Infrastructure.Pendulum.getInitialAngle()
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/Pendulum.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: getInitialAngle
    path: ../src/Shared/Draw/Internals/Helpers/Pendulum.cs
    startLine: 142
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public double getInitialAngle()
    return:
      type: System.Double
    content.vb: Public Function getInitialAngle() As Double
  overload: DrawnUi.Infrastructure.Pendulum.getInitialAngle*
- uid: DrawnUi.Infrastructure.Pendulum.setInitialAngle(System.Double)
  commentId: M:DrawnUi.Infrastructure.Pendulum.setInitialAngle(System.Double)
  id: setInitialAngle(System.Double)
  parent: DrawnUi.Infrastructure.Pendulum
  langs:
  - csharp
  - vb
  name: setInitialAngle(double)
  nameWithType: Pendulum.setInitialAngle(double)
  fullName: DrawnUi.Infrastructure.Pendulum.setInitialAngle(double)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/Pendulum.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: setInitialAngle
    path: ../src/Shared/Draw/Internals/Helpers/Pendulum.cs
    startLine: 143
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public void setInitialAngle(double initialAngle)
    parameters:
    - id: initialAngle
      type: System.Double
    content.vb: Public Sub setInitialAngle(initialAngle As Double)
  overload: DrawnUi.Infrastructure.Pendulum.setInitialAngle*
  nameWithType.vb: Pendulum.setInitialAngle(Double)
  fullName.vb: DrawnUi.Infrastructure.Pendulum.setInitialAngle(Double)
  name.vb: setInitialAngle(Double)
- uid: DrawnUi.Infrastructure.Pendulum.getInitialVelocity
  commentId: M:DrawnUi.Infrastructure.Pendulum.getInitialVelocity
  id: getInitialVelocity
  parent: DrawnUi.Infrastructure.Pendulum
  langs:
  - csharp
  - vb
  name: getInitialVelocity()
  nameWithType: Pendulum.getInitialVelocity()
  fullName: DrawnUi.Infrastructure.Pendulum.getInitialVelocity()
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/Pendulum.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: getInitialVelocity
    path: ../src/Shared/Draw/Internals/Helpers/Pendulum.cs
    startLine: 145
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public double getInitialVelocity()
    return:
      type: System.Double
    content.vb: Public Function getInitialVelocity() As Double
  overload: DrawnUi.Infrastructure.Pendulum.getInitialVelocity*
- uid: DrawnUi.Infrastructure.Pendulum.setInitialVelocity(System.Double)
  commentId: M:DrawnUi.Infrastructure.Pendulum.setInitialVelocity(System.Double)
  id: setInitialVelocity(System.Double)
  parent: DrawnUi.Infrastructure.Pendulum
  langs:
  - csharp
  - vb
  name: setInitialVelocity(double)
  nameWithType: Pendulum.setInitialVelocity(double)
  fullName: DrawnUi.Infrastructure.Pendulum.setInitialVelocity(double)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/Pendulum.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: setInitialVelocity
    path: ../src/Shared/Draw/Internals/Helpers/Pendulum.cs
    startLine: 146
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public void setInitialVelocity(double initialVelocity)
    parameters:
    - id: initialVelocity
      type: System.Double
    content.vb: Public Sub setInitialVelocity(initialVelocity As Double)
  overload: DrawnUi.Infrastructure.Pendulum.setInitialVelocity*
  nameWithType.vb: Pendulum.setInitialVelocity(Double)
  fullName.vb: DrawnUi.Infrastructure.Pendulum.setInitialVelocity(Double)
  name.vb: setInitialVelocity(Double)
- uid: DrawnUi.Infrastructure.Pendulum.getAngle
  commentId: M:DrawnUi.Infrastructure.Pendulum.getAngle
  id: getAngle
  parent: DrawnUi.Infrastructure.Pendulum
  langs:
  - csharp
  - vb
  name: getAngle()
  nameWithType: Pendulum.getAngle()
  fullName: DrawnUi.Infrastructure.Pendulum.getAngle()
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/Pendulum.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: getAngle
    path: ../src/Shared/Draw/Internals/Helpers/Pendulum.cs
    startLine: 148
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public double getAngle()
    return:
      type: System.Double
    content.vb: Public Function getAngle() As Double
  overload: DrawnUi.Infrastructure.Pendulum.getAngle*
- uid: DrawnUi.Infrastructure.Pendulum.getAirResistence
  commentId: M:DrawnUi.Infrastructure.Pendulum.getAirResistence
  id: getAirResistence
  parent: DrawnUi.Infrastructure.Pendulum
  langs:
  - csharp
  - vb
  name: getAirResistence()
  nameWithType: Pendulum.getAirResistence()
  fullName: DrawnUi.Infrastructure.Pendulum.getAirResistence()
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/Pendulum.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: getAirResistence
    path: ../src/Shared/Draw/Internals/Helpers/Pendulum.cs
    startLine: 152
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public double getAirResistence()
    return:
      type: System.Double
    content.vb: Public Function getAirResistence() As Double
  overload: DrawnUi.Infrastructure.Pendulum.getAirResistence*
- uid: DrawnUi.Infrastructure.Pendulum.setAirResistance(System.Double)
  commentId: M:DrawnUi.Infrastructure.Pendulum.setAirResistance(System.Double)
  id: setAirResistance(System.Double)
  parent: DrawnUi.Infrastructure.Pendulum
  langs:
  - csharp
  - vb
  name: setAirResistance(double)
  nameWithType: Pendulum.setAirResistance(double)
  fullName: DrawnUi.Infrastructure.Pendulum.setAirResistance(double)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/Pendulum.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: setAirResistance
    path: ../src/Shared/Draw/Internals/Helpers/Pendulum.cs
    startLine: 153
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public void setAirResistance(double airResistence)
    parameters:
    - id: airResistence
      type: System.Double
    content.vb: Public Sub setAirResistance(airResistence As Double)
  overload: DrawnUi.Infrastructure.Pendulum.setAirResistance*
  nameWithType.vb: Pendulum.setAirResistance(Double)
  fullName.vb: DrawnUi.Infrastructure.Pendulum.setAirResistance(Double)
  name.vb: setAirResistance(Double)
- uid: DrawnUi.Infrastructure.Pendulum.getWireVector
  commentId: M:DrawnUi.Infrastructure.Pendulum.getWireVector
  id: getWireVector
  parent: DrawnUi.Infrastructure.Pendulum
  langs:
  - csharp
  - vb
  name: getWireVector()
  nameWithType: Pendulum.getWireVector()
  fullName: DrawnUi.Infrastructure.Pendulum.getWireVector()
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/Pendulum.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: getWireVector
    path: ../src/Shared/Draw/Internals/Helpers/Pendulum.cs
    startLine: 155
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public Vector getWireVector()
    return:
      type: DrawnUi.Infrastructure.Vector
    content.vb: Public Function getWireVector() As Vector
  overload: DrawnUi.Infrastructure.Pendulum.getWireVector*
- uid: DrawnUi.Infrastructure.Pendulum.getAccelerationVector
  commentId: M:DrawnUi.Infrastructure.Pendulum.getAccelerationVector
  id: getAccelerationVector
  parent: DrawnUi.Infrastructure.Pendulum
  langs:
  - csharp
  - vb
  name: getAccelerationVector()
  nameWithType: Pendulum.getAccelerationVector()
  fullName: DrawnUi.Infrastructure.Pendulum.getAccelerationVector()
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/Pendulum.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: getAccelerationVector
    path: ../src/Shared/Draw/Internals/Helpers/Pendulum.cs
    startLine: 156
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public Vector getAccelerationVector()
    return:
      type: DrawnUi.Infrastructure.Vector
    content.vb: Public Function getAccelerationVector() As Vector
  overload: DrawnUi.Infrastructure.Pendulum.getAccelerationVector*
- uid: DrawnUi.Infrastructure.Pendulum.getVelocityVector
  commentId: M:DrawnUi.Infrastructure.Pendulum.getVelocityVector
  id: getVelocityVector
  parent: DrawnUi.Infrastructure.Pendulum
  langs:
  - csharp
  - vb
  name: getVelocityVector()
  nameWithType: Pendulum.getVelocityVector()
  fullName: DrawnUi.Infrastructure.Pendulum.getVelocityVector()
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/Pendulum.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: getVelocityVector
    path: ../src/Shared/Draw/Internals/Helpers/Pendulum.cs
    startLine: 157
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public Vector getVelocityVector()
    return:
      type: DrawnUi.Infrastructure.Vector
    content.vb: Public Function getVelocityVector() As Vector
  overload: DrawnUi.Infrastructure.Pendulum.getVelocityVector*
- uid: DrawnUi.Infrastructure.Pendulum.getAngularAcceleration
  commentId: M:DrawnUi.Infrastructure.Pendulum.getAngularAcceleration
  id: getAngularAcceleration
  parent: DrawnUi.Infrastructure.Pendulum
  langs:
  - csharp
  - vb
  name: getAngularAcceleration()
  nameWithType: Pendulum.getAngularAcceleration()
  fullName: DrawnUi.Infrastructure.Pendulum.getAngularAcceleration()
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/Pendulum.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: getAngularAcceleration
    path: ../src/Shared/Draw/Internals/Helpers/Pendulum.cs
    startLine: 159
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public double getAngularAcceleration()
    return:
      type: System.Double
    content.vb: Public Function getAngularAcceleration() As Double
  overload: DrawnUi.Infrastructure.Pendulum.getAngularAcceleration*
- uid: DrawnUi.Infrastructure.Pendulum.getAngularVelocity
  commentId: M:DrawnUi.Infrastructure.Pendulum.getAngularVelocity
  id: getAngularVelocity
  parent: DrawnUi.Infrastructure.Pendulum
  langs:
  - csharp
  - vb
  name: getAngularVelocity()
  nameWithType: Pendulum.getAngularVelocity()
  fullName: DrawnUi.Infrastructure.Pendulum.getAngularVelocity()
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/Pendulum.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: getAngularVelocity
    path: ../src/Shared/Draw/Internals/Helpers/Pendulum.cs
    startLine: 160
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public double getAngularVelocity()
    return:
      type: System.Double
    content.vb: Public Function getAngularVelocity() As Double
  overload: DrawnUi.Infrastructure.Pendulum.getAngularVelocity*
references:
- uid: DrawnUi.Infrastructure
  commentId: N:DrawnUi.Infrastructure
  href: DrawnUi.html
  name: DrawnUi.Infrastructure
  nameWithType: DrawnUi.Infrastructure
  fullName: DrawnUi.Infrastructure
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Infrastructure
    name: Infrastructure
    href: DrawnUi.Infrastructure.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Infrastructure
    name: Infrastructure
    href: DrawnUi.Infrastructure.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Infrastructure.Vector
  commentId: T:DrawnUi.Infrastructure.Vector
  parent: DrawnUi.Infrastructure
  href: DrawnUi.Infrastructure.Vector.html
  name: Vector
  nameWithType: Vector
  fullName: DrawnUi.Infrastructure.Vector
- uid: System.Double
  commentId: T:System.Double
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.double
  name: double
  nameWithType: double
  fullName: double
  nameWithType.vb: Double
  fullName.vb: Double
  name.vb: Double
- uid: DrawnUi.Infrastructure.Pendulum.AngularVelocity*
  commentId: Overload:DrawnUi.Infrastructure.Pendulum.AngularVelocity
  href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_AngularVelocity
  name: AngularVelocity
  nameWithType: Pendulum.AngularVelocity
  fullName: DrawnUi.Infrastructure.Pendulum.AngularVelocity
- uid: DrawnUi.Infrastructure.Pendulum.RodLength*
  commentId: Overload:DrawnUi.Infrastructure.Pendulum.RodLength
  href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_RodLength
  name: RodLength
  nameWithType: Pendulum.RodLength
  fullName: DrawnUi.Infrastructure.Pendulum.RodLength
- uid: DrawnUi.Infrastructure.Pendulum.AirResistance*
  commentId: Overload:DrawnUi.Infrastructure.Pendulum.AirResistance
  href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_AirResistance
  name: AirResistance
  nameWithType: Pendulum.AirResistance
  fullName: DrawnUi.Infrastructure.Pendulum.AirResistance
- uid: DrawnUi.Infrastructure.Pendulum.Gravity*
  commentId: Overload:DrawnUi.Infrastructure.Pendulum.Gravity
  href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_Gravity
  name: Gravity
  nameWithType: Pendulum.Gravity
  fullName: DrawnUi.Infrastructure.Pendulum.Gravity
- uid: DrawnUi.Infrastructure.Pendulum.#ctor*
  commentId: Overload:DrawnUi.Infrastructure.Pendulum.#ctor
  href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum__ctor
  name: Pendulum
  nameWithType: Pendulum.Pendulum
  fullName: DrawnUi.Infrastructure.Pendulum.Pendulum
  nameWithType.vb: Pendulum.New
  fullName.vb: DrawnUi.Infrastructure.Pendulum.New
  name.vb: New
- uid: DrawnUi.Infrastructure.Pendulum.Update*
  commentId: Overload:DrawnUi.Infrastructure.Pendulum.Update
  href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_Update_System_Double_
  name: Update
  nameWithType: Pendulum.Update
  fullName: DrawnUi.Infrastructure.Pendulum.Update
- uid: DrawnUi.Infrastructure.Pendulum.Reset*
  commentId: Overload:DrawnUi.Infrastructure.Pendulum.Reset
  href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_Reset
  name: Reset
  nameWithType: Pendulum.Reset
  fullName: DrawnUi.Infrastructure.Pendulum.Reset
- uid: DrawnUi.Infrastructure.Pendulum.SetAmplitude*
  commentId: Overload:DrawnUi.Infrastructure.Pendulum.SetAmplitude
  href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_SetAmplitude_System_Double_
  name: SetAmplitude
  nameWithType: Pendulum.SetAmplitude
  fullName: DrawnUi.Infrastructure.Pendulum.SetAmplitude
- uid: DrawnUi.Infrastructure.Pendulum.getInitialAngle*
  commentId: Overload:DrawnUi.Infrastructure.Pendulum.getInitialAngle
  href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_getInitialAngle
  name: getInitialAngle
  nameWithType: Pendulum.getInitialAngle
  fullName: DrawnUi.Infrastructure.Pendulum.getInitialAngle
- uid: DrawnUi.Infrastructure.Pendulum.setInitialAngle*
  commentId: Overload:DrawnUi.Infrastructure.Pendulum.setInitialAngle
  href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_setInitialAngle_System_Double_
  name: setInitialAngle
  nameWithType: Pendulum.setInitialAngle
  fullName: DrawnUi.Infrastructure.Pendulum.setInitialAngle
- uid: DrawnUi.Infrastructure.Pendulum.getInitialVelocity*
  commentId: Overload:DrawnUi.Infrastructure.Pendulum.getInitialVelocity
  href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_getInitialVelocity
  name: getInitialVelocity
  nameWithType: Pendulum.getInitialVelocity
  fullName: DrawnUi.Infrastructure.Pendulum.getInitialVelocity
- uid: DrawnUi.Infrastructure.Pendulum.setInitialVelocity*
  commentId: Overload:DrawnUi.Infrastructure.Pendulum.setInitialVelocity
  href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_setInitialVelocity_System_Double_
  name: setInitialVelocity
  nameWithType: Pendulum.setInitialVelocity
  fullName: DrawnUi.Infrastructure.Pendulum.setInitialVelocity
- uid: DrawnUi.Infrastructure.Pendulum.getAngle*
  commentId: Overload:DrawnUi.Infrastructure.Pendulum.getAngle
  href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_getAngle
  name: getAngle
  nameWithType: Pendulum.getAngle
  fullName: DrawnUi.Infrastructure.Pendulum.getAngle
- uid: DrawnUi.Infrastructure.Pendulum.getAirResistence*
  commentId: Overload:DrawnUi.Infrastructure.Pendulum.getAirResistence
  href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_getAirResistence
  name: getAirResistence
  nameWithType: Pendulum.getAirResistence
  fullName: DrawnUi.Infrastructure.Pendulum.getAirResistence
- uid: DrawnUi.Infrastructure.Pendulum.setAirResistance*
  commentId: Overload:DrawnUi.Infrastructure.Pendulum.setAirResistance
  href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_setAirResistance_System_Double_
  name: setAirResistance
  nameWithType: Pendulum.setAirResistance
  fullName: DrawnUi.Infrastructure.Pendulum.setAirResistance
- uid: DrawnUi.Infrastructure.Pendulum.getWireVector*
  commentId: Overload:DrawnUi.Infrastructure.Pendulum.getWireVector
  href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_getWireVector
  name: getWireVector
  nameWithType: Pendulum.getWireVector
  fullName: DrawnUi.Infrastructure.Pendulum.getWireVector
- uid: DrawnUi.Infrastructure.Pendulum.getAccelerationVector*
  commentId: Overload:DrawnUi.Infrastructure.Pendulum.getAccelerationVector
  href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_getAccelerationVector
  name: getAccelerationVector
  nameWithType: Pendulum.getAccelerationVector
  fullName: DrawnUi.Infrastructure.Pendulum.getAccelerationVector
- uid: DrawnUi.Infrastructure.Pendulum.getVelocityVector*
  commentId: Overload:DrawnUi.Infrastructure.Pendulum.getVelocityVector
  href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_getVelocityVector
  name: getVelocityVector
  nameWithType: Pendulum.getVelocityVector
  fullName: DrawnUi.Infrastructure.Pendulum.getVelocityVector
- uid: DrawnUi.Infrastructure.Pendulum.getAngularAcceleration*
  commentId: Overload:DrawnUi.Infrastructure.Pendulum.getAngularAcceleration
  href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_getAngularAcceleration
  name: getAngularAcceleration
  nameWithType: Pendulum.getAngularAcceleration
  fullName: DrawnUi.Infrastructure.Pendulum.getAngularAcceleration
- uid: DrawnUi.Infrastructure.Pendulum.getAngularVelocity*
  commentId: Overload:DrawnUi.Infrastructure.Pendulum.getAngularVelocity
  href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_getAngularVelocity
  name: getAngularVelocity
  nameWithType: Pendulum.getAngularVelocity
  fullName: DrawnUi.Infrastructure.Pendulum.getAngularVelocity
