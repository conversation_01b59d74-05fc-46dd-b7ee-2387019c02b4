<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <title>Enum <PERSON> | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width">
      <meta name="title" content="Enum MauiKey | DrawnUi Documentation ">
    
    <meta name="description" content="These are platform-independent. They correspond to JavaScript keys.">
      <link rel="shortcut icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../styles/docfx.vendor.min.css">
      <link rel="stylesheet" href="../styles/docfx.css">
      <link rel="stylesheet" href="../styles/main.css">
      <meta property="docfx:navrel" content="../toc.html">
      <meta property="docfx:tocrel" content="toc.html">
    
    <meta property="docfx:rel" content="../">
    
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first=First data-prev=Previous data-next=Next data-last=Last></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="DrawnUi.Draw.MauiKey">




  <h1 id="DrawnUi_Draw_MauiKey" data-uid="DrawnUi.Draw.MauiKey" class="text-break">Enum MauiKey</h1>
  <div class="markdown level0 summary"><p>These are platform-independent. They correspond to JavaScript keys.</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <h6><strong>Namespace</strong>: <a class="xref" href="DrawnUi.html">DrawnUi</a>.<a class="xref" href="DrawnUi.Draw.html">Draw</a></h6>
  <h6><strong>Assembly</strong>: DrawnUi.Maui.dll</h6>
  <h5 id="DrawnUi_Draw_MauiKey_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public enum MauiKey</code></pre>
  </div>
  <h3 id="fields">Fields
</h3>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    <thead>
    </thead></thead><tbody>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_AltLeft">AltLeft</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_AltRight">AltRight</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_ArrowDown">ArrowDown</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_ArrowLeft">ArrowLeft</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_ArrowRight">ArrowRight</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_ArrowUp">ArrowUp</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_AudioVolumeDown">AudioVolumeDown</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_AudioVolumeMute">AudioVolumeMute</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_AudioVolumeUp">AudioVolumeUp</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_Backquote">Backquote</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_Backslash">Backslash</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_Backspace">Backspace</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_BracketLeft">BracketLeft</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_BracketRight">BracketRight</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_CapsLock">CapsLock</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_Comma">Comma</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_ContextMenu">ContextMenu</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_ControlLeft">ControlLeft</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_ControlRight">ControlRight</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_Delete">Delete</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_Digit0">Digit0</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_Digit1">Digit1</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_Digit2">Digit2</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_Digit3">Digit3</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_Digit4">Digit4</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_Digit5">Digit5</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_Digit6">Digit6</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_Digit7">Digit7</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_Digit8">Digit8</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_Digit9">Digit9</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_End">End</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_Enter">Enter</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_Equal">Equal</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_Escape">Escape</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_F1">F1</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_F10">F10</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_F11">F11</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_F12">F12</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_F2">F2</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_F3">F3</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_F4">F4</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_F5">F5</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_F6">F6</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_F7">F7</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_F8">F8</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_F9">F9</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_Home">Home</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_Insert">Insert</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_IntBackslash">IntBackslash</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_KeyA">KeyA</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_KeyB">KeyB</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_KeyC">KeyC</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_KeyD">KeyD</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_KeyE">KeyE</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_KeyF">KeyF</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_KeyG">KeyG</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_KeyH">KeyH</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_KeyI">KeyI</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_KeyJ">KeyJ</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_KeyK">KeyK</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_KeyL">KeyL</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_KeyM">KeyM</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_KeyN">KeyN</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_KeyO">KeyO</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_KeyP">KeyP</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_KeyQ">KeyQ</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_KeyR">KeyR</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_KeyS">KeyS</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_KeyT">KeyT</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_KeyU">KeyU</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_KeyV">KeyV</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_KeyW">KeyW</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_KeyX">KeyX</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_KeyY">KeyY</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_KeyZ">KeyZ</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_LaunchApplication1">LaunchApplication1</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_LaunchApplication2">LaunchApplication2</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_LaunchMediaPlayer">LaunchMediaPlayer</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_MetaLeft">MetaLeft</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_MetaRight">MetaRight</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_Minus">Minus</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_NumLock">NumLock</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_Numpad0">Numpad0</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_Numpad1">Numpad1</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_Numpad2">Numpad2</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_Numpad3">Numpad3</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_Numpad4">Numpad4</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_Numpad5">Numpad5</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_Numpad6">Numpad6</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_Numpad7">Numpad7</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_Numpad8">Numpad8</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_Numpad9">Numpad9</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_NumpadAdd">NumpadAdd</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_NumpadDecimal">NumpadDecimal</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_NumpadDivide">NumpadDivide</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_NumpadMultiply">NumpadMultiply</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_NumpadSubtract">NumpadSubtract</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_PageDown">PageDown</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_PageUp">PageUp</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_Pause">Pause</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_Period">Period</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_PrintScreen">PrintScreen</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_Quote">Quote</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_ScrollLock">ScrollLock</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_Semicolon">Semicolon</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_ShiftLeft">ShiftLeft</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_ShiftRight">ShiftRight</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_Slash">Slash</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_Space">Space</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_Tab">Tab</td>
        <td></td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_MauiKey_Unknown">Unknown</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="extensionmethods">Extension Methods</h3>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_">InternalExtensions.FromPlatform(object)</a>
  </div>
</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_MauiKey.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.MauiKey%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A" class="contribution-link">Edit this page</a>
                  </li>
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Keyboard/MauiKeys.cs/#L6" class="contribution-link">View Source</a>
                  </li>
                </ul>
              </div>
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In this article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      
      <span>Generated by <strong>DocFX</strong></span>
          </div>
        </div>
      </footer>
    </div>

    <script type="text/javascript" src="../styles/docfx.vendor.min.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
