### YamlMime:ManagedReference
items:
- uid: DrawnUi.Infrastructure.Xaml.SkiaShadowsCollection
  commentId: T:DrawnUi.Infrastructure.Xaml.SkiaShadowsCollection
  id: SkiaShadowsCollection
  parent: DrawnUi.Infrastructure.Xaml
  children:
  - DrawnUi.Infrastructure.Xaml.SkiaShadowsCollection.Add(System.Object)
  langs:
  - csharp
  - vb
  name: SkiaShadowsCollection
  nameWithType: SkiaShadowsCollection
  fullName: DrawnUi.Infrastructure.Xaml.SkiaShadowsCollection
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Xaml/SkiaShadowsCollection.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: SkiaShadowsCollection
    path: ../src/Maui/DrawnUi/Internals/Xaml/SkiaShadowsCollection.cs
    startLine: 6
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure.Xaml
  syntax:
    content: 'public class SkiaShadowsCollection : ObservableCollection<SkiaShadow>, IList<SkiaShadow>, ICollection<SkiaShadow>, IReadOnlyList<SkiaShadow>, IReadOnlyCollection<SkiaShadow>, IEnumerable<SkiaShadow>, INotifyCollectionChanged, INotifyPropertyChanged, IList, ICollection, IEnumerable'
    content.vb: Public Class SkiaShadowsCollection Inherits ObservableCollection(Of SkiaShadow) Implements IList(Of SkiaShadow), ICollection(Of SkiaShadow), IReadOnlyList(Of SkiaShadow), IReadOnlyCollection(Of SkiaShadow), IEnumerable(Of SkiaShadow), INotifyCollectionChanged, INotifyPropertyChanged, IList, ICollection, IEnumerable
  inheritance:
  - System.Object
  - System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}
  - System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}
  implements:
  - System.Collections.Generic.IList{DrawnUi.Draw.SkiaShadow}
  - System.Collections.Generic.ICollection{DrawnUi.Draw.SkiaShadow}
  - System.Collections.Generic.IReadOnlyList{DrawnUi.Draw.SkiaShadow}
  - System.Collections.Generic.IReadOnlyCollection{DrawnUi.Draw.SkiaShadow}
  - System.Collections.Generic.IEnumerable{DrawnUi.Draw.SkiaShadow}
  - System.Collections.Specialized.INotifyCollectionChanged
  - System.ComponentModel.INotifyPropertyChanged
  - System.Collections.IList
  - System.Collections.ICollection
  - System.Collections.IEnumerable
  inheritedMembers:
  - System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.BlockReentrancy
  - System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.CheckReentrancy
  - System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.ClearItems
  - System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.InsertItem(System.Int32,DrawnUi.Draw.SkiaShadow)
  - System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.Move(System.Int32,System.Int32)
  - System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.MoveItem(System.Int32,System.Int32)
  - System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.OnCollectionChanged(System.Collections.Specialized.NotifyCollectionChangedEventArgs)
  - System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.OnPropertyChanged(System.ComponentModel.PropertyChangedEventArgs)
  - System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.RemoveItem(System.Int32)
  - System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.SetItem(System.Int32,DrawnUi.Draw.SkiaShadow)
  - System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.CollectionChanged
  - System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.PropertyChanged
  - System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.Add(DrawnUi.Draw.SkiaShadow)
  - System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.Clear
  - System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.Contains(DrawnUi.Draw.SkiaShadow)
  - System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.CopyTo(DrawnUi.Draw.SkiaShadow[],System.Int32)
  - System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.GetEnumerator
  - System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.IndexOf(DrawnUi.Draw.SkiaShadow)
  - System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.Insert(System.Int32,DrawnUi.Draw.SkiaShadow)
  - System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.Remove(DrawnUi.Draw.SkiaShadow)
  - System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.RemoveAt(System.Int32)
  - System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.Count
  - System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.Item(System.Int32)
  - System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.Items
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Infrastructure.Xaml.SkiaShadowsCollection.Add(System.Object)
  commentId: M:DrawnUi.Infrastructure.Xaml.SkiaShadowsCollection.Add(System.Object)
  id: Add(System.Object)
  parent: DrawnUi.Infrastructure.Xaml.SkiaShadowsCollection
  langs:
  - csharp
  - vb
  name: Add(object)
  nameWithType: SkiaShadowsCollection.Add(object)
  fullName: DrawnUi.Infrastructure.Xaml.SkiaShadowsCollection.Add(object)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Xaml/SkiaShadowsCollection.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Add
    path: ../src/Maui/DrawnUi/Internals/Xaml/SkiaShadowsCollection.cs
    startLine: 9
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure.Xaml
  summary: Adds an item to the <xref href="System.Collections.IList" data-throw-if-not-resolved="false"></xref>.
  example: []
  syntax:
    content: public int Add(object value)
    parameters:
    - id: value
      type: System.Object
      description: The object to add to the <xref href="System.Collections.IList" data-throw-if-not-resolved="false"></xref>.
    return:
      type: System.Int32
      description: The position into which the new element was inserted, or -1 to indicate that the item was not inserted into the collection.
    content.vb: Public Function Add(value As Object) As Integer
  overload: DrawnUi.Infrastructure.Xaml.SkiaShadowsCollection.Add*
  exceptions:
  - type: System.NotSupportedException
    commentId: T:System.NotSupportedException
    description: >-
      The <xref href="System.Collections.IList" data-throw-if-not-resolved="false"></xref> is read-only.  

       -or-  

       The <xref href="System.Collections.IList" data-throw-if-not-resolved="false"></xref> has a fixed size.
  implements:
  - System.Collections.IList.Add(System.Object)
  nameWithType.vb: SkiaShadowsCollection.Add(Object)
  fullName.vb: DrawnUi.Infrastructure.Xaml.SkiaShadowsCollection.Add(Object)
  name.vb: Add(Object)
references:
- uid: DrawnUi.Infrastructure.Xaml
  commentId: N:DrawnUi.Infrastructure.Xaml
  href: DrawnUi.html
  name: DrawnUi.Infrastructure.Xaml
  nameWithType: DrawnUi.Infrastructure.Xaml
  fullName: DrawnUi.Infrastructure.Xaml
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Infrastructure
    name: Infrastructure
    href: DrawnUi.Infrastructure.html
  - name: .
  - uid: DrawnUi.Infrastructure.Xaml
    name: Xaml
    href: DrawnUi.Infrastructure.Xaml.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Infrastructure
    name: Infrastructure
    href: DrawnUi.Infrastructure.html
  - name: .
  - uid: DrawnUi.Infrastructure.Xaml
    name: Xaml
    href: DrawnUi.Infrastructure.Xaml.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}
  commentId: T:System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}
  parent: System.Collections.ObjectModel
  definition: System.Collections.ObjectModel.Collection`1
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1
  name: Collection<SkiaShadow>
  nameWithType: Collection<SkiaShadow>
  fullName: System.Collections.ObjectModel.Collection<DrawnUi.Draw.SkiaShadow>
  nameWithType.vb: Collection(Of SkiaShadow)
  fullName.vb: System.Collections.ObjectModel.Collection(Of DrawnUi.Draw.SkiaShadow)
  name.vb: Collection(Of SkiaShadow)
  spec.csharp:
  - uid: System.Collections.ObjectModel.Collection`1
    name: Collection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1
  - name: <
  - uid: DrawnUi.Draw.SkiaShadow
    name: SkiaShadow
    href: DrawnUi.Draw.SkiaShadow.html
  - name: '>'
  spec.vb:
  - uid: System.Collections.ObjectModel.Collection`1
    name: Collection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Draw.SkiaShadow
    name: SkiaShadow
    href: DrawnUi.Draw.SkiaShadow.html
  - name: )
- uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}
  commentId: T:System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}
  parent: System.Collections.ObjectModel
  definition: System.Collections.ObjectModel.ObservableCollection`1
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1
  name: ObservableCollection<SkiaShadow>
  nameWithType: ObservableCollection<SkiaShadow>
  fullName: System.Collections.ObjectModel.ObservableCollection<DrawnUi.Draw.SkiaShadow>
  nameWithType.vb: ObservableCollection(Of SkiaShadow)
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of DrawnUi.Draw.SkiaShadow)
  name.vb: ObservableCollection(Of SkiaShadow)
  spec.csharp:
  - uid: System.Collections.ObjectModel.ObservableCollection`1
    name: ObservableCollection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1
  - name: <
  - uid: DrawnUi.Draw.SkiaShadow
    name: SkiaShadow
    href: DrawnUi.Draw.SkiaShadow.html
  - name: '>'
  spec.vb:
  - uid: System.Collections.ObjectModel.ObservableCollection`1
    name: ObservableCollection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Draw.SkiaShadow
    name: SkiaShadow
    href: DrawnUi.Draw.SkiaShadow.html
  - name: )
- uid: System.Collections.Generic.IList{DrawnUi.Draw.SkiaShadow}
  commentId: T:System.Collections.Generic.IList{DrawnUi.Draw.SkiaShadow}
  parent: System.Collections.Generic
  definition: System.Collections.Generic.IList`1
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ilist-1
  name: IList<SkiaShadow>
  nameWithType: IList<SkiaShadow>
  fullName: System.Collections.Generic.IList<DrawnUi.Draw.SkiaShadow>
  nameWithType.vb: IList(Of SkiaShadow)
  fullName.vb: System.Collections.Generic.IList(Of DrawnUi.Draw.SkiaShadow)
  name.vb: IList(Of SkiaShadow)
  spec.csharp:
  - uid: System.Collections.Generic.IList`1
    name: IList
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ilist-1
  - name: <
  - uid: DrawnUi.Draw.SkiaShadow
    name: SkiaShadow
    href: DrawnUi.Draw.SkiaShadow.html
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.IList`1
    name: IList
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ilist-1
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Draw.SkiaShadow
    name: SkiaShadow
    href: DrawnUi.Draw.SkiaShadow.html
  - name: )
- uid: System.Collections.Generic.ICollection{DrawnUi.Draw.SkiaShadow}
  commentId: T:System.Collections.Generic.ICollection{DrawnUi.Draw.SkiaShadow}
  parent: System.Collections.Generic
  definition: System.Collections.Generic.ICollection`1
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.icollection-1
  name: ICollection<SkiaShadow>
  nameWithType: ICollection<SkiaShadow>
  fullName: System.Collections.Generic.ICollection<DrawnUi.Draw.SkiaShadow>
  nameWithType.vb: ICollection(Of SkiaShadow)
  fullName.vb: System.Collections.Generic.ICollection(Of DrawnUi.Draw.SkiaShadow)
  name.vb: ICollection(Of SkiaShadow)
  spec.csharp:
  - uid: System.Collections.Generic.ICollection`1
    name: ICollection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.icollection-1
  - name: <
  - uid: DrawnUi.Draw.SkiaShadow
    name: SkiaShadow
    href: DrawnUi.Draw.SkiaShadow.html
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.ICollection`1
    name: ICollection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.icollection-1
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Draw.SkiaShadow
    name: SkiaShadow
    href: DrawnUi.Draw.SkiaShadow.html
  - name: )
- uid: System.Collections.Generic.IReadOnlyList{DrawnUi.Draw.SkiaShadow}
  commentId: T:System.Collections.Generic.IReadOnlyList{DrawnUi.Draw.SkiaShadow}
  parent: System.Collections.Generic
  definition: System.Collections.Generic.IReadOnlyList`1
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlylist-1
  name: IReadOnlyList<SkiaShadow>
  nameWithType: IReadOnlyList<SkiaShadow>
  fullName: System.Collections.Generic.IReadOnlyList<DrawnUi.Draw.SkiaShadow>
  nameWithType.vb: IReadOnlyList(Of SkiaShadow)
  fullName.vb: System.Collections.Generic.IReadOnlyList(Of DrawnUi.Draw.SkiaShadow)
  name.vb: IReadOnlyList(Of SkiaShadow)
  spec.csharp:
  - uid: System.Collections.Generic.IReadOnlyList`1
    name: IReadOnlyList
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlylist-1
  - name: <
  - uid: DrawnUi.Draw.SkiaShadow
    name: SkiaShadow
    href: DrawnUi.Draw.SkiaShadow.html
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.IReadOnlyList`1
    name: IReadOnlyList
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlylist-1
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Draw.SkiaShadow
    name: SkiaShadow
    href: DrawnUi.Draw.SkiaShadow.html
  - name: )
- uid: System.Collections.Generic.IReadOnlyCollection{DrawnUi.Draw.SkiaShadow}
  commentId: T:System.Collections.Generic.IReadOnlyCollection{DrawnUi.Draw.SkiaShadow}
  parent: System.Collections.Generic
  definition: System.Collections.Generic.IReadOnlyCollection`1
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlycollection-1
  name: IReadOnlyCollection<SkiaShadow>
  nameWithType: IReadOnlyCollection<SkiaShadow>
  fullName: System.Collections.Generic.IReadOnlyCollection<DrawnUi.Draw.SkiaShadow>
  nameWithType.vb: IReadOnlyCollection(Of SkiaShadow)
  fullName.vb: System.Collections.Generic.IReadOnlyCollection(Of DrawnUi.Draw.SkiaShadow)
  name.vb: IReadOnlyCollection(Of SkiaShadow)
  spec.csharp:
  - uid: System.Collections.Generic.IReadOnlyCollection`1
    name: IReadOnlyCollection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlycollection-1
  - name: <
  - uid: DrawnUi.Draw.SkiaShadow
    name: SkiaShadow
    href: DrawnUi.Draw.SkiaShadow.html
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.IReadOnlyCollection`1
    name: IReadOnlyCollection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlycollection-1
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Draw.SkiaShadow
    name: SkiaShadow
    href: DrawnUi.Draw.SkiaShadow.html
  - name: )
- uid: System.Collections.Generic.IEnumerable{DrawnUi.Draw.SkiaShadow}
  commentId: T:System.Collections.Generic.IEnumerable{DrawnUi.Draw.SkiaShadow}
  parent: System.Collections.Generic
  definition: System.Collections.Generic.IEnumerable`1
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1
  name: IEnumerable<SkiaShadow>
  nameWithType: IEnumerable<SkiaShadow>
  fullName: System.Collections.Generic.IEnumerable<DrawnUi.Draw.SkiaShadow>
  nameWithType.vb: IEnumerable(Of SkiaShadow)
  fullName.vb: System.Collections.Generic.IEnumerable(Of DrawnUi.Draw.SkiaShadow)
  name.vb: IEnumerable(Of SkiaShadow)
  spec.csharp:
  - uid: System.Collections.Generic.IEnumerable`1
    name: IEnumerable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1
  - name: <
  - uid: DrawnUi.Draw.SkiaShadow
    name: SkiaShadow
    href: DrawnUi.Draw.SkiaShadow.html
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.IEnumerable`1
    name: IEnumerable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Draw.SkiaShadow
    name: SkiaShadow
    href: DrawnUi.Draw.SkiaShadow.html
  - name: )
- uid: System.Collections.Specialized.INotifyCollectionChanged
  commentId: T:System.Collections.Specialized.INotifyCollectionChanged
  parent: System.Collections.Specialized
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.specialized.inotifycollectionchanged
  name: INotifyCollectionChanged
  nameWithType: INotifyCollectionChanged
  fullName: System.Collections.Specialized.INotifyCollectionChanged
- uid: System.ComponentModel.INotifyPropertyChanged
  commentId: T:System.ComponentModel.INotifyPropertyChanged
  parent: System.ComponentModel
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.inotifypropertychanged
  name: INotifyPropertyChanged
  nameWithType: INotifyPropertyChanged
  fullName: System.ComponentModel.INotifyPropertyChanged
- uid: System.Collections.IList
  commentId: T:System.Collections.IList
  parent: System.Collections
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.ilist
  name: IList
  nameWithType: IList
  fullName: System.Collections.IList
- uid: System.Collections.ICollection
  commentId: T:System.Collections.ICollection
  parent: System.Collections
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.icollection
  name: ICollection
  nameWithType: ICollection
  fullName: System.Collections.ICollection
- uid: System.Collections.IEnumerable
  commentId: T:System.Collections.IEnumerable
  parent: System.Collections
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.ienumerable
  name: IEnumerable
  nameWithType: IEnumerable
  fullName: System.Collections.IEnumerable
- uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.BlockReentrancy
  commentId: M:System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.BlockReentrancy
  parent: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}
  definition: System.Collections.ObjectModel.ObservableCollection`1.BlockReentrancy
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.blockreentrancy
  name: BlockReentrancy()
  nameWithType: ObservableCollection<SkiaShadow>.BlockReentrancy()
  fullName: System.Collections.ObjectModel.ObservableCollection<DrawnUi.Draw.SkiaShadow>.BlockReentrancy()
  nameWithType.vb: ObservableCollection(Of SkiaShadow).BlockReentrancy()
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of DrawnUi.Draw.SkiaShadow).BlockReentrancy()
  spec.csharp:
  - uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.BlockReentrancy
    name: BlockReentrancy
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.blockreentrancy
  - name: (
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.BlockReentrancy
    name: BlockReentrancy
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.blockreentrancy
  - name: (
  - name: )
- uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.CheckReentrancy
  commentId: M:System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.CheckReentrancy
  parent: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}
  definition: System.Collections.ObjectModel.ObservableCollection`1.CheckReentrancy
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.checkreentrancy
  name: CheckReentrancy()
  nameWithType: ObservableCollection<SkiaShadow>.CheckReentrancy()
  fullName: System.Collections.ObjectModel.ObservableCollection<DrawnUi.Draw.SkiaShadow>.CheckReentrancy()
  nameWithType.vb: ObservableCollection(Of SkiaShadow).CheckReentrancy()
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of DrawnUi.Draw.SkiaShadow).CheckReentrancy()
  spec.csharp:
  - uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.CheckReentrancy
    name: CheckReentrancy
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.checkreentrancy
  - name: (
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.CheckReentrancy
    name: CheckReentrancy
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.checkreentrancy
  - name: (
  - name: )
- uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.ClearItems
  commentId: M:System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.ClearItems
  parent: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}
  definition: System.Collections.ObjectModel.ObservableCollection`1.ClearItems
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.clearitems
  name: ClearItems()
  nameWithType: ObservableCollection<SkiaShadow>.ClearItems()
  fullName: System.Collections.ObjectModel.ObservableCollection<DrawnUi.Draw.SkiaShadow>.ClearItems()
  nameWithType.vb: ObservableCollection(Of SkiaShadow).ClearItems()
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of DrawnUi.Draw.SkiaShadow).ClearItems()
  spec.csharp:
  - uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.ClearItems
    name: ClearItems
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.clearitems
  - name: (
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.ClearItems
    name: ClearItems
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.clearitems
  - name: (
  - name: )
- uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.InsertItem(System.Int32,DrawnUi.Draw.SkiaShadow)
  commentId: M:System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.InsertItem(System.Int32,DrawnUi.Draw.SkiaShadow)
  parent: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}
  definition: System.Collections.ObjectModel.ObservableCollection`1.InsertItem(System.Int32,`0)
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.insertitem
  name: InsertItem(int, SkiaShadow)
  nameWithType: ObservableCollection<SkiaShadow>.InsertItem(int, SkiaShadow)
  fullName: System.Collections.ObjectModel.ObservableCollection<DrawnUi.Draw.SkiaShadow>.InsertItem(int, DrawnUi.Draw.SkiaShadow)
  nameWithType.vb: ObservableCollection(Of SkiaShadow).InsertItem(Integer, SkiaShadow)
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of DrawnUi.Draw.SkiaShadow).InsertItem(Integer, DrawnUi.Draw.SkiaShadow)
  name.vb: InsertItem(Integer, SkiaShadow)
  spec.csharp:
  - uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.InsertItem(System.Int32,DrawnUi.Draw.SkiaShadow)
    name: InsertItem
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.insertitem
  - name: (
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: DrawnUi.Draw.SkiaShadow
    name: SkiaShadow
    href: DrawnUi.Draw.SkiaShadow.html
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.InsertItem(System.Int32,DrawnUi.Draw.SkiaShadow)
    name: InsertItem
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.insertitem
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: DrawnUi.Draw.SkiaShadow
    name: SkiaShadow
    href: DrawnUi.Draw.SkiaShadow.html
  - name: )
- uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.Move(System.Int32,System.Int32)
  commentId: M:System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.Move(System.Int32,System.Int32)
  parent: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}
  definition: System.Collections.ObjectModel.ObservableCollection`1.Move(System.Int32,System.Int32)
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.move
  name: Move(int, int)
  nameWithType: ObservableCollection<SkiaShadow>.Move(int, int)
  fullName: System.Collections.ObjectModel.ObservableCollection<DrawnUi.Draw.SkiaShadow>.Move(int, int)
  nameWithType.vb: ObservableCollection(Of SkiaShadow).Move(Integer, Integer)
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of DrawnUi.Draw.SkiaShadow).Move(Integer, Integer)
  name.vb: Move(Integer, Integer)
  spec.csharp:
  - uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.Move(System.Int32,System.Int32)
    name: Move
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.move
  - name: (
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.Move(System.Int32,System.Int32)
    name: Move
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.move
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.MoveItem(System.Int32,System.Int32)
  commentId: M:System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.MoveItem(System.Int32,System.Int32)
  parent: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}
  definition: System.Collections.ObjectModel.ObservableCollection`1.MoveItem(System.Int32,System.Int32)
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.moveitem
  name: MoveItem(int, int)
  nameWithType: ObservableCollection<SkiaShadow>.MoveItem(int, int)
  fullName: System.Collections.ObjectModel.ObservableCollection<DrawnUi.Draw.SkiaShadow>.MoveItem(int, int)
  nameWithType.vb: ObservableCollection(Of SkiaShadow).MoveItem(Integer, Integer)
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of DrawnUi.Draw.SkiaShadow).MoveItem(Integer, Integer)
  name.vb: MoveItem(Integer, Integer)
  spec.csharp:
  - uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.MoveItem(System.Int32,System.Int32)
    name: MoveItem
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.moveitem
  - name: (
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.MoveItem(System.Int32,System.Int32)
    name: MoveItem
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.moveitem
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.OnCollectionChanged(System.Collections.Specialized.NotifyCollectionChangedEventArgs)
  commentId: M:System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.OnCollectionChanged(System.Collections.Specialized.NotifyCollectionChangedEventArgs)
  parent: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}
  definition: System.Collections.ObjectModel.ObservableCollection`1.OnCollectionChanged(System.Collections.Specialized.NotifyCollectionChangedEventArgs)
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.oncollectionchanged
  name: OnCollectionChanged(NotifyCollectionChangedEventArgs)
  nameWithType: ObservableCollection<SkiaShadow>.OnCollectionChanged(NotifyCollectionChangedEventArgs)
  fullName: System.Collections.ObjectModel.ObservableCollection<DrawnUi.Draw.SkiaShadow>.OnCollectionChanged(System.Collections.Specialized.NotifyCollectionChangedEventArgs)
  nameWithType.vb: ObservableCollection(Of SkiaShadow).OnCollectionChanged(NotifyCollectionChangedEventArgs)
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of DrawnUi.Draw.SkiaShadow).OnCollectionChanged(System.Collections.Specialized.NotifyCollectionChangedEventArgs)
  spec.csharp:
  - uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.OnCollectionChanged(System.Collections.Specialized.NotifyCollectionChangedEventArgs)
    name: OnCollectionChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.oncollectionchanged
  - name: (
  - uid: System.Collections.Specialized.NotifyCollectionChangedEventArgs
    name: NotifyCollectionChangedEventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.specialized.notifycollectionchangedeventargs
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.OnCollectionChanged(System.Collections.Specialized.NotifyCollectionChangedEventArgs)
    name: OnCollectionChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.oncollectionchanged
  - name: (
  - uid: System.Collections.Specialized.NotifyCollectionChangedEventArgs
    name: NotifyCollectionChangedEventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.specialized.notifycollectionchangedeventargs
  - name: )
- uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.OnPropertyChanged(System.ComponentModel.PropertyChangedEventArgs)
  commentId: M:System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.OnPropertyChanged(System.ComponentModel.PropertyChangedEventArgs)
  parent: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}
  definition: System.Collections.ObjectModel.ObservableCollection`1.OnPropertyChanged(System.ComponentModel.PropertyChangedEventArgs)
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.onpropertychanged
  name: OnPropertyChanged(PropertyChangedEventArgs)
  nameWithType: ObservableCollection<SkiaShadow>.OnPropertyChanged(PropertyChangedEventArgs)
  fullName: System.Collections.ObjectModel.ObservableCollection<DrawnUi.Draw.SkiaShadow>.OnPropertyChanged(System.ComponentModel.PropertyChangedEventArgs)
  nameWithType.vb: ObservableCollection(Of SkiaShadow).OnPropertyChanged(PropertyChangedEventArgs)
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of DrawnUi.Draw.SkiaShadow).OnPropertyChanged(System.ComponentModel.PropertyChangedEventArgs)
  spec.csharp:
  - uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.OnPropertyChanged(System.ComponentModel.PropertyChangedEventArgs)
    name: OnPropertyChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.onpropertychanged
  - name: (
  - uid: System.ComponentModel.PropertyChangedEventArgs
    name: PropertyChangedEventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.propertychangedeventargs
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.OnPropertyChanged(System.ComponentModel.PropertyChangedEventArgs)
    name: OnPropertyChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.onpropertychanged
  - name: (
  - uid: System.ComponentModel.PropertyChangedEventArgs
    name: PropertyChangedEventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.propertychangedeventargs
  - name: )
- uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.RemoveItem(System.Int32)
  commentId: M:System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.RemoveItem(System.Int32)
  parent: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}
  definition: System.Collections.ObjectModel.ObservableCollection`1.RemoveItem(System.Int32)
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.removeitem
  name: RemoveItem(int)
  nameWithType: ObservableCollection<SkiaShadow>.RemoveItem(int)
  fullName: System.Collections.ObjectModel.ObservableCollection<DrawnUi.Draw.SkiaShadow>.RemoveItem(int)
  nameWithType.vb: ObservableCollection(Of SkiaShadow).RemoveItem(Integer)
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of DrawnUi.Draw.SkiaShadow).RemoveItem(Integer)
  name.vb: RemoveItem(Integer)
  spec.csharp:
  - uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.RemoveItem(System.Int32)
    name: RemoveItem
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.removeitem
  - name: (
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.RemoveItem(System.Int32)
    name: RemoveItem
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.removeitem
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.SetItem(System.Int32,DrawnUi.Draw.SkiaShadow)
  commentId: M:System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.SetItem(System.Int32,DrawnUi.Draw.SkiaShadow)
  parent: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}
  definition: System.Collections.ObjectModel.ObservableCollection`1.SetItem(System.Int32,`0)
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.setitem
  name: SetItem(int, SkiaShadow)
  nameWithType: ObservableCollection<SkiaShadow>.SetItem(int, SkiaShadow)
  fullName: System.Collections.ObjectModel.ObservableCollection<DrawnUi.Draw.SkiaShadow>.SetItem(int, DrawnUi.Draw.SkiaShadow)
  nameWithType.vb: ObservableCollection(Of SkiaShadow).SetItem(Integer, SkiaShadow)
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of DrawnUi.Draw.SkiaShadow).SetItem(Integer, DrawnUi.Draw.SkiaShadow)
  name.vb: SetItem(Integer, SkiaShadow)
  spec.csharp:
  - uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.SetItem(System.Int32,DrawnUi.Draw.SkiaShadow)
    name: SetItem
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.setitem
  - name: (
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: DrawnUi.Draw.SkiaShadow
    name: SkiaShadow
    href: DrawnUi.Draw.SkiaShadow.html
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.SetItem(System.Int32,DrawnUi.Draw.SkiaShadow)
    name: SetItem
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.setitem
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: DrawnUi.Draw.SkiaShadow
    name: SkiaShadow
    href: DrawnUi.Draw.SkiaShadow.html
  - name: )
- uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.CollectionChanged
  commentId: E:System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.CollectionChanged
  parent: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}
  definition: System.Collections.ObjectModel.ObservableCollection`1.CollectionChanged
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.collectionchanged
  name: CollectionChanged
  nameWithType: ObservableCollection<SkiaShadow>.CollectionChanged
  fullName: System.Collections.ObjectModel.ObservableCollection<DrawnUi.Draw.SkiaShadow>.CollectionChanged
  nameWithType.vb: ObservableCollection(Of SkiaShadow).CollectionChanged
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of DrawnUi.Draw.SkiaShadow).CollectionChanged
- uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.PropertyChanged
  commentId: E:System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}.PropertyChanged
  parent: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaShadow}
  definition: System.Collections.ObjectModel.ObservableCollection`1.PropertyChanged
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.propertychanged
  name: PropertyChanged
  nameWithType: ObservableCollection<SkiaShadow>.PropertyChanged
  fullName: System.Collections.ObjectModel.ObservableCollection<DrawnUi.Draw.SkiaShadow>.PropertyChanged
  nameWithType.vb: ObservableCollection(Of SkiaShadow).PropertyChanged
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of DrawnUi.Draw.SkiaShadow).PropertyChanged
- uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.Add(DrawnUi.Draw.SkiaShadow)
  commentId: M:System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.Add(DrawnUi.Draw.SkiaShadow)
  parent: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}
  definition: System.Collections.ObjectModel.Collection`1.Add(`0)
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.add
  name: Add(SkiaShadow)
  nameWithType: Collection<SkiaShadow>.Add(SkiaShadow)
  fullName: System.Collections.ObjectModel.Collection<DrawnUi.Draw.SkiaShadow>.Add(DrawnUi.Draw.SkiaShadow)
  nameWithType.vb: Collection(Of SkiaShadow).Add(SkiaShadow)
  fullName.vb: System.Collections.ObjectModel.Collection(Of DrawnUi.Draw.SkiaShadow).Add(DrawnUi.Draw.SkiaShadow)
  spec.csharp:
  - uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.Add(DrawnUi.Draw.SkiaShadow)
    name: Add
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.add
  - name: (
  - uid: DrawnUi.Draw.SkiaShadow
    name: SkiaShadow
    href: DrawnUi.Draw.SkiaShadow.html
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.Add(DrawnUi.Draw.SkiaShadow)
    name: Add
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.add
  - name: (
  - uid: DrawnUi.Draw.SkiaShadow
    name: SkiaShadow
    href: DrawnUi.Draw.SkiaShadow.html
  - name: )
- uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.Clear
  commentId: M:System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.Clear
  parent: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}
  definition: System.Collections.ObjectModel.Collection`1.Clear
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.clear
  name: Clear()
  nameWithType: Collection<SkiaShadow>.Clear()
  fullName: System.Collections.ObjectModel.Collection<DrawnUi.Draw.SkiaShadow>.Clear()
  nameWithType.vb: Collection(Of SkiaShadow).Clear()
  fullName.vb: System.Collections.ObjectModel.Collection(Of DrawnUi.Draw.SkiaShadow).Clear()
  spec.csharp:
  - uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.Clear
    name: Clear
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.clear
  - name: (
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.Clear
    name: Clear
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.clear
  - name: (
  - name: )
- uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.Contains(DrawnUi.Draw.SkiaShadow)
  commentId: M:System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.Contains(DrawnUi.Draw.SkiaShadow)
  parent: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}
  definition: System.Collections.ObjectModel.Collection`1.Contains(`0)
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.contains
  name: Contains(SkiaShadow)
  nameWithType: Collection<SkiaShadow>.Contains(SkiaShadow)
  fullName: System.Collections.ObjectModel.Collection<DrawnUi.Draw.SkiaShadow>.Contains(DrawnUi.Draw.SkiaShadow)
  nameWithType.vb: Collection(Of SkiaShadow).Contains(SkiaShadow)
  fullName.vb: System.Collections.ObjectModel.Collection(Of DrawnUi.Draw.SkiaShadow).Contains(DrawnUi.Draw.SkiaShadow)
  spec.csharp:
  - uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.Contains(DrawnUi.Draw.SkiaShadow)
    name: Contains
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.contains
  - name: (
  - uid: DrawnUi.Draw.SkiaShadow
    name: SkiaShadow
    href: DrawnUi.Draw.SkiaShadow.html
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.Contains(DrawnUi.Draw.SkiaShadow)
    name: Contains
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.contains
  - name: (
  - uid: DrawnUi.Draw.SkiaShadow
    name: SkiaShadow
    href: DrawnUi.Draw.SkiaShadow.html
  - name: )
- uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.CopyTo(DrawnUi.Draw.SkiaShadow[],System.Int32)
  commentId: M:System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.CopyTo(DrawnUi.Draw.SkiaShadow[],System.Int32)
  parent: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}
  definition: System.Collections.ObjectModel.Collection`1.CopyTo(`0[],System.Int32)
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.copyto
  name: CopyTo(SkiaShadow[], int)
  nameWithType: Collection<SkiaShadow>.CopyTo(SkiaShadow[], int)
  fullName: System.Collections.ObjectModel.Collection<DrawnUi.Draw.SkiaShadow>.CopyTo(DrawnUi.Draw.SkiaShadow[], int)
  nameWithType.vb: Collection(Of SkiaShadow).CopyTo(SkiaShadow(), Integer)
  fullName.vb: System.Collections.ObjectModel.Collection(Of DrawnUi.Draw.SkiaShadow).CopyTo(DrawnUi.Draw.SkiaShadow(), Integer)
  name.vb: CopyTo(SkiaShadow(), Integer)
  spec.csharp:
  - uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.CopyTo(DrawnUi.Draw.SkiaShadow[],System.Int32)
    name: CopyTo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.copyto
  - name: (
  - uid: DrawnUi.Draw.SkiaShadow
    name: SkiaShadow
    href: DrawnUi.Draw.SkiaShadow.html
  - name: '['
  - name: ']'
  - name: ','
  - name: " "
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.CopyTo(DrawnUi.Draw.SkiaShadow[],System.Int32)
    name: CopyTo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.copyto
  - name: (
  - uid: DrawnUi.Draw.SkiaShadow
    name: SkiaShadow
    href: DrawnUi.Draw.SkiaShadow.html
  - name: (
  - name: )
  - name: ','
  - name: " "
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.GetEnumerator
  commentId: M:System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.GetEnumerator
  parent: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}
  definition: System.Collections.ObjectModel.Collection`1.GetEnumerator
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.getenumerator
  name: GetEnumerator()
  nameWithType: Collection<SkiaShadow>.GetEnumerator()
  fullName: System.Collections.ObjectModel.Collection<DrawnUi.Draw.SkiaShadow>.GetEnumerator()
  nameWithType.vb: Collection(Of SkiaShadow).GetEnumerator()
  fullName.vb: System.Collections.ObjectModel.Collection(Of DrawnUi.Draw.SkiaShadow).GetEnumerator()
  spec.csharp:
  - uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.GetEnumerator
    name: GetEnumerator
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.getenumerator
  - name: (
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.GetEnumerator
    name: GetEnumerator
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.getenumerator
  - name: (
  - name: )
- uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.IndexOf(DrawnUi.Draw.SkiaShadow)
  commentId: M:System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.IndexOf(DrawnUi.Draw.SkiaShadow)
  parent: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}
  definition: System.Collections.ObjectModel.Collection`1.IndexOf(`0)
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.indexof
  name: IndexOf(SkiaShadow)
  nameWithType: Collection<SkiaShadow>.IndexOf(SkiaShadow)
  fullName: System.Collections.ObjectModel.Collection<DrawnUi.Draw.SkiaShadow>.IndexOf(DrawnUi.Draw.SkiaShadow)
  nameWithType.vb: Collection(Of SkiaShadow).IndexOf(SkiaShadow)
  fullName.vb: System.Collections.ObjectModel.Collection(Of DrawnUi.Draw.SkiaShadow).IndexOf(DrawnUi.Draw.SkiaShadow)
  spec.csharp:
  - uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.IndexOf(DrawnUi.Draw.SkiaShadow)
    name: IndexOf
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.indexof
  - name: (
  - uid: DrawnUi.Draw.SkiaShadow
    name: SkiaShadow
    href: DrawnUi.Draw.SkiaShadow.html
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.IndexOf(DrawnUi.Draw.SkiaShadow)
    name: IndexOf
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.indexof
  - name: (
  - uid: DrawnUi.Draw.SkiaShadow
    name: SkiaShadow
    href: DrawnUi.Draw.SkiaShadow.html
  - name: )
- uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.Insert(System.Int32,DrawnUi.Draw.SkiaShadow)
  commentId: M:System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.Insert(System.Int32,DrawnUi.Draw.SkiaShadow)
  parent: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}
  definition: System.Collections.ObjectModel.Collection`1.Insert(System.Int32,`0)
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.insert
  name: Insert(int, SkiaShadow)
  nameWithType: Collection<SkiaShadow>.Insert(int, SkiaShadow)
  fullName: System.Collections.ObjectModel.Collection<DrawnUi.Draw.SkiaShadow>.Insert(int, DrawnUi.Draw.SkiaShadow)
  nameWithType.vb: Collection(Of SkiaShadow).Insert(Integer, SkiaShadow)
  fullName.vb: System.Collections.ObjectModel.Collection(Of DrawnUi.Draw.SkiaShadow).Insert(Integer, DrawnUi.Draw.SkiaShadow)
  name.vb: Insert(Integer, SkiaShadow)
  spec.csharp:
  - uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.Insert(System.Int32,DrawnUi.Draw.SkiaShadow)
    name: Insert
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.insert
  - name: (
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: DrawnUi.Draw.SkiaShadow
    name: SkiaShadow
    href: DrawnUi.Draw.SkiaShadow.html
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.Insert(System.Int32,DrawnUi.Draw.SkiaShadow)
    name: Insert
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.insert
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: DrawnUi.Draw.SkiaShadow
    name: SkiaShadow
    href: DrawnUi.Draw.SkiaShadow.html
  - name: )
- uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.Remove(DrawnUi.Draw.SkiaShadow)
  commentId: M:System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.Remove(DrawnUi.Draw.SkiaShadow)
  parent: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}
  definition: System.Collections.ObjectModel.Collection`1.Remove(`0)
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.remove
  name: Remove(SkiaShadow)
  nameWithType: Collection<SkiaShadow>.Remove(SkiaShadow)
  fullName: System.Collections.ObjectModel.Collection<DrawnUi.Draw.SkiaShadow>.Remove(DrawnUi.Draw.SkiaShadow)
  nameWithType.vb: Collection(Of SkiaShadow).Remove(SkiaShadow)
  fullName.vb: System.Collections.ObjectModel.Collection(Of DrawnUi.Draw.SkiaShadow).Remove(DrawnUi.Draw.SkiaShadow)
  spec.csharp:
  - uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.Remove(DrawnUi.Draw.SkiaShadow)
    name: Remove
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.remove
  - name: (
  - uid: DrawnUi.Draw.SkiaShadow
    name: SkiaShadow
    href: DrawnUi.Draw.SkiaShadow.html
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.Remove(DrawnUi.Draw.SkiaShadow)
    name: Remove
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.remove
  - name: (
  - uid: DrawnUi.Draw.SkiaShadow
    name: SkiaShadow
    href: DrawnUi.Draw.SkiaShadow.html
  - name: )
- uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.RemoveAt(System.Int32)
  commentId: M:System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.RemoveAt(System.Int32)
  parent: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}
  definition: System.Collections.ObjectModel.Collection`1.RemoveAt(System.Int32)
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.removeat
  name: RemoveAt(int)
  nameWithType: Collection<SkiaShadow>.RemoveAt(int)
  fullName: System.Collections.ObjectModel.Collection<DrawnUi.Draw.SkiaShadow>.RemoveAt(int)
  nameWithType.vb: Collection(Of SkiaShadow).RemoveAt(Integer)
  fullName.vb: System.Collections.ObjectModel.Collection(Of DrawnUi.Draw.SkiaShadow).RemoveAt(Integer)
  name.vb: RemoveAt(Integer)
  spec.csharp:
  - uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.RemoveAt(System.Int32)
    name: RemoveAt
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.removeat
  - name: (
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.RemoveAt(System.Int32)
    name: RemoveAt
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.removeat
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.Count
  commentId: P:System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.Count
  parent: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}
  definition: System.Collections.ObjectModel.Collection`1.Count
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.count
  name: Count
  nameWithType: Collection<SkiaShadow>.Count
  fullName: System.Collections.ObjectModel.Collection<DrawnUi.Draw.SkiaShadow>.Count
  nameWithType.vb: Collection(Of SkiaShadow).Count
  fullName.vb: System.Collections.ObjectModel.Collection(Of DrawnUi.Draw.SkiaShadow).Count
- uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.Item(System.Int32)
  commentId: P:System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.Item(System.Int32)
  parent: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}
  definition: System.Collections.ObjectModel.Collection`1.Item(System.Int32)
  href: https://learn.microsoft.com/dotnet/api/system.int32
  name: this[int]
  nameWithType: Collection<SkiaShadow>.this[int]
  fullName: System.Collections.ObjectModel.Collection<DrawnUi.Draw.SkiaShadow>.this[int]
  nameWithType.vb: Collection(Of SkiaShadow).this[](Integer)
  fullName.vb: System.Collections.ObjectModel.Collection(Of DrawnUi.Draw.SkiaShadow).this[](Integer)
  name.vb: this[](Integer)
  spec.csharp:
  - name: this
  - name: '['
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ']'
  spec.vb:
  - uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.Item(System.Int32)
    name: this[]
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.item
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.Items
  commentId: P:System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}.Items
  parent: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaShadow}
  definition: System.Collections.ObjectModel.Collection`1.Items
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.items
  name: Items
  nameWithType: Collection<SkiaShadow>.Items
  fullName: System.Collections.ObjectModel.Collection<DrawnUi.Draw.SkiaShadow>.Items
  nameWithType.vb: Collection(Of SkiaShadow).Items
  fullName.vb: System.Collections.ObjectModel.Collection(Of DrawnUi.Draw.SkiaShadow).Items
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: System.Collections.ObjectModel.Collection`1
  commentId: T:System.Collections.ObjectModel.Collection`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1
  name: Collection<T>
  nameWithType: Collection<T>
  fullName: System.Collections.ObjectModel.Collection<T>
  nameWithType.vb: Collection(Of T)
  fullName.vb: System.Collections.ObjectModel.Collection(Of T)
  name.vb: Collection(Of T)
  spec.csharp:
  - uid: System.Collections.ObjectModel.Collection`1
    name: Collection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Collections.ObjectModel.Collection`1
    name: Collection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: System.Collections.ObjectModel
  commentId: N:System.Collections.ObjectModel
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Collections.ObjectModel
  nameWithType: System.Collections.ObjectModel
  fullName: System.Collections.ObjectModel
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.ObjectModel
    name: ObjectModel
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.ObjectModel
    name: ObjectModel
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel
- uid: System.Collections.ObjectModel.ObservableCollection`1
  commentId: T:System.Collections.ObjectModel.ObservableCollection`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1
  name: ObservableCollection<T>
  nameWithType: ObservableCollection<T>
  fullName: System.Collections.ObjectModel.ObservableCollection<T>
  nameWithType.vb: ObservableCollection(Of T)
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of T)
  name.vb: ObservableCollection(Of T)
  spec.csharp:
  - uid: System.Collections.ObjectModel.ObservableCollection`1
    name: ObservableCollection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Collections.ObjectModel.ObservableCollection`1
    name: ObservableCollection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: System.Collections.Generic.IList`1
  commentId: T:System.Collections.Generic.IList`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ilist-1
  name: IList<T>
  nameWithType: IList<T>
  fullName: System.Collections.Generic.IList<T>
  nameWithType.vb: IList(Of T)
  fullName.vb: System.Collections.Generic.IList(Of T)
  name.vb: IList(Of T)
  spec.csharp:
  - uid: System.Collections.Generic.IList`1
    name: IList
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ilist-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.IList`1
    name: IList
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ilist-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: System.Collections.Generic
  commentId: N:System.Collections.Generic
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Collections.Generic
  nameWithType: System.Collections.Generic
  fullName: System.Collections.Generic
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Generic
    name: Generic
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Generic
    name: Generic
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic
- uid: System.Collections.Generic.ICollection`1
  commentId: T:System.Collections.Generic.ICollection`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.icollection-1
  name: ICollection<T>
  nameWithType: ICollection<T>
  fullName: System.Collections.Generic.ICollection<T>
  nameWithType.vb: ICollection(Of T)
  fullName.vb: System.Collections.Generic.ICollection(Of T)
  name.vb: ICollection(Of T)
  spec.csharp:
  - uid: System.Collections.Generic.ICollection`1
    name: ICollection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.icollection-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.ICollection`1
    name: ICollection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.icollection-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: System.Collections.Generic.IReadOnlyList`1
  commentId: T:System.Collections.Generic.IReadOnlyList`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlylist-1
  name: IReadOnlyList<T>
  nameWithType: IReadOnlyList<T>
  fullName: System.Collections.Generic.IReadOnlyList<T>
  nameWithType.vb: IReadOnlyList(Of T)
  fullName.vb: System.Collections.Generic.IReadOnlyList(Of T)
  name.vb: IReadOnlyList(Of T)
  spec.csharp:
  - uid: System.Collections.Generic.IReadOnlyList`1
    name: IReadOnlyList
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlylist-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.IReadOnlyList`1
    name: IReadOnlyList
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlylist-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: System.Collections.Generic.IReadOnlyCollection`1
  commentId: T:System.Collections.Generic.IReadOnlyCollection`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlycollection-1
  name: IReadOnlyCollection<T>
  nameWithType: IReadOnlyCollection<T>
  fullName: System.Collections.Generic.IReadOnlyCollection<T>
  nameWithType.vb: IReadOnlyCollection(Of T)
  fullName.vb: System.Collections.Generic.IReadOnlyCollection(Of T)
  name.vb: IReadOnlyCollection(Of T)
  spec.csharp:
  - uid: System.Collections.Generic.IReadOnlyCollection`1
    name: IReadOnlyCollection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlycollection-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.IReadOnlyCollection`1
    name: IReadOnlyCollection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlycollection-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: System.Collections.Generic.IEnumerable`1
  commentId: T:System.Collections.Generic.IEnumerable`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1
  name: IEnumerable<T>
  nameWithType: IEnumerable<T>
  fullName: System.Collections.Generic.IEnumerable<T>
  nameWithType.vb: IEnumerable(Of T)
  fullName.vb: System.Collections.Generic.IEnumerable(Of T)
  name.vb: IEnumerable(Of T)
  spec.csharp:
  - uid: System.Collections.Generic.IEnumerable`1
    name: IEnumerable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.IEnumerable`1
    name: IEnumerable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: System.Collections.Specialized
  commentId: N:System.Collections.Specialized
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Collections.Specialized
  nameWithType: System.Collections.Specialized
  fullName: System.Collections.Specialized
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Specialized
    name: Specialized
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.specialized
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Specialized
    name: Specialized
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.specialized
- uid: System.ComponentModel
  commentId: N:System.ComponentModel
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.ComponentModel
  nameWithType: System.ComponentModel
  fullName: System.ComponentModel
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.ComponentModel
    name: ComponentModel
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.ComponentModel
    name: ComponentModel
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel
- uid: System.Collections
  commentId: N:System.Collections
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Collections
  nameWithType: System.Collections
  fullName: System.Collections
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
- uid: System.Collections.ObjectModel.ObservableCollection`1.BlockReentrancy
  commentId: M:System.Collections.ObjectModel.ObservableCollection`1.BlockReentrancy
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.blockreentrancy
  name: BlockReentrancy()
  nameWithType: ObservableCollection<T>.BlockReentrancy()
  fullName: System.Collections.ObjectModel.ObservableCollection<T>.BlockReentrancy()
  nameWithType.vb: ObservableCollection(Of T).BlockReentrancy()
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of T).BlockReentrancy()
  spec.csharp:
  - uid: System.Collections.ObjectModel.ObservableCollection`1.BlockReentrancy
    name: BlockReentrancy
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.blockreentrancy
  - name: (
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.ObservableCollection`1.BlockReentrancy
    name: BlockReentrancy
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.blockreentrancy
  - name: (
  - name: )
- uid: System.Collections.ObjectModel.ObservableCollection`1.CheckReentrancy
  commentId: M:System.Collections.ObjectModel.ObservableCollection`1.CheckReentrancy
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.checkreentrancy
  name: CheckReentrancy()
  nameWithType: ObservableCollection<T>.CheckReentrancy()
  fullName: System.Collections.ObjectModel.ObservableCollection<T>.CheckReentrancy()
  nameWithType.vb: ObservableCollection(Of T).CheckReentrancy()
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of T).CheckReentrancy()
  spec.csharp:
  - uid: System.Collections.ObjectModel.ObservableCollection`1.CheckReentrancy
    name: CheckReentrancy
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.checkreentrancy
  - name: (
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.ObservableCollection`1.CheckReentrancy
    name: CheckReentrancy
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.checkreentrancy
  - name: (
  - name: )
- uid: System.Collections.ObjectModel.ObservableCollection`1.ClearItems
  commentId: M:System.Collections.ObjectModel.ObservableCollection`1.ClearItems
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.clearitems
  name: ClearItems()
  nameWithType: ObservableCollection<T>.ClearItems()
  fullName: System.Collections.ObjectModel.ObservableCollection<T>.ClearItems()
  nameWithType.vb: ObservableCollection(Of T).ClearItems()
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of T).ClearItems()
  spec.csharp:
  - uid: System.Collections.ObjectModel.ObservableCollection`1.ClearItems
    name: ClearItems
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.clearitems
  - name: (
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.ObservableCollection`1.ClearItems
    name: ClearItems
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.clearitems
  - name: (
  - name: )
- uid: System.Collections.ObjectModel.ObservableCollection`1.InsertItem(System.Int32,`0)
  commentId: M:System.Collections.ObjectModel.ObservableCollection`1.InsertItem(System.Int32,`0)
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.insertitem
  name: InsertItem(int, T)
  nameWithType: ObservableCollection<T>.InsertItem(int, T)
  fullName: System.Collections.ObjectModel.ObservableCollection<T>.InsertItem(int, T)
  nameWithType.vb: ObservableCollection(Of T).InsertItem(Integer, T)
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of T).InsertItem(Integer, T)
  name.vb: InsertItem(Integer, T)
  spec.csharp:
  - uid: System.Collections.ObjectModel.ObservableCollection`1.InsertItem(System.Int32,`0)
    name: InsertItem
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.insertitem
  - name: (
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - name: T
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.ObservableCollection`1.InsertItem(System.Int32,`0)
    name: InsertItem
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.insertitem
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - name: T
  - name: )
- uid: System.Collections.ObjectModel.ObservableCollection`1.Move(System.Int32,System.Int32)
  commentId: M:System.Collections.ObjectModel.ObservableCollection`1.Move(System.Int32,System.Int32)
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.move
  name: Move(int, int)
  nameWithType: ObservableCollection<T>.Move(int, int)
  fullName: System.Collections.ObjectModel.ObservableCollection<T>.Move(int, int)
  nameWithType.vb: ObservableCollection(Of T).Move(Integer, Integer)
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of T).Move(Integer, Integer)
  name.vb: Move(Integer, Integer)
  spec.csharp:
  - uid: System.Collections.ObjectModel.ObservableCollection`1.Move(System.Int32,System.Int32)
    name: Move
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.move
  - name: (
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.ObservableCollection`1.Move(System.Int32,System.Int32)
    name: Move
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.move
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: System.Collections.ObjectModel.ObservableCollection`1.MoveItem(System.Int32,System.Int32)
  commentId: M:System.Collections.ObjectModel.ObservableCollection`1.MoveItem(System.Int32,System.Int32)
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.moveitem
  name: MoveItem(int, int)
  nameWithType: ObservableCollection<T>.MoveItem(int, int)
  fullName: System.Collections.ObjectModel.ObservableCollection<T>.MoveItem(int, int)
  nameWithType.vb: ObservableCollection(Of T).MoveItem(Integer, Integer)
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of T).MoveItem(Integer, Integer)
  name.vb: MoveItem(Integer, Integer)
  spec.csharp:
  - uid: System.Collections.ObjectModel.ObservableCollection`1.MoveItem(System.Int32,System.Int32)
    name: MoveItem
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.moveitem
  - name: (
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.ObservableCollection`1.MoveItem(System.Int32,System.Int32)
    name: MoveItem
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.moveitem
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: System.Collections.ObjectModel.ObservableCollection`1.OnCollectionChanged(System.Collections.Specialized.NotifyCollectionChangedEventArgs)
  commentId: M:System.Collections.ObjectModel.ObservableCollection`1.OnCollectionChanged(System.Collections.Specialized.NotifyCollectionChangedEventArgs)
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.oncollectionchanged
  name: OnCollectionChanged(NotifyCollectionChangedEventArgs)
  nameWithType: ObservableCollection<T>.OnCollectionChanged(NotifyCollectionChangedEventArgs)
  fullName: System.Collections.ObjectModel.ObservableCollection<T>.OnCollectionChanged(System.Collections.Specialized.NotifyCollectionChangedEventArgs)
  nameWithType.vb: ObservableCollection(Of T).OnCollectionChanged(NotifyCollectionChangedEventArgs)
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of T).OnCollectionChanged(System.Collections.Specialized.NotifyCollectionChangedEventArgs)
  spec.csharp:
  - uid: System.Collections.ObjectModel.ObservableCollection`1.OnCollectionChanged(System.Collections.Specialized.NotifyCollectionChangedEventArgs)
    name: OnCollectionChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.oncollectionchanged
  - name: (
  - uid: System.Collections.Specialized.NotifyCollectionChangedEventArgs
    name: NotifyCollectionChangedEventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.specialized.notifycollectionchangedeventargs
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.ObservableCollection`1.OnCollectionChanged(System.Collections.Specialized.NotifyCollectionChangedEventArgs)
    name: OnCollectionChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.oncollectionchanged
  - name: (
  - uid: System.Collections.Specialized.NotifyCollectionChangedEventArgs
    name: NotifyCollectionChangedEventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.specialized.notifycollectionchangedeventargs
  - name: )
- uid: System.Collections.ObjectModel.ObservableCollection`1.OnPropertyChanged(System.ComponentModel.PropertyChangedEventArgs)
  commentId: M:System.Collections.ObjectModel.ObservableCollection`1.OnPropertyChanged(System.ComponentModel.PropertyChangedEventArgs)
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.onpropertychanged
  name: OnPropertyChanged(PropertyChangedEventArgs)
  nameWithType: ObservableCollection<T>.OnPropertyChanged(PropertyChangedEventArgs)
  fullName: System.Collections.ObjectModel.ObservableCollection<T>.OnPropertyChanged(System.ComponentModel.PropertyChangedEventArgs)
  nameWithType.vb: ObservableCollection(Of T).OnPropertyChanged(PropertyChangedEventArgs)
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of T).OnPropertyChanged(System.ComponentModel.PropertyChangedEventArgs)
  spec.csharp:
  - uid: System.Collections.ObjectModel.ObservableCollection`1.OnPropertyChanged(System.ComponentModel.PropertyChangedEventArgs)
    name: OnPropertyChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.onpropertychanged
  - name: (
  - uid: System.ComponentModel.PropertyChangedEventArgs
    name: PropertyChangedEventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.propertychangedeventargs
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.ObservableCollection`1.OnPropertyChanged(System.ComponentModel.PropertyChangedEventArgs)
    name: OnPropertyChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.onpropertychanged
  - name: (
  - uid: System.ComponentModel.PropertyChangedEventArgs
    name: PropertyChangedEventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.propertychangedeventargs
  - name: )
- uid: System.Collections.ObjectModel.ObservableCollection`1.RemoveItem(System.Int32)
  commentId: M:System.Collections.ObjectModel.ObservableCollection`1.RemoveItem(System.Int32)
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.removeitem
  name: RemoveItem(int)
  nameWithType: ObservableCollection<T>.RemoveItem(int)
  fullName: System.Collections.ObjectModel.ObservableCollection<T>.RemoveItem(int)
  nameWithType.vb: ObservableCollection(Of T).RemoveItem(Integer)
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of T).RemoveItem(Integer)
  name.vb: RemoveItem(Integer)
  spec.csharp:
  - uid: System.Collections.ObjectModel.ObservableCollection`1.RemoveItem(System.Int32)
    name: RemoveItem
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.removeitem
  - name: (
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.ObservableCollection`1.RemoveItem(System.Int32)
    name: RemoveItem
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.removeitem
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: System.Collections.ObjectModel.ObservableCollection`1.SetItem(System.Int32,`0)
  commentId: M:System.Collections.ObjectModel.ObservableCollection`1.SetItem(System.Int32,`0)
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.setitem
  name: SetItem(int, T)
  nameWithType: ObservableCollection<T>.SetItem(int, T)
  fullName: System.Collections.ObjectModel.ObservableCollection<T>.SetItem(int, T)
  nameWithType.vb: ObservableCollection(Of T).SetItem(Integer, T)
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of T).SetItem(Integer, T)
  name.vb: SetItem(Integer, T)
  spec.csharp:
  - uid: System.Collections.ObjectModel.ObservableCollection`1.SetItem(System.Int32,`0)
    name: SetItem
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.setitem
  - name: (
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - name: T
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.ObservableCollection`1.SetItem(System.Int32,`0)
    name: SetItem
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.setitem
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - name: T
  - name: )
- uid: System.Collections.ObjectModel.ObservableCollection`1.CollectionChanged
  commentId: E:System.Collections.ObjectModel.ObservableCollection`1.CollectionChanged
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.collectionchanged
  name: CollectionChanged
  nameWithType: ObservableCollection<T>.CollectionChanged
  fullName: System.Collections.ObjectModel.ObservableCollection<T>.CollectionChanged
  nameWithType.vb: ObservableCollection(Of T).CollectionChanged
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of T).CollectionChanged
- uid: System.Collections.ObjectModel.ObservableCollection`1.PropertyChanged
  commentId: E:System.Collections.ObjectModel.ObservableCollection`1.PropertyChanged
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.propertychanged
  name: PropertyChanged
  nameWithType: ObservableCollection<T>.PropertyChanged
  fullName: System.Collections.ObjectModel.ObservableCollection<T>.PropertyChanged
  nameWithType.vb: ObservableCollection(Of T).PropertyChanged
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of T).PropertyChanged
- uid: System.Collections.ObjectModel.Collection`1.Add(`0)
  commentId: M:System.Collections.ObjectModel.Collection`1.Add(`0)
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.add
  name: Add(T)
  nameWithType: Collection<T>.Add(T)
  fullName: System.Collections.ObjectModel.Collection<T>.Add(T)
  nameWithType.vb: Collection(Of T).Add(T)
  fullName.vb: System.Collections.ObjectModel.Collection(Of T).Add(T)
  spec.csharp:
  - uid: System.Collections.ObjectModel.Collection`1.Add(`0)
    name: Add
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.add
  - name: (
  - name: T
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.Collection`1.Add(`0)
    name: Add
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.add
  - name: (
  - name: T
  - name: )
- uid: System.Collections.ObjectModel.Collection`1.Clear
  commentId: M:System.Collections.ObjectModel.Collection`1.Clear
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.clear
  name: Clear()
  nameWithType: Collection<T>.Clear()
  fullName: System.Collections.ObjectModel.Collection<T>.Clear()
  nameWithType.vb: Collection(Of T).Clear()
  fullName.vb: System.Collections.ObjectModel.Collection(Of T).Clear()
  spec.csharp:
  - uid: System.Collections.ObjectModel.Collection`1.Clear
    name: Clear
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.clear
  - name: (
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.Collection`1.Clear
    name: Clear
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.clear
  - name: (
  - name: )
- uid: System.Collections.ObjectModel.Collection`1.Contains(`0)
  commentId: M:System.Collections.ObjectModel.Collection`1.Contains(`0)
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.contains
  name: Contains(T)
  nameWithType: Collection<T>.Contains(T)
  fullName: System.Collections.ObjectModel.Collection<T>.Contains(T)
  nameWithType.vb: Collection(Of T).Contains(T)
  fullName.vb: System.Collections.ObjectModel.Collection(Of T).Contains(T)
  spec.csharp:
  - uid: System.Collections.ObjectModel.Collection`1.Contains(`0)
    name: Contains
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.contains
  - name: (
  - name: T
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.Collection`1.Contains(`0)
    name: Contains
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.contains
  - name: (
  - name: T
  - name: )
- uid: System.Collections.ObjectModel.Collection`1.CopyTo(`0[],System.Int32)
  commentId: M:System.Collections.ObjectModel.Collection`1.CopyTo(`0[],System.Int32)
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.copyto
  name: CopyTo(T[], int)
  nameWithType: Collection<T>.CopyTo(T[], int)
  fullName: System.Collections.ObjectModel.Collection<T>.CopyTo(T[], int)
  nameWithType.vb: Collection(Of T).CopyTo(T(), Integer)
  fullName.vb: System.Collections.ObjectModel.Collection(Of T).CopyTo(T(), Integer)
  name.vb: CopyTo(T(), Integer)
  spec.csharp:
  - uid: System.Collections.ObjectModel.Collection`1.CopyTo(`0[],System.Int32)
    name: CopyTo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.copyto
  - name: (
  - name: T
  - name: '['
  - name: ']'
  - name: ','
  - name: " "
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.Collection`1.CopyTo(`0[],System.Int32)
    name: CopyTo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.copyto
  - name: (
  - name: T
  - name: (
  - name: )
  - name: ','
  - name: " "
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: System.Collections.ObjectModel.Collection`1.GetEnumerator
  commentId: M:System.Collections.ObjectModel.Collection`1.GetEnumerator
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.getenumerator
  name: GetEnumerator()
  nameWithType: Collection<T>.GetEnumerator()
  fullName: System.Collections.ObjectModel.Collection<T>.GetEnumerator()
  nameWithType.vb: Collection(Of T).GetEnumerator()
  fullName.vb: System.Collections.ObjectModel.Collection(Of T).GetEnumerator()
  spec.csharp:
  - uid: System.Collections.ObjectModel.Collection`1.GetEnumerator
    name: GetEnumerator
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.getenumerator
  - name: (
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.Collection`1.GetEnumerator
    name: GetEnumerator
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.getenumerator
  - name: (
  - name: )
- uid: System.Collections.ObjectModel.Collection`1.IndexOf(`0)
  commentId: M:System.Collections.ObjectModel.Collection`1.IndexOf(`0)
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.indexof
  name: IndexOf(T)
  nameWithType: Collection<T>.IndexOf(T)
  fullName: System.Collections.ObjectModel.Collection<T>.IndexOf(T)
  nameWithType.vb: Collection(Of T).IndexOf(T)
  fullName.vb: System.Collections.ObjectModel.Collection(Of T).IndexOf(T)
  spec.csharp:
  - uid: System.Collections.ObjectModel.Collection`1.IndexOf(`0)
    name: IndexOf
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.indexof
  - name: (
  - name: T
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.Collection`1.IndexOf(`0)
    name: IndexOf
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.indexof
  - name: (
  - name: T
  - name: )
- uid: System.Collections.ObjectModel.Collection`1.Insert(System.Int32,`0)
  commentId: M:System.Collections.ObjectModel.Collection`1.Insert(System.Int32,`0)
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.insert
  name: Insert(int, T)
  nameWithType: Collection<T>.Insert(int, T)
  fullName: System.Collections.ObjectModel.Collection<T>.Insert(int, T)
  nameWithType.vb: Collection(Of T).Insert(Integer, T)
  fullName.vb: System.Collections.ObjectModel.Collection(Of T).Insert(Integer, T)
  name.vb: Insert(Integer, T)
  spec.csharp:
  - uid: System.Collections.ObjectModel.Collection`1.Insert(System.Int32,`0)
    name: Insert
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.insert
  - name: (
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - name: T
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.Collection`1.Insert(System.Int32,`0)
    name: Insert
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.insert
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - name: T
  - name: )
- uid: System.Collections.ObjectModel.Collection`1.Remove(`0)
  commentId: M:System.Collections.ObjectModel.Collection`1.Remove(`0)
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.remove
  name: Remove(T)
  nameWithType: Collection<T>.Remove(T)
  fullName: System.Collections.ObjectModel.Collection<T>.Remove(T)
  nameWithType.vb: Collection(Of T).Remove(T)
  fullName.vb: System.Collections.ObjectModel.Collection(Of T).Remove(T)
  spec.csharp:
  - uid: System.Collections.ObjectModel.Collection`1.Remove(`0)
    name: Remove
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.remove
  - name: (
  - name: T
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.Collection`1.Remove(`0)
    name: Remove
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.remove
  - name: (
  - name: T
  - name: )
- uid: System.Collections.ObjectModel.Collection`1.RemoveAt(System.Int32)
  commentId: M:System.Collections.ObjectModel.Collection`1.RemoveAt(System.Int32)
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.removeat
  name: RemoveAt(int)
  nameWithType: Collection<T>.RemoveAt(int)
  fullName: System.Collections.ObjectModel.Collection<T>.RemoveAt(int)
  nameWithType.vb: Collection(Of T).RemoveAt(Integer)
  fullName.vb: System.Collections.ObjectModel.Collection(Of T).RemoveAt(Integer)
  name.vb: RemoveAt(Integer)
  spec.csharp:
  - uid: System.Collections.ObjectModel.Collection`1.RemoveAt(System.Int32)
    name: RemoveAt
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.removeat
  - name: (
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.Collection`1.RemoveAt(System.Int32)
    name: RemoveAt
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.removeat
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: System.Collections.ObjectModel.Collection`1.Count
  commentId: P:System.Collections.ObjectModel.Collection`1.Count
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.count
  name: Count
  nameWithType: Collection<T>.Count
  fullName: System.Collections.ObjectModel.Collection<T>.Count
  nameWithType.vb: Collection(Of T).Count
  fullName.vb: System.Collections.ObjectModel.Collection(Of T).Count
- uid: System.Collections.ObjectModel.Collection`1.Item(System.Int32)
  commentId: P:System.Collections.ObjectModel.Collection`1.Item(System.Int32)
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.int32
  name: this[int]
  nameWithType: Collection<T>.this[int]
  fullName: System.Collections.ObjectModel.Collection<T>.this[int]
  nameWithType.vb: Collection(Of T).this[](Integer)
  fullName.vb: System.Collections.ObjectModel.Collection(Of T).this[](Integer)
  name.vb: this[](Integer)
  spec.csharp:
  - name: this
  - name: '['
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ']'
  spec.vb:
  - uid: System.Collections.ObjectModel.Collection`1.Item(System.Int32)
    name: this[]
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.item
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: System.Collections.ObjectModel.Collection`1.Items
  commentId: P:System.Collections.ObjectModel.Collection`1.Items
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.items
  name: Items
  nameWithType: Collection<T>.Items
  fullName: System.Collections.ObjectModel.Collection<T>.Items
  nameWithType.vb: Collection(Of T).Items
  fullName.vb: System.Collections.ObjectModel.Collection(Of T).Items
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: System.NotSupportedException
  commentId: T:System.NotSupportedException
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.notsupportedexception
  name: NotSupportedException
  nameWithType: NotSupportedException
  fullName: System.NotSupportedException
- uid: DrawnUi.Infrastructure.Xaml.SkiaShadowsCollection.Add*
  commentId: Overload:DrawnUi.Infrastructure.Xaml.SkiaShadowsCollection.Add
  href: DrawnUi.Infrastructure.Xaml.SkiaShadowsCollection.html#DrawnUi_Infrastructure_Xaml_SkiaShadowsCollection_Add_System_Object_
  name: Add
  nameWithType: SkiaShadowsCollection.Add
  fullName: DrawnUi.Infrastructure.Xaml.SkiaShadowsCollection.Add
- uid: System.Collections.IList.Add(System.Object)
  commentId: M:System.Collections.IList.Add(System.Object)
  parent: System.Collections.IList
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.ilist.add
  name: Add(object)
  nameWithType: IList.Add(object)
  fullName: System.Collections.IList.Add(object)
  nameWithType.vb: IList.Add(Object)
  fullName.vb: System.Collections.IList.Add(Object)
  name.vb: Add(Object)
  spec.csharp:
  - uid: System.Collections.IList.Add(System.Object)
    name: Add
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.ilist.add
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Collections.IList.Add(System.Object)
    name: Add
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.ilist.add
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Int32
  commentId: T:System.Int32
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.int32
  name: int
  nameWithType: int
  fullName: int
  nameWithType.vb: Integer
  fullName.vb: Integer
  name.vb: Integer
