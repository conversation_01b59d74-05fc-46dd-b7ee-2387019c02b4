### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.TextLine
  commentId: T:DrawnUi.Draw.TextLine
  id: TextLine
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.TextLine.#ctor
  - DrawnUi.Draw.TextLine.Bounds
  - DrawnUi.Draw.TextLine.Height
  - DrawnUi.Draw.TextLine.IsLastInParagraph
  - DrawnUi.Draw.TextLine.IsNewParagraph
  - DrawnUi.Draw.TextLine.Spans
  - DrawnUi.Draw.TextLine.Value
  - DrawnUi.Draw.TextLine.Width
  langs:
  - csharp
  - vb
  name: TextLine
  nameWithType: TextLine
  fullName: DrawnUi.Draw.TextLine
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextLine.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: TextLine
    path: ../src/Maui/DrawnUi/Draw/Text/TextLine.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public class TextLine
    content.vb: Public Class TextLine
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.TextLine.Bounds
  commentId: P:DrawnUi.Draw.TextLine.Bounds
  id: Bounds
  parent: DrawnUi.Draw.TextLine
  langs:
  - csharp
  - vb
  name: Bounds
  nameWithType: TextLine.Bounds
  fullName: DrawnUi.Draw.TextLine.Bounds
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextLine.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Bounds
    path: ../src/Maui/DrawnUi/Draw/Text/TextLine.cs
    startLine: 7
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Set during rendering
  example: []
  syntax:
    content: public SKRect Bounds { get; set; }
    parameters: []
    return:
      type: SkiaSharp.SKRect
    content.vb: Public Property Bounds As SKRect
  overload: DrawnUi.Draw.TextLine.Bounds*
- uid: DrawnUi.Draw.TextLine.Value
  commentId: P:DrawnUi.Draw.TextLine.Value
  id: Value
  parent: DrawnUi.Draw.TextLine
  langs:
  - csharp
  - vb
  name: Value
  nameWithType: TextLine.Value
  fullName: DrawnUi.Draw.TextLine.Value
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextLine.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Value
    path: ../src/Maui/DrawnUi/Draw/Text/TextLine.cs
    startLine: 9
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public string Value { get; set; }
    parameters: []
    return:
      type: System.String
    content.vb: Public Property Value As String
  overload: DrawnUi.Draw.TextLine.Value*
- uid: DrawnUi.Draw.TextLine.IsNewParagraph
  commentId: P:DrawnUi.Draw.TextLine.IsNewParagraph
  id: IsNewParagraph
  parent: DrawnUi.Draw.TextLine
  langs:
  - csharp
  - vb
  name: IsNewParagraph
  nameWithType: TextLine.IsNewParagraph
  fullName: DrawnUi.Draw.TextLine.IsNewParagraph
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextLine.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IsNewParagraph
    path: ../src/Maui/DrawnUi/Draw/Text/TextLine.cs
    startLine: 11
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public bool IsNewParagraph { get; set; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property IsNewParagraph As Boolean
  overload: DrawnUi.Draw.TextLine.IsNewParagraph*
- uid: DrawnUi.Draw.TextLine.IsLastInParagraph
  commentId: P:DrawnUi.Draw.TextLine.IsLastInParagraph
  id: IsLastInParagraph
  parent: DrawnUi.Draw.TextLine
  langs:
  - csharp
  - vb
  name: IsLastInParagraph
  nameWithType: TextLine.IsLastInParagraph
  fullName: DrawnUi.Draw.TextLine.IsLastInParagraph
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextLine.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IsLastInParagraph
    path: ../src/Maui/DrawnUi/Draw/Text/TextLine.cs
    startLine: 12
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public bool IsLastInParagraph { get; set; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property IsLastInParagraph As Boolean
  overload: DrawnUi.Draw.TextLine.IsLastInParagraph*
- uid: DrawnUi.Draw.TextLine.Width
  commentId: P:DrawnUi.Draw.TextLine.Width
  id: Width
  parent: DrawnUi.Draw.TextLine
  langs:
  - csharp
  - vb
  name: Width
  nameWithType: TextLine.Width
  fullName: DrawnUi.Draw.TextLine.Width
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextLine.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Width
    path: ../src/Maui/DrawnUi/Draw/Text/TextLine.cs
    startLine: 14
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public float Width { get; set; }
    parameters: []
    return:
      type: System.Single
    content.vb: Public Property Width As Single
  overload: DrawnUi.Draw.TextLine.Width*
- uid: DrawnUi.Draw.TextLine.Height
  commentId: P:DrawnUi.Draw.TextLine.Height
  id: Height
  parent: DrawnUi.Draw.TextLine
  langs:
  - csharp
  - vb
  name: Height
  nameWithType: TextLine.Height
  fullName: DrawnUi.Draw.TextLine.Height
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextLine.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Height
    path: ../src/Maui/DrawnUi/Draw/Text/TextLine.cs
    startLine: 15
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public float Height { get; set; }
    parameters: []
    return:
      type: System.Single
    content.vb: Public Property Height As Single
  overload: DrawnUi.Draw.TextLine.Height*
- uid: DrawnUi.Draw.TextLine.Spans
  commentId: P:DrawnUi.Draw.TextLine.Spans
  id: Spans
  parent: DrawnUi.Draw.TextLine
  langs:
  - csharp
  - vb
  name: Spans
  nameWithType: TextLine.Spans
  fullName: DrawnUi.Draw.TextLine.Spans
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextLine.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Spans
    path: ../src/Maui/DrawnUi/Draw/Text/TextLine.cs
    startLine: 21
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public List<LineSpan> Spans { get; set; }
    parameters: []
    return:
      type: System.Collections.Generic.List{DrawnUi.Draw.LineSpan}
    content.vb: Public Property Spans As List(Of LineSpan)
  overload: DrawnUi.Draw.TextLine.Spans*
- uid: DrawnUi.Draw.TextLine.#ctor
  commentId: M:DrawnUi.Draw.TextLine.#ctor
  id: '#ctor'
  parent: DrawnUi.Draw.TextLine
  langs:
  - csharp
  - vb
  name: TextLine()
  nameWithType: TextLine.TextLine()
  fullName: DrawnUi.Draw.TextLine.TextLine()
  type: Constructor
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextLine.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Maui/DrawnUi/Draw/Text/TextLine.cs
    startLine: 23
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public TextLine()
    content.vb: Public Sub New()
  overload: DrawnUi.Draw.TextLine.#ctor*
  nameWithType.vb: TextLine.New()
  fullName.vb: DrawnUi.Draw.TextLine.New()
  name.vb: New()
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.TextLine.Bounds*
  commentId: Overload:DrawnUi.Draw.TextLine.Bounds
  href: DrawnUi.Draw.TextLine.html#DrawnUi_Draw_TextLine_Bounds
  name: Bounds
  nameWithType: TextLine.Bounds
  fullName: DrawnUi.Draw.TextLine.Bounds
- uid: SkiaSharp.SKRect
  commentId: T:SkiaSharp.SKRect
  parent: SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.skrect
  name: SKRect
  nameWithType: SKRect
  fullName: SkiaSharp.SKRect
- uid: SkiaSharp
  commentId: N:SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp
  name: SkiaSharp
  nameWithType: SkiaSharp
  fullName: SkiaSharp
- uid: DrawnUi.Draw.TextLine.Value*
  commentId: Overload:DrawnUi.Draw.TextLine.Value
  href: DrawnUi.Draw.TextLine.html#DrawnUi_Draw_TextLine_Value
  name: Value
  nameWithType: TextLine.Value
  fullName: DrawnUi.Draw.TextLine.Value
- uid: System.String
  commentId: T:System.String
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.string
  name: string
  nameWithType: string
  fullName: string
  nameWithType.vb: String
  fullName.vb: String
  name.vb: String
- uid: DrawnUi.Draw.TextLine.IsNewParagraph*
  commentId: Overload:DrawnUi.Draw.TextLine.IsNewParagraph
  href: DrawnUi.Draw.TextLine.html#DrawnUi_Draw_TextLine_IsNewParagraph
  name: IsNewParagraph
  nameWithType: TextLine.IsNewParagraph
  fullName: DrawnUi.Draw.TextLine.IsNewParagraph
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: DrawnUi.Draw.TextLine.IsLastInParagraph*
  commentId: Overload:DrawnUi.Draw.TextLine.IsLastInParagraph
  href: DrawnUi.Draw.TextLine.html#DrawnUi_Draw_TextLine_IsLastInParagraph
  name: IsLastInParagraph
  nameWithType: TextLine.IsLastInParagraph
  fullName: DrawnUi.Draw.TextLine.IsLastInParagraph
- uid: DrawnUi.Draw.TextLine.Width*
  commentId: Overload:DrawnUi.Draw.TextLine.Width
  href: DrawnUi.Draw.TextLine.html#DrawnUi_Draw_TextLine_Width
  name: Width
  nameWithType: TextLine.Width
  fullName: DrawnUi.Draw.TextLine.Width
- uid: System.Single
  commentId: T:System.Single
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.single
  name: float
  nameWithType: float
  fullName: float
  nameWithType.vb: Single
  fullName.vb: Single
  name.vb: Single
- uid: DrawnUi.Draw.TextLine.Height*
  commentId: Overload:DrawnUi.Draw.TextLine.Height
  href: DrawnUi.Draw.TextLine.html#DrawnUi_Draw_TextLine_Height
  name: Height
  nameWithType: TextLine.Height
  fullName: DrawnUi.Draw.TextLine.Height
- uid: DrawnUi.Draw.TextLine.Spans*
  commentId: Overload:DrawnUi.Draw.TextLine.Spans
  href: DrawnUi.Draw.TextLine.html#DrawnUi_Draw_TextLine_Spans
  name: Spans
  nameWithType: TextLine.Spans
  fullName: DrawnUi.Draw.TextLine.Spans
- uid: System.Collections.Generic.List{DrawnUi.Draw.LineSpan}
  commentId: T:System.Collections.Generic.List{DrawnUi.Draw.LineSpan}
  parent: System.Collections.Generic
  definition: System.Collections.Generic.List`1
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  name: List<LineSpan>
  nameWithType: List<LineSpan>
  fullName: System.Collections.Generic.List<DrawnUi.Draw.LineSpan>
  nameWithType.vb: List(Of LineSpan)
  fullName.vb: System.Collections.Generic.List(Of DrawnUi.Draw.LineSpan)
  name.vb: List(Of LineSpan)
  spec.csharp:
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: <
  - uid: DrawnUi.Draw.LineSpan
    name: LineSpan
    href: DrawnUi.Draw.LineSpan.html
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Draw.LineSpan
    name: LineSpan
    href: DrawnUi.Draw.LineSpan.html
  - name: )
- uid: System.Collections.Generic.List`1
  commentId: T:System.Collections.Generic.List`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  name: List<T>
  nameWithType: List<T>
  fullName: System.Collections.Generic.List<T>
  nameWithType.vb: List(Of T)
  fullName.vb: System.Collections.Generic.List(Of T)
  name.vb: List(Of T)
  spec.csharp:
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: System.Collections.Generic
  commentId: N:System.Collections.Generic
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Collections.Generic
  nameWithType: System.Collections.Generic
  fullName: System.Collections.Generic
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Generic
    name: Generic
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Generic
    name: Generic
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic
- uid: DrawnUi.Draw.TextLine.#ctor*
  commentId: Overload:DrawnUi.Draw.TextLine.#ctor
  href: DrawnUi.Draw.TextLine.html#DrawnUi_Draw_TextLine__ctor
  name: TextLine
  nameWithType: TextLine.TextLine
  fullName: DrawnUi.Draw.TextLine.TextLine
  nameWithType.vb: TextLine.New
  fullName.vb: DrawnUi.Draw.TextLine.New
  name.vb: New
