### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.ShapeType
  commentId: T:DrawnUi.Draw.ShapeType
  id: ShapeType
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.ShapeType.Arc
  - DrawnUi.Draw.ShapeType.Circle
  - DrawnUi.Draw.ShapeType.Custom
  - DrawnUi.Draw.ShapeType.Ellipse
  - DrawnUi.Draw.ShapeType.Line
  - DrawnUi.Draw.ShapeType.Path
  - DrawnUi.Draw.ShapeType.Polygon
  - DrawnUi.Draw.ShapeType.Rectangle
  - DrawnUi.Draw.ShapeType.Squricle
  langs:
  - csharp
  - vb
  name: ShapeType
  nameWithType: ShapeType
  fullName: DrawnUi.Draw.ShapeType
  type: Enum
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/ShapeType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ShapeType
    path: ../src/Shared/Draw/Internals/Enums/ShapeType.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public enum ShapeType
    content.vb: Public Enum ShapeType
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.ShapeType.Rectangle
  commentId: F:DrawnUi.Draw.ShapeType.Rectangle
  id: Rectangle
  parent: DrawnUi.Draw.ShapeType
  langs:
  - csharp
  - vb
  name: Rectangle
  nameWithType: ShapeType.Rectangle
  fullName: DrawnUi.Draw.ShapeType.Rectangle
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/ShapeType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Rectangle
    path: ../src/Shared/Draw/Internals/Enums/ShapeType.cs
    startLine: 7
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Default type for SkiaShape
  example: []
  syntax:
    content: Rectangle = 0
    return:
      type: DrawnUi.Draw.ShapeType
- uid: DrawnUi.Draw.ShapeType.Circle
  commentId: F:DrawnUi.Draw.ShapeType.Circle
  id: Circle
  parent: DrawnUi.Draw.ShapeType
  langs:
  - csharp
  - vb
  name: Circle
  nameWithType: ShapeType.Circle
  fullName: DrawnUi.Draw.ShapeType.Circle
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/ShapeType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Circle
    path: ../src/Shared/Draw/Internals/Enums/ShapeType.cs
    startLine: 9
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: Circle = 1
    return:
      type: DrawnUi.Draw.ShapeType
- uid: DrawnUi.Draw.ShapeType.Ellipse
  commentId: F:DrawnUi.Draw.ShapeType.Ellipse
  id: Ellipse
  parent: DrawnUi.Draw.ShapeType
  langs:
  - csharp
  - vb
  name: Ellipse
  nameWithType: ShapeType.Ellipse
  fullName: DrawnUi.Draw.ShapeType.Ellipse
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/ShapeType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Ellipse
    path: ../src/Shared/Draw/Internals/Enums/ShapeType.cs
    startLine: 11
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: Ellipse = 2
    return:
      type: DrawnUi.Draw.ShapeType
- uid: DrawnUi.Draw.ShapeType.Arc
  commentId: F:DrawnUi.Draw.ShapeType.Arc
  id: Arc
  parent: DrawnUi.Draw.ShapeType
  langs:
  - csharp
  - vb
  name: Arc
  nameWithType: ShapeType.Arc
  fullName: DrawnUi.Draw.ShapeType.Arc
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/ShapeType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Arc
    path: ../src/Shared/Draw/Internals/Enums/ShapeType.cs
    startLine: 13
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: Arc = 3
    return:
      type: DrawnUi.Draw.ShapeType
- uid: DrawnUi.Draw.ShapeType.Squricle
  commentId: F:DrawnUi.Draw.ShapeType.Squricle
  id: Squricle
  parent: DrawnUi.Draw.ShapeType
  langs:
  - csharp
  - vb
  name: Squricle
  nameWithType: ShapeType.Squricle
  fullName: DrawnUi.Draw.ShapeType.Squricle
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/ShapeType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Squricle
    path: ../src/Shared/Draw/Internals/Enums/ShapeType.cs
    startLine: 18
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: TODO unimplemented yet
  example: []
  syntax:
    content: Squricle = 4
    return:
      type: DrawnUi.Draw.ShapeType
- uid: DrawnUi.Draw.ShapeType.Path
  commentId: F:DrawnUi.Draw.ShapeType.Path
  id: Path
  parent: DrawnUi.Draw.ShapeType
  langs:
  - csharp
  - vb
  name: Path
  nameWithType: ShapeType.Path
  fullName: DrawnUi.Draw.ShapeType.Path
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/ShapeType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Path
    path: ../src/Shared/Draw/Internals/Enums/ShapeType.cs
    startLine: 23
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: ''
  example: []
  syntax:
    content: Path = 5
    return:
      type: DrawnUi.Draw.ShapeType
- uid: DrawnUi.Draw.ShapeType.Polygon
  commentId: F:DrawnUi.Draw.ShapeType.Polygon
  id: Polygon
  parent: DrawnUi.Draw.ShapeType
  langs:
  - csharp
  - vb
  name: Polygon
  nameWithType: ShapeType.Polygon
  fullName: DrawnUi.Draw.ShapeType.Polygon
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/ShapeType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Polygon
    path: ../src/Shared/Draw/Internals/Enums/ShapeType.cs
    startLine: 29
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Uses multiple Points
  example: []
  syntax:
    content: Polygon = 6
    return:
      type: DrawnUi.Draw.ShapeType
- uid: DrawnUi.Draw.ShapeType.Line
  commentId: F:DrawnUi.Draw.ShapeType.Line
  id: Line
  parent: DrawnUi.Draw.ShapeType
  langs:
  - csharp
  - vb
  name: Line
  nameWithType: ShapeType.Line
  fullName: DrawnUi.Draw.ShapeType.Line
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/ShapeType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Line
    path: ../src/Shared/Draw/Internals/Enums/ShapeType.cs
    startLine: 34
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Uses multiple Points
  example: []
  syntax:
    content: Line = 7
    return:
      type: DrawnUi.Draw.ShapeType
- uid: DrawnUi.Draw.ShapeType.Custom
  commentId: F:DrawnUi.Draw.ShapeType.Custom
  id: Custom
  parent: DrawnUi.Draw.ShapeType
  langs:
  - csharp
  - vb
  name: Custom
  nameWithType: ShapeType.Custom
  fullName: DrawnUi.Draw.ShapeType.Custom
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/ShapeType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Custom
    path: ../src/Shared/Draw/Internals/Enums/ShapeType.cs
    startLine: 39
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: unimplemented
  example: []
  syntax:
    content: Custom = 8
    return:
      type: DrawnUi.Draw.ShapeType
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.ShapeType
  commentId: T:DrawnUi.Draw.ShapeType
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ShapeType.html
  name: ShapeType
  nameWithType: ShapeType
  fullName: DrawnUi.Draw.ShapeType
