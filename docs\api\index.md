# API Documentation

This section contains the API documentation for DrawnUi, automatically generated from the code comments.

## Main Namespaces

- **DrawnUi.Draw**: Core drawing and rendering functionality
- **DrawnUi.Controls**: UI controls and components
- **DrawnUi.Features**: Additional features and capabilities

## Core Components

The main components you'll interact with include:

- `SkiaControl`: Base class for all drawn controls
- `SkiaShape`: Basic shape rendering
- `SkiaLabel`: Text rendering with advanced formatting options
- `SkiaLayout`: Layout management for drawn elements
- `SkiaButton`, `SkiaSwitch`, `SkiaCheckbox`: Platform-styled UI controls