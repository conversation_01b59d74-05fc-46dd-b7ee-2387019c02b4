### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.StringReference
  commentId: T:DrawnUi.Draw.StringReference
  id: StringReference
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.StringReference.Length
  - DrawnUi.Draw.StringReference.Source
  - DrawnUi.Draw.StringReference.Spans
  - DrawnUi.Draw.StringReference.StartIndex
  langs:
  - csharp
  - vb
  name: StringReference
  nameWithType: StringReference
  fullName: DrawnUi.Draw.StringReference
  type: Struct
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/StringReference.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: StringReference
    path: ../src/Maui/DrawnUi/Draw/Text/StringReference.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public struct StringReference
    content.vb: Public Structure StringReference
  inheritedMembers:
  - System.ValueType.Equals(System.Object)
  - System.ValueType.GetHashCode
  - System.ValueType.ToString
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetType
  - System.Object.ReferenceEquals(System.Object,System.Object)
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.StringReference.Spans
  commentId: P:DrawnUi.Draw.StringReference.Spans
  id: Spans
  parent: DrawnUi.Draw.StringReference
  langs:
  - csharp
  - vb
  name: Spans
  nameWithType: StringReference.Spans
  fullName: DrawnUi.Draw.StringReference.Spans
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/StringReference.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Spans
    path: ../src/Maui/DrawnUi/Draw/Text/StringReference.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public ReadOnlySpan<char> Spans { get; }
    parameters: []
    return:
      type: System.ReadOnlySpan{System.Char}
    content.vb: Public ReadOnly Property Spans As ReadOnlySpan(Of Char)
  overload: DrawnUi.Draw.StringReference.Spans*
- uid: DrawnUi.Draw.StringReference.Source
  commentId: P:DrawnUi.Draw.StringReference.Source
  id: Source
  parent: DrawnUi.Draw.StringReference
  langs:
  - csharp
  - vb
  name: Source
  nameWithType: StringReference.Source
  fullName: DrawnUi.Draw.StringReference.Source
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/StringReference.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Source
    path: ../src/Maui/DrawnUi/Draw/Text/StringReference.cs
    startLine: 12
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public string Source { readonly get; set; }
    parameters: []
    return:
      type: System.String
    content.vb: Public Property Source As String
  overload: DrawnUi.Draw.StringReference.Source*
- uid: DrawnUi.Draw.StringReference.StartIndex
  commentId: P:DrawnUi.Draw.StringReference.StartIndex
  id: StartIndex
  parent: DrawnUi.Draw.StringReference
  langs:
  - csharp
  - vb
  name: StartIndex
  nameWithType: StringReference.StartIndex
  fullName: DrawnUi.Draw.StringReference.StartIndex
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/StringReference.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: StartIndex
    path: ../src/Maui/DrawnUi/Draw/Text/StringReference.cs
    startLine: 17
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Position inside existing string
  example: []
  syntax:
    content: public int StartIndex { readonly get; set; }
    parameters: []
    return:
      type: System.Int32
    content.vb: Public Property StartIndex As Integer
  overload: DrawnUi.Draw.StringReference.StartIndex*
- uid: DrawnUi.Draw.StringReference.Length
  commentId: P:DrawnUi.Draw.StringReference.Length
  id: Length
  parent: DrawnUi.Draw.StringReference
  langs:
  - csharp
  - vb
  name: Length
  nameWithType: StringReference.Length
  fullName: DrawnUi.Draw.StringReference.Length
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/StringReference.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Length
    path: ../src/Maui/DrawnUi/Draw/Text/StringReference.cs
    startLine: 22
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Length inside existing string
  example: []
  syntax:
    content: public int Length { readonly get; set; }
    parameters: []
    return:
      type: System.Int32
    content.vb: Public Property Length As Integer
  overload: DrawnUi.Draw.StringReference.Length*
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.ValueType.Equals(System.Object)
  commentId: M:System.ValueType.Equals(System.Object)
  parent: System.ValueType
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype.equals
  name: Equals(object)
  nameWithType: ValueType.Equals(object)
  fullName: System.ValueType.Equals(object)
  nameWithType.vb: ValueType.Equals(Object)
  fullName.vb: System.ValueType.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.ValueType.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.equals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.ValueType.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.equals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.ValueType.GetHashCode
  commentId: M:System.ValueType.GetHashCode
  parent: System.ValueType
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype.gethashcode
  name: GetHashCode()
  nameWithType: ValueType.GetHashCode()
  fullName: System.ValueType.GetHashCode()
  spec.csharp:
  - uid: System.ValueType.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.ValueType.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.gethashcode
  - name: (
  - name: )
- uid: System.ValueType.ToString
  commentId: M:System.ValueType.ToString
  parent: System.ValueType
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype.tostring
  name: ToString()
  nameWithType: ValueType.ToString()
  fullName: System.ValueType.ToString()
  spec.csharp:
  - uid: System.ValueType.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.ValueType.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.tostring
  - name: (
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.ValueType
  commentId: T:System.ValueType
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype
  name: ValueType
  nameWithType: ValueType
  fullName: System.ValueType
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.StringReference.Spans*
  commentId: Overload:DrawnUi.Draw.StringReference.Spans
  href: DrawnUi.Draw.StringReference.html#DrawnUi_Draw_StringReference_Spans
  name: Spans
  nameWithType: StringReference.Spans
  fullName: DrawnUi.Draw.StringReference.Spans
- uid: System.ReadOnlySpan{System.Char}
  commentId: T:System.ReadOnlySpan{System.Char}
  parent: System
  definition: System.ReadOnlySpan`1
  href: https://learn.microsoft.com/dotnet/api/system.readonlyspan-1
  name: ReadOnlySpan<char>
  nameWithType: ReadOnlySpan<char>
  fullName: System.ReadOnlySpan<char>
  nameWithType.vb: ReadOnlySpan(Of Char)
  fullName.vb: System.ReadOnlySpan(Of Char)
  name.vb: ReadOnlySpan(Of Char)
  spec.csharp:
  - uid: System.ReadOnlySpan`1
    name: ReadOnlySpan
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.readonlyspan-1
  - name: <
  - uid: System.Char
    name: char
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.char
  - name: '>'
  spec.vb:
  - uid: System.ReadOnlySpan`1
    name: ReadOnlySpan
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.readonlyspan-1
  - name: (
  - name: Of
  - name: " "
  - uid: System.Char
    name: Char
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.char
  - name: )
- uid: System.ReadOnlySpan`1
  commentId: T:System.ReadOnlySpan`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.readonlyspan-1
  name: ReadOnlySpan<T>
  nameWithType: ReadOnlySpan<T>
  fullName: System.ReadOnlySpan<T>
  nameWithType.vb: ReadOnlySpan(Of T)
  fullName.vb: System.ReadOnlySpan(Of T)
  name.vb: ReadOnlySpan(Of T)
  spec.csharp:
  - uid: System.ReadOnlySpan`1
    name: ReadOnlySpan
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.readonlyspan-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.ReadOnlySpan`1
    name: ReadOnlySpan
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.readonlyspan-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: DrawnUi.Draw.StringReference.Source*
  commentId: Overload:DrawnUi.Draw.StringReference.Source
  href: DrawnUi.Draw.StringReference.html#DrawnUi_Draw_StringReference_Source
  name: Source
  nameWithType: StringReference.Source
  fullName: DrawnUi.Draw.StringReference.Source
- uid: System.String
  commentId: T:System.String
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.string
  name: string
  nameWithType: string
  fullName: string
  nameWithType.vb: String
  fullName.vb: String
  name.vb: String
- uid: DrawnUi.Draw.StringReference.StartIndex*
  commentId: Overload:DrawnUi.Draw.StringReference.StartIndex
  href: DrawnUi.Draw.StringReference.html#DrawnUi_Draw_StringReference_StartIndex
  name: StartIndex
  nameWithType: StringReference.StartIndex
  fullName: DrawnUi.Draw.StringReference.StartIndex
- uid: System.Int32
  commentId: T:System.Int32
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.int32
  name: int
  nameWithType: int
  fullName: int
  nameWithType.vb: Integer
  fullName.vb: Integer
  name.vb: Integer
- uid: DrawnUi.Draw.StringReference.Length*
  commentId: Overload:DrawnUi.Draw.StringReference.Length
  href: DrawnUi.Draw.StringReference.html#DrawnUi_Draw_StringReference_Length
  name: Length
  nameWithType: StringReference.Length
  fullName: DrawnUi.Draw.StringReference.Length
