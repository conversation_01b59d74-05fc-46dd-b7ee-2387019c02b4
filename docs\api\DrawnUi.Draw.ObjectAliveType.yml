### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.ObjectAliveType
  commentId: T:DrawnUi.Draw.ObjectAliveType
  id: ObjectAliveType
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.ObjectAliveType.Alive
  - DrawnUi.Draw.ObjectAliveType.BeingDisposed
  - DrawnUi.Draw.ObjectAliveType.Disposed
  - DrawnUi.Draw.ObjectAliveType.ToBeKilled
  langs:
  - csharp
  - vb
  name: ObjectAliveType
  nameWithType: ObjectAliveType
  fullName: DrawnUi.Draw.ObjectAliveType
  type: Enum
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/ObjectAliveType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ObjectAliveType
    path: ../src/Shared/Draw/Internals/Enums/ObjectAliveType.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public enum ObjectAliveType
    content.vb: Public Enum ObjectAliveType
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.ObjectAliveType.Alive
  commentId: F:DrawnUi.Draw.ObjectAliveType.Alive
  id: Alive
  parent: DrawnUi.Draw.ObjectAliveType
  langs:
  - csharp
  - vb
  name: Alive
  nameWithType: ObjectAliveType.Alive
  fullName: DrawnUi.Draw.ObjectAliveType.Alive
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/ObjectAliveType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Alive
    path: ../src/Shared/Draw/Internals/Enums/ObjectAliveType.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: Alive = 0
    return:
      type: DrawnUi.Draw.ObjectAliveType
- uid: DrawnUi.Draw.ObjectAliveType.ToBeKilled
  commentId: F:DrawnUi.Draw.ObjectAliveType.ToBeKilled
  id: ToBeKilled
  parent: DrawnUi.Draw.ObjectAliveType
  langs:
  - csharp
  - vb
  name: ToBeKilled
  nameWithType: ObjectAliveType.ToBeKilled
  fullName: DrawnUi.Draw.ObjectAliveType.ToBeKilled
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/ObjectAliveType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ToBeKilled
    path: ../src/Shared/Draw/Internals/Enums/ObjectAliveType.cs
    startLine: 5
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: ToBeKilled = 1
    return:
      type: DrawnUi.Draw.ObjectAliveType
- uid: DrawnUi.Draw.ObjectAliveType.BeingDisposed
  commentId: F:DrawnUi.Draw.ObjectAliveType.BeingDisposed
  id: BeingDisposed
  parent: DrawnUi.Draw.ObjectAliveType
  langs:
  - csharp
  - vb
  name: BeingDisposed
  nameWithType: ObjectAliveType.BeingDisposed
  fullName: DrawnUi.Draw.ObjectAliveType.BeingDisposed
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/ObjectAliveType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: BeingDisposed
    path: ../src/Shared/Draw/Internals/Enums/ObjectAliveType.cs
    startLine: 6
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: BeingDisposed = 2
    return:
      type: DrawnUi.Draw.ObjectAliveType
- uid: DrawnUi.Draw.ObjectAliveType.Disposed
  commentId: F:DrawnUi.Draw.ObjectAliveType.Disposed
  id: Disposed
  parent: DrawnUi.Draw.ObjectAliveType
  langs:
  - csharp
  - vb
  name: Disposed
  nameWithType: ObjectAliveType.Disposed
  fullName: DrawnUi.Draw.ObjectAliveType.Disposed
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/ObjectAliveType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Disposed
    path: ../src/Shared/Draw/Internals/Enums/ObjectAliveType.cs
    startLine: 7
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: Disposed = 3
    return:
      type: DrawnUi.Draw.ObjectAliveType
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.ObjectAliveType
  commentId: T:DrawnUi.Draw.ObjectAliveType
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ObjectAliveType.html
  name: ObjectAliveType
  nameWithType: ObjectAliveType
  fullName: DrawnUi.Draw.ObjectAliveType
