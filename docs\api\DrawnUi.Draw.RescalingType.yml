### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.RescalingType
  commentId: T:DrawnUi.Draw.RescalingType
  id: RescalingType
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.RescalingType.Default
  - DrawnUi.Draw.RescalingType.EdgePreserving
  - DrawnUi.Draw.RescalingType.MultiPass
  langs:
  - csharp
  - vb
  name: RescalingType
  nameWithType: RescalingType
  fullName: DrawnUi.Draw.RescalingType
  type: Enum
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/RescalingType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: RescalingType
    path: ../src/Shared/Draw/Internals/Enums/RescalingType.cs
    startLine: 5
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Rescaling method types for different image processing approaches
  example: []
  syntax:
    content: public enum RescalingType
    content.vb: Public Enum RescalingType
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.RescalingType.Default
  commentId: F:DrawnUi.Draw.RescalingType.Default
  id: Default
  parent: DrawnUi.Draw.RescalingType
  langs:
  - csharp
  - vb
  name: Default
  nameWithType: RescalingType.Default
  fullName: DrawnUi.Draw.RescalingType.Default
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/RescalingType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Default
    path: ../src/Shared/Draw/Internals/Enums/RescalingType.cs
    startLine: 11
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: >-
    Standard SkiaSharp rescaling:  "I just need it to work fast".

    Best for: Minor size adjustments, performance-focused.
  example: []
  syntax:
    content: Default = 0
    return:
      type: DrawnUi.Draw.RescalingType
- uid: DrawnUi.Draw.RescalingType.EdgePreserving
  commentId: F:DrawnUi.Draw.RescalingType.EdgePreserving
  id: EdgePreserving
  parent: DrawnUi.Draw.RescalingType
  langs:
  - csharp
  - vb
  name: EdgePreserving
  nameWithType: RescalingType.EdgePreserving
  fullName: DrawnUi.Draw.RescalingType.EdgePreserving
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/RescalingType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: EdgePreserving
    path: ../src/Shared/Draw/Internals/Enums/RescalingType.cs
    startLine: 17
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: >-
    Edge-preserving rescaling optimized for sharp graphics and pixel-perfect content, RescalingQuality not used here: "I'm working with pixel art/UI graphics".

    Best for: Pixel art, very small icons (16x16, 32x32), screenshots, text graphics.
  example: []
  syntax:
    content: EdgePreserving = 1
    return:
      type: DrawnUi.Draw.RescalingType
- uid: DrawnUi.Draw.RescalingType.MultiPass
  commentId: F:DrawnUi.Draw.RescalingType.MultiPass
  id: MultiPass
  parent: DrawnUi.Draw.RescalingType
  langs:
  - csharp
  - vb
  name: MultiPass
  nameWithType: RescalingType.MultiPass
  fullName: DrawnUi.Draw.RescalingType.MultiPass
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/RescalingType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: MultiPass
    path: ../src/Shared/Draw/Internals/Enums/RescalingType.cs
    startLine: 23
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: >-
    Multi-pass progressive rescaling for superior quality on significant size changes: "I want good quality".

    Best for: Icons, logos, transparent graphics with sharp edges, large size reductions (2x smaller).
  example: []
  syntax:
    content: MultiPass = 2
    return:
      type: DrawnUi.Draw.RescalingType
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.RescalingType
  commentId: T:DrawnUi.Draw.RescalingType
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.RescalingType.html
  name: RescalingType
  nameWithType: RescalingType
  fullName: DrawnUi.Draw.RescalingType
