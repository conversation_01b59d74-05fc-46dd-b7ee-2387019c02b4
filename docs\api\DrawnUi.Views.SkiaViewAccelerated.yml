### YamlMime:ManagedReference
items:
- uid: DrawnUi.Views.SkiaViewAccelerated
  commentId: T:DrawnUi.Views.SkiaViewAccelerated
  id: SkiaViewAccelerated
  parent: DrawnUi.Views
  children:
  - DrawnUi.Views.SkiaViewAccelerated.#ctor(DrawnUi.Views.DrawnView)
  - DrawnUi.Views.SkiaViewAccelerated.CreateStandaloneSurface(System.Int32,System.Int32)
  - DrawnUi.Views.SkiaViewAccelerated.Dispose
  - DrawnUi.Views.SkiaViewAccelerated.FPS
  - DrawnUi.Views.SkiaViewAccelerated.FrameTime
  - DrawnUi.Views.SkiaViewAccelerated.HasDrawn
  - DrawnUi.Views.SkiaViewAccelerated.IsDrawing
  - DrawnUi.Views.SkiaViewAccelerated.IsHardwareAccelerated
  - DrawnUi.Views.SkiaViewAccelerated.OnDraw
  - DrawnUi.Views.SkiaViewAccelerated.OnHandlerChanged
  - DrawnUi.Views.SkiaViewAccelerated.OnHandlerChanging(Microsoft.Maui.Controls.HandlerChangingEventArgs)
  - DrawnUi.Views.SkiaViewAccelerated.SignalFrame(System.Int64)
  - DrawnUi.Views.SkiaViewAccelerated.Superview
  - DrawnUi.Views.SkiaViewAccelerated.Surface
  - DrawnUi.Views.SkiaViewAccelerated.Uid
  - DrawnUi.Views.SkiaViewAccelerated.Update(System.Int64)
  langs:
  - csharp
  - vb
  name: SkiaViewAccelerated
  nameWithType: SkiaViewAccelerated
  fullName: DrawnUi.Views.SkiaViewAccelerated
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Views/SkiaViewAccelerated.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: SkiaViewAccelerated
    path: ../src/Maui/DrawnUi/Views/SkiaViewAccelerated.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: 'public class SkiaViewAccelerated : SKGLView, INotifyPropertyChanged, IVisualTreeElement, IEffectControlProvider, IToolTipElement, IContextFlyoutElement, IAnimatable, IViewController, IVisualElementController, IElementController, IGestureController, IGestureRecognizers, IPropertyMapperView, IHotReloadableView, IReplaceableView, ISKGLView, IView, IElement, ITransform, ISkiaDrawable, ISkiaSharpView, IDisposable'
    content.vb: Public Class SkiaViewAccelerated Inherits SKGLView Implements INotifyPropertyChanged, IVisualTreeElement, IEffectControlProvider, IToolTipElement, IContextFlyoutElement, IAnimatable, IViewController, IVisualElementController, IElementController, IGestureController, IGestureRecognizers, IPropertyMapperView, IHotReloadableView, IReplaceableView, ISKGLView, IView, IElement, ITransform, ISkiaDrawable, ISkiaSharpView, IDisposable
  inheritance:
  - System.Object
  - Microsoft.Maui.Controls.BindableObject
  - Microsoft.Maui.Controls.Element
  - Microsoft.Maui.Controls.StyleableElement
  - Microsoft.Maui.Controls.NavigableElement
  - Microsoft.Maui.Controls.VisualElement
  - Microsoft.Maui.Controls.View
  - SkiaSharp.Views.Maui.Controls.SKGLView
  implements:
  - System.ComponentModel.INotifyPropertyChanged
  - Microsoft.Maui.IVisualTreeElement
  - Microsoft.Maui.Controls.IEffectControlProvider
  - Microsoft.Maui.IToolTipElement
  - Microsoft.Maui.IContextFlyoutElement
  - Microsoft.Maui.Controls.IAnimatable
  - Microsoft.Maui.Controls.IViewController
  - Microsoft.Maui.Controls.IVisualElementController
  - Microsoft.Maui.Controls.IElementController
  - Microsoft.Maui.Controls.Internals.IGestureController
  - Microsoft.Maui.Controls.IGestureRecognizers
  - Microsoft.Maui.IPropertyMapperView
  - Microsoft.Maui.HotReload.IHotReloadableView
  - Microsoft.Maui.IReplaceableView
  - SkiaSharp.Views.Maui.ISKGLView
  - Microsoft.Maui.IView
  - Microsoft.Maui.IElement
  - Microsoft.Maui.ITransform
  - DrawnUi.Draw.ISkiaDrawable
  - DrawnUi.Draw.ISkiaSharpView
  - System.IDisposable
  inheritedMembers:
  - SkiaSharp.Views.Maui.Controls.SKGLView.IgnorePixelScalingProperty
  - SkiaSharp.Views.Maui.Controls.SKGLView.HasRenderLoopProperty
  - SkiaSharp.Views.Maui.Controls.SKGLView.EnableTouchEventsProperty
  - SkiaSharp.Views.Maui.Controls.SKGLView.InvalidateSurface
  - SkiaSharp.Views.Maui.Controls.SKGLView.OnPaintSurface(SkiaSharp.Views.Maui.SKPaintGLSurfaceEventArgs)
  - SkiaSharp.Views.Maui.Controls.SKGLView.OnTouch(SkiaSharp.Views.Maui.SKTouchEventArgs)
  - SkiaSharp.Views.Maui.Controls.SKGLView.IgnorePixelScaling
  - SkiaSharp.Views.Maui.Controls.SKGLView.HasRenderLoop
  - SkiaSharp.Views.Maui.Controls.SKGLView.EnableTouchEvents
  - SkiaSharp.Views.Maui.Controls.SKGLView.CanvasSize
  - SkiaSharp.Views.Maui.Controls.SKGLView.GRContext
  - SkiaSharp.Views.Maui.Controls.SKGLView.PaintSurface
  - SkiaSharp.Views.Maui.Controls.SKGLView.Touch
  - Microsoft.Maui.Controls.View.VerticalOptionsProperty
  - Microsoft.Maui.Controls.View.HorizontalOptionsProperty
  - Microsoft.Maui.Controls.View.MarginProperty
  - Microsoft.Maui.Controls.View.propertyMapper
  - Microsoft.Maui.Controls.View.ChangeVisualState
  - Microsoft.Maui.Controls.View.GetChildElements(Microsoft.Maui.Graphics.Point)
  - Microsoft.Maui.Controls.View.OnBindingContextChanged
  - Microsoft.Maui.Controls.View.GetRendererOverrides``1
  - Microsoft.Maui.Controls.View.GestureController
  - Microsoft.Maui.Controls.View.GestureRecognizers
  - Microsoft.Maui.Controls.View.HorizontalOptions
  - Microsoft.Maui.Controls.View.Margin
  - Microsoft.Maui.Controls.View.VerticalOptions
  - Microsoft.Maui.Controls.VisualElement.NavigationProperty
  - Microsoft.Maui.Controls.VisualElement.StyleProperty
  - Microsoft.Maui.Controls.VisualElement.InputTransparentProperty
  - Microsoft.Maui.Controls.VisualElement.IsEnabledProperty
  - Microsoft.Maui.Controls.VisualElement.XProperty
  - Microsoft.Maui.Controls.VisualElement.YProperty
  - Microsoft.Maui.Controls.VisualElement.AnchorXProperty
  - Microsoft.Maui.Controls.VisualElement.AnchorYProperty
  - Microsoft.Maui.Controls.VisualElement.TranslationXProperty
  - Microsoft.Maui.Controls.VisualElement.TranslationYProperty
  - Microsoft.Maui.Controls.VisualElement.WidthProperty
  - Microsoft.Maui.Controls.VisualElement.HeightProperty
  - Microsoft.Maui.Controls.VisualElement.RotationProperty
  - Microsoft.Maui.Controls.VisualElement.RotationXProperty
  - Microsoft.Maui.Controls.VisualElement.RotationYProperty
  - Microsoft.Maui.Controls.VisualElement.ScaleProperty
  - Microsoft.Maui.Controls.VisualElement.ScaleXProperty
  - Microsoft.Maui.Controls.VisualElement.ScaleYProperty
  - Microsoft.Maui.Controls.VisualElement.ClipProperty
  - Microsoft.Maui.Controls.VisualElement.VisualProperty
  - Microsoft.Maui.Controls.VisualElement.IsVisibleProperty
  - Microsoft.Maui.Controls.VisualElement.OpacityProperty
  - Microsoft.Maui.Controls.VisualElement.BackgroundColorProperty
  - Microsoft.Maui.Controls.VisualElement.BackgroundProperty
  - Microsoft.Maui.Controls.VisualElement.BehaviorsProperty
  - Microsoft.Maui.Controls.VisualElement.TriggersProperty
  - Microsoft.Maui.Controls.VisualElement.WidthRequestProperty
  - Microsoft.Maui.Controls.VisualElement.HeightRequestProperty
  - Microsoft.Maui.Controls.VisualElement.MinimumWidthRequestProperty
  - Microsoft.Maui.Controls.VisualElement.MinimumHeightRequestProperty
  - Microsoft.Maui.Controls.VisualElement.MaximumWidthRequestProperty
  - Microsoft.Maui.Controls.VisualElement.MaximumHeightRequestProperty
  - Microsoft.Maui.Controls.VisualElement.IsFocusedProperty
  - Microsoft.Maui.Controls.VisualElement.FlowDirectionProperty
  - Microsoft.Maui.Controls.VisualElement.WindowProperty
  - Microsoft.Maui.Controls.VisualElement.ShadowProperty
  - Microsoft.Maui.Controls.VisualElement.ZIndexProperty
  - Microsoft.Maui.Controls.VisualElement.BatchBegin
  - Microsoft.Maui.Controls.VisualElement.BatchCommit
  - Microsoft.Maui.Controls.VisualElement.Focus
  - Microsoft.Maui.Controls.VisualElement.Measure(System.Double,System.Double)
  - Microsoft.Maui.Controls.VisualElement.Measure(System.Double,System.Double,Microsoft.Maui.Controls.MeasureFlags)
  - Microsoft.Maui.Controls.VisualElement.Unfocus
  - Microsoft.Maui.Controls.VisualElement.InvalidateMeasure
  - Microsoft.Maui.Controls.VisualElement.OnChildAdded(Microsoft.Maui.Controls.Element)
  - Microsoft.Maui.Controls.VisualElement.OnChildRemoved(Microsoft.Maui.Controls.Element,System.Int32)
  - Microsoft.Maui.Controls.VisualElement.OnChildrenReordered
  - Microsoft.Maui.Controls.VisualElement.OnMeasure(System.Double,System.Double)
  - Microsoft.Maui.Controls.VisualElement.OnSizeAllocated(System.Double,System.Double)
  - Microsoft.Maui.Controls.VisualElement.SizeAllocated(System.Double,System.Double)
  - Microsoft.Maui.Controls.VisualElement.RefreshIsEnabledProperty
  - Microsoft.Maui.Controls.VisualElement.Arrange(Microsoft.Maui.Graphics.Rect)
  - Microsoft.Maui.Controls.VisualElement.ArrangeOverride(Microsoft.Maui.Graphics.Rect)
  - Microsoft.Maui.Controls.VisualElement.Layout(Microsoft.Maui.Graphics.Rect)
  - Microsoft.Maui.Controls.VisualElement.InvalidateMeasureOverride
  - Microsoft.Maui.Controls.VisualElement.MeasureOverride(System.Double,System.Double)
  - Microsoft.Maui.Controls.VisualElement.MapBackgroundColor(Microsoft.Maui.IViewHandler,Microsoft.Maui.IView)
  - Microsoft.Maui.Controls.VisualElement.MapBackgroundImageSource(Microsoft.Maui.IViewHandler,Microsoft.Maui.IView)
  - Microsoft.Maui.Controls.VisualElement.Visual
  - Microsoft.Maui.Controls.VisualElement.FlowDirection
  - Microsoft.Maui.Controls.VisualElement.Window
  - Microsoft.Maui.Controls.VisualElement.AnchorX
  - Microsoft.Maui.Controls.VisualElement.AnchorY
  - Microsoft.Maui.Controls.VisualElement.BackgroundColor
  - Microsoft.Maui.Controls.VisualElement.Background
  - Microsoft.Maui.Controls.VisualElement.Behaviors
  - Microsoft.Maui.Controls.VisualElement.Bounds
  - Microsoft.Maui.Controls.VisualElement.Height
  - Microsoft.Maui.Controls.VisualElement.HeightRequest
  - Microsoft.Maui.Controls.VisualElement.InputTransparent
  - Microsoft.Maui.Controls.VisualElement.IsEnabled
  - Microsoft.Maui.Controls.VisualElement.IsEnabledCore
  - Microsoft.Maui.Controls.VisualElement.IsFocused
  - Microsoft.Maui.Controls.VisualElement.IsVisible
  - Microsoft.Maui.Controls.VisualElement.MinimumHeightRequest
  - Microsoft.Maui.Controls.VisualElement.MinimumWidthRequest
  - Microsoft.Maui.Controls.VisualElement.MaximumHeightRequest
  - Microsoft.Maui.Controls.VisualElement.MaximumWidthRequest
  - Microsoft.Maui.Controls.VisualElement.Opacity
  - Microsoft.Maui.Controls.VisualElement.Rotation
  - Microsoft.Maui.Controls.VisualElement.RotationX
  - Microsoft.Maui.Controls.VisualElement.RotationY
  - Microsoft.Maui.Controls.VisualElement.Scale
  - Microsoft.Maui.Controls.VisualElement.ScaleX
  - Microsoft.Maui.Controls.VisualElement.ScaleY
  - Microsoft.Maui.Controls.VisualElement.TranslationX
  - Microsoft.Maui.Controls.VisualElement.TranslationY
  - Microsoft.Maui.Controls.VisualElement.Triggers
  - Microsoft.Maui.Controls.VisualElement.Width
  - Microsoft.Maui.Controls.VisualElement.WidthRequest
  - Microsoft.Maui.Controls.VisualElement.X
  - Microsoft.Maui.Controls.VisualElement.Y
  - Microsoft.Maui.Controls.VisualElement.Clip
  - Microsoft.Maui.Controls.VisualElement.Resources
  - Microsoft.Maui.Controls.VisualElement.Frame
  - Microsoft.Maui.Controls.VisualElement.Handler
  - Microsoft.Maui.Controls.VisualElement.Shadow
  - Microsoft.Maui.Controls.VisualElement.ZIndex
  - Microsoft.Maui.Controls.VisualElement.DesiredSize
  - Microsoft.Maui.Controls.VisualElement.IsLoaded
  - Microsoft.Maui.Controls.VisualElement.ChildrenReordered
  - Microsoft.Maui.Controls.VisualElement.Focused
  - Microsoft.Maui.Controls.VisualElement.MeasureInvalidated
  - Microsoft.Maui.Controls.VisualElement.SizeChanged
  - Microsoft.Maui.Controls.VisualElement.Unfocused
  - Microsoft.Maui.Controls.VisualElement.Loaded
  - Microsoft.Maui.Controls.VisualElement.Unloaded
  - Microsoft.Maui.Controls.NavigableElement.OnParentSet
  - Microsoft.Maui.Controls.NavigableElement.Navigation
  - Microsoft.Maui.Controls.StyleableElement.Style
  - Microsoft.Maui.Controls.StyleableElement.StyleClass
  - Microsoft.Maui.Controls.StyleableElement.class
  - Microsoft.Maui.Controls.Element.AutomationIdProperty
  - Microsoft.Maui.Controls.Element.ClassIdProperty
  - Microsoft.Maui.Controls.Element.InsertLogicalChild(System.Int32,Microsoft.Maui.Controls.Element)
  - Microsoft.Maui.Controls.Element.AddLogicalChild(Microsoft.Maui.Controls.Element)
  - Microsoft.Maui.Controls.Element.RemoveLogicalChild(Microsoft.Maui.Controls.Element)
  - Microsoft.Maui.Controls.Element.ClearLogicalChildren
  - Microsoft.Maui.Controls.Element.FindByName(System.String)
  - Microsoft.Maui.Controls.Element.RemoveDynamicResource(Microsoft.Maui.Controls.BindableProperty)
  - Microsoft.Maui.Controls.Element.SetDynamicResource(Microsoft.Maui.Controls.BindableProperty,System.String)
  - Microsoft.Maui.Controls.Element.OnPropertyChanged(System.String)
  - Microsoft.Maui.Controls.Element.OnParentChanging(Microsoft.Maui.Controls.ParentChangingEventArgs)
  - Microsoft.Maui.Controls.Element.OnParentChanged
  - Microsoft.Maui.Controls.Element.MapAutomationPropertiesIsInAccessibleTree(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
  - Microsoft.Maui.Controls.Element.MapAutomationPropertiesExcludedWithChildren(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
  - Microsoft.Maui.Controls.Element.AutomationId
  - Microsoft.Maui.Controls.Element.ClassId
  - Microsoft.Maui.Controls.Element.Effects
  - Microsoft.Maui.Controls.Element.Id
  - Microsoft.Maui.Controls.Element.StyleId
  - Microsoft.Maui.Controls.Element.Parent
  - Microsoft.Maui.Controls.Element.ChildAdded
  - Microsoft.Maui.Controls.Element.ChildRemoved
  - Microsoft.Maui.Controls.Element.DescendantAdded
  - Microsoft.Maui.Controls.Element.DescendantRemoved
  - Microsoft.Maui.Controls.Element.ParentChanging
  - Microsoft.Maui.Controls.Element.ParentChanged
  - Microsoft.Maui.Controls.Element.HandlerChanging
  - Microsoft.Maui.Controls.Element.HandlerChanged
  - Microsoft.Maui.Controls.BindableObject.BindingContextProperty
  - Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindableProperty)
  - Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindablePropertyKey)
  - Microsoft.Maui.Controls.BindableObject.GetValue(Microsoft.Maui.Controls.BindableProperty)
  - Microsoft.Maui.Controls.BindableObject.IsSet(Microsoft.Maui.Controls.BindableProperty)
  - Microsoft.Maui.Controls.BindableObject.RemoveBinding(Microsoft.Maui.Controls.BindableProperty)
  - Microsoft.Maui.Controls.BindableObject.SetBinding(Microsoft.Maui.Controls.BindableProperty,Microsoft.Maui.Controls.BindingBase)
  - Microsoft.Maui.Controls.BindableObject.ApplyBindings
  - Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(System.String)
  - Microsoft.Maui.Controls.BindableObject.UnapplyBindings
  - Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty,System.Object)
  - Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey,System.Object)
  - Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindableProperty)
  - Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindablePropertyKey)
  - Microsoft.Maui.Controls.BindableObject.Dispatcher
  - Microsoft.Maui.Controls.BindableObject.BindingContext
  - Microsoft.Maui.Controls.BindableObject.PropertyChanged
  - Microsoft.Maui.Controls.BindableObject.PropertyChanging
  - Microsoft.Maui.Controls.BindableObject.BindingContextChanged
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - DrawnUi.Views.SkiaViewAccelerated.DrawnUi.Draw.FluentExtensions.AssignNative``1(DrawnUi.Views.SkiaViewAccelerated@)
  - Microsoft.Maui.Controls.Element.DrawnUi.Draw.StaticResourcesExtensions.FindParent``1
  - Microsoft.Maui.Controls.Element.DrawnUi.Extensions.InternalExtensions.FindMauiContext(System.Boolean)
  - Microsoft.Maui.Controls.Element.DrawnUi.Extensions.InternalExtensions.GetParentsPath
  - Microsoft.Maui.Controls.VisualElement.DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents
  - Microsoft.Maui.IView.DrawnUi.Extensions.InternalExtensions.DisposeControlAndChildren
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Views.SkiaViewAccelerated.Uid
  commentId: P:DrawnUi.Views.SkiaViewAccelerated.Uid
  id: Uid
  parent: DrawnUi.Views.SkiaViewAccelerated
  langs:
  - csharp
  - vb
  name: Uid
  nameWithType: SkiaViewAccelerated.Uid
  fullName: DrawnUi.Views.SkiaViewAccelerated.Uid
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Views/SkiaViewAccelerated.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Uid
    path: ../src/Maui/DrawnUi/Views/SkiaViewAccelerated.cs
    startLine: 6
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  example: []
  syntax:
    content: public Guid Uid { get; }
    parameters: []
    return:
      type: System.Guid
    content.vb: Public ReadOnly Property Uid As Guid
  overload: DrawnUi.Views.SkiaViewAccelerated.Uid*
  implements:
  - DrawnUi.Draw.ISkiaDrawable.Uid
- uid: DrawnUi.Views.SkiaViewAccelerated.CreateStandaloneSurface(System.Int32,System.Int32)
  commentId: M:DrawnUi.Views.SkiaViewAccelerated.CreateStandaloneSurface(System.Int32,System.Int32)
  id: CreateStandaloneSurface(System.Int32,System.Int32)
  parent: DrawnUi.Views.SkiaViewAccelerated
  langs:
  - csharp
  - vb
  name: CreateStandaloneSurface(int, int)
  nameWithType: SkiaViewAccelerated.CreateStandaloneSurface(int, int)
  fullName: DrawnUi.Views.SkiaViewAccelerated.CreateStandaloneSurface(int, int)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/SkiaViewAccelerated.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: CreateStandaloneSurface
    path: ../src/Maui/DrawnUi/Views/SkiaViewAccelerated.cs
    startLine: 8
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  summary: This is needed to get a hardware accelerated surface or a normal one depending on the platform
  example: []
  syntax:
    content: public SKSurface CreateStandaloneSurface(int width, int height)
    parameters:
    - id: width
      type: System.Int32
      description: ''
    - id: height
      type: System.Int32
      description: ''
    return:
      type: SkiaSharp.SKSurface
      description: ''
    content.vb: Public Function CreateStandaloneSurface(width As Integer, height As Integer) As SKSurface
  overload: DrawnUi.Views.SkiaViewAccelerated.CreateStandaloneSurface*
  implements:
  - DrawnUi.Draw.ISkiaSharpView.CreateStandaloneSurface(System.Int32,System.Int32)
  nameWithType.vb: SkiaViewAccelerated.CreateStandaloneSurface(Integer, Integer)
  fullName.vb: DrawnUi.Views.SkiaViewAccelerated.CreateStandaloneSurface(Integer, Integer)
  name.vb: CreateStandaloneSurface(Integer, Integer)
- uid: DrawnUi.Views.SkiaViewAccelerated.OnDraw
  commentId: P:DrawnUi.Views.SkiaViewAccelerated.OnDraw
  id: OnDraw
  parent: DrawnUi.Views.SkiaViewAccelerated
  langs:
  - csharp
  - vb
  name: OnDraw
  nameWithType: SkiaViewAccelerated.OnDraw
  fullName: DrawnUi.Views.SkiaViewAccelerated.OnDraw
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Views/SkiaViewAccelerated.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: OnDraw
    path: ../src/Maui/DrawnUi/Views/SkiaViewAccelerated.cs
    startLine: 13
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  summary: Return true if need force invalidation on next frame
  example: []
  syntax:
    content: public Func<SKSurface, SKRect, bool> OnDraw { get; set; }
    parameters: []
    return:
      type: System.Func{SkiaSharp.SKSurface,SkiaSharp.SKRect,System.Boolean}
    content.vb: Public Property OnDraw As Func(Of SKSurface, SKRect, Boolean)
  overload: DrawnUi.Views.SkiaViewAccelerated.OnDraw*
  implements:
  - DrawnUi.Draw.ISkiaDrawable.OnDraw
- uid: DrawnUi.Views.SkiaViewAccelerated.#ctor(DrawnUi.Views.DrawnView)
  commentId: M:DrawnUi.Views.SkiaViewAccelerated.#ctor(DrawnUi.Views.DrawnView)
  id: '#ctor(DrawnUi.Views.DrawnView)'
  parent: DrawnUi.Views.SkiaViewAccelerated
  langs:
  - csharp
  - vb
  name: SkiaViewAccelerated(DrawnView)
  nameWithType: SkiaViewAccelerated.SkiaViewAccelerated(DrawnView)
  fullName: DrawnUi.Views.SkiaViewAccelerated.SkiaViewAccelerated(DrawnUi.Views.DrawnView)
  type: Constructor
  source:
    remote:
      path: src/Maui/DrawnUi/Views/SkiaViewAccelerated.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Maui/DrawnUi/Views/SkiaViewAccelerated.cs
    startLine: 15
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: public SkiaViewAccelerated(DrawnView superview)
    parameters:
    - id: superview
      type: DrawnUi.Views.DrawnView
    content.vb: Public Sub New(superview As DrawnView)
  overload: DrawnUi.Views.SkiaViewAccelerated.#ctor*
  nameWithType.vb: SkiaViewAccelerated.New(DrawnView)
  fullName.vb: DrawnUi.Views.SkiaViewAccelerated.New(DrawnUi.Views.DrawnView)
  name.vb: New(DrawnView)
- uid: DrawnUi.Views.SkiaViewAccelerated.OnHandlerChanging(Microsoft.Maui.Controls.HandlerChangingEventArgs)
  commentId: M:DrawnUi.Views.SkiaViewAccelerated.OnHandlerChanging(Microsoft.Maui.Controls.HandlerChangingEventArgs)
  id: OnHandlerChanging(Microsoft.Maui.Controls.HandlerChangingEventArgs)
  parent: DrawnUi.Views.SkiaViewAccelerated
  langs:
  - csharp
  - vb
  name: OnHandlerChanging(HandlerChangingEventArgs)
  nameWithType: SkiaViewAccelerated.OnHandlerChanging(HandlerChangingEventArgs)
  fullName: DrawnUi.Views.SkiaViewAccelerated.OnHandlerChanging(Microsoft.Maui.Controls.HandlerChangingEventArgs)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/SkiaViewAccelerated.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: OnHandlerChanging
    path: ../src/Maui/DrawnUi/Views/SkiaViewAccelerated.cs
    startLine: 54
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  summary: When overridden in a derived class, should raise the <xref href="Microsoft.Maui.Controls.Element.HandlerChanging" data-throw-if-not-resolved="false"></xref> event.
  remarks: It is the implementor's responsibility to raise the <xref href="Microsoft.Maui.Controls.Element.HandlerChanging" data-throw-if-not-resolved="false"></xref> event.
  example: []
  syntax:
    content: protected override void OnHandlerChanging(HandlerChangingEventArgs args)
    parameters:
    - id: args
      type: Microsoft.Maui.Controls.HandlerChangingEventArgs
      description: Provides data for the <xref href="Microsoft.Maui.Controls.Element.HandlerChanging" data-throw-if-not-resolved="false"></xref> event.
    content.vb: Protected Overrides Sub OnHandlerChanging(args As HandlerChangingEventArgs)
  overridden: Microsoft.Maui.Controls.Element.OnHandlerChanging(Microsoft.Maui.Controls.HandlerChangingEventArgs)
  overload: DrawnUi.Views.SkiaViewAccelerated.OnHandlerChanging*
- uid: DrawnUi.Views.SkiaViewAccelerated.OnHandlerChanged
  commentId: M:DrawnUi.Views.SkiaViewAccelerated.OnHandlerChanged
  id: OnHandlerChanged
  parent: DrawnUi.Views.SkiaViewAccelerated
  langs:
  - csharp
  - vb
  name: OnHandlerChanged()
  nameWithType: SkiaViewAccelerated.OnHandlerChanged()
  fullName: DrawnUi.Views.SkiaViewAccelerated.OnHandlerChanged()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/SkiaViewAccelerated.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: OnHandlerChanged
    path: ../src/Maui/DrawnUi/Views/SkiaViewAccelerated.cs
    startLine: 76
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  summary: When overridden in a derived class, should raise the <xref href="Microsoft.Maui.Controls.Element.HandlerChanged" data-throw-if-not-resolved="false"></xref> event.
  remarks: It is the implementor's responsibility to raise the <xref href="Microsoft.Maui.Controls.Element.HandlerChanged" data-throw-if-not-resolved="false"></xref> event.
  example: []
  syntax:
    content: protected override void OnHandlerChanged()
    content.vb: Protected Overrides Sub OnHandlerChanged()
  overridden: Microsoft.Maui.Controls.Element.OnHandlerChanged
  overload: DrawnUi.Views.SkiaViewAccelerated.OnHandlerChanged*
- uid: DrawnUi.Views.SkiaViewAccelerated.Superview
  commentId: P:DrawnUi.Views.SkiaViewAccelerated.Superview
  id: Superview
  parent: DrawnUi.Views.SkiaViewAccelerated
  langs:
  - csharp
  - vb
  name: Superview
  nameWithType: SkiaViewAccelerated.Superview
  fullName: DrawnUi.Views.SkiaViewAccelerated.Superview
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Views/SkiaViewAccelerated.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Superview
    path: ../src/Maui/DrawnUi/Views/SkiaViewAccelerated.cs
    startLine: 121
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: public DrawnView Superview { get; protected set; }
    parameters: []
    return:
      type: DrawnUi.Views.DrawnView
    content.vb: Public Property Superview As DrawnView
  overload: DrawnUi.Views.SkiaViewAccelerated.Superview*
- uid: DrawnUi.Views.SkiaViewAccelerated.Dispose
  commentId: M:DrawnUi.Views.SkiaViewAccelerated.Dispose
  id: Dispose
  parent: DrawnUi.Views.SkiaViewAccelerated
  langs:
  - csharp
  - vb
  name: Dispose()
  nameWithType: SkiaViewAccelerated.Dispose()
  fullName: DrawnUi.Views.SkiaViewAccelerated.Dispose()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/SkiaViewAccelerated.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Dispose
    path: ../src/Maui/DrawnUi/Views/SkiaViewAccelerated.cs
    startLine: 124
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  summary: Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
  example: []
  syntax:
    content: public void Dispose()
    content.vb: Public Sub Dispose()
  overload: DrawnUi.Views.SkiaViewAccelerated.Dispose*
  implements:
  - System.IDisposable.Dispose
- uid: DrawnUi.Views.SkiaViewAccelerated.Surface
  commentId: P:DrawnUi.Views.SkiaViewAccelerated.Surface
  id: Surface
  parent: DrawnUi.Views.SkiaViewAccelerated
  langs:
  - csharp
  - vb
  name: Surface
  nameWithType: SkiaViewAccelerated.Surface
  fullName: DrawnUi.Views.SkiaViewAccelerated.Surface
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Views/SkiaViewAccelerated.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Surface
    path: ../src/Maui/DrawnUi/Views/SkiaViewAccelerated.cs
    startLine: 138
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  example: []
  syntax:
    content: public SKSurface Surface { get; }
    parameters: []
    return:
      type: SkiaSharp.SKSurface
    content.vb: Public ReadOnly Property Surface As SKSurface
  overload: DrawnUi.Views.SkiaViewAccelerated.Surface*
  implements:
  - DrawnUi.Draw.ISkiaDrawable.Surface
- uid: DrawnUi.Views.SkiaViewAccelerated.IsHardwareAccelerated
  commentId: P:DrawnUi.Views.SkiaViewAccelerated.IsHardwareAccelerated
  id: IsHardwareAccelerated
  parent: DrawnUi.Views.SkiaViewAccelerated
  langs:
  - csharp
  - vb
  name: IsHardwareAccelerated
  nameWithType: SkiaViewAccelerated.IsHardwareAccelerated
  fullName: DrawnUi.Views.SkiaViewAccelerated.IsHardwareAccelerated
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Views/SkiaViewAccelerated.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IsHardwareAccelerated
    path: ../src/Maui/DrawnUi/Views/SkiaViewAccelerated.cs
    startLine: 146
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  example: []
  syntax:
    content: public bool IsHardwareAccelerated { get; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public ReadOnly Property IsHardwareAccelerated As Boolean
  overload: DrawnUi.Views.SkiaViewAccelerated.IsHardwareAccelerated*
  implements:
  - DrawnUi.Draw.ISkiaDrawable.IsHardwareAccelerated
- uid: DrawnUi.Views.SkiaViewAccelerated.FPS
  commentId: P:DrawnUi.Views.SkiaViewAccelerated.FPS
  id: FPS
  parent: DrawnUi.Views.SkiaViewAccelerated
  langs:
  - csharp
  - vb
  name: FPS
  nameWithType: SkiaViewAccelerated.FPS
  fullName: DrawnUi.Views.SkiaViewAccelerated.FPS
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Views/SkiaViewAccelerated.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: FPS
    path: ../src/Maui/DrawnUi/Views/SkiaViewAccelerated.cs
    startLine: 148
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  example: []
  syntax:
    content: public double FPS { get; }
    parameters: []
    return:
      type: System.Double
    content.vb: Public ReadOnly Property FPS As Double
  overload: DrawnUi.Views.SkiaViewAccelerated.FPS*
  implements:
  - DrawnUi.Draw.ISkiaDrawable.FPS
- uid: DrawnUi.Views.SkiaViewAccelerated.IsDrawing
  commentId: P:DrawnUi.Views.SkiaViewAccelerated.IsDrawing
  id: IsDrawing
  parent: DrawnUi.Views.SkiaViewAccelerated
  langs:
  - csharp
  - vb
  name: IsDrawing
  nameWithType: SkiaViewAccelerated.IsDrawing
  fullName: DrawnUi.Views.SkiaViewAccelerated.IsDrawing
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Views/SkiaViewAccelerated.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IsDrawing
    path: ../src/Maui/DrawnUi/Views/SkiaViewAccelerated.cs
    startLine: 156
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  example: []
  syntax:
    content: public bool IsDrawing { get; set; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property IsDrawing As Boolean
  overload: DrawnUi.Views.SkiaViewAccelerated.IsDrawing*
  implements:
  - DrawnUi.Draw.ISkiaDrawable.IsDrawing
- uid: DrawnUi.Views.SkiaViewAccelerated.HasDrawn
  commentId: P:DrawnUi.Views.SkiaViewAccelerated.HasDrawn
  id: HasDrawn
  parent: DrawnUi.Views.SkiaViewAccelerated
  langs:
  - csharp
  - vb
  name: HasDrawn
  nameWithType: SkiaViewAccelerated.HasDrawn
  fullName: DrawnUi.Views.SkiaViewAccelerated.HasDrawn
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Views/SkiaViewAccelerated.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: HasDrawn
    path: ../src/Maui/DrawnUi/Views/SkiaViewAccelerated.cs
    startLine: 167
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  example: []
  syntax:
    content: public bool HasDrawn { get; protected set; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property HasDrawn As Boolean
  overload: DrawnUi.Views.SkiaViewAccelerated.HasDrawn*
  implements:
  - DrawnUi.Draw.ISkiaDrawable.HasDrawn
- uid: DrawnUi.Views.SkiaViewAccelerated.FrameTime
  commentId: P:DrawnUi.Views.SkiaViewAccelerated.FrameTime
  id: FrameTime
  parent: DrawnUi.Views.SkiaViewAccelerated
  langs:
  - csharp
  - vb
  name: FrameTime
  nameWithType: SkiaViewAccelerated.FrameTime
  fullName: DrawnUi.Views.SkiaViewAccelerated.FrameTime
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Views/SkiaViewAccelerated.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: FrameTime
    path: ../src/Maui/DrawnUi/Views/SkiaViewAccelerated.cs
    startLine: 168
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  example: []
  syntax:
    content: public long FrameTime { get; protected set; }
    parameters: []
    return:
      type: System.Int64
    content.vb: Public Property FrameTime As Long
  overload: DrawnUi.Views.SkiaViewAccelerated.FrameTime*
  implements:
  - DrawnUi.Draw.ISkiaDrawable.FrameTime
- uid: DrawnUi.Views.SkiaViewAccelerated.SignalFrame(System.Int64)
  commentId: M:DrawnUi.Views.SkiaViewAccelerated.SignalFrame(System.Int64)
  id: SignalFrame(System.Int64)
  parent: DrawnUi.Views.SkiaViewAccelerated
  langs:
  - csharp
  - vb
  name: SignalFrame(long)
  nameWithType: SkiaViewAccelerated.SignalFrame(long)
  fullName: DrawnUi.Views.SkiaViewAccelerated.SignalFrame(long)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/SkiaViewAccelerated.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: SignalFrame
    path: ../src/Maui/DrawnUi/Views/SkiaViewAccelerated.cs
    startLine: 170
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  example: []
  syntax:
    content: public void SignalFrame(long nanoseconds)
    parameters:
    - id: nanoseconds
      type: System.Int64
    content.vb: Public Sub SignalFrame(nanoseconds As Long)
  overload: DrawnUi.Views.SkiaViewAccelerated.SignalFrame*
  implements:
  - DrawnUi.Draw.ISkiaSharpView.SignalFrame(System.Int64)
  nameWithType.vb: SkiaViewAccelerated.SignalFrame(Long)
  fullName.vb: DrawnUi.Views.SkiaViewAccelerated.SignalFrame(Long)
  name.vb: SignalFrame(Long)
- uid: DrawnUi.Views.SkiaViewAccelerated.Update(System.Int64)
  commentId: M:DrawnUi.Views.SkiaViewAccelerated.Update(System.Int64)
  id: Update(System.Int64)
  parent: DrawnUi.Views.SkiaViewAccelerated
  langs:
  - csharp
  - vb
  name: Update(long)
  nameWithType: SkiaViewAccelerated.Update(long)
  fullName: DrawnUi.Views.SkiaViewAccelerated.Update(long)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/SkiaViewAccelerated.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Update
    path: ../src/Maui/DrawnUi/Views/SkiaViewAccelerated.cs
    startLine: 175
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  summary: Safe InvalidateSurface() call. If nanos not specified will generate ittself
  example: []
  syntax:
    content: public void Update(long nanos)
    parameters:
    - id: nanos
      type: System.Int64
    content.vb: Public Sub Update(nanos As Long)
  overload: DrawnUi.Views.SkiaViewAccelerated.Update*
  implements:
  - DrawnUi.Draw.ISkiaSharpView.Update(System.Int64)
  nameWithType.vb: SkiaViewAccelerated.Update(Long)
  fullName.vb: DrawnUi.Views.SkiaViewAccelerated.Update(Long)
  name.vb: Update(Long)
references:
- uid: DrawnUi.Views
  commentId: N:DrawnUi.Views
  href: DrawnUi.html
  name: DrawnUi.Views
  nameWithType: DrawnUi.Views
  fullName: DrawnUi.Views
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Views
    name: Views
    href: DrawnUi.Views.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Views
    name: Views
    href: DrawnUi.Views.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: Microsoft.Maui.Controls.BindableObject
  commentId: T:Microsoft.Maui.Controls.BindableObject
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject
  name: BindableObject
  nameWithType: BindableObject
  fullName: Microsoft.Maui.Controls.BindableObject
- uid: Microsoft.Maui.Controls.Element
  commentId: T:Microsoft.Maui.Controls.Element
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  name: Element
  nameWithType: Element
  fullName: Microsoft.Maui.Controls.Element
- uid: Microsoft.Maui.Controls.StyleableElement
  commentId: T:Microsoft.Maui.Controls.StyleableElement
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.styleableelement
  name: StyleableElement
  nameWithType: StyleableElement
  fullName: Microsoft.Maui.Controls.StyleableElement
- uid: Microsoft.Maui.Controls.NavigableElement
  commentId: T:Microsoft.Maui.Controls.NavigableElement
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.navigableelement
  name: NavigableElement
  nameWithType: NavigableElement
  fullName: Microsoft.Maui.Controls.NavigableElement
- uid: Microsoft.Maui.Controls.VisualElement
  commentId: T:Microsoft.Maui.Controls.VisualElement
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement
  name: VisualElement
  nameWithType: VisualElement
  fullName: Microsoft.Maui.Controls.VisualElement
- uid: Microsoft.Maui.Controls.View
  commentId: T:Microsoft.Maui.Controls.View
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view
  name: View
  nameWithType: View
  fullName: Microsoft.Maui.Controls.View
- uid: SkiaSharp.Views.Maui.Controls.SKGLView
  commentId: T:SkiaSharp.Views.Maui.Controls.SKGLView
  parent: SkiaSharp.Views.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.views.maui.controls.skglview
  name: SKGLView
  nameWithType: SKGLView
  fullName: SkiaSharp.Views.Maui.Controls.SKGLView
- uid: System.ComponentModel.INotifyPropertyChanged
  commentId: T:System.ComponentModel.INotifyPropertyChanged
  parent: System.ComponentModel
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.inotifypropertychanged
  name: INotifyPropertyChanged
  nameWithType: INotifyPropertyChanged
  fullName: System.ComponentModel.INotifyPropertyChanged
- uid: Microsoft.Maui.IVisualTreeElement
  commentId: T:Microsoft.Maui.IVisualTreeElement
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.ivisualtreeelement
  name: IVisualTreeElement
  nameWithType: IVisualTreeElement
  fullName: Microsoft.Maui.IVisualTreeElement
- uid: Microsoft.Maui.Controls.IEffectControlProvider
  commentId: T:Microsoft.Maui.Controls.IEffectControlProvider
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ieffectcontrolprovider
  name: IEffectControlProvider
  nameWithType: IEffectControlProvider
  fullName: Microsoft.Maui.Controls.IEffectControlProvider
- uid: Microsoft.Maui.IToolTipElement
  commentId: T:Microsoft.Maui.IToolTipElement
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.itooltipelement
  name: IToolTipElement
  nameWithType: IToolTipElement
  fullName: Microsoft.Maui.IToolTipElement
- uid: Microsoft.Maui.IContextFlyoutElement
  commentId: T:Microsoft.Maui.IContextFlyoutElement
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.icontextflyoutelement
  name: IContextFlyoutElement
  nameWithType: IContextFlyoutElement
  fullName: Microsoft.Maui.IContextFlyoutElement
- uid: Microsoft.Maui.Controls.IAnimatable
  commentId: T:Microsoft.Maui.Controls.IAnimatable
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ianimatable
  name: IAnimatable
  nameWithType: IAnimatable
  fullName: Microsoft.Maui.Controls.IAnimatable
- uid: Microsoft.Maui.Controls.IViewController
  commentId: T:Microsoft.Maui.Controls.IViewController
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.iviewcontroller
  name: IViewController
  nameWithType: IViewController
  fullName: Microsoft.Maui.Controls.IViewController
- uid: Microsoft.Maui.Controls.IVisualElementController
  commentId: T:Microsoft.Maui.Controls.IVisualElementController
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ivisualelementcontroller
  name: IVisualElementController
  nameWithType: IVisualElementController
  fullName: Microsoft.Maui.Controls.IVisualElementController
- uid: Microsoft.Maui.Controls.IElementController
  commentId: T:Microsoft.Maui.Controls.IElementController
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ielementcontroller
  name: IElementController
  nameWithType: IElementController
  fullName: Microsoft.Maui.Controls.IElementController
- uid: Microsoft.Maui.Controls.Internals.IGestureController
  commentId: T:Microsoft.Maui.Controls.Internals.IGestureController
  parent: Microsoft.Maui.Controls.Internals
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.internals.igesturecontroller
  name: IGestureController
  nameWithType: IGestureController
  fullName: Microsoft.Maui.Controls.Internals.IGestureController
- uid: Microsoft.Maui.Controls.IGestureRecognizers
  commentId: T:Microsoft.Maui.Controls.IGestureRecognizers
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.igesturerecognizers
  name: IGestureRecognizers
  nameWithType: IGestureRecognizers
  fullName: Microsoft.Maui.Controls.IGestureRecognizers
- uid: Microsoft.Maui.IPropertyMapperView
  commentId: T:Microsoft.Maui.IPropertyMapperView
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.ipropertymapperview
  name: IPropertyMapperView
  nameWithType: IPropertyMapperView
  fullName: Microsoft.Maui.IPropertyMapperView
- uid: Microsoft.Maui.HotReload.IHotReloadableView
  commentId: T:Microsoft.Maui.HotReload.IHotReloadableView
  parent: Microsoft.Maui.HotReload
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.hotreload.ihotreloadableview
  name: IHotReloadableView
  nameWithType: IHotReloadableView
  fullName: Microsoft.Maui.HotReload.IHotReloadableView
- uid: Microsoft.Maui.IReplaceableView
  commentId: T:Microsoft.Maui.IReplaceableView
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.ireplaceableview
  name: IReplaceableView
  nameWithType: IReplaceableView
  fullName: Microsoft.Maui.IReplaceableView
- uid: SkiaSharp.Views.Maui.ISKGLView
  commentId: T:SkiaSharp.Views.Maui.ISKGLView
  parent: SkiaSharp.Views.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.views.maui.iskglview
  name: ISKGLView
  nameWithType: ISKGLView
  fullName: SkiaSharp.Views.Maui.ISKGLView
- uid: Microsoft.Maui.IView
  commentId: T:Microsoft.Maui.IView
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iview
  name: IView
  nameWithType: IView
  fullName: Microsoft.Maui.IView
- uid: Microsoft.Maui.IElement
  commentId: T:Microsoft.Maui.IElement
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.ielement
  name: IElement
  nameWithType: IElement
  fullName: Microsoft.Maui.IElement
- uid: Microsoft.Maui.ITransform
  commentId: T:Microsoft.Maui.ITransform
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.itransform
  name: ITransform
  nameWithType: ITransform
  fullName: Microsoft.Maui.ITransform
- uid: DrawnUi.Draw.ISkiaDrawable
  commentId: T:DrawnUi.Draw.ISkiaDrawable
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ISkiaDrawable.html
  name: ISkiaDrawable
  nameWithType: ISkiaDrawable
  fullName: DrawnUi.Draw.ISkiaDrawable
- uid: DrawnUi.Draw.ISkiaSharpView
  commentId: T:DrawnUi.Draw.ISkiaSharpView
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ISkiaSharpView.html
  name: ISkiaSharpView
  nameWithType: ISkiaSharpView
  fullName: DrawnUi.Draw.ISkiaSharpView
- uid: System.IDisposable
  commentId: T:System.IDisposable
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.idisposable
  name: IDisposable
  nameWithType: IDisposable
  fullName: System.IDisposable
- uid: SkiaSharp.Views.Maui.Controls.SKGLView.IgnorePixelScalingProperty
  commentId: F:SkiaSharp.Views.Maui.Controls.SKGLView.IgnorePixelScalingProperty
  parent: SkiaSharp.Views.Maui.Controls.SKGLView
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.views.maui.controls.skglview.ignorepixelscalingproperty
  name: IgnorePixelScalingProperty
  nameWithType: SKGLView.IgnorePixelScalingProperty
  fullName: SkiaSharp.Views.Maui.Controls.SKGLView.IgnorePixelScalingProperty
- uid: SkiaSharp.Views.Maui.Controls.SKGLView.HasRenderLoopProperty
  commentId: F:SkiaSharp.Views.Maui.Controls.SKGLView.HasRenderLoopProperty
  parent: SkiaSharp.Views.Maui.Controls.SKGLView
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.views.maui.controls.skglview.hasrenderloopproperty
  name: HasRenderLoopProperty
  nameWithType: SKGLView.HasRenderLoopProperty
  fullName: SkiaSharp.Views.Maui.Controls.SKGLView.HasRenderLoopProperty
- uid: SkiaSharp.Views.Maui.Controls.SKGLView.EnableTouchEventsProperty
  commentId: F:SkiaSharp.Views.Maui.Controls.SKGLView.EnableTouchEventsProperty
  parent: SkiaSharp.Views.Maui.Controls.SKGLView
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.views.maui.controls.skglview.enabletoucheventsproperty
  name: EnableTouchEventsProperty
  nameWithType: SKGLView.EnableTouchEventsProperty
  fullName: SkiaSharp.Views.Maui.Controls.SKGLView.EnableTouchEventsProperty
- uid: SkiaSharp.Views.Maui.Controls.SKGLView.InvalidateSurface
  commentId: M:SkiaSharp.Views.Maui.Controls.SKGLView.InvalidateSurface
  parent: SkiaSharp.Views.Maui.Controls.SKGLView
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.views.maui.controls.skglview.invalidatesurface
  name: InvalidateSurface()
  nameWithType: SKGLView.InvalidateSurface()
  fullName: SkiaSharp.Views.Maui.Controls.SKGLView.InvalidateSurface()
  spec.csharp:
  - uid: SkiaSharp.Views.Maui.Controls.SKGLView.InvalidateSurface
    name: InvalidateSurface
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.views.maui.controls.skglview.invalidatesurface
  - name: (
  - name: )
  spec.vb:
  - uid: SkiaSharp.Views.Maui.Controls.SKGLView.InvalidateSurface
    name: InvalidateSurface
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.views.maui.controls.skglview.invalidatesurface
  - name: (
  - name: )
- uid: SkiaSharp.Views.Maui.Controls.SKGLView.OnPaintSurface(SkiaSharp.Views.Maui.SKPaintGLSurfaceEventArgs)
  commentId: M:SkiaSharp.Views.Maui.Controls.SKGLView.OnPaintSurface(SkiaSharp.Views.Maui.SKPaintGLSurfaceEventArgs)
  parent: SkiaSharp.Views.Maui.Controls.SKGLView
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.views.maui.controls.skglview.onpaintsurface
  name: OnPaintSurface(SKPaintGLSurfaceEventArgs)
  nameWithType: SKGLView.OnPaintSurface(SKPaintGLSurfaceEventArgs)
  fullName: SkiaSharp.Views.Maui.Controls.SKGLView.OnPaintSurface(SkiaSharp.Views.Maui.SKPaintGLSurfaceEventArgs)
  spec.csharp:
  - uid: SkiaSharp.Views.Maui.Controls.SKGLView.OnPaintSurface(SkiaSharp.Views.Maui.SKPaintGLSurfaceEventArgs)
    name: OnPaintSurface
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.views.maui.controls.skglview.onpaintsurface
  - name: (
  - uid: SkiaSharp.Views.Maui.SKPaintGLSurfaceEventArgs
    name: SKPaintGLSurfaceEventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.views.maui.skpaintglsurfaceeventargs
  - name: )
  spec.vb:
  - uid: SkiaSharp.Views.Maui.Controls.SKGLView.OnPaintSurface(SkiaSharp.Views.Maui.SKPaintGLSurfaceEventArgs)
    name: OnPaintSurface
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.views.maui.controls.skglview.onpaintsurface
  - name: (
  - uid: SkiaSharp.Views.Maui.SKPaintGLSurfaceEventArgs
    name: SKPaintGLSurfaceEventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.views.maui.skpaintglsurfaceeventargs
  - name: )
- uid: SkiaSharp.Views.Maui.Controls.SKGLView.OnTouch(SkiaSharp.Views.Maui.SKTouchEventArgs)
  commentId: M:SkiaSharp.Views.Maui.Controls.SKGLView.OnTouch(SkiaSharp.Views.Maui.SKTouchEventArgs)
  parent: SkiaSharp.Views.Maui.Controls.SKGLView
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.views.maui.controls.skglview.ontouch
  name: OnTouch(SKTouchEventArgs)
  nameWithType: SKGLView.OnTouch(SKTouchEventArgs)
  fullName: SkiaSharp.Views.Maui.Controls.SKGLView.OnTouch(SkiaSharp.Views.Maui.SKTouchEventArgs)
  spec.csharp:
  - uid: SkiaSharp.Views.Maui.Controls.SKGLView.OnTouch(SkiaSharp.Views.Maui.SKTouchEventArgs)
    name: OnTouch
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.views.maui.controls.skglview.ontouch
  - name: (
  - uid: SkiaSharp.Views.Maui.SKTouchEventArgs
    name: SKTouchEventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.views.maui.sktoucheventargs
  - name: )
  spec.vb:
  - uid: SkiaSharp.Views.Maui.Controls.SKGLView.OnTouch(SkiaSharp.Views.Maui.SKTouchEventArgs)
    name: OnTouch
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.views.maui.controls.skglview.ontouch
  - name: (
  - uid: SkiaSharp.Views.Maui.SKTouchEventArgs
    name: SKTouchEventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.views.maui.sktoucheventargs
  - name: )
- uid: SkiaSharp.Views.Maui.Controls.SKGLView.IgnorePixelScaling
  commentId: P:SkiaSharp.Views.Maui.Controls.SKGLView.IgnorePixelScaling
  parent: SkiaSharp.Views.Maui.Controls.SKGLView
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.views.maui.controls.skglview.ignorepixelscaling
  name: IgnorePixelScaling
  nameWithType: SKGLView.IgnorePixelScaling
  fullName: SkiaSharp.Views.Maui.Controls.SKGLView.IgnorePixelScaling
- uid: SkiaSharp.Views.Maui.Controls.SKGLView.HasRenderLoop
  commentId: P:SkiaSharp.Views.Maui.Controls.SKGLView.HasRenderLoop
  parent: SkiaSharp.Views.Maui.Controls.SKGLView
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.views.maui.controls.skglview.hasrenderloop
  name: HasRenderLoop
  nameWithType: SKGLView.HasRenderLoop
  fullName: SkiaSharp.Views.Maui.Controls.SKGLView.HasRenderLoop
- uid: SkiaSharp.Views.Maui.Controls.SKGLView.EnableTouchEvents
  commentId: P:SkiaSharp.Views.Maui.Controls.SKGLView.EnableTouchEvents
  parent: SkiaSharp.Views.Maui.Controls.SKGLView
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.views.maui.controls.skglview.enabletouchevents
  name: EnableTouchEvents
  nameWithType: SKGLView.EnableTouchEvents
  fullName: SkiaSharp.Views.Maui.Controls.SKGLView.EnableTouchEvents
- uid: SkiaSharp.Views.Maui.Controls.SKGLView.CanvasSize
  commentId: P:SkiaSharp.Views.Maui.Controls.SKGLView.CanvasSize
  parent: SkiaSharp.Views.Maui.Controls.SKGLView
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.views.maui.controls.skglview.canvassize
  name: CanvasSize
  nameWithType: SKGLView.CanvasSize
  fullName: SkiaSharp.Views.Maui.Controls.SKGLView.CanvasSize
- uid: SkiaSharp.Views.Maui.Controls.SKGLView.GRContext
  commentId: P:SkiaSharp.Views.Maui.Controls.SKGLView.GRContext
  parent: SkiaSharp.Views.Maui.Controls.SKGLView
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.views.maui.controls.skglview.grcontext
  name: GRContext
  nameWithType: SKGLView.GRContext
  fullName: SkiaSharp.Views.Maui.Controls.SKGLView.GRContext
- uid: SkiaSharp.Views.Maui.Controls.SKGLView.PaintSurface
  commentId: E:SkiaSharp.Views.Maui.Controls.SKGLView.PaintSurface
  parent: SkiaSharp.Views.Maui.Controls.SKGLView
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.views.maui.controls.skglview.paintsurface
  name: PaintSurface
  nameWithType: SKGLView.PaintSurface
  fullName: SkiaSharp.Views.Maui.Controls.SKGLView.PaintSurface
- uid: SkiaSharp.Views.Maui.Controls.SKGLView.Touch
  commentId: E:SkiaSharp.Views.Maui.Controls.SKGLView.Touch
  parent: SkiaSharp.Views.Maui.Controls.SKGLView
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.views.maui.controls.skglview.touch
  name: Touch
  nameWithType: SKGLView.Touch
  fullName: SkiaSharp.Views.Maui.Controls.SKGLView.Touch
- uid: Microsoft.Maui.Controls.View.VerticalOptionsProperty
  commentId: F:Microsoft.Maui.Controls.View.VerticalOptionsProperty
  parent: Microsoft.Maui.Controls.View
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.verticaloptionsproperty
  name: VerticalOptionsProperty
  nameWithType: View.VerticalOptionsProperty
  fullName: Microsoft.Maui.Controls.View.VerticalOptionsProperty
- uid: Microsoft.Maui.Controls.View.HorizontalOptionsProperty
  commentId: F:Microsoft.Maui.Controls.View.HorizontalOptionsProperty
  parent: Microsoft.Maui.Controls.View
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.horizontaloptionsproperty
  name: HorizontalOptionsProperty
  nameWithType: View.HorizontalOptionsProperty
  fullName: Microsoft.Maui.Controls.View.HorizontalOptionsProperty
- uid: Microsoft.Maui.Controls.View.MarginProperty
  commentId: F:Microsoft.Maui.Controls.View.MarginProperty
  parent: Microsoft.Maui.Controls.View
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.marginproperty
  name: MarginProperty
  nameWithType: View.MarginProperty
  fullName: Microsoft.Maui.Controls.View.MarginProperty
- uid: Microsoft.Maui.Controls.View.propertyMapper
  commentId: F:Microsoft.Maui.Controls.View.propertyMapper
  parent: Microsoft.Maui.Controls.View
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.propertymapper
  name: propertyMapper
  nameWithType: View.propertyMapper
  fullName: Microsoft.Maui.Controls.View.propertyMapper
- uid: Microsoft.Maui.Controls.View.ChangeVisualState
  commentId: M:Microsoft.Maui.Controls.View.ChangeVisualState
  parent: Microsoft.Maui.Controls.View
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.changevisualstate
  name: ChangeVisualState()
  nameWithType: View.ChangeVisualState()
  fullName: Microsoft.Maui.Controls.View.ChangeVisualState()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.View.ChangeVisualState
    name: ChangeVisualState
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.changevisualstate
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.View.ChangeVisualState
    name: ChangeVisualState
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.changevisualstate
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.View.GetChildElements(Microsoft.Maui.Graphics.Point)
  commentId: M:Microsoft.Maui.Controls.View.GetChildElements(Microsoft.Maui.Graphics.Point)
  parent: Microsoft.Maui.Controls.View
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.getchildelements
  name: GetChildElements(Point)
  nameWithType: View.GetChildElements(Point)
  fullName: Microsoft.Maui.Controls.View.GetChildElements(Microsoft.Maui.Graphics.Point)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.View.GetChildElements(Microsoft.Maui.Graphics.Point)
    name: GetChildElements
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.getchildelements
  - name: (
  - uid: Microsoft.Maui.Graphics.Point
    name: Point
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.point
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.View.GetChildElements(Microsoft.Maui.Graphics.Point)
    name: GetChildElements
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.getchildelements
  - name: (
  - uid: Microsoft.Maui.Graphics.Point
    name: Point
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.point
  - name: )
- uid: Microsoft.Maui.Controls.View.OnBindingContextChanged
  commentId: M:Microsoft.Maui.Controls.View.OnBindingContextChanged
  parent: Microsoft.Maui.Controls.View
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.onbindingcontextchanged
  name: OnBindingContextChanged()
  nameWithType: View.OnBindingContextChanged()
  fullName: Microsoft.Maui.Controls.View.OnBindingContextChanged()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.View.OnBindingContextChanged
    name: OnBindingContextChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.onbindingcontextchanged
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.View.OnBindingContextChanged
    name: OnBindingContextChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.onbindingcontextchanged
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.View.GetRendererOverrides``1
  commentId: M:Microsoft.Maui.Controls.View.GetRendererOverrides``1
  parent: Microsoft.Maui.Controls.View
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.getrendereroverrides
  name: GetRendererOverrides<T>()
  nameWithType: View.GetRendererOverrides<T>()
  fullName: Microsoft.Maui.Controls.View.GetRendererOverrides<T>()
  nameWithType.vb: View.GetRendererOverrides(Of T)()
  fullName.vb: Microsoft.Maui.Controls.View.GetRendererOverrides(Of T)()
  name.vb: GetRendererOverrides(Of T)()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.View.GetRendererOverrides``1
    name: GetRendererOverrides
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.getrendereroverrides
  - name: <
  - name: T
  - name: '>'
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.View.GetRendererOverrides``1
    name: GetRendererOverrides
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.getrendereroverrides
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.View.GestureController
  commentId: P:Microsoft.Maui.Controls.View.GestureController
  parent: Microsoft.Maui.Controls.View
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.gesturecontroller
  name: GestureController
  nameWithType: View.GestureController
  fullName: Microsoft.Maui.Controls.View.GestureController
- uid: Microsoft.Maui.Controls.View.GestureRecognizers
  commentId: P:Microsoft.Maui.Controls.View.GestureRecognizers
  parent: Microsoft.Maui.Controls.View
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.gesturerecognizers
  name: GestureRecognizers
  nameWithType: View.GestureRecognizers
  fullName: Microsoft.Maui.Controls.View.GestureRecognizers
- uid: Microsoft.Maui.Controls.View.HorizontalOptions
  commentId: P:Microsoft.Maui.Controls.View.HorizontalOptions
  parent: Microsoft.Maui.Controls.View
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.horizontaloptions
  name: HorizontalOptions
  nameWithType: View.HorizontalOptions
  fullName: Microsoft.Maui.Controls.View.HorizontalOptions
- uid: Microsoft.Maui.Controls.View.Margin
  commentId: P:Microsoft.Maui.Controls.View.Margin
  parent: Microsoft.Maui.Controls.View
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.margin
  name: Margin
  nameWithType: View.Margin
  fullName: Microsoft.Maui.Controls.View.Margin
- uid: Microsoft.Maui.Controls.View.VerticalOptions
  commentId: P:Microsoft.Maui.Controls.View.VerticalOptions
  parent: Microsoft.Maui.Controls.View
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.verticaloptions
  name: VerticalOptions
  nameWithType: View.VerticalOptions
  fullName: Microsoft.Maui.Controls.View.VerticalOptions
- uid: Microsoft.Maui.Controls.VisualElement.NavigationProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.NavigationProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.navigationproperty
  name: NavigationProperty
  nameWithType: VisualElement.NavigationProperty
  fullName: Microsoft.Maui.Controls.VisualElement.NavigationProperty
- uid: Microsoft.Maui.Controls.VisualElement.StyleProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.StyleProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.styleproperty
  name: StyleProperty
  nameWithType: VisualElement.StyleProperty
  fullName: Microsoft.Maui.Controls.VisualElement.StyleProperty
- uid: Microsoft.Maui.Controls.VisualElement.InputTransparentProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.InputTransparentProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.inputtransparentproperty
  name: InputTransparentProperty
  nameWithType: VisualElement.InputTransparentProperty
  fullName: Microsoft.Maui.Controls.VisualElement.InputTransparentProperty
- uid: Microsoft.Maui.Controls.VisualElement.IsEnabledProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.IsEnabledProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isenabledproperty
  name: IsEnabledProperty
  nameWithType: VisualElement.IsEnabledProperty
  fullName: Microsoft.Maui.Controls.VisualElement.IsEnabledProperty
- uid: Microsoft.Maui.Controls.VisualElement.XProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.XProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.xproperty
  name: XProperty
  nameWithType: VisualElement.XProperty
  fullName: Microsoft.Maui.Controls.VisualElement.XProperty
- uid: Microsoft.Maui.Controls.VisualElement.YProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.YProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.yproperty
  name: YProperty
  nameWithType: VisualElement.YProperty
  fullName: Microsoft.Maui.Controls.VisualElement.YProperty
- uid: Microsoft.Maui.Controls.VisualElement.AnchorXProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.AnchorXProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.anchorxproperty
  name: AnchorXProperty
  nameWithType: VisualElement.AnchorXProperty
  fullName: Microsoft.Maui.Controls.VisualElement.AnchorXProperty
- uid: Microsoft.Maui.Controls.VisualElement.AnchorYProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.AnchorYProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.anchoryproperty
  name: AnchorYProperty
  nameWithType: VisualElement.AnchorYProperty
  fullName: Microsoft.Maui.Controls.VisualElement.AnchorYProperty
- uid: Microsoft.Maui.Controls.VisualElement.TranslationXProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.TranslationXProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.translationxproperty
  name: TranslationXProperty
  nameWithType: VisualElement.TranslationXProperty
  fullName: Microsoft.Maui.Controls.VisualElement.TranslationXProperty
- uid: Microsoft.Maui.Controls.VisualElement.TranslationYProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.TranslationYProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.translationyproperty
  name: TranslationYProperty
  nameWithType: VisualElement.TranslationYProperty
  fullName: Microsoft.Maui.Controls.VisualElement.TranslationYProperty
- uid: Microsoft.Maui.Controls.VisualElement.WidthProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.WidthProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.widthproperty
  name: WidthProperty
  nameWithType: VisualElement.WidthProperty
  fullName: Microsoft.Maui.Controls.VisualElement.WidthProperty
- uid: Microsoft.Maui.Controls.VisualElement.HeightProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.HeightProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.heightproperty
  name: HeightProperty
  nameWithType: VisualElement.HeightProperty
  fullName: Microsoft.Maui.Controls.VisualElement.HeightProperty
- uid: Microsoft.Maui.Controls.VisualElement.RotationProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.RotationProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotationproperty
  name: RotationProperty
  nameWithType: VisualElement.RotationProperty
  fullName: Microsoft.Maui.Controls.VisualElement.RotationProperty
- uid: Microsoft.Maui.Controls.VisualElement.RotationXProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.RotationXProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotationxproperty
  name: RotationXProperty
  nameWithType: VisualElement.RotationXProperty
  fullName: Microsoft.Maui.Controls.VisualElement.RotationXProperty
- uid: Microsoft.Maui.Controls.VisualElement.RotationYProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.RotationYProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotationyproperty
  name: RotationYProperty
  nameWithType: VisualElement.RotationYProperty
  fullName: Microsoft.Maui.Controls.VisualElement.RotationYProperty
- uid: Microsoft.Maui.Controls.VisualElement.ScaleProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.ScaleProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.scaleproperty
  name: ScaleProperty
  nameWithType: VisualElement.ScaleProperty
  fullName: Microsoft.Maui.Controls.VisualElement.ScaleProperty
- uid: Microsoft.Maui.Controls.VisualElement.ScaleXProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.ScaleXProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.scalexproperty
  name: ScaleXProperty
  nameWithType: VisualElement.ScaleXProperty
  fullName: Microsoft.Maui.Controls.VisualElement.ScaleXProperty
- uid: Microsoft.Maui.Controls.VisualElement.ScaleYProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.ScaleYProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.scaleyproperty
  name: ScaleYProperty
  nameWithType: VisualElement.ScaleYProperty
  fullName: Microsoft.Maui.Controls.VisualElement.ScaleYProperty
- uid: Microsoft.Maui.Controls.VisualElement.ClipProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.ClipProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.clipproperty
  name: ClipProperty
  nameWithType: VisualElement.ClipProperty
  fullName: Microsoft.Maui.Controls.VisualElement.ClipProperty
- uid: Microsoft.Maui.Controls.VisualElement.VisualProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.VisualProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.visualproperty
  name: VisualProperty
  nameWithType: VisualElement.VisualProperty
  fullName: Microsoft.Maui.Controls.VisualElement.VisualProperty
- uid: Microsoft.Maui.Controls.VisualElement.IsVisibleProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.IsVisibleProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isvisibleproperty
  name: IsVisibleProperty
  nameWithType: VisualElement.IsVisibleProperty
  fullName: Microsoft.Maui.Controls.VisualElement.IsVisibleProperty
- uid: Microsoft.Maui.Controls.VisualElement.OpacityProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.OpacityProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.opacityproperty
  name: OpacityProperty
  nameWithType: VisualElement.OpacityProperty
  fullName: Microsoft.Maui.Controls.VisualElement.OpacityProperty
- uid: Microsoft.Maui.Controls.VisualElement.BackgroundColorProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.BackgroundColorProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.backgroundcolorproperty
  name: BackgroundColorProperty
  nameWithType: VisualElement.BackgroundColorProperty
  fullName: Microsoft.Maui.Controls.VisualElement.BackgroundColorProperty
- uid: Microsoft.Maui.Controls.VisualElement.BackgroundProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.BackgroundProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.backgroundproperty
  name: BackgroundProperty
  nameWithType: VisualElement.BackgroundProperty
  fullName: Microsoft.Maui.Controls.VisualElement.BackgroundProperty
- uid: Microsoft.Maui.Controls.VisualElement.BehaviorsProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.BehaviorsProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.behaviorsproperty
  name: BehaviorsProperty
  nameWithType: VisualElement.BehaviorsProperty
  fullName: Microsoft.Maui.Controls.VisualElement.BehaviorsProperty
- uid: Microsoft.Maui.Controls.VisualElement.TriggersProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.TriggersProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.triggersproperty
  name: TriggersProperty
  nameWithType: VisualElement.TriggersProperty
  fullName: Microsoft.Maui.Controls.VisualElement.TriggersProperty
- uid: Microsoft.Maui.Controls.VisualElement.WidthRequestProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.WidthRequestProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.widthrequestproperty
  name: WidthRequestProperty
  nameWithType: VisualElement.WidthRequestProperty
  fullName: Microsoft.Maui.Controls.VisualElement.WidthRequestProperty
- uid: Microsoft.Maui.Controls.VisualElement.HeightRequestProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.HeightRequestProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.heightrequestproperty
  name: HeightRequestProperty
  nameWithType: VisualElement.HeightRequestProperty
  fullName: Microsoft.Maui.Controls.VisualElement.HeightRequestProperty
- uid: Microsoft.Maui.Controls.VisualElement.MinimumWidthRequestProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.MinimumWidthRequestProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.minimumwidthrequestproperty
  name: MinimumWidthRequestProperty
  nameWithType: VisualElement.MinimumWidthRequestProperty
  fullName: Microsoft.Maui.Controls.VisualElement.MinimumWidthRequestProperty
- uid: Microsoft.Maui.Controls.VisualElement.MinimumHeightRequestProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.MinimumHeightRequestProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.minimumheightrequestproperty
  name: MinimumHeightRequestProperty
  nameWithType: VisualElement.MinimumHeightRequestProperty
  fullName: Microsoft.Maui.Controls.VisualElement.MinimumHeightRequestProperty
- uid: Microsoft.Maui.Controls.VisualElement.MaximumWidthRequestProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.MaximumWidthRequestProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.maximumwidthrequestproperty
  name: MaximumWidthRequestProperty
  nameWithType: VisualElement.MaximumWidthRequestProperty
  fullName: Microsoft.Maui.Controls.VisualElement.MaximumWidthRequestProperty
- uid: Microsoft.Maui.Controls.VisualElement.MaximumHeightRequestProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.MaximumHeightRequestProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.maximumheightrequestproperty
  name: MaximumHeightRequestProperty
  nameWithType: VisualElement.MaximumHeightRequestProperty
  fullName: Microsoft.Maui.Controls.VisualElement.MaximumHeightRequestProperty
- uid: Microsoft.Maui.Controls.VisualElement.IsFocusedProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.IsFocusedProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isfocusedproperty
  name: IsFocusedProperty
  nameWithType: VisualElement.IsFocusedProperty
  fullName: Microsoft.Maui.Controls.VisualElement.IsFocusedProperty
- uid: Microsoft.Maui.Controls.VisualElement.FlowDirectionProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.FlowDirectionProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.flowdirectionproperty
  name: FlowDirectionProperty
  nameWithType: VisualElement.FlowDirectionProperty
  fullName: Microsoft.Maui.Controls.VisualElement.FlowDirectionProperty
- uid: Microsoft.Maui.Controls.VisualElement.WindowProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.WindowProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.windowproperty
  name: WindowProperty
  nameWithType: VisualElement.WindowProperty
  fullName: Microsoft.Maui.Controls.VisualElement.WindowProperty
- uid: Microsoft.Maui.Controls.VisualElement.ShadowProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.ShadowProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.shadowproperty
  name: ShadowProperty
  nameWithType: VisualElement.ShadowProperty
  fullName: Microsoft.Maui.Controls.VisualElement.ShadowProperty
- uid: Microsoft.Maui.Controls.VisualElement.ZIndexProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.ZIndexProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.zindexproperty
  name: ZIndexProperty
  nameWithType: VisualElement.ZIndexProperty
  fullName: Microsoft.Maui.Controls.VisualElement.ZIndexProperty
- uid: Microsoft.Maui.Controls.VisualElement.BatchBegin
  commentId: M:Microsoft.Maui.Controls.VisualElement.BatchBegin
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.batchbegin
  name: BatchBegin()
  nameWithType: VisualElement.BatchBegin()
  fullName: Microsoft.Maui.Controls.VisualElement.BatchBegin()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.BatchBegin
    name: BatchBegin
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.batchbegin
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.BatchBegin
    name: BatchBegin
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.batchbegin
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.BatchCommit
  commentId: M:Microsoft.Maui.Controls.VisualElement.BatchCommit
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.batchcommit
  name: BatchCommit()
  nameWithType: VisualElement.BatchCommit()
  fullName: Microsoft.Maui.Controls.VisualElement.BatchCommit()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.BatchCommit
    name: BatchCommit
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.batchcommit
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.BatchCommit
    name: BatchCommit
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.batchcommit
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.Focus
  commentId: M:Microsoft.Maui.Controls.VisualElement.Focus
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.focus
  name: Focus()
  nameWithType: VisualElement.Focus()
  fullName: Microsoft.Maui.Controls.VisualElement.Focus()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.Focus
    name: Focus
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.focus
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.Focus
    name: Focus
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.focus
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.Measure(System.Double,System.Double)
  commentId: M:Microsoft.Maui.Controls.VisualElement.Measure(System.Double,System.Double)
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.measure#microsoft-maui-controls-visualelement-measure(system-double-system-double)
  name: Measure(double, double)
  nameWithType: VisualElement.Measure(double, double)
  fullName: Microsoft.Maui.Controls.VisualElement.Measure(double, double)
  nameWithType.vb: VisualElement.Measure(Double, Double)
  fullName.vb: Microsoft.Maui.Controls.VisualElement.Measure(Double, Double)
  name.vb: Measure(Double, Double)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.Measure(System.Double,System.Double)
    name: Measure
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.measure#microsoft-maui-controls-visualelement-measure(system-double-system-double)
  - name: (
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.Measure(System.Double,System.Double)
    name: Measure
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.measure#microsoft-maui-controls-visualelement-measure(system-double-system-double)
  - name: (
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.Measure(System.Double,System.Double,Microsoft.Maui.Controls.MeasureFlags)
  commentId: M:Microsoft.Maui.Controls.VisualElement.Measure(System.Double,System.Double,Microsoft.Maui.Controls.MeasureFlags)
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.measure#microsoft-maui-controls-visualelement-measure(system-double-system-double-microsoft-maui-controls-measureflags)
  name: Measure(double, double, MeasureFlags)
  nameWithType: VisualElement.Measure(double, double, MeasureFlags)
  fullName: Microsoft.Maui.Controls.VisualElement.Measure(double, double, Microsoft.Maui.Controls.MeasureFlags)
  nameWithType.vb: VisualElement.Measure(Double, Double, MeasureFlags)
  fullName.vb: Microsoft.Maui.Controls.VisualElement.Measure(Double, Double, Microsoft.Maui.Controls.MeasureFlags)
  name.vb: Measure(Double, Double, MeasureFlags)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.Measure(System.Double,System.Double,Microsoft.Maui.Controls.MeasureFlags)
    name: Measure
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.measure#microsoft-maui-controls-visualelement-measure(system-double-system-double-microsoft-maui-controls-measureflags)
  - name: (
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.MeasureFlags
    name: MeasureFlags
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.measureflags
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.Measure(System.Double,System.Double,Microsoft.Maui.Controls.MeasureFlags)
    name: Measure
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.measure#microsoft-maui-controls-visualelement-measure(system-double-system-double-microsoft-maui-controls-measureflags)
  - name: (
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.MeasureFlags
    name: MeasureFlags
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.measureflags
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.Unfocus
  commentId: M:Microsoft.Maui.Controls.VisualElement.Unfocus
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.unfocus
  name: Unfocus()
  nameWithType: VisualElement.Unfocus()
  fullName: Microsoft.Maui.Controls.VisualElement.Unfocus()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.Unfocus
    name: Unfocus
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.unfocus
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.Unfocus
    name: Unfocus
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.unfocus
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.InvalidateMeasure
  commentId: M:Microsoft.Maui.Controls.VisualElement.InvalidateMeasure
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.invalidatemeasure
  name: InvalidateMeasure()
  nameWithType: VisualElement.InvalidateMeasure()
  fullName: Microsoft.Maui.Controls.VisualElement.InvalidateMeasure()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.InvalidateMeasure
    name: InvalidateMeasure
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.invalidatemeasure
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.InvalidateMeasure
    name: InvalidateMeasure
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.invalidatemeasure
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.OnChildAdded(Microsoft.Maui.Controls.Element)
  commentId: M:Microsoft.Maui.Controls.VisualElement.OnChildAdded(Microsoft.Maui.Controls.Element)
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onchildadded
  name: OnChildAdded(Element)
  nameWithType: VisualElement.OnChildAdded(Element)
  fullName: Microsoft.Maui.Controls.VisualElement.OnChildAdded(Microsoft.Maui.Controls.Element)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.OnChildAdded(Microsoft.Maui.Controls.Element)
    name: OnChildAdded
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onchildadded
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.OnChildAdded(Microsoft.Maui.Controls.Element)
    name: OnChildAdded
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onchildadded
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.OnChildRemoved(Microsoft.Maui.Controls.Element,System.Int32)
  commentId: M:Microsoft.Maui.Controls.VisualElement.OnChildRemoved(Microsoft.Maui.Controls.Element,System.Int32)
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onchildremoved
  name: OnChildRemoved(Element, int)
  nameWithType: VisualElement.OnChildRemoved(Element, int)
  fullName: Microsoft.Maui.Controls.VisualElement.OnChildRemoved(Microsoft.Maui.Controls.Element, int)
  nameWithType.vb: VisualElement.OnChildRemoved(Element, Integer)
  fullName.vb: Microsoft.Maui.Controls.VisualElement.OnChildRemoved(Microsoft.Maui.Controls.Element, Integer)
  name.vb: OnChildRemoved(Element, Integer)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.OnChildRemoved(Microsoft.Maui.Controls.Element,System.Int32)
    name: OnChildRemoved
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onchildremoved
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: ','
  - name: " "
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.OnChildRemoved(Microsoft.Maui.Controls.Element,System.Int32)
    name: OnChildRemoved
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onchildremoved
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: ','
  - name: " "
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.OnChildrenReordered
  commentId: M:Microsoft.Maui.Controls.VisualElement.OnChildrenReordered
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onchildrenreordered
  name: OnChildrenReordered()
  nameWithType: VisualElement.OnChildrenReordered()
  fullName: Microsoft.Maui.Controls.VisualElement.OnChildrenReordered()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.OnChildrenReordered
    name: OnChildrenReordered
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onchildrenreordered
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.OnChildrenReordered
    name: OnChildrenReordered
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onchildrenreordered
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.OnMeasure(System.Double,System.Double)
  commentId: M:Microsoft.Maui.Controls.VisualElement.OnMeasure(System.Double,System.Double)
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onmeasure
  name: OnMeasure(double, double)
  nameWithType: VisualElement.OnMeasure(double, double)
  fullName: Microsoft.Maui.Controls.VisualElement.OnMeasure(double, double)
  nameWithType.vb: VisualElement.OnMeasure(Double, Double)
  fullName.vb: Microsoft.Maui.Controls.VisualElement.OnMeasure(Double, Double)
  name.vb: OnMeasure(Double, Double)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.OnMeasure(System.Double,System.Double)
    name: OnMeasure
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onmeasure
  - name: (
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.OnMeasure(System.Double,System.Double)
    name: OnMeasure
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onmeasure
  - name: (
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.OnSizeAllocated(System.Double,System.Double)
  commentId: M:Microsoft.Maui.Controls.VisualElement.OnSizeAllocated(System.Double,System.Double)
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onsizeallocated
  name: OnSizeAllocated(double, double)
  nameWithType: VisualElement.OnSizeAllocated(double, double)
  fullName: Microsoft.Maui.Controls.VisualElement.OnSizeAllocated(double, double)
  nameWithType.vb: VisualElement.OnSizeAllocated(Double, Double)
  fullName.vb: Microsoft.Maui.Controls.VisualElement.OnSizeAllocated(Double, Double)
  name.vb: OnSizeAllocated(Double, Double)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.OnSizeAllocated(System.Double,System.Double)
    name: OnSizeAllocated
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onsizeallocated
  - name: (
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.OnSizeAllocated(System.Double,System.Double)
    name: OnSizeAllocated
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onsizeallocated
  - name: (
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.SizeAllocated(System.Double,System.Double)
  commentId: M:Microsoft.Maui.Controls.VisualElement.SizeAllocated(System.Double,System.Double)
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.sizeallocated
  name: SizeAllocated(double, double)
  nameWithType: VisualElement.SizeAllocated(double, double)
  fullName: Microsoft.Maui.Controls.VisualElement.SizeAllocated(double, double)
  nameWithType.vb: VisualElement.SizeAllocated(Double, Double)
  fullName.vb: Microsoft.Maui.Controls.VisualElement.SizeAllocated(Double, Double)
  name.vb: SizeAllocated(Double, Double)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.SizeAllocated(System.Double,System.Double)
    name: SizeAllocated
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.sizeallocated
  - name: (
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.SizeAllocated(System.Double,System.Double)
    name: SizeAllocated
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.sizeallocated
  - name: (
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.RefreshIsEnabledProperty
  commentId: M:Microsoft.Maui.Controls.VisualElement.RefreshIsEnabledProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.refreshisenabledproperty
  name: RefreshIsEnabledProperty()
  nameWithType: VisualElement.RefreshIsEnabledProperty()
  fullName: Microsoft.Maui.Controls.VisualElement.RefreshIsEnabledProperty()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.RefreshIsEnabledProperty
    name: RefreshIsEnabledProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.refreshisenabledproperty
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.RefreshIsEnabledProperty
    name: RefreshIsEnabledProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.refreshisenabledproperty
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.Arrange(Microsoft.Maui.Graphics.Rect)
  commentId: M:Microsoft.Maui.Controls.VisualElement.Arrange(Microsoft.Maui.Graphics.Rect)
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.arrange
  name: Arrange(Rect)
  nameWithType: VisualElement.Arrange(Rect)
  fullName: Microsoft.Maui.Controls.VisualElement.Arrange(Microsoft.Maui.Graphics.Rect)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.Arrange(Microsoft.Maui.Graphics.Rect)
    name: Arrange
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.arrange
  - name: (
  - uid: Microsoft.Maui.Graphics.Rect
    name: Rect
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.rect
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.Arrange(Microsoft.Maui.Graphics.Rect)
    name: Arrange
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.arrange
  - name: (
  - uid: Microsoft.Maui.Graphics.Rect
    name: Rect
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.rect
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.ArrangeOverride(Microsoft.Maui.Graphics.Rect)
  commentId: M:Microsoft.Maui.Controls.VisualElement.ArrangeOverride(Microsoft.Maui.Graphics.Rect)
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.arrangeoverride
  name: ArrangeOverride(Rect)
  nameWithType: VisualElement.ArrangeOverride(Rect)
  fullName: Microsoft.Maui.Controls.VisualElement.ArrangeOverride(Microsoft.Maui.Graphics.Rect)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.ArrangeOverride(Microsoft.Maui.Graphics.Rect)
    name: ArrangeOverride
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.arrangeoverride
  - name: (
  - uid: Microsoft.Maui.Graphics.Rect
    name: Rect
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.rect
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.ArrangeOverride(Microsoft.Maui.Graphics.Rect)
    name: ArrangeOverride
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.arrangeoverride
  - name: (
  - uid: Microsoft.Maui.Graphics.Rect
    name: Rect
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.rect
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.Layout(Microsoft.Maui.Graphics.Rect)
  commentId: M:Microsoft.Maui.Controls.VisualElement.Layout(Microsoft.Maui.Graphics.Rect)
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.layout
  name: Layout(Rect)
  nameWithType: VisualElement.Layout(Rect)
  fullName: Microsoft.Maui.Controls.VisualElement.Layout(Microsoft.Maui.Graphics.Rect)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.Layout(Microsoft.Maui.Graphics.Rect)
    name: Layout
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.layout
  - name: (
  - uid: Microsoft.Maui.Graphics.Rect
    name: Rect
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.rect
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.Layout(Microsoft.Maui.Graphics.Rect)
    name: Layout
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.layout
  - name: (
  - uid: Microsoft.Maui.Graphics.Rect
    name: Rect
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.rect
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.InvalidateMeasureOverride
  commentId: M:Microsoft.Maui.Controls.VisualElement.InvalidateMeasureOverride
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.invalidatemeasureoverride
  name: InvalidateMeasureOverride()
  nameWithType: VisualElement.InvalidateMeasureOverride()
  fullName: Microsoft.Maui.Controls.VisualElement.InvalidateMeasureOverride()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.InvalidateMeasureOverride
    name: InvalidateMeasureOverride
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.invalidatemeasureoverride
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.InvalidateMeasureOverride
    name: InvalidateMeasureOverride
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.invalidatemeasureoverride
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.MeasureOverride(System.Double,System.Double)
  commentId: M:Microsoft.Maui.Controls.VisualElement.MeasureOverride(System.Double,System.Double)
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.measureoverride
  name: MeasureOverride(double, double)
  nameWithType: VisualElement.MeasureOverride(double, double)
  fullName: Microsoft.Maui.Controls.VisualElement.MeasureOverride(double, double)
  nameWithType.vb: VisualElement.MeasureOverride(Double, Double)
  fullName.vb: Microsoft.Maui.Controls.VisualElement.MeasureOverride(Double, Double)
  name.vb: MeasureOverride(Double, Double)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.MeasureOverride(System.Double,System.Double)
    name: MeasureOverride
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.measureoverride
  - name: (
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.MeasureOverride(System.Double,System.Double)
    name: MeasureOverride
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.measureoverride
  - name: (
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.MapBackgroundColor(Microsoft.Maui.IViewHandler,Microsoft.Maui.IView)
  commentId: M:Microsoft.Maui.Controls.VisualElement.MapBackgroundColor(Microsoft.Maui.IViewHandler,Microsoft.Maui.IView)
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.mapbackgroundcolor
  name: MapBackgroundColor(IViewHandler, IView)
  nameWithType: VisualElement.MapBackgroundColor(IViewHandler, IView)
  fullName: Microsoft.Maui.Controls.VisualElement.MapBackgroundColor(Microsoft.Maui.IViewHandler, Microsoft.Maui.IView)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.MapBackgroundColor(Microsoft.Maui.IViewHandler,Microsoft.Maui.IView)
    name: MapBackgroundColor
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.mapbackgroundcolor
  - name: (
  - uid: Microsoft.Maui.IViewHandler
    name: IViewHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iviewhandler
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.IView
    name: IView
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iview
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.MapBackgroundColor(Microsoft.Maui.IViewHandler,Microsoft.Maui.IView)
    name: MapBackgroundColor
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.mapbackgroundcolor
  - name: (
  - uid: Microsoft.Maui.IViewHandler
    name: IViewHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iviewhandler
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.IView
    name: IView
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iview
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.MapBackgroundImageSource(Microsoft.Maui.IViewHandler,Microsoft.Maui.IView)
  commentId: M:Microsoft.Maui.Controls.VisualElement.MapBackgroundImageSource(Microsoft.Maui.IViewHandler,Microsoft.Maui.IView)
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.mapbackgroundimagesource
  name: MapBackgroundImageSource(IViewHandler, IView)
  nameWithType: VisualElement.MapBackgroundImageSource(IViewHandler, IView)
  fullName: Microsoft.Maui.Controls.VisualElement.MapBackgroundImageSource(Microsoft.Maui.IViewHandler, Microsoft.Maui.IView)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.MapBackgroundImageSource(Microsoft.Maui.IViewHandler,Microsoft.Maui.IView)
    name: MapBackgroundImageSource
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.mapbackgroundimagesource
  - name: (
  - uid: Microsoft.Maui.IViewHandler
    name: IViewHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iviewhandler
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.IView
    name: IView
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iview
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.MapBackgroundImageSource(Microsoft.Maui.IViewHandler,Microsoft.Maui.IView)
    name: MapBackgroundImageSource
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.mapbackgroundimagesource
  - name: (
  - uid: Microsoft.Maui.IViewHandler
    name: IViewHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iviewhandler
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.IView
    name: IView
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iview
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.Visual
  commentId: P:Microsoft.Maui.Controls.VisualElement.Visual
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.visual
  name: Visual
  nameWithType: VisualElement.Visual
  fullName: Microsoft.Maui.Controls.VisualElement.Visual
- uid: Microsoft.Maui.Controls.VisualElement.FlowDirection
  commentId: P:Microsoft.Maui.Controls.VisualElement.FlowDirection
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.flowdirection
  name: FlowDirection
  nameWithType: VisualElement.FlowDirection
  fullName: Microsoft.Maui.Controls.VisualElement.FlowDirection
- uid: Microsoft.Maui.Controls.VisualElement.Window
  commentId: P:Microsoft.Maui.Controls.VisualElement.Window
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.window
  name: Window
  nameWithType: VisualElement.Window
  fullName: Microsoft.Maui.Controls.VisualElement.Window
- uid: Microsoft.Maui.Controls.VisualElement.AnchorX
  commentId: P:Microsoft.Maui.Controls.VisualElement.AnchorX
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.anchorx
  name: AnchorX
  nameWithType: VisualElement.AnchorX
  fullName: Microsoft.Maui.Controls.VisualElement.AnchorX
- uid: Microsoft.Maui.Controls.VisualElement.AnchorY
  commentId: P:Microsoft.Maui.Controls.VisualElement.AnchorY
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.anchory
  name: AnchorY
  nameWithType: VisualElement.AnchorY
  fullName: Microsoft.Maui.Controls.VisualElement.AnchorY
- uid: Microsoft.Maui.Controls.VisualElement.BackgroundColor
  commentId: P:Microsoft.Maui.Controls.VisualElement.BackgroundColor
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.backgroundcolor
  name: BackgroundColor
  nameWithType: VisualElement.BackgroundColor
  fullName: Microsoft.Maui.Controls.VisualElement.BackgroundColor
- uid: Microsoft.Maui.Controls.VisualElement.Background
  commentId: P:Microsoft.Maui.Controls.VisualElement.Background
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.background
  name: Background
  nameWithType: VisualElement.Background
  fullName: Microsoft.Maui.Controls.VisualElement.Background
- uid: Microsoft.Maui.Controls.VisualElement.Behaviors
  commentId: P:Microsoft.Maui.Controls.VisualElement.Behaviors
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.behaviors
  name: Behaviors
  nameWithType: VisualElement.Behaviors
  fullName: Microsoft.Maui.Controls.VisualElement.Behaviors
- uid: Microsoft.Maui.Controls.VisualElement.Bounds
  commentId: P:Microsoft.Maui.Controls.VisualElement.Bounds
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.bounds
  name: Bounds
  nameWithType: VisualElement.Bounds
  fullName: Microsoft.Maui.Controls.VisualElement.Bounds
- uid: Microsoft.Maui.Controls.VisualElement.Height
  commentId: P:Microsoft.Maui.Controls.VisualElement.Height
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.height
  name: Height
  nameWithType: VisualElement.Height
  fullName: Microsoft.Maui.Controls.VisualElement.Height
- uid: Microsoft.Maui.Controls.VisualElement.HeightRequest
  commentId: P:Microsoft.Maui.Controls.VisualElement.HeightRequest
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.heightrequest
  name: HeightRequest
  nameWithType: VisualElement.HeightRequest
  fullName: Microsoft.Maui.Controls.VisualElement.HeightRequest
- uid: Microsoft.Maui.Controls.VisualElement.InputTransparent
  commentId: P:Microsoft.Maui.Controls.VisualElement.InputTransparent
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.inputtransparent
  name: InputTransparent
  nameWithType: VisualElement.InputTransparent
  fullName: Microsoft.Maui.Controls.VisualElement.InputTransparent
- uid: Microsoft.Maui.Controls.VisualElement.IsEnabled
  commentId: P:Microsoft.Maui.Controls.VisualElement.IsEnabled
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isenabled
  name: IsEnabled
  nameWithType: VisualElement.IsEnabled
  fullName: Microsoft.Maui.Controls.VisualElement.IsEnabled
- uid: Microsoft.Maui.Controls.VisualElement.IsEnabledCore
  commentId: P:Microsoft.Maui.Controls.VisualElement.IsEnabledCore
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isenabledcore
  name: IsEnabledCore
  nameWithType: VisualElement.IsEnabledCore
  fullName: Microsoft.Maui.Controls.VisualElement.IsEnabledCore
- uid: Microsoft.Maui.Controls.VisualElement.IsFocused
  commentId: P:Microsoft.Maui.Controls.VisualElement.IsFocused
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isfocused
  name: IsFocused
  nameWithType: VisualElement.IsFocused
  fullName: Microsoft.Maui.Controls.VisualElement.IsFocused
- uid: Microsoft.Maui.Controls.VisualElement.IsVisible
  commentId: P:Microsoft.Maui.Controls.VisualElement.IsVisible
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isvisible
  name: IsVisible
  nameWithType: VisualElement.IsVisible
  fullName: Microsoft.Maui.Controls.VisualElement.IsVisible
- uid: Microsoft.Maui.Controls.VisualElement.MinimumHeightRequest
  commentId: P:Microsoft.Maui.Controls.VisualElement.MinimumHeightRequest
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.minimumheightrequest
  name: MinimumHeightRequest
  nameWithType: VisualElement.MinimumHeightRequest
  fullName: Microsoft.Maui.Controls.VisualElement.MinimumHeightRequest
- uid: Microsoft.Maui.Controls.VisualElement.MinimumWidthRequest
  commentId: P:Microsoft.Maui.Controls.VisualElement.MinimumWidthRequest
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.minimumwidthrequest
  name: MinimumWidthRequest
  nameWithType: VisualElement.MinimumWidthRequest
  fullName: Microsoft.Maui.Controls.VisualElement.MinimumWidthRequest
- uid: Microsoft.Maui.Controls.VisualElement.MaximumHeightRequest
  commentId: P:Microsoft.Maui.Controls.VisualElement.MaximumHeightRequest
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.maximumheightrequest
  name: MaximumHeightRequest
  nameWithType: VisualElement.MaximumHeightRequest
  fullName: Microsoft.Maui.Controls.VisualElement.MaximumHeightRequest
- uid: Microsoft.Maui.Controls.VisualElement.MaximumWidthRequest
  commentId: P:Microsoft.Maui.Controls.VisualElement.MaximumWidthRequest
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.maximumwidthrequest
  name: MaximumWidthRequest
  nameWithType: VisualElement.MaximumWidthRequest
  fullName: Microsoft.Maui.Controls.VisualElement.MaximumWidthRequest
- uid: Microsoft.Maui.Controls.VisualElement.Opacity
  commentId: P:Microsoft.Maui.Controls.VisualElement.Opacity
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.opacity
  name: Opacity
  nameWithType: VisualElement.Opacity
  fullName: Microsoft.Maui.Controls.VisualElement.Opacity
- uid: Microsoft.Maui.Controls.VisualElement.Rotation
  commentId: P:Microsoft.Maui.Controls.VisualElement.Rotation
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotation
  name: Rotation
  nameWithType: VisualElement.Rotation
  fullName: Microsoft.Maui.Controls.VisualElement.Rotation
- uid: Microsoft.Maui.Controls.VisualElement.RotationX
  commentId: P:Microsoft.Maui.Controls.VisualElement.RotationX
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotationx
  name: RotationX
  nameWithType: VisualElement.RotationX
  fullName: Microsoft.Maui.Controls.VisualElement.RotationX
- uid: Microsoft.Maui.Controls.VisualElement.RotationY
  commentId: P:Microsoft.Maui.Controls.VisualElement.RotationY
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotationy
  name: RotationY
  nameWithType: VisualElement.RotationY
  fullName: Microsoft.Maui.Controls.VisualElement.RotationY
- uid: Microsoft.Maui.Controls.VisualElement.Scale
  commentId: P:Microsoft.Maui.Controls.VisualElement.Scale
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.scale
  name: Scale
  nameWithType: VisualElement.Scale
  fullName: Microsoft.Maui.Controls.VisualElement.Scale
- uid: Microsoft.Maui.Controls.VisualElement.ScaleX
  commentId: P:Microsoft.Maui.Controls.VisualElement.ScaleX
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.scalex
  name: ScaleX
  nameWithType: VisualElement.ScaleX
  fullName: Microsoft.Maui.Controls.VisualElement.ScaleX
- uid: Microsoft.Maui.Controls.VisualElement.ScaleY
  commentId: P:Microsoft.Maui.Controls.VisualElement.ScaleY
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.scaley
  name: ScaleY
  nameWithType: VisualElement.ScaleY
  fullName: Microsoft.Maui.Controls.VisualElement.ScaleY
- uid: Microsoft.Maui.Controls.VisualElement.TranslationX
  commentId: P:Microsoft.Maui.Controls.VisualElement.TranslationX
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.translationx
  name: TranslationX
  nameWithType: VisualElement.TranslationX
  fullName: Microsoft.Maui.Controls.VisualElement.TranslationX
- uid: Microsoft.Maui.Controls.VisualElement.TranslationY
  commentId: P:Microsoft.Maui.Controls.VisualElement.TranslationY
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.translationy
  name: TranslationY
  nameWithType: VisualElement.TranslationY
  fullName: Microsoft.Maui.Controls.VisualElement.TranslationY
- uid: Microsoft.Maui.Controls.VisualElement.Triggers
  commentId: P:Microsoft.Maui.Controls.VisualElement.Triggers
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.triggers
  name: Triggers
  nameWithType: VisualElement.Triggers
  fullName: Microsoft.Maui.Controls.VisualElement.Triggers
- uid: Microsoft.Maui.Controls.VisualElement.Width
  commentId: P:Microsoft.Maui.Controls.VisualElement.Width
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.width
  name: Width
  nameWithType: VisualElement.Width
  fullName: Microsoft.Maui.Controls.VisualElement.Width
- uid: Microsoft.Maui.Controls.VisualElement.WidthRequest
  commentId: P:Microsoft.Maui.Controls.VisualElement.WidthRequest
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.widthrequest
  name: WidthRequest
  nameWithType: VisualElement.WidthRequest
  fullName: Microsoft.Maui.Controls.VisualElement.WidthRequest
- uid: Microsoft.Maui.Controls.VisualElement.X
  commentId: P:Microsoft.Maui.Controls.VisualElement.X
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.x
  name: X
  nameWithType: VisualElement.X
  fullName: Microsoft.Maui.Controls.VisualElement.X
- uid: Microsoft.Maui.Controls.VisualElement.Y
  commentId: P:Microsoft.Maui.Controls.VisualElement.Y
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.y
  name: Y
  nameWithType: VisualElement.Y
  fullName: Microsoft.Maui.Controls.VisualElement.Y
- uid: Microsoft.Maui.Controls.VisualElement.Clip
  commentId: P:Microsoft.Maui.Controls.VisualElement.Clip
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.clip
  name: Clip
  nameWithType: VisualElement.Clip
  fullName: Microsoft.Maui.Controls.VisualElement.Clip
- uid: Microsoft.Maui.Controls.VisualElement.Resources
  commentId: P:Microsoft.Maui.Controls.VisualElement.Resources
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.resources
  name: Resources
  nameWithType: VisualElement.Resources
  fullName: Microsoft.Maui.Controls.VisualElement.Resources
- uid: Microsoft.Maui.Controls.VisualElement.Frame
  commentId: P:Microsoft.Maui.Controls.VisualElement.Frame
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.frame
  name: Frame
  nameWithType: VisualElement.Frame
  fullName: Microsoft.Maui.Controls.VisualElement.Frame
- uid: Microsoft.Maui.Controls.VisualElement.Handler
  commentId: P:Microsoft.Maui.Controls.VisualElement.Handler
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.handler
  name: Handler
  nameWithType: VisualElement.Handler
  fullName: Microsoft.Maui.Controls.VisualElement.Handler
- uid: Microsoft.Maui.Controls.VisualElement.Shadow
  commentId: P:Microsoft.Maui.Controls.VisualElement.Shadow
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.shadow
  name: Shadow
  nameWithType: VisualElement.Shadow
  fullName: Microsoft.Maui.Controls.VisualElement.Shadow
- uid: Microsoft.Maui.Controls.VisualElement.ZIndex
  commentId: P:Microsoft.Maui.Controls.VisualElement.ZIndex
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.zindex
  name: ZIndex
  nameWithType: VisualElement.ZIndex
  fullName: Microsoft.Maui.Controls.VisualElement.ZIndex
- uid: Microsoft.Maui.Controls.VisualElement.DesiredSize
  commentId: P:Microsoft.Maui.Controls.VisualElement.DesiredSize
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.desiredsize
  name: DesiredSize
  nameWithType: VisualElement.DesiredSize
  fullName: Microsoft.Maui.Controls.VisualElement.DesiredSize
- uid: Microsoft.Maui.Controls.VisualElement.IsLoaded
  commentId: P:Microsoft.Maui.Controls.VisualElement.IsLoaded
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isloaded
  name: IsLoaded
  nameWithType: VisualElement.IsLoaded
  fullName: Microsoft.Maui.Controls.VisualElement.IsLoaded
- uid: Microsoft.Maui.Controls.VisualElement.ChildrenReordered
  commentId: E:Microsoft.Maui.Controls.VisualElement.ChildrenReordered
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.childrenreordered
  name: ChildrenReordered
  nameWithType: VisualElement.ChildrenReordered
  fullName: Microsoft.Maui.Controls.VisualElement.ChildrenReordered
- uid: Microsoft.Maui.Controls.VisualElement.Focused
  commentId: E:Microsoft.Maui.Controls.VisualElement.Focused
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.focused
  name: Focused
  nameWithType: VisualElement.Focused
  fullName: Microsoft.Maui.Controls.VisualElement.Focused
- uid: Microsoft.Maui.Controls.VisualElement.MeasureInvalidated
  commentId: E:Microsoft.Maui.Controls.VisualElement.MeasureInvalidated
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.measureinvalidated
  name: MeasureInvalidated
  nameWithType: VisualElement.MeasureInvalidated
  fullName: Microsoft.Maui.Controls.VisualElement.MeasureInvalidated
- uid: Microsoft.Maui.Controls.VisualElement.SizeChanged
  commentId: E:Microsoft.Maui.Controls.VisualElement.SizeChanged
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.sizechanged
  name: SizeChanged
  nameWithType: VisualElement.SizeChanged
  fullName: Microsoft.Maui.Controls.VisualElement.SizeChanged
- uid: Microsoft.Maui.Controls.VisualElement.Unfocused
  commentId: E:Microsoft.Maui.Controls.VisualElement.Unfocused
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.unfocused
  name: Unfocused
  nameWithType: VisualElement.Unfocused
  fullName: Microsoft.Maui.Controls.VisualElement.Unfocused
- uid: Microsoft.Maui.Controls.VisualElement.Loaded
  commentId: E:Microsoft.Maui.Controls.VisualElement.Loaded
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.loaded
  name: Loaded
  nameWithType: VisualElement.Loaded
  fullName: Microsoft.Maui.Controls.VisualElement.Loaded
- uid: Microsoft.Maui.Controls.VisualElement.Unloaded
  commentId: E:Microsoft.Maui.Controls.VisualElement.Unloaded
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.unloaded
  name: Unloaded
  nameWithType: VisualElement.Unloaded
  fullName: Microsoft.Maui.Controls.VisualElement.Unloaded
- uid: Microsoft.Maui.Controls.NavigableElement.OnParentSet
  commentId: M:Microsoft.Maui.Controls.NavigableElement.OnParentSet
  parent: Microsoft.Maui.Controls.NavigableElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.navigableelement.onparentset
  name: OnParentSet()
  nameWithType: NavigableElement.OnParentSet()
  fullName: Microsoft.Maui.Controls.NavigableElement.OnParentSet()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.NavigableElement.OnParentSet
    name: OnParentSet
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.navigableelement.onparentset
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.NavigableElement.OnParentSet
    name: OnParentSet
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.navigableelement.onparentset
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.NavigableElement.Navigation
  commentId: P:Microsoft.Maui.Controls.NavigableElement.Navigation
  parent: Microsoft.Maui.Controls.NavigableElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.navigableelement.navigation
  name: Navigation
  nameWithType: NavigableElement.Navigation
  fullName: Microsoft.Maui.Controls.NavigableElement.Navigation
- uid: Microsoft.Maui.Controls.StyleableElement.Style
  commentId: P:Microsoft.Maui.Controls.StyleableElement.Style
  parent: Microsoft.Maui.Controls.StyleableElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.styleableelement.style
  name: Style
  nameWithType: StyleableElement.Style
  fullName: Microsoft.Maui.Controls.StyleableElement.Style
- uid: Microsoft.Maui.Controls.StyleableElement.StyleClass
  commentId: P:Microsoft.Maui.Controls.StyleableElement.StyleClass
  parent: Microsoft.Maui.Controls.StyleableElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.styleableelement.styleclass
  name: StyleClass
  nameWithType: StyleableElement.StyleClass
  fullName: Microsoft.Maui.Controls.StyleableElement.StyleClass
- uid: Microsoft.Maui.Controls.StyleableElement.class
  commentId: P:Microsoft.Maui.Controls.StyleableElement.class
  parent: Microsoft.Maui.Controls.StyleableElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.styleableelement.class
  name: class
  nameWithType: StyleableElement.class
  fullName: Microsoft.Maui.Controls.StyleableElement.class
- uid: Microsoft.Maui.Controls.Element.AutomationIdProperty
  commentId: F:Microsoft.Maui.Controls.Element.AutomationIdProperty
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.automationidproperty
  name: AutomationIdProperty
  nameWithType: Element.AutomationIdProperty
  fullName: Microsoft.Maui.Controls.Element.AutomationIdProperty
- uid: Microsoft.Maui.Controls.Element.ClassIdProperty
  commentId: F:Microsoft.Maui.Controls.Element.ClassIdProperty
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.classidproperty
  name: ClassIdProperty
  nameWithType: Element.ClassIdProperty
  fullName: Microsoft.Maui.Controls.Element.ClassIdProperty
- uid: Microsoft.Maui.Controls.Element.InsertLogicalChild(System.Int32,Microsoft.Maui.Controls.Element)
  commentId: M:Microsoft.Maui.Controls.Element.InsertLogicalChild(System.Int32,Microsoft.Maui.Controls.Element)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.insertlogicalchild
  name: InsertLogicalChild(int, Element)
  nameWithType: Element.InsertLogicalChild(int, Element)
  fullName: Microsoft.Maui.Controls.Element.InsertLogicalChild(int, Microsoft.Maui.Controls.Element)
  nameWithType.vb: Element.InsertLogicalChild(Integer, Element)
  fullName.vb: Microsoft.Maui.Controls.Element.InsertLogicalChild(Integer, Microsoft.Maui.Controls.Element)
  name.vb: InsertLogicalChild(Integer, Element)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.InsertLogicalChild(System.Int32,Microsoft.Maui.Controls.Element)
    name: InsertLogicalChild
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.insertlogicalchild
  - name: (
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.InsertLogicalChild(System.Int32,Microsoft.Maui.Controls.Element)
    name: InsertLogicalChild
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.insertlogicalchild
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: Microsoft.Maui.Controls.Element.AddLogicalChild(Microsoft.Maui.Controls.Element)
  commentId: M:Microsoft.Maui.Controls.Element.AddLogicalChild(Microsoft.Maui.Controls.Element)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.addlogicalchild
  name: AddLogicalChild(Element)
  nameWithType: Element.AddLogicalChild(Element)
  fullName: Microsoft.Maui.Controls.Element.AddLogicalChild(Microsoft.Maui.Controls.Element)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.AddLogicalChild(Microsoft.Maui.Controls.Element)
    name: AddLogicalChild
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.addlogicalchild
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.AddLogicalChild(Microsoft.Maui.Controls.Element)
    name: AddLogicalChild
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.addlogicalchild
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: Microsoft.Maui.Controls.Element.RemoveLogicalChild(Microsoft.Maui.Controls.Element)
  commentId: M:Microsoft.Maui.Controls.Element.RemoveLogicalChild(Microsoft.Maui.Controls.Element)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.removelogicalchild
  name: RemoveLogicalChild(Element)
  nameWithType: Element.RemoveLogicalChild(Element)
  fullName: Microsoft.Maui.Controls.Element.RemoveLogicalChild(Microsoft.Maui.Controls.Element)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.RemoveLogicalChild(Microsoft.Maui.Controls.Element)
    name: RemoveLogicalChild
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.removelogicalchild
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.RemoveLogicalChild(Microsoft.Maui.Controls.Element)
    name: RemoveLogicalChild
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.removelogicalchild
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: Microsoft.Maui.Controls.Element.ClearLogicalChildren
  commentId: M:Microsoft.Maui.Controls.Element.ClearLogicalChildren
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.clearlogicalchildren
  name: ClearLogicalChildren()
  nameWithType: Element.ClearLogicalChildren()
  fullName: Microsoft.Maui.Controls.Element.ClearLogicalChildren()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.ClearLogicalChildren
    name: ClearLogicalChildren
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.clearlogicalchildren
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.ClearLogicalChildren
    name: ClearLogicalChildren
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.clearlogicalchildren
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.Element.FindByName(System.String)
  commentId: M:Microsoft.Maui.Controls.Element.FindByName(System.String)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.findbyname
  name: FindByName(string)
  nameWithType: Element.FindByName(string)
  fullName: Microsoft.Maui.Controls.Element.FindByName(string)
  nameWithType.vb: Element.FindByName(String)
  fullName.vb: Microsoft.Maui.Controls.Element.FindByName(String)
  name.vb: FindByName(String)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.FindByName(System.String)
    name: FindByName
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.findbyname
  - name: (
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.FindByName(System.String)
    name: FindByName
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.findbyname
  - name: (
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: Microsoft.Maui.Controls.Element.RemoveDynamicResource(Microsoft.Maui.Controls.BindableProperty)
  commentId: M:Microsoft.Maui.Controls.Element.RemoveDynamicResource(Microsoft.Maui.Controls.BindableProperty)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.removedynamicresource
  name: RemoveDynamicResource(BindableProperty)
  nameWithType: Element.RemoveDynamicResource(BindableProperty)
  fullName: Microsoft.Maui.Controls.Element.RemoveDynamicResource(Microsoft.Maui.Controls.BindableProperty)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.RemoveDynamicResource(Microsoft.Maui.Controls.BindableProperty)
    name: RemoveDynamicResource
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.removedynamicresource
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.RemoveDynamicResource(Microsoft.Maui.Controls.BindableProperty)
    name: RemoveDynamicResource
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.removedynamicresource
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
- uid: Microsoft.Maui.Controls.Element.SetDynamicResource(Microsoft.Maui.Controls.BindableProperty,System.String)
  commentId: M:Microsoft.Maui.Controls.Element.SetDynamicResource(Microsoft.Maui.Controls.BindableProperty,System.String)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.setdynamicresource
  name: SetDynamicResource(BindableProperty, string)
  nameWithType: Element.SetDynamicResource(BindableProperty, string)
  fullName: Microsoft.Maui.Controls.Element.SetDynamicResource(Microsoft.Maui.Controls.BindableProperty, string)
  nameWithType.vb: Element.SetDynamicResource(BindableProperty, String)
  fullName.vb: Microsoft.Maui.Controls.Element.SetDynamicResource(Microsoft.Maui.Controls.BindableProperty, String)
  name.vb: SetDynamicResource(BindableProperty, String)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.SetDynamicResource(Microsoft.Maui.Controls.BindableProperty,System.String)
    name: SetDynamicResource
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.setdynamicresource
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.SetDynamicResource(Microsoft.Maui.Controls.BindableProperty,System.String)
    name: SetDynamicResource
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.setdynamicresource
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: Microsoft.Maui.Controls.Element.OnPropertyChanged(System.String)
  commentId: M:Microsoft.Maui.Controls.Element.OnPropertyChanged(System.String)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onpropertychanged
  name: OnPropertyChanged(string)
  nameWithType: Element.OnPropertyChanged(string)
  fullName: Microsoft.Maui.Controls.Element.OnPropertyChanged(string)
  nameWithType.vb: Element.OnPropertyChanged(String)
  fullName.vb: Microsoft.Maui.Controls.Element.OnPropertyChanged(String)
  name.vb: OnPropertyChanged(String)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.OnPropertyChanged(System.String)
    name: OnPropertyChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onpropertychanged
  - name: (
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.OnPropertyChanged(System.String)
    name: OnPropertyChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onpropertychanged
  - name: (
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: Microsoft.Maui.Controls.Element.OnParentChanging(Microsoft.Maui.Controls.ParentChangingEventArgs)
  commentId: M:Microsoft.Maui.Controls.Element.OnParentChanging(Microsoft.Maui.Controls.ParentChangingEventArgs)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentchanging
  name: OnParentChanging(ParentChangingEventArgs)
  nameWithType: Element.OnParentChanging(ParentChangingEventArgs)
  fullName: Microsoft.Maui.Controls.Element.OnParentChanging(Microsoft.Maui.Controls.ParentChangingEventArgs)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.OnParentChanging(Microsoft.Maui.Controls.ParentChangingEventArgs)
    name: OnParentChanging
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentchanging
  - name: (
  - uid: Microsoft.Maui.Controls.ParentChangingEventArgs
    name: ParentChangingEventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.parentchangingeventargs
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.OnParentChanging(Microsoft.Maui.Controls.ParentChangingEventArgs)
    name: OnParentChanging
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentchanging
  - name: (
  - uid: Microsoft.Maui.Controls.ParentChangingEventArgs
    name: ParentChangingEventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.parentchangingeventargs
  - name: )
- uid: Microsoft.Maui.Controls.Element.OnParentChanged
  commentId: M:Microsoft.Maui.Controls.Element.OnParentChanged
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentchanged
  name: OnParentChanged()
  nameWithType: Element.OnParentChanged()
  fullName: Microsoft.Maui.Controls.Element.OnParentChanged()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.OnParentChanged
    name: OnParentChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentchanged
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.OnParentChanged
    name: OnParentChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentchanged
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.Element.MapAutomationPropertiesIsInAccessibleTree(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
  commentId: M:Microsoft.Maui.Controls.Element.MapAutomationPropertiesIsInAccessibleTree(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.mapautomationpropertiesisinaccessibletree
  name: MapAutomationPropertiesIsInAccessibleTree(IElementHandler, Element)
  nameWithType: Element.MapAutomationPropertiesIsInAccessibleTree(IElementHandler, Element)
  fullName: Microsoft.Maui.Controls.Element.MapAutomationPropertiesIsInAccessibleTree(Microsoft.Maui.IElementHandler, Microsoft.Maui.Controls.Element)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.MapAutomationPropertiesIsInAccessibleTree(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
    name: MapAutomationPropertiesIsInAccessibleTree
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.mapautomationpropertiesisinaccessibletree
  - name: (
  - uid: Microsoft.Maui.IElementHandler
    name: IElementHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.ielementhandler
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.MapAutomationPropertiesIsInAccessibleTree(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
    name: MapAutomationPropertiesIsInAccessibleTree
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.mapautomationpropertiesisinaccessibletree
  - name: (
  - uid: Microsoft.Maui.IElementHandler
    name: IElementHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.ielementhandler
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: Microsoft.Maui.Controls.Element.MapAutomationPropertiesExcludedWithChildren(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
  commentId: M:Microsoft.Maui.Controls.Element.MapAutomationPropertiesExcludedWithChildren(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.mapautomationpropertiesexcludedwithchildren
  name: MapAutomationPropertiesExcludedWithChildren(IElementHandler, Element)
  nameWithType: Element.MapAutomationPropertiesExcludedWithChildren(IElementHandler, Element)
  fullName: Microsoft.Maui.Controls.Element.MapAutomationPropertiesExcludedWithChildren(Microsoft.Maui.IElementHandler, Microsoft.Maui.Controls.Element)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.MapAutomationPropertiesExcludedWithChildren(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
    name: MapAutomationPropertiesExcludedWithChildren
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.mapautomationpropertiesexcludedwithchildren
  - name: (
  - uid: Microsoft.Maui.IElementHandler
    name: IElementHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.ielementhandler
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.MapAutomationPropertiesExcludedWithChildren(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
    name: MapAutomationPropertiesExcludedWithChildren
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.mapautomationpropertiesexcludedwithchildren
  - name: (
  - uid: Microsoft.Maui.IElementHandler
    name: IElementHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.ielementhandler
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: Microsoft.Maui.Controls.Element.AutomationId
  commentId: P:Microsoft.Maui.Controls.Element.AutomationId
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.automationid
  name: AutomationId
  nameWithType: Element.AutomationId
  fullName: Microsoft.Maui.Controls.Element.AutomationId
- uid: Microsoft.Maui.Controls.Element.ClassId
  commentId: P:Microsoft.Maui.Controls.Element.ClassId
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.classid
  name: ClassId
  nameWithType: Element.ClassId
  fullName: Microsoft.Maui.Controls.Element.ClassId
- uid: Microsoft.Maui.Controls.Element.Effects
  commentId: P:Microsoft.Maui.Controls.Element.Effects
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.effects
  name: Effects
  nameWithType: Element.Effects
  fullName: Microsoft.Maui.Controls.Element.Effects
- uid: Microsoft.Maui.Controls.Element.Id
  commentId: P:Microsoft.Maui.Controls.Element.Id
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.id
  name: Id
  nameWithType: Element.Id
  fullName: Microsoft.Maui.Controls.Element.Id
- uid: Microsoft.Maui.Controls.Element.StyleId
  commentId: P:Microsoft.Maui.Controls.Element.StyleId
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.styleid
  name: StyleId
  nameWithType: Element.StyleId
  fullName: Microsoft.Maui.Controls.Element.StyleId
- uid: Microsoft.Maui.Controls.Element.Parent
  commentId: P:Microsoft.Maui.Controls.Element.Parent
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.parent
  name: Parent
  nameWithType: Element.Parent
  fullName: Microsoft.Maui.Controls.Element.Parent
- uid: Microsoft.Maui.Controls.Element.ChildAdded
  commentId: E:Microsoft.Maui.Controls.Element.ChildAdded
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.childadded
  name: ChildAdded
  nameWithType: Element.ChildAdded
  fullName: Microsoft.Maui.Controls.Element.ChildAdded
- uid: Microsoft.Maui.Controls.Element.ChildRemoved
  commentId: E:Microsoft.Maui.Controls.Element.ChildRemoved
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.childremoved
  name: ChildRemoved
  nameWithType: Element.ChildRemoved
  fullName: Microsoft.Maui.Controls.Element.ChildRemoved
- uid: Microsoft.Maui.Controls.Element.DescendantAdded
  commentId: E:Microsoft.Maui.Controls.Element.DescendantAdded
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.descendantadded
  name: DescendantAdded
  nameWithType: Element.DescendantAdded
  fullName: Microsoft.Maui.Controls.Element.DescendantAdded
- uid: Microsoft.Maui.Controls.Element.DescendantRemoved
  commentId: E:Microsoft.Maui.Controls.Element.DescendantRemoved
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.descendantremoved
  name: DescendantRemoved
  nameWithType: Element.DescendantRemoved
  fullName: Microsoft.Maui.Controls.Element.DescendantRemoved
- uid: Microsoft.Maui.Controls.Element.ParentChanging
  commentId: E:Microsoft.Maui.Controls.Element.ParentChanging
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.parentchanging
  name: ParentChanging
  nameWithType: Element.ParentChanging
  fullName: Microsoft.Maui.Controls.Element.ParentChanging
- uid: Microsoft.Maui.Controls.Element.ParentChanged
  commentId: E:Microsoft.Maui.Controls.Element.ParentChanged
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.parentchanged
  name: ParentChanged
  nameWithType: Element.ParentChanged
  fullName: Microsoft.Maui.Controls.Element.ParentChanged
- uid: Microsoft.Maui.Controls.Element.HandlerChanging
  commentId: E:Microsoft.Maui.Controls.Element.HandlerChanging
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.handlerchanging
  name: HandlerChanging
  nameWithType: Element.HandlerChanging
  fullName: Microsoft.Maui.Controls.Element.HandlerChanging
- uid: Microsoft.Maui.Controls.Element.HandlerChanged
  commentId: E:Microsoft.Maui.Controls.Element.HandlerChanged
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.handlerchanged
  name: HandlerChanged
  nameWithType: Element.HandlerChanged
  fullName: Microsoft.Maui.Controls.Element.HandlerChanged
- uid: Microsoft.Maui.Controls.BindableObject.BindingContextProperty
  commentId: F:Microsoft.Maui.Controls.BindableObject.BindingContextProperty
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.bindingcontextproperty
  name: BindingContextProperty
  nameWithType: BindableObject.BindingContextProperty
  fullName: Microsoft.Maui.Controls.BindableObject.BindingContextProperty
- uid: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindableProperty)
  commentId: M:Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindableProperty)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindableproperty)
  name: ClearValue(BindableProperty)
  nameWithType: BindableObject.ClearValue(BindableProperty)
  fullName: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindableProperty)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindableProperty)
    name: ClearValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindableproperty)
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindableProperty)
    name: ClearValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindableproperty)
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindablePropertyKey)
  commentId: M:Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindablePropertyKey)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindablepropertykey)
  name: ClearValue(BindablePropertyKey)
  nameWithType: BindableObject.ClearValue(BindablePropertyKey)
  fullName: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindablePropertyKey)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindablePropertyKey)
    name: ClearValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindablepropertykey)
  - name: (
  - uid: Microsoft.Maui.Controls.BindablePropertyKey
    name: BindablePropertyKey
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindablepropertykey
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindablePropertyKey)
    name: ClearValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindablepropertykey)
  - name: (
  - uid: Microsoft.Maui.Controls.BindablePropertyKey
    name: BindablePropertyKey
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindablepropertykey
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.GetValue(Microsoft.Maui.Controls.BindableProperty)
  commentId: M:Microsoft.Maui.Controls.BindableObject.GetValue(Microsoft.Maui.Controls.BindableProperty)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.getvalue
  name: GetValue(BindableProperty)
  nameWithType: BindableObject.GetValue(BindableProperty)
  fullName: Microsoft.Maui.Controls.BindableObject.GetValue(Microsoft.Maui.Controls.BindableProperty)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.GetValue(Microsoft.Maui.Controls.BindableProperty)
    name: GetValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.getvalue
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.GetValue(Microsoft.Maui.Controls.BindableProperty)
    name: GetValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.getvalue
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.IsSet(Microsoft.Maui.Controls.BindableProperty)
  commentId: M:Microsoft.Maui.Controls.BindableObject.IsSet(Microsoft.Maui.Controls.BindableProperty)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.isset
  name: IsSet(BindableProperty)
  nameWithType: BindableObject.IsSet(BindableProperty)
  fullName: Microsoft.Maui.Controls.BindableObject.IsSet(Microsoft.Maui.Controls.BindableProperty)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.IsSet(Microsoft.Maui.Controls.BindableProperty)
    name: IsSet
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.isset
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.IsSet(Microsoft.Maui.Controls.BindableProperty)
    name: IsSet
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.isset
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.RemoveBinding(Microsoft.Maui.Controls.BindableProperty)
  commentId: M:Microsoft.Maui.Controls.BindableObject.RemoveBinding(Microsoft.Maui.Controls.BindableProperty)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.removebinding
  name: RemoveBinding(BindableProperty)
  nameWithType: BindableObject.RemoveBinding(BindableProperty)
  fullName: Microsoft.Maui.Controls.BindableObject.RemoveBinding(Microsoft.Maui.Controls.BindableProperty)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.RemoveBinding(Microsoft.Maui.Controls.BindableProperty)
    name: RemoveBinding
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.removebinding
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.RemoveBinding(Microsoft.Maui.Controls.BindableProperty)
    name: RemoveBinding
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.removebinding
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.SetBinding(Microsoft.Maui.Controls.BindableProperty,Microsoft.Maui.Controls.BindingBase)
  commentId: M:Microsoft.Maui.Controls.BindableObject.SetBinding(Microsoft.Maui.Controls.BindableProperty,Microsoft.Maui.Controls.BindingBase)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setbinding
  name: SetBinding(BindableProperty, BindingBase)
  nameWithType: BindableObject.SetBinding(BindableProperty, BindingBase)
  fullName: Microsoft.Maui.Controls.BindableObject.SetBinding(Microsoft.Maui.Controls.BindableProperty, Microsoft.Maui.Controls.BindingBase)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.SetBinding(Microsoft.Maui.Controls.BindableProperty,Microsoft.Maui.Controls.BindingBase)
    name: SetBinding
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setbinding
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.BindingBase
    name: BindingBase
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindingbase
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.SetBinding(Microsoft.Maui.Controls.BindableProperty,Microsoft.Maui.Controls.BindingBase)
    name: SetBinding
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setbinding
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.BindingBase
    name: BindingBase
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindingbase
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.ApplyBindings
  commentId: M:Microsoft.Maui.Controls.BindableObject.ApplyBindings
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.applybindings
  name: ApplyBindings()
  nameWithType: BindableObject.ApplyBindings()
  fullName: Microsoft.Maui.Controls.BindableObject.ApplyBindings()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.ApplyBindings
    name: ApplyBindings
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.applybindings
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.ApplyBindings
    name: ApplyBindings
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.applybindings
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(System.String)
  commentId: M:Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(System.String)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.onpropertychanging
  name: OnPropertyChanging(string)
  nameWithType: BindableObject.OnPropertyChanging(string)
  fullName: Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(string)
  nameWithType.vb: BindableObject.OnPropertyChanging(String)
  fullName.vb: Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(String)
  name.vb: OnPropertyChanging(String)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(System.String)
    name: OnPropertyChanging
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.onpropertychanging
  - name: (
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(System.String)
    name: OnPropertyChanging
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.onpropertychanging
  - name: (
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.UnapplyBindings
  commentId: M:Microsoft.Maui.Controls.BindableObject.UnapplyBindings
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.unapplybindings
  name: UnapplyBindings()
  nameWithType: BindableObject.UnapplyBindings()
  fullName: Microsoft.Maui.Controls.BindableObject.UnapplyBindings()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.UnapplyBindings
    name: UnapplyBindings
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.unapplybindings
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.UnapplyBindings
    name: UnapplyBindings
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.unapplybindings
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty,System.Object)
  commentId: M:Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty,System.Object)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindableproperty-system-object)
  name: SetValue(BindableProperty, object)
  nameWithType: BindableObject.SetValue(BindableProperty, object)
  fullName: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty, object)
  nameWithType.vb: BindableObject.SetValue(BindableProperty, Object)
  fullName.vb: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty, Object)
  name.vb: SetValue(BindableProperty, Object)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty,System.Object)
    name: SetValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindableproperty-system-object)
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty,System.Object)
    name: SetValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindableproperty-system-object)
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey,System.Object)
  commentId: M:Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey,System.Object)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindablepropertykey-system-object)
  name: SetValue(BindablePropertyKey, object)
  nameWithType: BindableObject.SetValue(BindablePropertyKey, object)
  fullName: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey, object)
  nameWithType.vb: BindableObject.SetValue(BindablePropertyKey, Object)
  fullName.vb: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey, Object)
  name.vb: SetValue(BindablePropertyKey, Object)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey,System.Object)
    name: SetValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindablepropertykey-system-object)
  - name: (
  - uid: Microsoft.Maui.Controls.BindablePropertyKey
    name: BindablePropertyKey
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindablepropertykey
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey,System.Object)
    name: SetValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindablepropertykey-system-object)
  - name: (
  - uid: Microsoft.Maui.Controls.BindablePropertyKey
    name: BindablePropertyKey
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindablepropertykey
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindableProperty)
  commentId: M:Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindableProperty)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindableproperty)
  name: CoerceValue(BindableProperty)
  nameWithType: BindableObject.CoerceValue(BindableProperty)
  fullName: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindableProperty)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindableProperty)
    name: CoerceValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindableproperty)
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindableProperty)
    name: CoerceValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindableproperty)
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindablePropertyKey)
  commentId: M:Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindablePropertyKey)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindablepropertykey)
  name: CoerceValue(BindablePropertyKey)
  nameWithType: BindableObject.CoerceValue(BindablePropertyKey)
  fullName: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindablePropertyKey)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindablePropertyKey)
    name: CoerceValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindablepropertykey)
  - name: (
  - uid: Microsoft.Maui.Controls.BindablePropertyKey
    name: BindablePropertyKey
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindablepropertykey
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindablePropertyKey)
    name: CoerceValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindablepropertykey)
  - name: (
  - uid: Microsoft.Maui.Controls.BindablePropertyKey
    name: BindablePropertyKey
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindablepropertykey
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.Dispatcher
  commentId: P:Microsoft.Maui.Controls.BindableObject.Dispatcher
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.dispatcher
  name: Dispatcher
  nameWithType: BindableObject.Dispatcher
  fullName: Microsoft.Maui.Controls.BindableObject.Dispatcher
- uid: Microsoft.Maui.Controls.BindableObject.BindingContext
  commentId: P:Microsoft.Maui.Controls.BindableObject.BindingContext
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.bindingcontext
  name: BindingContext
  nameWithType: BindableObject.BindingContext
  fullName: Microsoft.Maui.Controls.BindableObject.BindingContext
- uid: Microsoft.Maui.Controls.BindableObject.PropertyChanged
  commentId: E:Microsoft.Maui.Controls.BindableObject.PropertyChanged
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.propertychanged
  name: PropertyChanged
  nameWithType: BindableObject.PropertyChanged
  fullName: Microsoft.Maui.Controls.BindableObject.PropertyChanged
- uid: Microsoft.Maui.Controls.BindableObject.PropertyChanging
  commentId: E:Microsoft.Maui.Controls.BindableObject.PropertyChanging
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.propertychanging
  name: PropertyChanging
  nameWithType: BindableObject.PropertyChanging
  fullName: Microsoft.Maui.Controls.BindableObject.PropertyChanging
- uid: Microsoft.Maui.Controls.BindableObject.BindingContextChanged
  commentId: E:Microsoft.Maui.Controls.BindableObject.BindingContextChanged
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.bindingcontextchanged
  name: BindingContextChanged
  nameWithType: BindableObject.BindingContextChanged
  fullName: Microsoft.Maui.Controls.BindableObject.BindingContextChanged
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: DrawnUi.Views.SkiaViewAccelerated.DrawnUi.Draw.FluentExtensions.AssignNative``1(DrawnUi.Views.SkiaViewAccelerated@)
  commentId: M:DrawnUi.Draw.FluentExtensions.AssignNative``1(``0,``0@)
  parent: DrawnUi.Draw.FluentExtensions
  definition: DrawnUi.Draw.FluentExtensions.AssignNative``1(``0,``0@)
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_AssignNative__1___0___0__
  name: AssignNative<SkiaViewAccelerated>(SkiaViewAccelerated, out SkiaViewAccelerated)
  nameWithType: FluentExtensions.AssignNative<SkiaViewAccelerated>(SkiaViewAccelerated, out SkiaViewAccelerated)
  fullName: DrawnUi.Draw.FluentExtensions.AssignNative<DrawnUi.Views.SkiaViewAccelerated>(DrawnUi.Views.SkiaViewAccelerated, out DrawnUi.Views.SkiaViewAccelerated)
  nameWithType.vb: FluentExtensions.AssignNative(Of SkiaViewAccelerated)(SkiaViewAccelerated, SkiaViewAccelerated)
  fullName.vb: DrawnUi.Draw.FluentExtensions.AssignNative(Of DrawnUi.Views.SkiaViewAccelerated)(DrawnUi.Views.SkiaViewAccelerated, DrawnUi.Views.SkiaViewAccelerated)
  name.vb: AssignNative(Of SkiaViewAccelerated)(SkiaViewAccelerated, SkiaViewAccelerated)
  spec.csharp:
  - uid: DrawnUi.Draw.FluentExtensions.AssignNative``1(DrawnUi.Views.SkiaViewAccelerated,DrawnUi.Views.SkiaViewAccelerated@)
    name: AssignNative
    href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_AssignNative__1___0___0__
  - name: <
  - uid: DrawnUi.Views.SkiaViewAccelerated
    name: SkiaViewAccelerated
    href: DrawnUi.Views.SkiaViewAccelerated.html
  - name: '>'
  - name: (
  - uid: DrawnUi.Views.SkiaViewAccelerated
    name: SkiaViewAccelerated
    href: DrawnUi.Views.SkiaViewAccelerated.html
  - name: ','
  - name: " "
  - name: out
  - name: " "
  - uid: DrawnUi.Views.SkiaViewAccelerated
    name: SkiaViewAccelerated
    href: DrawnUi.Views.SkiaViewAccelerated.html
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.FluentExtensions.AssignNative``1(DrawnUi.Views.SkiaViewAccelerated,DrawnUi.Views.SkiaViewAccelerated@)
    name: AssignNative
    href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_AssignNative__1___0___0__
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Views.SkiaViewAccelerated
    name: SkiaViewAccelerated
    href: DrawnUi.Views.SkiaViewAccelerated.html
  - name: )
  - name: (
  - uid: DrawnUi.Views.SkiaViewAccelerated
    name: SkiaViewAccelerated
    href: DrawnUi.Views.SkiaViewAccelerated.html
  - name: ','
  - name: " "
  - uid: DrawnUi.Views.SkiaViewAccelerated
    name: SkiaViewAccelerated
    href: DrawnUi.Views.SkiaViewAccelerated.html
  - name: )
- uid: Microsoft.Maui.Controls.Element.DrawnUi.Draw.StaticResourcesExtensions.FindParent``1
  commentId: M:DrawnUi.Draw.StaticResourcesExtensions.FindParent``1(Microsoft.Maui.Controls.Element)
  parent: DrawnUi.Draw.StaticResourcesExtensions
  definition: DrawnUi.Draw.StaticResourcesExtensions.FindParent``1(Microsoft.Maui.Controls.Element)
  href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_FindParent__1_Microsoft_Maui_Controls_Element_
  name: FindParent<T>(Element)
  nameWithType: StaticResourcesExtensions.FindParent<T>(Element)
  fullName: DrawnUi.Draw.StaticResourcesExtensions.FindParent<T>(Microsoft.Maui.Controls.Element)
  nameWithType.vb: StaticResourcesExtensions.FindParent(Of T)(Element)
  fullName.vb: DrawnUi.Draw.StaticResourcesExtensions.FindParent(Of T)(Microsoft.Maui.Controls.Element)
  name.vb: FindParent(Of T)(Element)
  spec.csharp:
  - uid: DrawnUi.Draw.StaticResourcesExtensions.FindParent``1(Microsoft.Maui.Controls.Element)
    name: FindParent
    href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_FindParent__1_Microsoft_Maui_Controls_Element_
  - name: <
  - name: T
  - name: '>'
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.StaticResourcesExtensions.FindParent``1(Microsoft.Maui.Controls.Element)
    name: FindParent
    href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_FindParent__1_Microsoft_Maui_Controls_Element_
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: Microsoft.Maui.Controls.Element.DrawnUi.Extensions.InternalExtensions.FindMauiContext(System.Boolean)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element,System.Boolean)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element,System.Boolean)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FindMauiContext_Microsoft_Maui_Controls_Element_System_Boolean_
  name: FindMauiContext(Element, bool)
  nameWithType: InternalExtensions.FindMauiContext(Element, bool)
  fullName: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element, bool)
  nameWithType.vb: InternalExtensions.FindMauiContext(Element, Boolean)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element, Boolean)
  name.vb: FindMauiContext(Element, Boolean)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element,System.Boolean)
    name: FindMauiContext
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FindMauiContext_Microsoft_Maui_Controls_Element_System_Boolean_
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: bool
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element,System.Boolean)
    name: FindMauiContext
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FindMauiContext_Microsoft_Maui_Controls_Element_System_Boolean_
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: Boolean
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
- uid: Microsoft.Maui.Controls.Element.DrawnUi.Extensions.InternalExtensions.GetParentsPath
  commentId: M:DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_GetParentsPath_Microsoft_Maui_Controls_Element_
  name: GetParentsPath(Element)
  nameWithType: InternalExtensions.GetParentsPath(Element)
  fullName: DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
    name: GetParentsPath
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_GetParentsPath_Microsoft_Maui_Controls_Element_
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
    name: GetParentsPath
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_GetParentsPath_Microsoft_Maui_Controls_Element_
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents
  commentId: M:DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents(Microsoft.Maui.Controls.VisualElement)
  parent: DrawnUi.Draw.StaticResourcesExtensions
  definition: DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents(Microsoft.Maui.Controls.VisualElement)
  href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_GetAllWithMyselfParents_Microsoft_Maui_Controls_VisualElement_
  name: GetAllWithMyselfParents(VisualElement)
  nameWithType: StaticResourcesExtensions.GetAllWithMyselfParents(VisualElement)
  fullName: DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents(Microsoft.Maui.Controls.VisualElement)
  spec.csharp:
  - uid: DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents(Microsoft.Maui.Controls.VisualElement)
    name: GetAllWithMyselfParents
    href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_GetAllWithMyselfParents_Microsoft_Maui_Controls_VisualElement_
  - name: (
  - uid: Microsoft.Maui.Controls.VisualElement
    name: VisualElement
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents(Microsoft.Maui.Controls.VisualElement)
    name: GetAllWithMyselfParents
    href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_GetAllWithMyselfParents_Microsoft_Maui_Controls_VisualElement_
  - name: (
  - uid: Microsoft.Maui.Controls.VisualElement
    name: VisualElement
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement
  - name: )
- uid: Microsoft.Maui.IView.DrawnUi.Extensions.InternalExtensions.DisposeControlAndChildren
  commentId: M:DrawnUi.Extensions.InternalExtensions.DisposeControlAndChildren(Microsoft.Maui.IView)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.DisposeControlAndChildren(Microsoft.Maui.IView)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_DisposeControlAndChildren_Microsoft_Maui_IView_
  name: DisposeControlAndChildren(IView)
  nameWithType: InternalExtensions.DisposeControlAndChildren(IView)
  fullName: DrawnUi.Extensions.InternalExtensions.DisposeControlAndChildren(Microsoft.Maui.IView)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.DisposeControlAndChildren(Microsoft.Maui.IView)
    name: DisposeControlAndChildren
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_DisposeControlAndChildren_Microsoft_Maui_IView_
  - name: (
  - uid: Microsoft.Maui.IView
    name: IView
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iview
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.DisposeControlAndChildren(Microsoft.Maui.IView)
    name: DisposeControlAndChildren
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_DisposeControlAndChildren_Microsoft_Maui_IView_
  - name: (
  - uid: Microsoft.Maui.IView
    name: IView
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iview
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: Microsoft.Maui.Controls
  commentId: N:Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft
  name: Microsoft.Maui.Controls
  nameWithType: Microsoft.Maui.Controls
  fullName: Microsoft.Maui.Controls
  spec.csharp:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Controls
    name: Controls
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls
  spec.vb:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Controls
    name: Controls
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls
- uid: SkiaSharp.Views.Maui.Controls
  commentId: N:SkiaSharp.Views.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp
  name: SkiaSharp.Views.Maui.Controls
  nameWithType: SkiaSharp.Views.Maui.Controls
  fullName: SkiaSharp.Views.Maui.Controls
  spec.csharp:
  - uid: SkiaSharp
    name: SkiaSharp
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp
  - name: .
  - uid: SkiaSharp.Views
    name: Views
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.views
  - name: .
  - uid: SkiaSharp.Views.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.views.maui
  - name: .
  - uid: SkiaSharp.Views.Maui.Controls
    name: Controls
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.views.maui.controls
  spec.vb:
  - uid: SkiaSharp
    name: SkiaSharp
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp
  - name: .
  - uid: SkiaSharp.Views
    name: Views
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.views
  - name: .
  - uid: SkiaSharp.Views.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.views.maui
  - name: .
  - uid: SkiaSharp.Views.Maui.Controls
    name: Controls
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.views.maui.controls
- uid: System.ComponentModel
  commentId: N:System.ComponentModel
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.ComponentModel
  nameWithType: System.ComponentModel
  fullName: System.ComponentModel
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.ComponentModel
    name: ComponentModel
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.ComponentModel
    name: ComponentModel
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel
- uid: Microsoft.Maui
  commentId: N:Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft
  name: Microsoft.Maui
  nameWithType: Microsoft.Maui
  fullName: Microsoft.Maui
  spec.csharp:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  spec.vb:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
- uid: Microsoft.Maui.Controls.Internals
  commentId: N:Microsoft.Maui.Controls.Internals
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft
  name: Microsoft.Maui.Controls.Internals
  nameWithType: Microsoft.Maui.Controls.Internals
  fullName: Microsoft.Maui.Controls.Internals
  spec.csharp:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Controls
    name: Controls
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls
  - name: .
  - uid: Microsoft.Maui.Controls.Internals
    name: Internals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.internals
  spec.vb:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Controls
    name: Controls
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls
  - name: .
  - uid: Microsoft.Maui.Controls.Internals
    name: Internals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.internals
- uid: Microsoft.Maui.HotReload
  commentId: N:Microsoft.Maui.HotReload
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft
  name: Microsoft.Maui.HotReload
  nameWithType: Microsoft.Maui.HotReload
  fullName: Microsoft.Maui.HotReload
  spec.csharp:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.HotReload
    name: HotReload
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.hotreload
  spec.vb:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.HotReload
    name: HotReload
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.hotreload
- uid: SkiaSharp.Views.Maui
  commentId: N:SkiaSharp.Views.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp
  name: SkiaSharp.Views.Maui
  nameWithType: SkiaSharp.Views.Maui
  fullName: SkiaSharp.Views.Maui
  spec.csharp:
  - uid: SkiaSharp
    name: SkiaSharp
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp
  - name: .
  - uid: SkiaSharp.Views
    name: Views
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.views
  - name: .
  - uid: SkiaSharp.Views.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.views.maui
  spec.vb:
  - uid: SkiaSharp
    name: SkiaSharp
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp
  - name: .
  - uid: SkiaSharp.Views
    name: Views
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.views
  - name: .
  - uid: SkiaSharp.Views.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.views.maui
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: DrawnUi.Draw.FluentExtensions.AssignNative``1(``0,``0@)
  commentId: M:DrawnUi.Draw.FluentExtensions.AssignNative``1(``0,``0@)
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_AssignNative__1___0___0__
  name: AssignNative<T>(T, out T)
  nameWithType: FluentExtensions.AssignNative<T>(T, out T)
  fullName: DrawnUi.Draw.FluentExtensions.AssignNative<T>(T, out T)
  nameWithType.vb: FluentExtensions.AssignNative(Of T)(T, T)
  fullName.vb: DrawnUi.Draw.FluentExtensions.AssignNative(Of T)(T, T)
  name.vb: AssignNative(Of T)(T, T)
  spec.csharp:
  - uid: DrawnUi.Draw.FluentExtensions.AssignNative``1(``0,``0@)
    name: AssignNative
    href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_AssignNative__1___0___0__
  - name: <
  - name: T
  - name: '>'
  - name: (
  - name: T
  - name: ','
  - name: " "
  - name: out
  - name: " "
  - name: T
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.FluentExtensions.AssignNative``1(``0,``0@)
    name: AssignNative
    href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_AssignNative__1___0___0__
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
  - name: (
  - name: T
  - name: ','
  - name: " "
  - name: T
  - name: )
- uid: DrawnUi.Draw.FluentExtensions
  commentId: T:DrawnUi.Draw.FluentExtensions
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.FluentExtensions.html
  name: FluentExtensions
  nameWithType: FluentExtensions
  fullName: DrawnUi.Draw.FluentExtensions
- uid: DrawnUi.Draw.StaticResourcesExtensions.FindParent``1(Microsoft.Maui.Controls.Element)
  commentId: M:DrawnUi.Draw.StaticResourcesExtensions.FindParent``1(Microsoft.Maui.Controls.Element)
  isExternal: true
  href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_FindParent__1_Microsoft_Maui_Controls_Element_
  name: FindParent<T>(Element)
  nameWithType: StaticResourcesExtensions.FindParent<T>(Element)
  fullName: DrawnUi.Draw.StaticResourcesExtensions.FindParent<T>(Microsoft.Maui.Controls.Element)
  nameWithType.vb: StaticResourcesExtensions.FindParent(Of T)(Element)
  fullName.vb: DrawnUi.Draw.StaticResourcesExtensions.FindParent(Of T)(Microsoft.Maui.Controls.Element)
  name.vb: FindParent(Of T)(Element)
  spec.csharp:
  - uid: DrawnUi.Draw.StaticResourcesExtensions.FindParent``1(Microsoft.Maui.Controls.Element)
    name: FindParent
    href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_FindParent__1_Microsoft_Maui_Controls_Element_
  - name: <
  - name: T
  - name: '>'
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.StaticResourcesExtensions.FindParent``1(Microsoft.Maui.Controls.Element)
    name: FindParent
    href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_FindParent__1_Microsoft_Maui_Controls_Element_
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: DrawnUi.Draw.StaticResourcesExtensions
  commentId: T:DrawnUi.Draw.StaticResourcesExtensions
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.StaticResourcesExtensions.html
  name: StaticResourcesExtensions
  nameWithType: StaticResourcesExtensions
  fullName: DrawnUi.Draw.StaticResourcesExtensions
- uid: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element,System.Boolean)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element,System.Boolean)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FindMauiContext_Microsoft_Maui_Controls_Element_System_Boolean_
  name: FindMauiContext(Element, bool)
  nameWithType: InternalExtensions.FindMauiContext(Element, bool)
  fullName: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element, bool)
  nameWithType.vb: InternalExtensions.FindMauiContext(Element, Boolean)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element, Boolean)
  name.vb: FindMauiContext(Element, Boolean)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element,System.Boolean)
    name: FindMauiContext
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FindMauiContext_Microsoft_Maui_Controls_Element_System_Boolean_
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: bool
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element,System.Boolean)
    name: FindMauiContext
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FindMauiContext_Microsoft_Maui_Controls_Element_System_Boolean_
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: Boolean
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
  commentId: M:DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_GetParentsPath_Microsoft_Maui_Controls_Element_
  name: GetParentsPath(Element)
  nameWithType: InternalExtensions.GetParentsPath(Element)
  fullName: DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
    name: GetParentsPath
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_GetParentsPath_Microsoft_Maui_Controls_Element_
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
    name: GetParentsPath
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_GetParentsPath_Microsoft_Maui_Controls_Element_
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents(Microsoft.Maui.Controls.VisualElement)
  commentId: M:DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents(Microsoft.Maui.Controls.VisualElement)
  isExternal: true
  href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_GetAllWithMyselfParents_Microsoft_Maui_Controls_VisualElement_
  name: GetAllWithMyselfParents(VisualElement)
  nameWithType: StaticResourcesExtensions.GetAllWithMyselfParents(VisualElement)
  fullName: DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents(Microsoft.Maui.Controls.VisualElement)
  spec.csharp:
  - uid: DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents(Microsoft.Maui.Controls.VisualElement)
    name: GetAllWithMyselfParents
    href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_GetAllWithMyselfParents_Microsoft_Maui_Controls_VisualElement_
  - name: (
  - uid: Microsoft.Maui.Controls.VisualElement
    name: VisualElement
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents(Microsoft.Maui.Controls.VisualElement)
    name: GetAllWithMyselfParents
    href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_GetAllWithMyselfParents_Microsoft_Maui_Controls_VisualElement_
  - name: (
  - uid: Microsoft.Maui.Controls.VisualElement
    name: VisualElement
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.DisposeControlAndChildren(Microsoft.Maui.IView)
  commentId: M:DrawnUi.Extensions.InternalExtensions.DisposeControlAndChildren(Microsoft.Maui.IView)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_DisposeControlAndChildren_Microsoft_Maui_IView_
  name: DisposeControlAndChildren(IView)
  nameWithType: InternalExtensions.DisposeControlAndChildren(IView)
  fullName: DrawnUi.Extensions.InternalExtensions.DisposeControlAndChildren(Microsoft.Maui.IView)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.DisposeControlAndChildren(Microsoft.Maui.IView)
    name: DisposeControlAndChildren
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_DisposeControlAndChildren_Microsoft_Maui_IView_
  - name: (
  - uid: Microsoft.Maui.IView
    name: IView
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iview
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.DisposeControlAndChildren(Microsoft.Maui.IView)
    name: DisposeControlAndChildren
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_DisposeControlAndChildren_Microsoft_Maui_IView_
  - name: (
  - uid: Microsoft.Maui.IView
    name: IView
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iview
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Views.SkiaViewAccelerated.Uid*
  commentId: Overload:DrawnUi.Views.SkiaViewAccelerated.Uid
  href: DrawnUi.Views.SkiaViewAccelerated.html#DrawnUi_Views_SkiaViewAccelerated_Uid
  name: Uid
  nameWithType: SkiaViewAccelerated.Uid
  fullName: DrawnUi.Views.SkiaViewAccelerated.Uid
- uid: DrawnUi.Draw.ISkiaDrawable.Uid
  commentId: P:DrawnUi.Draw.ISkiaDrawable.Uid
  parent: DrawnUi.Draw.ISkiaDrawable
  href: DrawnUi.Draw.ISkiaDrawable.html#DrawnUi_Draw_ISkiaDrawable_Uid
  name: Uid
  nameWithType: ISkiaDrawable.Uid
  fullName: DrawnUi.Draw.ISkiaDrawable.Uid
- uid: System.Guid
  commentId: T:System.Guid
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.guid
  name: Guid
  nameWithType: Guid
  fullName: System.Guid
- uid: DrawnUi.Views.SkiaViewAccelerated.CreateStandaloneSurface*
  commentId: Overload:DrawnUi.Views.SkiaViewAccelerated.CreateStandaloneSurface
  href: DrawnUi.Views.SkiaViewAccelerated.html#DrawnUi_Views_SkiaViewAccelerated_CreateStandaloneSurface_System_Int32_System_Int32_
  name: CreateStandaloneSurface
  nameWithType: SkiaViewAccelerated.CreateStandaloneSurface
  fullName: DrawnUi.Views.SkiaViewAccelerated.CreateStandaloneSurface
- uid: DrawnUi.Draw.ISkiaSharpView.CreateStandaloneSurface(System.Int32,System.Int32)
  commentId: M:DrawnUi.Draw.ISkiaSharpView.CreateStandaloneSurface(System.Int32,System.Int32)
  parent: DrawnUi.Draw.ISkiaSharpView
  isExternal: true
  href: DrawnUi.Draw.ISkiaSharpView.html#DrawnUi_Draw_ISkiaSharpView_CreateStandaloneSurface_System_Int32_System_Int32_
  name: CreateStandaloneSurface(int, int)
  nameWithType: ISkiaSharpView.CreateStandaloneSurface(int, int)
  fullName: DrawnUi.Draw.ISkiaSharpView.CreateStandaloneSurface(int, int)
  nameWithType.vb: ISkiaSharpView.CreateStandaloneSurface(Integer, Integer)
  fullName.vb: DrawnUi.Draw.ISkiaSharpView.CreateStandaloneSurface(Integer, Integer)
  name.vb: CreateStandaloneSurface(Integer, Integer)
  spec.csharp:
  - uid: DrawnUi.Draw.ISkiaSharpView.CreateStandaloneSurface(System.Int32,System.Int32)
    name: CreateStandaloneSurface
    href: DrawnUi.Draw.ISkiaSharpView.html#DrawnUi_Draw_ISkiaSharpView_CreateStandaloneSurface_System_Int32_System_Int32_
  - name: (
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.ISkiaSharpView.CreateStandaloneSurface(System.Int32,System.Int32)
    name: CreateStandaloneSurface
    href: DrawnUi.Draw.ISkiaSharpView.html#DrawnUi_Draw_ISkiaSharpView_CreateStandaloneSurface_System_Int32_System_Int32_
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: System.Int32
  commentId: T:System.Int32
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.int32
  name: int
  nameWithType: int
  fullName: int
  nameWithType.vb: Integer
  fullName.vb: Integer
  name.vb: Integer
- uid: SkiaSharp.SKSurface
  commentId: T:SkiaSharp.SKSurface
  parent: SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.sksurface
  name: SKSurface
  nameWithType: SKSurface
  fullName: SkiaSharp.SKSurface
- uid: SkiaSharp
  commentId: N:SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp
  name: SkiaSharp
  nameWithType: SkiaSharp
  fullName: SkiaSharp
- uid: DrawnUi.Views.SkiaViewAccelerated.OnDraw*
  commentId: Overload:DrawnUi.Views.SkiaViewAccelerated.OnDraw
  href: DrawnUi.Views.SkiaViewAccelerated.html#DrawnUi_Views_SkiaViewAccelerated_OnDraw
  name: OnDraw
  nameWithType: SkiaViewAccelerated.OnDraw
  fullName: DrawnUi.Views.SkiaViewAccelerated.OnDraw
- uid: DrawnUi.Draw.ISkiaDrawable.OnDraw
  commentId: P:DrawnUi.Draw.ISkiaDrawable.OnDraw
  parent: DrawnUi.Draw.ISkiaDrawable
  href: DrawnUi.Draw.ISkiaDrawable.html#DrawnUi_Draw_ISkiaDrawable_OnDraw
  name: OnDraw
  nameWithType: ISkiaDrawable.OnDraw
  fullName: DrawnUi.Draw.ISkiaDrawable.OnDraw
- uid: System.Func{SkiaSharp.SKSurface,SkiaSharp.SKRect,System.Boolean}
  commentId: T:System.Func{SkiaSharp.SKSurface,SkiaSharp.SKRect,System.Boolean}
  parent: System
  definition: System.Func`3
  href: https://learn.microsoft.com/dotnet/api/system.func-3
  name: Func<SKSurface, SKRect, bool>
  nameWithType: Func<SKSurface, SKRect, bool>
  fullName: System.Func<SkiaSharp.SKSurface, SkiaSharp.SKRect, bool>
  nameWithType.vb: Func(Of SKSurface, SKRect, Boolean)
  fullName.vb: System.Func(Of SkiaSharp.SKSurface, SkiaSharp.SKRect, Boolean)
  name.vb: Func(Of SKSurface, SKRect, Boolean)
  spec.csharp:
  - uid: System.Func`3
    name: Func
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.func-3
  - name: <
  - uid: SkiaSharp.SKSurface
    name: SKSurface
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.sksurface
  - name: ','
  - name: " "
  - uid: SkiaSharp.SKRect
    name: SKRect
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skrect
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: bool
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: '>'
  spec.vb:
  - uid: System.Func`3
    name: Func
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.func-3
  - name: (
  - name: Of
  - name: " "
  - uid: SkiaSharp.SKSurface
    name: SKSurface
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.sksurface
  - name: ','
  - name: " "
  - uid: SkiaSharp.SKRect
    name: SKRect
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skrect
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: Boolean
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
- uid: System.Func`3
  commentId: T:System.Func`3
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.func-3
  name: Func<T1, T2, TResult>
  nameWithType: Func<T1, T2, TResult>
  fullName: System.Func<T1, T2, TResult>
  nameWithType.vb: Func(Of T1, T2, TResult)
  fullName.vb: System.Func(Of T1, T2, TResult)
  name.vb: Func(Of T1, T2, TResult)
  spec.csharp:
  - uid: System.Func`3
    name: Func
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.func-3
  - name: <
  - name: T1
  - name: ','
  - name: " "
  - name: T2
  - name: ','
  - name: " "
  - name: TResult
  - name: '>'
  spec.vb:
  - uid: System.Func`3
    name: Func
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.func-3
  - name: (
  - name: Of
  - name: " "
  - name: T1
  - name: ','
  - name: " "
  - name: T2
  - name: ','
  - name: " "
  - name: TResult
  - name: )
- uid: DrawnUi.Views.SkiaViewAccelerated.#ctor*
  commentId: Overload:DrawnUi.Views.SkiaViewAccelerated.#ctor
  href: DrawnUi.Views.SkiaViewAccelerated.html#DrawnUi_Views_SkiaViewAccelerated__ctor_DrawnUi_Views_DrawnView_
  name: SkiaViewAccelerated
  nameWithType: SkiaViewAccelerated.SkiaViewAccelerated
  fullName: DrawnUi.Views.SkiaViewAccelerated.SkiaViewAccelerated
  nameWithType.vb: SkiaViewAccelerated.New
  fullName.vb: DrawnUi.Views.SkiaViewAccelerated.New
  name.vb: New
- uid: DrawnUi.Views.DrawnView
  commentId: T:DrawnUi.Views.DrawnView
  parent: DrawnUi.Views
  href: DrawnUi.Views.DrawnView.html
  name: DrawnView
  nameWithType: DrawnView
  fullName: DrawnUi.Views.DrawnView
- uid: Microsoft.Maui.Controls.Element.OnHandlerChanging(Microsoft.Maui.Controls.HandlerChangingEventArgs)
  commentId: M:Microsoft.Maui.Controls.Element.OnHandlerChanging(Microsoft.Maui.Controls.HandlerChangingEventArgs)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onhandlerchanging
  name: OnHandlerChanging(HandlerChangingEventArgs)
  nameWithType: Element.OnHandlerChanging(HandlerChangingEventArgs)
  fullName: Microsoft.Maui.Controls.Element.OnHandlerChanging(Microsoft.Maui.Controls.HandlerChangingEventArgs)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.OnHandlerChanging(Microsoft.Maui.Controls.HandlerChangingEventArgs)
    name: OnHandlerChanging
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onhandlerchanging
  - name: (
  - uid: Microsoft.Maui.Controls.HandlerChangingEventArgs
    name: HandlerChangingEventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.handlerchangingeventargs
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.OnHandlerChanging(Microsoft.Maui.Controls.HandlerChangingEventArgs)
    name: OnHandlerChanging
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onhandlerchanging
  - name: (
  - uid: Microsoft.Maui.Controls.HandlerChangingEventArgs
    name: HandlerChangingEventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.handlerchangingeventargs
  - name: )
- uid: DrawnUi.Views.SkiaViewAccelerated.OnHandlerChanging*
  commentId: Overload:DrawnUi.Views.SkiaViewAccelerated.OnHandlerChanging
  href: DrawnUi.Views.SkiaViewAccelerated.html#DrawnUi_Views_SkiaViewAccelerated_OnHandlerChanging_Microsoft_Maui_Controls_HandlerChangingEventArgs_
  name: OnHandlerChanging
  nameWithType: SkiaViewAccelerated.OnHandlerChanging
  fullName: DrawnUi.Views.SkiaViewAccelerated.OnHandlerChanging
- uid: Microsoft.Maui.Controls.HandlerChangingEventArgs
  commentId: T:Microsoft.Maui.Controls.HandlerChangingEventArgs
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.handlerchangingeventargs
  name: HandlerChangingEventArgs
  nameWithType: HandlerChangingEventArgs
  fullName: Microsoft.Maui.Controls.HandlerChangingEventArgs
- uid: Microsoft.Maui.Controls.Element.OnHandlerChanged
  commentId: M:Microsoft.Maui.Controls.Element.OnHandlerChanged
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onhandlerchanged
  name: OnHandlerChanged()
  nameWithType: Element.OnHandlerChanged()
  fullName: Microsoft.Maui.Controls.Element.OnHandlerChanged()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.OnHandlerChanged
    name: OnHandlerChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onhandlerchanged
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.OnHandlerChanged
    name: OnHandlerChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onhandlerchanged
  - name: (
  - name: )
- uid: DrawnUi.Views.SkiaViewAccelerated.OnHandlerChanged*
  commentId: Overload:DrawnUi.Views.SkiaViewAccelerated.OnHandlerChanged
  href: DrawnUi.Views.SkiaViewAccelerated.html#DrawnUi_Views_SkiaViewAccelerated_OnHandlerChanged
  name: OnHandlerChanged
  nameWithType: SkiaViewAccelerated.OnHandlerChanged
  fullName: DrawnUi.Views.SkiaViewAccelerated.OnHandlerChanged
- uid: DrawnUi.Views.SkiaViewAccelerated.Superview*
  commentId: Overload:DrawnUi.Views.SkiaViewAccelerated.Superview
  href: DrawnUi.Views.SkiaViewAccelerated.html#DrawnUi_Views_SkiaViewAccelerated_Superview
  name: Superview
  nameWithType: SkiaViewAccelerated.Superview
  fullName: DrawnUi.Views.SkiaViewAccelerated.Superview
- uid: DrawnUi.Views.SkiaViewAccelerated.Dispose*
  commentId: Overload:DrawnUi.Views.SkiaViewAccelerated.Dispose
  href: DrawnUi.Views.SkiaViewAccelerated.html#DrawnUi_Views_SkiaViewAccelerated_Dispose
  name: Dispose
  nameWithType: SkiaViewAccelerated.Dispose
  fullName: DrawnUi.Views.SkiaViewAccelerated.Dispose
- uid: System.IDisposable.Dispose
  commentId: M:System.IDisposable.Dispose
  parent: System.IDisposable
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.idisposable.dispose
  name: Dispose()
  nameWithType: IDisposable.Dispose()
  fullName: System.IDisposable.Dispose()
  spec.csharp:
  - uid: System.IDisposable.Dispose
    name: Dispose
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.idisposable.dispose
  - name: (
  - name: )
  spec.vb:
  - uid: System.IDisposable.Dispose
    name: Dispose
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.idisposable.dispose
  - name: (
  - name: )
- uid: DrawnUi.Views.SkiaViewAccelerated.Surface*
  commentId: Overload:DrawnUi.Views.SkiaViewAccelerated.Surface
  href: DrawnUi.Views.SkiaViewAccelerated.html#DrawnUi_Views_SkiaViewAccelerated_Surface
  name: Surface
  nameWithType: SkiaViewAccelerated.Surface
  fullName: DrawnUi.Views.SkiaViewAccelerated.Surface
- uid: DrawnUi.Draw.ISkiaDrawable.Surface
  commentId: P:DrawnUi.Draw.ISkiaDrawable.Surface
  parent: DrawnUi.Draw.ISkiaDrawable
  href: DrawnUi.Draw.ISkiaDrawable.html#DrawnUi_Draw_ISkiaDrawable_Surface
  name: Surface
  nameWithType: ISkiaDrawable.Surface
  fullName: DrawnUi.Draw.ISkiaDrawable.Surface
- uid: DrawnUi.Views.SkiaViewAccelerated.IsHardwareAccelerated*
  commentId: Overload:DrawnUi.Views.SkiaViewAccelerated.IsHardwareAccelerated
  href: DrawnUi.Views.SkiaViewAccelerated.html#DrawnUi_Views_SkiaViewAccelerated_IsHardwareAccelerated
  name: IsHardwareAccelerated
  nameWithType: SkiaViewAccelerated.IsHardwareAccelerated
  fullName: DrawnUi.Views.SkiaViewAccelerated.IsHardwareAccelerated
- uid: DrawnUi.Draw.ISkiaDrawable.IsHardwareAccelerated
  commentId: P:DrawnUi.Draw.ISkiaDrawable.IsHardwareAccelerated
  parent: DrawnUi.Draw.ISkiaDrawable
  href: DrawnUi.Draw.ISkiaDrawable.html#DrawnUi_Draw_ISkiaDrawable_IsHardwareAccelerated
  name: IsHardwareAccelerated
  nameWithType: ISkiaDrawable.IsHardwareAccelerated
  fullName: DrawnUi.Draw.ISkiaDrawable.IsHardwareAccelerated
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: DrawnUi.Views.SkiaViewAccelerated.FPS*
  commentId: Overload:DrawnUi.Views.SkiaViewAccelerated.FPS
  href: DrawnUi.Views.SkiaViewAccelerated.html#DrawnUi_Views_SkiaViewAccelerated_FPS
  name: FPS
  nameWithType: SkiaViewAccelerated.FPS
  fullName: DrawnUi.Views.SkiaViewAccelerated.FPS
- uid: DrawnUi.Draw.ISkiaDrawable.FPS
  commentId: P:DrawnUi.Draw.ISkiaDrawable.FPS
  parent: DrawnUi.Draw.ISkiaDrawable
  href: DrawnUi.Draw.ISkiaDrawable.html#DrawnUi_Draw_ISkiaDrawable_FPS
  name: FPS
  nameWithType: ISkiaDrawable.FPS
  fullName: DrawnUi.Draw.ISkiaDrawable.FPS
- uid: System.Double
  commentId: T:System.Double
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.double
  name: double
  nameWithType: double
  fullName: double
  nameWithType.vb: Double
  fullName.vb: Double
  name.vb: Double
- uid: DrawnUi.Views.SkiaViewAccelerated.IsDrawing*
  commentId: Overload:DrawnUi.Views.SkiaViewAccelerated.IsDrawing
  href: DrawnUi.Views.SkiaViewAccelerated.html#DrawnUi_Views_SkiaViewAccelerated_IsDrawing
  name: IsDrawing
  nameWithType: SkiaViewAccelerated.IsDrawing
  fullName: DrawnUi.Views.SkiaViewAccelerated.IsDrawing
- uid: DrawnUi.Draw.ISkiaDrawable.IsDrawing
  commentId: P:DrawnUi.Draw.ISkiaDrawable.IsDrawing
  parent: DrawnUi.Draw.ISkiaDrawable
  href: DrawnUi.Draw.ISkiaDrawable.html#DrawnUi_Draw_ISkiaDrawable_IsDrawing
  name: IsDrawing
  nameWithType: ISkiaDrawable.IsDrawing
  fullName: DrawnUi.Draw.ISkiaDrawable.IsDrawing
- uid: DrawnUi.Views.SkiaViewAccelerated.HasDrawn*
  commentId: Overload:DrawnUi.Views.SkiaViewAccelerated.HasDrawn
  href: DrawnUi.Views.SkiaViewAccelerated.html#DrawnUi_Views_SkiaViewAccelerated_HasDrawn
  name: HasDrawn
  nameWithType: SkiaViewAccelerated.HasDrawn
  fullName: DrawnUi.Views.SkiaViewAccelerated.HasDrawn
- uid: DrawnUi.Draw.ISkiaDrawable.HasDrawn
  commentId: P:DrawnUi.Draw.ISkiaDrawable.HasDrawn
  parent: DrawnUi.Draw.ISkiaDrawable
  href: DrawnUi.Draw.ISkiaDrawable.html#DrawnUi_Draw_ISkiaDrawable_HasDrawn
  name: HasDrawn
  nameWithType: ISkiaDrawable.HasDrawn
  fullName: DrawnUi.Draw.ISkiaDrawable.HasDrawn
- uid: DrawnUi.Views.SkiaViewAccelerated.FrameTime*
  commentId: Overload:DrawnUi.Views.SkiaViewAccelerated.FrameTime
  href: DrawnUi.Views.SkiaViewAccelerated.html#DrawnUi_Views_SkiaViewAccelerated_FrameTime
  name: FrameTime
  nameWithType: SkiaViewAccelerated.FrameTime
  fullName: DrawnUi.Views.SkiaViewAccelerated.FrameTime
- uid: DrawnUi.Draw.ISkiaDrawable.FrameTime
  commentId: P:DrawnUi.Draw.ISkiaDrawable.FrameTime
  parent: DrawnUi.Draw.ISkiaDrawable
  href: DrawnUi.Draw.ISkiaDrawable.html#DrawnUi_Draw_ISkiaDrawable_FrameTime
  name: FrameTime
  nameWithType: ISkiaDrawable.FrameTime
  fullName: DrawnUi.Draw.ISkiaDrawable.FrameTime
- uid: System.Int64
  commentId: T:System.Int64
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.int64
  name: long
  nameWithType: long
  fullName: long
  nameWithType.vb: Long
  fullName.vb: Long
  name.vb: Long
- uid: DrawnUi.Views.SkiaViewAccelerated.SignalFrame*
  commentId: Overload:DrawnUi.Views.SkiaViewAccelerated.SignalFrame
  href: DrawnUi.Views.SkiaViewAccelerated.html#DrawnUi_Views_SkiaViewAccelerated_SignalFrame_System_Int64_
  name: SignalFrame
  nameWithType: SkiaViewAccelerated.SignalFrame
  fullName: DrawnUi.Views.SkiaViewAccelerated.SignalFrame
- uid: DrawnUi.Draw.ISkiaSharpView.SignalFrame(System.Int64)
  commentId: M:DrawnUi.Draw.ISkiaSharpView.SignalFrame(System.Int64)
  parent: DrawnUi.Draw.ISkiaSharpView
  isExternal: true
  href: DrawnUi.Draw.ISkiaSharpView.html#DrawnUi_Draw_ISkiaSharpView_SignalFrame_System_Int64_
  name: SignalFrame(long)
  nameWithType: ISkiaSharpView.SignalFrame(long)
  fullName: DrawnUi.Draw.ISkiaSharpView.SignalFrame(long)
  nameWithType.vb: ISkiaSharpView.SignalFrame(Long)
  fullName.vb: DrawnUi.Draw.ISkiaSharpView.SignalFrame(Long)
  name.vb: SignalFrame(Long)
  spec.csharp:
  - uid: DrawnUi.Draw.ISkiaSharpView.SignalFrame(System.Int64)
    name: SignalFrame
    href: DrawnUi.Draw.ISkiaSharpView.html#DrawnUi_Draw_ISkiaSharpView_SignalFrame_System_Int64_
  - name: (
  - uid: System.Int64
    name: long
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int64
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.ISkiaSharpView.SignalFrame(System.Int64)
    name: SignalFrame
    href: DrawnUi.Draw.ISkiaSharpView.html#DrawnUi_Draw_ISkiaSharpView_SignalFrame_System_Int64_
  - name: (
  - uid: System.Int64
    name: Long
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int64
  - name: )
- uid: DrawnUi.Views.SkiaViewAccelerated.Update*
  commentId: Overload:DrawnUi.Views.SkiaViewAccelerated.Update
  href: DrawnUi.Views.SkiaViewAccelerated.html#DrawnUi_Views_SkiaViewAccelerated_Update_System_Int64_
  name: Update
  nameWithType: SkiaViewAccelerated.Update
  fullName: DrawnUi.Views.SkiaViewAccelerated.Update
- uid: DrawnUi.Draw.ISkiaSharpView.Update(System.Int64)
  commentId: M:DrawnUi.Draw.ISkiaSharpView.Update(System.Int64)
  parent: DrawnUi.Draw.ISkiaSharpView
  isExternal: true
  href: DrawnUi.Draw.ISkiaSharpView.html#DrawnUi_Draw_ISkiaSharpView_Update_System_Int64_
  name: Update(long)
  nameWithType: ISkiaSharpView.Update(long)
  fullName: DrawnUi.Draw.ISkiaSharpView.Update(long)
  nameWithType.vb: ISkiaSharpView.Update(Long)
  fullName.vb: DrawnUi.Draw.ISkiaSharpView.Update(Long)
  name.vb: Update(Long)
  spec.csharp:
  - uid: DrawnUi.Draw.ISkiaSharpView.Update(System.Int64)
    name: Update
    href: DrawnUi.Draw.ISkiaSharpView.html#DrawnUi_Draw_ISkiaSharpView_Update_System_Int64_
  - name: (
  - uid: System.Int64
    name: long
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int64
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.ISkiaSharpView.Update(System.Int64)
    name: Update
    href: DrawnUi.Draw.ISkiaSharpView.html#DrawnUi_Draw_ISkiaSharpView_Update_System_Int64_
  - name: (
  - uid: System.Int64
    name: Long
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int64
  - name: )
