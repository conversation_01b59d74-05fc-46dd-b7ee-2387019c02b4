### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.ScaledPoint
  commentId: T:DrawnUi.Draw.ScaledPoint
  id: ScaledPoint
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.ScaledPoint.#ctor
  - DrawnUi.Draw.ScaledPoint.FromPixels(SkiaSharp.SKPoint,System.Single)
  - DrawnUi.Draw.ScaledPoint.FromPixels(System.Single,System.Single,System.Single)
  - DrawnUi.Draw.ScaledPoint.FromUnits(System.Single,System.Single,System.Single,System.Boolean)
  - DrawnUi.Draw.ScaledPoint.Pixels
  - DrawnUi.Draw.ScaledPoint.Scale
  - DrawnUi.Draw.ScaledPoint.Units
  langs:
  - csharp
  - vb
  name: ScaledPoint
  nameWithType: ScaledPoint
  fullName: DrawnUi.Draw.ScaledPoint
  type: Struct
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ScaledPoint.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ScaledPoint
    path: ../src/Shared/Draw/Internals/Models/ScaledPoint.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public struct ScaledPoint
    content.vb: Public Structure ScaledPoint
  inheritedMembers:
  - System.ValueType.Equals(System.Object)
  - System.ValueType.GetHashCode
  - System.ValueType.ToString
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetType
  - System.Object.ReferenceEquals(System.Object,System.Object)
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.ScaledPoint.#ctor
  commentId: M:DrawnUi.Draw.ScaledPoint.#ctor
  id: '#ctor'
  parent: DrawnUi.Draw.ScaledPoint
  langs:
  - csharp
  - vb
  name: ScaledPoint()
  nameWithType: ScaledPoint.ScaledPoint()
  fullName: DrawnUi.Draw.ScaledPoint.ScaledPoint()
  type: Constructor
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ScaledPoint.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Shared/Draw/Internals/Models/ScaledPoint.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public ScaledPoint()
    content.vb: Public Sub New()
  overload: DrawnUi.Draw.ScaledPoint.#ctor*
  nameWithType.vb: ScaledPoint.New()
  fullName.vb: DrawnUi.Draw.ScaledPoint.New()
  name.vb: New()
- uid: DrawnUi.Draw.ScaledPoint.Scale
  commentId: P:DrawnUi.Draw.ScaledPoint.Scale
  id: Scale
  parent: DrawnUi.Draw.ScaledPoint
  langs:
  - csharp
  - vb
  name: Scale
  nameWithType: ScaledPoint.Scale
  fullName: DrawnUi.Draw.ScaledPoint.Scale
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ScaledPoint.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Scale
    path: ../src/Shared/Draw/Internals/Models/ScaledPoint.cs
    startLine: 10
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public double Scale { readonly get; set; }
    parameters: []
    return:
      type: System.Double
    content.vb: Public Property Scale As Double
  overload: DrawnUi.Draw.ScaledPoint.Scale*
- uid: DrawnUi.Draw.ScaledPoint.Units
  commentId: P:DrawnUi.Draw.ScaledPoint.Units
  id: Units
  parent: DrawnUi.Draw.ScaledPoint
  langs:
  - csharp
  - vb
  name: Units
  nameWithType: ScaledPoint.Units
  fullName: DrawnUi.Draw.ScaledPoint.Units
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ScaledPoint.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Units
    path: ../src/Shared/Draw/Internals/Models/ScaledPoint.cs
    startLine: 11
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public SKPoint Units { readonly get; set; }
    parameters: []
    return:
      type: SkiaSharp.SKPoint
    content.vb: Public Property Units As SKPoint
  overload: DrawnUi.Draw.ScaledPoint.Units*
- uid: DrawnUi.Draw.ScaledPoint.Pixels
  commentId: P:DrawnUi.Draw.ScaledPoint.Pixels
  id: Pixels
  parent: DrawnUi.Draw.ScaledPoint
  langs:
  - csharp
  - vb
  name: Pixels
  nameWithType: ScaledPoint.Pixels
  fullName: DrawnUi.Draw.ScaledPoint.Pixels
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ScaledPoint.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Pixels
    path: ../src/Shared/Draw/Internals/Models/ScaledPoint.cs
    startLine: 12
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public SKPoint Pixels { readonly get; set; }
    parameters: []
    return:
      type: SkiaSharp.SKPoint
    content.vb: Public Property Pixels As SKPoint
  overload: DrawnUi.Draw.ScaledPoint.Pixels*
- uid: DrawnUi.Draw.ScaledPoint.FromUnits(System.Single,System.Single,System.Single,System.Boolean)
  commentId: M:DrawnUi.Draw.ScaledPoint.FromUnits(System.Single,System.Single,System.Single,System.Boolean)
  id: FromUnits(System.Single,System.Single,System.Single,System.Boolean)
  parent: DrawnUi.Draw.ScaledPoint
  langs:
  - csharp
  - vb
  name: FromUnits(float, float, float, bool)
  nameWithType: ScaledPoint.FromUnits(float, float, float, bool)
  fullName: DrawnUi.Draw.ScaledPoint.FromUnits(float, float, float, bool)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ScaledPoint.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: FromUnits
    path: ../src/Shared/Draw/Internals/Models/ScaledPoint.cs
    startLine: 14
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static ScaledPoint FromUnits(float width, float height, float scale, bool roundPixels = true)
    parameters:
    - id: width
      type: System.Single
    - id: height
      type: System.Single
    - id: scale
      type: System.Single
    - id: roundPixels
      type: System.Boolean
    return:
      type: DrawnUi.Draw.ScaledPoint
    content.vb: Public Shared Function FromUnits(width As Single, height As Single, scale As Single, roundPixels As Boolean = True) As ScaledPoint
  overload: DrawnUi.Draw.ScaledPoint.FromUnits*
  nameWithType.vb: ScaledPoint.FromUnits(Single, Single, Single, Boolean)
  fullName.vb: DrawnUi.Draw.ScaledPoint.FromUnits(Single, Single, Single, Boolean)
  name.vb: FromUnits(Single, Single, Single, Boolean)
- uid: DrawnUi.Draw.ScaledPoint.FromPixels(SkiaSharp.SKPoint,System.Single)
  commentId: M:DrawnUi.Draw.ScaledPoint.FromPixels(SkiaSharp.SKPoint,System.Single)
  id: FromPixels(SkiaSharp.SKPoint,System.Single)
  parent: DrawnUi.Draw.ScaledPoint
  langs:
  - csharp
  - vb
  name: FromPixels(SKPoint, float)
  nameWithType: ScaledPoint.FromPixels(SKPoint, float)
  fullName: DrawnUi.Draw.ScaledPoint.FromPixels(SkiaSharp.SKPoint, float)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ScaledPoint.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: FromPixels
    path: ../src/Shared/Draw/Internals/Models/ScaledPoint.cs
    startLine: 47
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static ScaledPoint FromPixels(SKPoint size, float scale)
    parameters:
    - id: size
      type: SkiaSharp.SKPoint
    - id: scale
      type: System.Single
    return:
      type: DrawnUi.Draw.ScaledPoint
    content.vb: Public Shared Function FromPixels(size As SKPoint, scale As Single) As ScaledPoint
  overload: DrawnUi.Draw.ScaledPoint.FromPixels*
  nameWithType.vb: ScaledPoint.FromPixels(SKPoint, Single)
  fullName.vb: DrawnUi.Draw.ScaledPoint.FromPixels(SkiaSharp.SKPoint, Single)
  name.vb: FromPixels(SKPoint, Single)
- uid: DrawnUi.Draw.ScaledPoint.FromPixels(System.Single,System.Single,System.Single)
  commentId: M:DrawnUi.Draw.ScaledPoint.FromPixels(System.Single,System.Single,System.Single)
  id: FromPixels(System.Single,System.Single,System.Single)
  parent: DrawnUi.Draw.ScaledPoint
  langs:
  - csharp
  - vb
  name: FromPixels(float, float, float)
  nameWithType: ScaledPoint.FromPixels(float, float, float)
  fullName: DrawnUi.Draw.ScaledPoint.FromPixels(float, float, float)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ScaledPoint.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: FromPixels
    path: ../src/Shared/Draw/Internals/Models/ScaledPoint.cs
    startLine: 52
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static ScaledPoint FromPixels(float width, float height, float scale)
    parameters:
    - id: width
      type: System.Single
    - id: height
      type: System.Single
    - id: scale
      type: System.Single
    return:
      type: DrawnUi.Draw.ScaledPoint
    content.vb: Public Shared Function FromPixels(width As Single, height As Single, scale As Single) As ScaledPoint
  overload: DrawnUi.Draw.ScaledPoint.FromPixels*
  nameWithType.vb: ScaledPoint.FromPixels(Single, Single, Single)
  fullName.vb: DrawnUi.Draw.ScaledPoint.FromPixels(Single, Single, Single)
  name.vb: FromPixels(Single, Single, Single)
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.ValueType.Equals(System.Object)
  commentId: M:System.ValueType.Equals(System.Object)
  parent: System.ValueType
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype.equals
  name: Equals(object)
  nameWithType: ValueType.Equals(object)
  fullName: System.ValueType.Equals(object)
  nameWithType.vb: ValueType.Equals(Object)
  fullName.vb: System.ValueType.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.ValueType.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.equals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.ValueType.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.equals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.ValueType.GetHashCode
  commentId: M:System.ValueType.GetHashCode
  parent: System.ValueType
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype.gethashcode
  name: GetHashCode()
  nameWithType: ValueType.GetHashCode()
  fullName: System.ValueType.GetHashCode()
  spec.csharp:
  - uid: System.ValueType.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.ValueType.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.gethashcode
  - name: (
  - name: )
- uid: System.ValueType.ToString
  commentId: M:System.ValueType.ToString
  parent: System.ValueType
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype.tostring
  name: ToString()
  nameWithType: ValueType.ToString()
  fullName: System.ValueType.ToString()
  spec.csharp:
  - uid: System.ValueType.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.ValueType.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.tostring
  - name: (
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.ValueType
  commentId: T:System.ValueType
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype
  name: ValueType
  nameWithType: ValueType
  fullName: System.ValueType
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.ScaledPoint.#ctor*
  commentId: Overload:DrawnUi.Draw.ScaledPoint.#ctor
  href: DrawnUi.Draw.ScaledPoint.html#DrawnUi_Draw_ScaledPoint__ctor
  name: ScaledPoint
  nameWithType: ScaledPoint.ScaledPoint
  fullName: DrawnUi.Draw.ScaledPoint.ScaledPoint
  nameWithType.vb: ScaledPoint.New
  fullName.vb: DrawnUi.Draw.ScaledPoint.New
  name.vb: New
- uid: DrawnUi.Draw.ScaledPoint.Scale*
  commentId: Overload:DrawnUi.Draw.ScaledPoint.Scale
  href: DrawnUi.Draw.ScaledPoint.html#DrawnUi_Draw_ScaledPoint_Scale
  name: Scale
  nameWithType: ScaledPoint.Scale
  fullName: DrawnUi.Draw.ScaledPoint.Scale
- uid: System.Double
  commentId: T:System.Double
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.double
  name: double
  nameWithType: double
  fullName: double
  nameWithType.vb: Double
  fullName.vb: Double
  name.vb: Double
- uid: DrawnUi.Draw.ScaledPoint.Units*
  commentId: Overload:DrawnUi.Draw.ScaledPoint.Units
  href: DrawnUi.Draw.ScaledPoint.html#DrawnUi_Draw_ScaledPoint_Units
  name: Units
  nameWithType: ScaledPoint.Units
  fullName: DrawnUi.Draw.ScaledPoint.Units
- uid: SkiaSharp.SKPoint
  commentId: T:SkiaSharp.SKPoint
  parent: SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.skpoint
  name: SKPoint
  nameWithType: SKPoint
  fullName: SkiaSharp.SKPoint
- uid: SkiaSharp
  commentId: N:SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp
  name: SkiaSharp
  nameWithType: SkiaSharp
  fullName: SkiaSharp
- uid: DrawnUi.Draw.ScaledPoint.Pixels*
  commentId: Overload:DrawnUi.Draw.ScaledPoint.Pixels
  href: DrawnUi.Draw.ScaledPoint.html#DrawnUi_Draw_ScaledPoint_Pixels
  name: Pixels
  nameWithType: ScaledPoint.Pixels
  fullName: DrawnUi.Draw.ScaledPoint.Pixels
- uid: DrawnUi.Draw.ScaledPoint.FromUnits*
  commentId: Overload:DrawnUi.Draw.ScaledPoint.FromUnits
  href: DrawnUi.Draw.ScaledPoint.html#DrawnUi_Draw_ScaledPoint_FromUnits_System_Single_System_Single_System_Single_System_Boolean_
  name: FromUnits
  nameWithType: ScaledPoint.FromUnits
  fullName: DrawnUi.Draw.ScaledPoint.FromUnits
- uid: System.Single
  commentId: T:System.Single
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.single
  name: float
  nameWithType: float
  fullName: float
  nameWithType.vb: Single
  fullName.vb: Single
  name.vb: Single
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: DrawnUi.Draw.ScaledPoint
  commentId: T:DrawnUi.Draw.ScaledPoint
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ScaledPoint.html
  name: ScaledPoint
  nameWithType: ScaledPoint
  fullName: DrawnUi.Draw.ScaledPoint
- uid: DrawnUi.Draw.ScaledPoint.FromPixels*
  commentId: Overload:DrawnUi.Draw.ScaledPoint.FromPixels
  href: DrawnUi.Draw.ScaledPoint.html#DrawnUi_Draw_ScaledPoint_FromPixels_SkiaSharp_SKPoint_System_Single_
  name: FromPixels
  nameWithType: ScaledPoint.FromPixels
  fullName: DrawnUi.Draw.ScaledPoint.FromPixels
