### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.GesturesMode
  commentId: T:DrawnUi.Draw.GesturesMode
  id: GesturesMode
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.GesturesMode.Disabled
  - DrawnUi.Draw.GesturesMode.Enabled
  - DrawnUi.Draw.GesturesMode.Lock
  langs:
  - csharp
  - vb
  name: GesturesMode
  nameWithType: GesturesMode
  fullName: DrawnUi.Draw.GesturesMode
  type: Enum
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Gestures/GesturesMode.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: GesturesMode
    path: ../src/Maui/DrawnUi/Features/Gestures/GesturesMode.cs
    startLine: 5
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Used by the canvas, do not need this for drawn controls
  example: []
  syntax:
    content: public enum GesturesMode
    content.vb: Public Enum GesturesMode
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.GesturesMode.Disabled
  commentId: F:DrawnUi.Draw.GesturesMode.Disabled
  id: Disabled
  parent: DrawnUi.Draw.GesturesMode
  langs:
  - csharp
  - vb
  name: Disabled
  nameWithType: GesturesMode.Disabled
  fullName: DrawnUi.Draw.GesturesMode.Disabled
  type: Field
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Gestures/GesturesMode.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Disabled
    path: ../src/Maui/DrawnUi/Features/Gestures/GesturesMode.cs
    startLine: 10
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Default
  example: []
  syntax:
    content: Disabled = 0
    return:
      type: DrawnUi.Draw.GesturesMode
- uid: DrawnUi.Draw.GesturesMode.Enabled
  commentId: F:DrawnUi.Draw.GesturesMode.Enabled
  id: Enabled
  parent: DrawnUi.Draw.GesturesMode
  langs:
  - csharp
  - vb
  name: Enabled
  nameWithType: GesturesMode.Enabled
  fullName: DrawnUi.Draw.GesturesMode.Enabled
  type: Field
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Gestures/GesturesMode.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Enabled
    path: ../src/Maui/DrawnUi/Features/Gestures/GesturesMode.cs
    startLine: 15
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Gestures attached
  example: []
  syntax:
    content: Enabled = 1
    return:
      type: DrawnUi.Draw.GesturesMode
- uid: DrawnUi.Draw.GesturesMode.Lock
  commentId: F:DrawnUi.Draw.GesturesMode.Lock
  id: Lock
  parent: DrawnUi.Draw.GesturesMode
  langs:
  - csharp
  - vb
  name: Lock
  nameWithType: GesturesMode.Lock
  fullName: DrawnUi.Draw.GesturesMode.Lock
  type: Field
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Gestures/GesturesMode.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Lock
    path: ../src/Maui/DrawnUi/Features/Gestures/GesturesMode.cs
    startLine: 20
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Lock input for self, useful inside scroll view, panning controls like slider etc
  example: []
  syntax:
    content: Lock = 2
    return:
      type: DrawnUi.Draw.GesturesMode
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.GesturesMode
  commentId: T:DrawnUi.Draw.GesturesMode
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.GesturesMode.html
  name: GesturesMode
  nameWithType: GesturesMode
  fullName: DrawnUi.Draw.GesturesMode
