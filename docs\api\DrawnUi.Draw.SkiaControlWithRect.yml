### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.SkiaControlWithRect
  commentId: T:DrawnUi.Draw.SkiaControlWithRect
  id: SkiaControlWithRect
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.SkiaControlWithRect.#ctor(DrawnUi.Draw.SkiaControl,SkiaSharp.SKRect,SkiaSharp.SKRect,System.Int32)
  - DrawnUi.Draw.SkiaControlWithRect.Control
  - DrawnUi.Draw.SkiaControlWithRect.HitRect
  - DrawnUi.Draw.SkiaControlWithRect.Index
  - DrawnUi.Draw.SkiaControlWithRect.Rect
  langs:
  - csharp
  - vb
  name: SkiaControlWithRect
  nameWithType: SkiaControlWithRect
  fullName: DrawnUi.Draw.SkiaControlWithRect
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: SkiaControlWithRect
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
    startLine: 9
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Used inside RenderingTree. Rect is real drawing position
  example: []
  syntax:
    content: 'public record SkiaControlWithRect : IEquatable<SkiaControlWithRect>'
    content.vb: Public Class SkiaControlWithRect Implements IEquatable(Of SkiaControlWithRect)
  inheritance:
  - System.Object
  implements:
  - System.IEquatable{DrawnUi.Draw.SkiaControlWithRect}
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.SkiaControlWithRect.#ctor(DrawnUi.Draw.SkiaControl,SkiaSharp.SKRect,SkiaSharp.SKRect,System.Int32)
  commentId: M:DrawnUi.Draw.SkiaControlWithRect.#ctor(DrawnUi.Draw.SkiaControl,SkiaSharp.SKRect,SkiaSharp.SKRect,System.Int32)
  id: '#ctor(DrawnUi.Draw.SkiaControl,SkiaSharp.SKRect,SkiaSharp.SKRect,System.Int32)'
  parent: DrawnUi.Draw.SkiaControlWithRect
  langs:
  - csharp
  - vb
  name: SkiaControlWithRect(SkiaControl, SKRect, SKRect, int)
  nameWithType: SkiaControlWithRect.SkiaControlWithRect(SkiaControl, SKRect, SKRect, int)
  fullName: DrawnUi.Draw.SkiaControlWithRect.SkiaControlWithRect(DrawnUi.Draw.SkiaControl, SkiaSharp.SKRect, SkiaSharp.SKRect, int)
  type: Constructor
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
    startLine: 9
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Used inside RenderingTree. Rect is real drawing position
  example: []
  syntax:
    content: public SkiaControlWithRect(SkiaControl Control, SKRect Rect, SKRect HitRect, int Index)
    parameters:
    - id: Control
      type: DrawnUi.Draw.SkiaControl
      description: ''
    - id: Rect
      type: SkiaSharp.SKRect
      description: ''
    - id: HitRect
      type: SkiaSharp.SKRect
    - id: Index
      type: System.Int32
      description: ''
    content.vb: Public Sub New(Control As SkiaControl, Rect As SKRect, HitRect As SKRect, Index As Integer)
  overload: DrawnUi.Draw.SkiaControlWithRect.#ctor*
  nameWithType.vb: SkiaControlWithRect.New(SkiaControl, SKRect, SKRect, Integer)
  fullName.vb: DrawnUi.Draw.SkiaControlWithRect.New(DrawnUi.Draw.SkiaControl, SkiaSharp.SKRect, SkiaSharp.SKRect, Integer)
  name.vb: New(SkiaControl, SKRect, SKRect, Integer)
- uid: DrawnUi.Draw.SkiaControlWithRect.Control
  commentId: P:DrawnUi.Draw.SkiaControlWithRect.Control
  id: Control
  parent: DrawnUi.Draw.SkiaControlWithRect
  langs:
  - csharp
  - vb
  name: Control
  nameWithType: SkiaControlWithRect.Control
  fullName: DrawnUi.Draw.SkiaControlWithRect.Control
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Control
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
    startLine: 9
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: ''
  example: []
  syntax:
    content: public SkiaControl Control { get; init; }
    parameters: []
    return:
      type: DrawnUi.Draw.SkiaControl
    content.vb: Public Property Control As SkiaControl
  overload: DrawnUi.Draw.SkiaControlWithRect.Control*
- uid: DrawnUi.Draw.SkiaControlWithRect.Rect
  commentId: P:DrawnUi.Draw.SkiaControlWithRect.Rect
  id: Rect
  parent: DrawnUi.Draw.SkiaControlWithRect
  langs:
  - csharp
  - vb
  name: Rect
  nameWithType: SkiaControlWithRect.Rect
  fullName: DrawnUi.Draw.SkiaControlWithRect.Rect
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Rect
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
    startLine: 10
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: ''
  example: []
  syntax:
    content: public SKRect Rect { get; init; }
    parameters: []
    return:
      type: SkiaSharp.SKRect
    content.vb: Public Property Rect As SKRect
  overload: DrawnUi.Draw.SkiaControlWithRect.Rect*
- uid: DrawnUi.Draw.SkiaControlWithRect.HitRect
  commentId: P:DrawnUi.Draw.SkiaControlWithRect.HitRect
  id: HitRect
  parent: DrawnUi.Draw.SkiaControlWithRect
  langs:
  - csharp
  - vb
  name: HitRect
  nameWithType: SkiaControlWithRect.HitRect
  fullName: DrawnUi.Draw.SkiaControlWithRect.HitRect
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: HitRect
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
    startLine: 11
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public SKRect HitRect { get; init; }
    parameters: []
    return:
      type: SkiaSharp.SKRect
    content.vb: Public Property HitRect As SKRect
  overload: DrawnUi.Draw.SkiaControlWithRect.HitRect*
- uid: DrawnUi.Draw.SkiaControlWithRect.Index
  commentId: P:DrawnUi.Draw.SkiaControlWithRect.Index
  id: Index
  parent: DrawnUi.Draw.SkiaControlWithRect
  langs:
  - csharp
  - vb
  name: Index
  nameWithType: SkiaControlWithRect.Index
  fullName: DrawnUi.Draw.SkiaControlWithRect.Index
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Index
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaControlWithRect.cs
    startLine: 12
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: ''
  example: []
  syntax:
    content: public int Index { get; init; }
    parameters: []
    return:
      type: System.Int32
    content.vb: Public Property Index As Integer
  overload: DrawnUi.Draw.SkiaControlWithRect.Index*
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.IEquatable{DrawnUi.Draw.SkiaControlWithRect}
  commentId: T:System.IEquatable{DrawnUi.Draw.SkiaControlWithRect}
  parent: System
  definition: System.IEquatable`1
  href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  name: IEquatable<SkiaControlWithRect>
  nameWithType: IEquatable<SkiaControlWithRect>
  fullName: System.IEquatable<DrawnUi.Draw.SkiaControlWithRect>
  nameWithType.vb: IEquatable(Of SkiaControlWithRect)
  fullName.vb: System.IEquatable(Of DrawnUi.Draw.SkiaControlWithRect)
  name.vb: IEquatable(Of SkiaControlWithRect)
  spec.csharp:
  - uid: System.IEquatable`1
    name: IEquatable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  - name: <
  - uid: DrawnUi.Draw.SkiaControlWithRect
    name: SkiaControlWithRect
    href: DrawnUi.Draw.SkiaControlWithRect.html
  - name: '>'
  spec.vb:
  - uid: System.IEquatable`1
    name: IEquatable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Draw.SkiaControlWithRect
    name: SkiaControlWithRect
    href: DrawnUi.Draw.SkiaControlWithRect.html
  - name: )
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: System.IEquatable`1
  commentId: T:System.IEquatable`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  name: IEquatable<T>
  nameWithType: IEquatable<T>
  fullName: System.IEquatable<T>
  nameWithType.vb: IEquatable(Of T)
  fullName.vb: System.IEquatable(Of T)
  name.vb: IEquatable(Of T)
  spec.csharp:
  - uid: System.IEquatable`1
    name: IEquatable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.IEquatable`1
    name: IEquatable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.SkiaControlWithRect.#ctor*
  commentId: Overload:DrawnUi.Draw.SkiaControlWithRect.#ctor
  href: DrawnUi.Draw.SkiaControlWithRect.html#DrawnUi_Draw_SkiaControlWithRect__ctor_DrawnUi_Draw_SkiaControl_SkiaSharp_SKRect_SkiaSharp_SKRect_System_Int32_
  name: SkiaControlWithRect
  nameWithType: SkiaControlWithRect.SkiaControlWithRect
  fullName: DrawnUi.Draw.SkiaControlWithRect.SkiaControlWithRect
  nameWithType.vb: SkiaControlWithRect.New
  fullName.vb: DrawnUi.Draw.SkiaControlWithRect.New
  name.vb: New
- uid: DrawnUi.Draw.SkiaControl
  commentId: T:DrawnUi.Draw.SkiaControl
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaControl.html
  name: SkiaControl
  nameWithType: SkiaControl
  fullName: DrawnUi.Draw.SkiaControl
- uid: SkiaSharp.SKRect
  commentId: T:SkiaSharp.SKRect
  parent: SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.skrect
  name: SKRect
  nameWithType: SKRect
  fullName: SkiaSharp.SKRect
- uid: System.Int32
  commentId: T:System.Int32
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.int32
  name: int
  nameWithType: int
  fullName: int
  nameWithType.vb: Integer
  fullName.vb: Integer
  name.vb: Integer
- uid: SkiaSharp
  commentId: N:SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp
  name: SkiaSharp
  nameWithType: SkiaSharp
  fullName: SkiaSharp
- uid: DrawnUi.Draw.SkiaControlWithRect.Control*
  commentId: Overload:DrawnUi.Draw.SkiaControlWithRect.Control
  href: DrawnUi.Draw.SkiaControlWithRect.html#DrawnUi_Draw_SkiaControlWithRect_Control
  name: Control
  nameWithType: SkiaControlWithRect.Control
  fullName: DrawnUi.Draw.SkiaControlWithRect.Control
- uid: DrawnUi.Draw.SkiaControlWithRect.Rect*
  commentId: Overload:DrawnUi.Draw.SkiaControlWithRect.Rect
  href: DrawnUi.Draw.SkiaControlWithRect.html#DrawnUi_Draw_SkiaControlWithRect_Rect
  name: Rect
  nameWithType: SkiaControlWithRect.Rect
  fullName: DrawnUi.Draw.SkiaControlWithRect.Rect
- uid: DrawnUi.Draw.SkiaControlWithRect.HitRect*
  commentId: Overload:DrawnUi.Draw.SkiaControlWithRect.HitRect
  href: DrawnUi.Draw.SkiaControlWithRect.html#DrawnUi_Draw_SkiaControlWithRect_HitRect
  name: HitRect
  nameWithType: SkiaControlWithRect.HitRect
  fullName: DrawnUi.Draw.SkiaControlWithRect.HitRect
- uid: DrawnUi.Draw.SkiaControlWithRect.Index*
  commentId: Overload:DrawnUi.Draw.SkiaControlWithRect.Index
  href: DrawnUi.Draw.SkiaControlWithRect.html#DrawnUi_Draw_SkiaControlWithRect_Index
  name: Index
  nameWithType: SkiaControlWithRect.Index
  fullName: DrawnUi.Draw.SkiaControlWithRect.Index
