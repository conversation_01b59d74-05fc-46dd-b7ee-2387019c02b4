name: Manage Inactive Issues

on:
  schedule:
    - cron: '0 0 * * *'  # Runs daily at midnight UTC

jobs:
  manage_inactive_issues:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/stale@v8
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          stale-issue-message: |
            :hourglass_flowing_sand: Additional information has been requested to identify the issue, though the thread has been inactive for 7 days. If no additional information is provided within the next 3 days, it will be closed automatically.
          close-issue-message: |
            :warning: Closing this issue due to no response in the last 10 days. Feel free to reopen it or create a new issue if you have more information.
          days-before-stale: 7        # Days of inactivity before marking as stale
          days-before-close: 3        # Days after marking as stale before closing
          stale-issue-label: 'more-info-needed'  # Label to identify issues needing more info
          only-labels: 'more-info-needed'        # Only process issues with this label
          operations-per-run: 30      # Adjust as needed to handle more issues
          mark-issue-as-stale: true   # Mark the issue as stale
          close-issue: true           # Enable issue closing
          only-issues: true           # Only process issues, not PRs
          exempt-issue-labels: 'pinned,important'  # Optional: Exclude certain labels
