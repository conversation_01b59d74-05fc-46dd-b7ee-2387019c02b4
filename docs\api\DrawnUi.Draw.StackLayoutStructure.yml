### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.StackLayoutStructure
  commentId: T:DrawnUi.Draw.StackLayoutStructure
  id: StackLayoutStructure
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.StackLayoutStructure.#ctor(DrawnUi.Draw.SkiaLayout)
  - DrawnUi.Draw.StackLayoutStructure.Build(SkiaSharp.SKRect,System.Single)
  - DrawnUi.Draw.StackLayoutStructure.ChildrenCount
  - DrawnUi.Draw.StackLayoutStructure.CreateWrapper(System.Int32,DrawnUi.Draw.SkiaControl)
  - DrawnUi.Draw.StackLayoutStructure.EnumerateViewsForMeasurement
  - DrawnUi.Draw.StackLayoutStructure.GetSpacingForIndex(System.Int32,System.Single)
  - DrawnUi.Draw.StackLayoutStructure.MeasureCell(SkiaSharp.SKRect,DrawnUi.Draw.ControlInStack,DrawnUi.Draw.SkiaControl,System.Single)
  - DrawnUi.Draw.StackLayoutStructure._layout
  langs:
  - csharp
  - vb
  name: StackLayoutStructure
  nameWithType: StackLayoutStructure
  fullName: DrawnUi.Draw.StackLayoutStructure
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/StackLayoutStructure.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: StackLayoutStructure
    path: ../src/Maui/DrawnUi/Draw/Layout/StackLayoutStructure.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public abstract class StackLayoutStructure
    content.vb: Public MustInherit Class StackLayoutStructure
  inheritance:
  - System.Object
  derivedClasses:
  - DrawnUi.Draw.SkiaLayout.BuildWrapLayout
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.StackLayoutStructure._layout
  commentId: F:DrawnUi.Draw.StackLayoutStructure._layout
  id: _layout
  parent: DrawnUi.Draw.StackLayoutStructure
  langs:
  - csharp
  - vb
  name: _layout
  nameWithType: StackLayoutStructure._layout
  fullName: DrawnUi.Draw.StackLayoutStructure._layout
  type: Field
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/StackLayoutStructure.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: _layout
    path: ../src/Maui/DrawnUi/Draw/Layout/StackLayoutStructure.cs
    startLine: 6
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: protected readonly SkiaLayout _layout
    return:
      type: DrawnUi.Draw.SkiaLayout
    content.vb: Protected ReadOnly _layout As SkiaLayout
- uid: DrawnUi.Draw.StackLayoutStructure.ChildrenCount
  commentId: F:DrawnUi.Draw.StackLayoutStructure.ChildrenCount
  id: ChildrenCount
  parent: DrawnUi.Draw.StackLayoutStructure
  langs:
  - csharp
  - vb
  name: ChildrenCount
  nameWithType: StackLayoutStructure.ChildrenCount
  fullName: DrawnUi.Draw.StackLayoutStructure.ChildrenCount
  type: Field
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/StackLayoutStructure.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ChildrenCount
    path: ../src/Maui/DrawnUi/Draw/Layout/StackLayoutStructure.cs
    startLine: 8
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public long ChildrenCount
    return:
      type: System.Int64
    content.vb: Public ChildrenCount As Long
- uid: DrawnUi.Draw.StackLayoutStructure.#ctor(DrawnUi.Draw.SkiaLayout)
  commentId: M:DrawnUi.Draw.StackLayoutStructure.#ctor(DrawnUi.Draw.SkiaLayout)
  id: '#ctor(DrawnUi.Draw.SkiaLayout)'
  parent: DrawnUi.Draw.StackLayoutStructure
  langs:
  - csharp
  - vb
  name: StackLayoutStructure(SkiaLayout)
  nameWithType: StackLayoutStructure.StackLayoutStructure(SkiaLayout)
  fullName: DrawnUi.Draw.StackLayoutStructure.StackLayoutStructure(DrawnUi.Draw.SkiaLayout)
  type: Constructor
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/StackLayoutStructure.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Maui/DrawnUi/Draw/Layout/StackLayoutStructure.cs
    startLine: 10
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public StackLayoutStructure(SkiaLayout layout)
    parameters:
    - id: layout
      type: DrawnUi.Draw.SkiaLayout
    content.vb: Public Sub New(layout As SkiaLayout)
  overload: DrawnUi.Draw.StackLayoutStructure.#ctor*
  nameWithType.vb: StackLayoutStructure.New(SkiaLayout)
  fullName.vb: DrawnUi.Draw.StackLayoutStructure.New(DrawnUi.Draw.SkiaLayout)
  name.vb: New(SkiaLayout)
- uid: DrawnUi.Draw.StackLayoutStructure.EnumerateViewsForMeasurement
  commentId: M:DrawnUi.Draw.StackLayoutStructure.EnumerateViewsForMeasurement
  id: EnumerateViewsForMeasurement
  parent: DrawnUi.Draw.StackLayoutStructure
  langs:
  - csharp
  - vb
  name: EnumerateViewsForMeasurement()
  nameWithType: StackLayoutStructure.EnumerateViewsForMeasurement()
  fullName: DrawnUi.Draw.StackLayoutStructure.EnumerateViewsForMeasurement()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/StackLayoutStructure.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: EnumerateViewsForMeasurement
    path: ../src/Maui/DrawnUi/Draw/Layout/StackLayoutStructure.cs
    startLine: 17
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public virtual IEnumerable<SkiaControl> EnumerateViewsForMeasurement()
    return:
      type: System.Collections.Generic.IEnumerable{DrawnUi.Draw.SkiaControl}
    content.vb: Public Overridable Function EnumerateViewsForMeasurement() As IEnumerable(Of SkiaControl)
  overload: DrawnUi.Draw.StackLayoutStructure.EnumerateViewsForMeasurement*
- uid: DrawnUi.Draw.StackLayoutStructure.Build(SkiaSharp.SKRect,System.Single)
  commentId: M:DrawnUi.Draw.StackLayoutStructure.Build(SkiaSharp.SKRect,System.Single)
  id: Build(SkiaSharp.SKRect,System.Single)
  parent: DrawnUi.Draw.StackLayoutStructure
  langs:
  - csharp
  - vb
  name: Build(SKRect, float)
  nameWithType: StackLayoutStructure.Build(SKRect, float)
  fullName: DrawnUi.Draw.StackLayoutStructure.Build(SkiaSharp.SKRect, float)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/StackLayoutStructure.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Build
    path: ../src/Maui/DrawnUi/Draw/Layout/StackLayoutStructure.cs
    startLine: 87
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Will measure children and build appropriate stack structure for the layout
  example: []
  syntax:
    content: public abstract ScaledSize Build(SKRect rectForChildrenPixels, float scale)
    parameters:
    - id: rectForChildrenPixels
      type: SkiaSharp.SKRect
    - id: scale
      type: System.Single
    return:
      type: DrawnUi.Draw.ScaledSize
    content.vb: Public MustOverride Function Build(rectForChildrenPixels As SKRect, scale As Single) As ScaledSize
  overload: DrawnUi.Draw.StackLayoutStructure.Build*
  nameWithType.vb: StackLayoutStructure.Build(SKRect, Single)
  fullName.vb: DrawnUi.Draw.StackLayoutStructure.Build(SkiaSharp.SKRect, Single)
  name.vb: Build(SKRect, Single)
- uid: DrawnUi.Draw.StackLayoutStructure.MeasureCell(SkiaSharp.SKRect,DrawnUi.Draw.ControlInStack,DrawnUi.Draw.SkiaControl,System.Single)
  commentId: M:DrawnUi.Draw.StackLayoutStructure.MeasureCell(SkiaSharp.SKRect,DrawnUi.Draw.ControlInStack,DrawnUi.Draw.SkiaControl,System.Single)
  id: MeasureCell(SkiaSharp.SKRect,DrawnUi.Draw.ControlInStack,DrawnUi.Draw.SkiaControl,System.Single)
  parent: DrawnUi.Draw.StackLayoutStructure
  langs:
  - csharp
  - vb
  name: MeasureCell(SKRect, ControlInStack, SkiaControl, float)
  nameWithType: StackLayoutStructure.MeasureCell(SKRect, ControlInStack, SkiaControl, float)
  fullName: DrawnUi.Draw.StackLayoutStructure.MeasureCell(SkiaSharp.SKRect, DrawnUi.Draw.ControlInStack, DrawnUi.Draw.SkiaControl, float)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/StackLayoutStructure.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: MeasureCell
    path: ../src/Maui/DrawnUi/Draw/Layout/StackLayoutStructure.cs
    startLine: 90
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: protected virtual ScaledSize MeasureCell(SKRect destination, ControlInStack cell, SkiaControl child, float scale)
    parameters:
    - id: destination
      type: SkiaSharp.SKRect
    - id: cell
      type: DrawnUi.Draw.ControlInStack
    - id: child
      type: DrawnUi.Draw.SkiaControl
    - id: scale
      type: System.Single
    return:
      type: DrawnUi.Draw.ScaledSize
    content.vb: Protected Overridable Function MeasureCell(destination As SKRect, cell As ControlInStack, child As SkiaControl, scale As Single) As ScaledSize
  overload: DrawnUi.Draw.StackLayoutStructure.MeasureCell*
  nameWithType.vb: StackLayoutStructure.MeasureCell(SKRect, ControlInStack, SkiaControl, Single)
  fullName.vb: DrawnUi.Draw.StackLayoutStructure.MeasureCell(SkiaSharp.SKRect, DrawnUi.Draw.ControlInStack, DrawnUi.Draw.SkiaControl, Single)
  name.vb: MeasureCell(SKRect, ControlInStack, SkiaControl, Single)
- uid: DrawnUi.Draw.StackLayoutStructure.GetSpacingForIndex(System.Int32,System.Single)
  commentId: M:DrawnUi.Draw.StackLayoutStructure.GetSpacingForIndex(System.Int32,System.Single)
  id: GetSpacingForIndex(System.Int32,System.Single)
  parent: DrawnUi.Draw.StackLayoutStructure
  langs:
  - csharp
  - vb
  name: GetSpacingForIndex(int, float)
  nameWithType: StackLayoutStructure.GetSpacingForIndex(int, float)
  fullName: DrawnUi.Draw.StackLayoutStructure.GetSpacingForIndex(int, float)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/StackLayoutStructure.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: GetSpacingForIndex
    path: ../src/Maui/DrawnUi/Draw/Layout/StackLayoutStructure.cs
    startLine: 152
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public virtual float GetSpacingForIndex(int forIndex, float scale)
    parameters:
    - id: forIndex
      type: System.Int32
    - id: scale
      type: System.Single
    return:
      type: System.Single
    content.vb: Public Overridable Function GetSpacingForIndex(forIndex As Integer, scale As Single) As Single
  overload: DrawnUi.Draw.StackLayoutStructure.GetSpacingForIndex*
  nameWithType.vb: StackLayoutStructure.GetSpacingForIndex(Integer, Single)
  fullName.vb: DrawnUi.Draw.StackLayoutStructure.GetSpacingForIndex(Integer, Single)
  name.vb: GetSpacingForIndex(Integer, Single)
- uid: DrawnUi.Draw.StackLayoutStructure.CreateWrapper(System.Int32,DrawnUi.Draw.SkiaControl)
  commentId: M:DrawnUi.Draw.StackLayoutStructure.CreateWrapper(System.Int32,DrawnUi.Draw.SkiaControl)
  id: CreateWrapper(System.Int32,DrawnUi.Draw.SkiaControl)
  parent: DrawnUi.Draw.StackLayoutStructure
  langs:
  - csharp
  - vb
  name: CreateWrapper(int, SkiaControl)
  nameWithType: StackLayoutStructure.CreateWrapper(int, SkiaControl)
  fullName: DrawnUi.Draw.StackLayoutStructure.CreateWrapper(int, DrawnUi.Draw.SkiaControl)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/StackLayoutStructure.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: CreateWrapper
    path: ../src/Maui/DrawnUi/Draw/Layout/StackLayoutStructure.cs
    startLine: 163
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public virtual ControlInStack CreateWrapper(int i, SkiaControl control)
    parameters:
    - id: i
      type: System.Int32
    - id: control
      type: DrawnUi.Draw.SkiaControl
    return:
      type: DrawnUi.Draw.ControlInStack
    content.vb: Public Overridable Function CreateWrapper(i As Integer, control As SkiaControl) As ControlInStack
  overload: DrawnUi.Draw.StackLayoutStructure.CreateWrapper*
  nameWithType.vb: StackLayoutStructure.CreateWrapper(Integer, SkiaControl)
  fullName.vb: DrawnUi.Draw.StackLayoutStructure.CreateWrapper(Integer, DrawnUi.Draw.SkiaControl)
  name.vb: CreateWrapper(Integer, SkiaControl)
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.SkiaLayout
  commentId: T:DrawnUi.Draw.SkiaLayout
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaLayout.html
  name: SkiaLayout
  nameWithType: SkiaLayout
  fullName: DrawnUi.Draw.SkiaLayout
- uid: System.Int64
  commentId: T:System.Int64
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.int64
  name: long
  nameWithType: long
  fullName: long
  nameWithType.vb: Long
  fullName.vb: Long
  name.vb: Long
- uid: DrawnUi.Draw.StackLayoutStructure.#ctor*
  commentId: Overload:DrawnUi.Draw.StackLayoutStructure.#ctor
  href: DrawnUi.Draw.StackLayoutStructure.html#DrawnUi_Draw_StackLayoutStructure__ctor_DrawnUi_Draw_SkiaLayout_
  name: StackLayoutStructure
  nameWithType: StackLayoutStructure.StackLayoutStructure
  fullName: DrawnUi.Draw.StackLayoutStructure.StackLayoutStructure
  nameWithType.vb: StackLayoutStructure.New
  fullName.vb: DrawnUi.Draw.StackLayoutStructure.New
  name.vb: New
- uid: DrawnUi.Draw.StackLayoutStructure.EnumerateViewsForMeasurement*
  commentId: Overload:DrawnUi.Draw.StackLayoutStructure.EnumerateViewsForMeasurement
  href: DrawnUi.Draw.StackLayoutStructure.html#DrawnUi_Draw_StackLayoutStructure_EnumerateViewsForMeasurement
  name: EnumerateViewsForMeasurement
  nameWithType: StackLayoutStructure.EnumerateViewsForMeasurement
  fullName: DrawnUi.Draw.StackLayoutStructure.EnumerateViewsForMeasurement
- uid: System.Collections.Generic.IEnumerable{DrawnUi.Draw.SkiaControl}
  commentId: T:System.Collections.Generic.IEnumerable{DrawnUi.Draw.SkiaControl}
  parent: System.Collections.Generic
  definition: System.Collections.Generic.IEnumerable`1
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1
  name: IEnumerable<SkiaControl>
  nameWithType: IEnumerable<SkiaControl>
  fullName: System.Collections.Generic.IEnumerable<DrawnUi.Draw.SkiaControl>
  nameWithType.vb: IEnumerable(Of SkiaControl)
  fullName.vb: System.Collections.Generic.IEnumerable(Of DrawnUi.Draw.SkiaControl)
  name.vb: IEnumerable(Of SkiaControl)
  spec.csharp:
  - uid: System.Collections.Generic.IEnumerable`1
    name: IEnumerable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1
  - name: <
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.IEnumerable`1
    name: IEnumerable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
- uid: System.Collections.Generic.IEnumerable`1
  commentId: T:System.Collections.Generic.IEnumerable`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1
  name: IEnumerable<T>
  nameWithType: IEnumerable<T>
  fullName: System.Collections.Generic.IEnumerable<T>
  nameWithType.vb: IEnumerable(Of T)
  fullName.vb: System.Collections.Generic.IEnumerable(Of T)
  name.vb: IEnumerable(Of T)
  spec.csharp:
  - uid: System.Collections.Generic.IEnumerable`1
    name: IEnumerable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.IEnumerable`1
    name: IEnumerable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: System.Collections.Generic
  commentId: N:System.Collections.Generic
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Collections.Generic
  nameWithType: System.Collections.Generic
  fullName: System.Collections.Generic
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Generic
    name: Generic
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Generic
    name: Generic
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic
- uid: DrawnUi.Draw.StackLayoutStructure.Build*
  commentId: Overload:DrawnUi.Draw.StackLayoutStructure.Build
  href: DrawnUi.Draw.StackLayoutStructure.html#DrawnUi_Draw_StackLayoutStructure_Build_SkiaSharp_SKRect_System_Single_
  name: Build
  nameWithType: StackLayoutStructure.Build
  fullName: DrawnUi.Draw.StackLayoutStructure.Build
- uid: SkiaSharp.SKRect
  commentId: T:SkiaSharp.SKRect
  parent: SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.skrect
  name: SKRect
  nameWithType: SKRect
  fullName: SkiaSharp.SKRect
- uid: System.Single
  commentId: T:System.Single
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.single
  name: float
  nameWithType: float
  fullName: float
  nameWithType.vb: Single
  fullName.vb: Single
  name.vb: Single
- uid: DrawnUi.Draw.ScaledSize
  commentId: T:DrawnUi.Draw.ScaledSize
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ScaledSize.html
  name: ScaledSize
  nameWithType: ScaledSize
  fullName: DrawnUi.Draw.ScaledSize
- uid: SkiaSharp
  commentId: N:SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp
  name: SkiaSharp
  nameWithType: SkiaSharp
  fullName: SkiaSharp
- uid: DrawnUi.Draw.StackLayoutStructure.MeasureCell*
  commentId: Overload:DrawnUi.Draw.StackLayoutStructure.MeasureCell
  href: DrawnUi.Draw.StackLayoutStructure.html#DrawnUi_Draw_StackLayoutStructure_MeasureCell_SkiaSharp_SKRect_DrawnUi_Draw_ControlInStack_DrawnUi_Draw_SkiaControl_System_Single_
  name: MeasureCell
  nameWithType: StackLayoutStructure.MeasureCell
  fullName: DrawnUi.Draw.StackLayoutStructure.MeasureCell
- uid: DrawnUi.Draw.ControlInStack
  commentId: T:DrawnUi.Draw.ControlInStack
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ControlInStack.html
  name: ControlInStack
  nameWithType: ControlInStack
  fullName: DrawnUi.Draw.ControlInStack
- uid: DrawnUi.Draw.SkiaControl
  commentId: T:DrawnUi.Draw.SkiaControl
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaControl.html
  name: SkiaControl
  nameWithType: SkiaControl
  fullName: DrawnUi.Draw.SkiaControl
- uid: DrawnUi.Draw.StackLayoutStructure.GetSpacingForIndex*
  commentId: Overload:DrawnUi.Draw.StackLayoutStructure.GetSpacingForIndex
  href: DrawnUi.Draw.StackLayoutStructure.html#DrawnUi_Draw_StackLayoutStructure_GetSpacingForIndex_System_Int32_System_Single_
  name: GetSpacingForIndex
  nameWithType: StackLayoutStructure.GetSpacingForIndex
  fullName: DrawnUi.Draw.StackLayoutStructure.GetSpacingForIndex
- uid: System.Int32
  commentId: T:System.Int32
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.int32
  name: int
  nameWithType: int
  fullName: int
  nameWithType.vb: Integer
  fullName.vb: Integer
  name.vb: Integer
- uid: DrawnUi.Draw.StackLayoutStructure.CreateWrapper*
  commentId: Overload:DrawnUi.Draw.StackLayoutStructure.CreateWrapper
  href: DrawnUi.Draw.StackLayoutStructure.html#DrawnUi_Draw_StackLayoutStructure_CreateWrapper_System_Int32_DrawnUi_Draw_SkiaControl_
  name: CreateWrapper
  nameWithType: StackLayoutStructure.CreateWrapper
  fullName: DrawnUi.Draw.StackLayoutStructure.CreateWrapper
