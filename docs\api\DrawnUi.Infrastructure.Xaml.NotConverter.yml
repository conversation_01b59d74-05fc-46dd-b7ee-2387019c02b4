### YamlMime:ManagedReference
items:
- uid: DrawnUi.Infrastructure.Xaml.NotConverter
  commentId: T:DrawnUi.Infrastructure.Xaml.NotConverter
  id: NotConverter
  parent: DrawnUi.Infrastructure.Xaml
  children:
  - DrawnUi.Infrastructure.Xaml.NotConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)
  - DrawnUi.Infrastructure.Xaml.NotConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)
  langs:
  - csharp
  - vb
  name: NotConverter
  nameWithType: NotConverter
  fullName: DrawnUi.Infrastructure.Xaml.NotConverter
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Xaml/NotConverter.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: NotConverter
    path: ../src/Maui/DrawnUi/Internals/Xaml/NotConverter.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure.Xaml
  syntax:
    content: 'public class NotConverter : IValueConverter'
    content.vb: Public Class NotConverter Implements IValueConverter
  inheritance:
  - System.Object
  implements:
  - Microsoft.Maui.Controls.IValueConverter
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Infrastructure.Xaml.NotConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)
  commentId: M:DrawnUi.Infrastructure.Xaml.NotConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)
  id: Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)
  parent: DrawnUi.Infrastructure.Xaml.NotConverter
  langs:
  - csharp
  - vb
  name: Convert(object, Type, object, CultureInfo)
  nameWithType: NotConverter.Convert(object, Type, object, CultureInfo)
  fullName: DrawnUi.Infrastructure.Xaml.NotConverter.Convert(object, System.Type, object, System.Globalization.CultureInfo)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Xaml/NotConverter.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Convert
    path: ../src/Maui/DrawnUi/Internals/Xaml/NotConverter.cs
    startLine: 6
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure.Xaml
  example: []
  syntax:
    content: public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    parameters:
    - id: value
      type: System.Object
    - id: targetType
      type: System.Type
    - id: parameter
      type: System.Object
    - id: culture
      type: System.Globalization.CultureInfo
    return:
      type: System.Object
    content.vb: Public Function Convert(value As Object, targetType As Type, parameter As Object, culture As CultureInfo) As Object
  overload: DrawnUi.Infrastructure.Xaml.NotConverter.Convert*
  implements:
  - Microsoft.Maui.Controls.IValueConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)
  nameWithType.vb: NotConverter.Convert(Object, Type, Object, CultureInfo)
  fullName.vb: DrawnUi.Infrastructure.Xaml.NotConverter.Convert(Object, System.Type, Object, System.Globalization.CultureInfo)
  name.vb: Convert(Object, Type, Object, CultureInfo)
- uid: DrawnUi.Infrastructure.Xaml.NotConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)
  commentId: M:DrawnUi.Infrastructure.Xaml.NotConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)
  id: ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)
  parent: DrawnUi.Infrastructure.Xaml.NotConverter
  langs:
  - csharp
  - vb
  name: ConvertBack(object, Type, object, CultureInfo)
  nameWithType: NotConverter.ConvertBack(object, Type, object, CultureInfo)
  fullName: DrawnUi.Infrastructure.Xaml.NotConverter.ConvertBack(object, System.Type, object, System.Globalization.CultureInfo)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Xaml/NotConverter.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ConvertBack
    path: ../src/Maui/DrawnUi/Internals/Xaml/NotConverter.cs
    startLine: 11
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure.Xaml
  example: []
  syntax:
    content: public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    parameters:
    - id: value
      type: System.Object
    - id: targetType
      type: System.Type
    - id: parameter
      type: System.Object
    - id: culture
      type: System.Globalization.CultureInfo
    return:
      type: System.Object
    content.vb: Public Function ConvertBack(value As Object, targetType As Type, parameter As Object, culture As CultureInfo) As Object
  overload: DrawnUi.Infrastructure.Xaml.NotConverter.ConvertBack*
  implements:
  - Microsoft.Maui.Controls.IValueConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)
  nameWithType.vb: NotConverter.ConvertBack(Object, Type, Object, CultureInfo)
  fullName.vb: DrawnUi.Infrastructure.Xaml.NotConverter.ConvertBack(Object, System.Type, Object, System.Globalization.CultureInfo)
  name.vb: ConvertBack(Object, Type, Object, CultureInfo)
references:
- uid: DrawnUi.Infrastructure.Xaml
  commentId: N:DrawnUi.Infrastructure.Xaml
  href: DrawnUi.html
  name: DrawnUi.Infrastructure.Xaml
  nameWithType: DrawnUi.Infrastructure.Xaml
  fullName: DrawnUi.Infrastructure.Xaml
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Infrastructure
    name: Infrastructure
    href: DrawnUi.Infrastructure.html
  - name: .
  - uid: DrawnUi.Infrastructure.Xaml
    name: Xaml
    href: DrawnUi.Infrastructure.Xaml.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Infrastructure
    name: Infrastructure
    href: DrawnUi.Infrastructure.html
  - name: .
  - uid: DrawnUi.Infrastructure.Xaml
    name: Xaml
    href: DrawnUi.Infrastructure.Xaml.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: Microsoft.Maui.Controls.IValueConverter
  commentId: T:Microsoft.Maui.Controls.IValueConverter
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ivalueconverter
  name: IValueConverter
  nameWithType: IValueConverter
  fullName: Microsoft.Maui.Controls.IValueConverter
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: Microsoft.Maui.Controls
  commentId: N:Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft
  name: Microsoft.Maui.Controls
  nameWithType: Microsoft.Maui.Controls
  fullName: Microsoft.Maui.Controls
  spec.csharp:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Controls
    name: Controls
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls
  spec.vb:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Controls
    name: Controls
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Infrastructure.Xaml.NotConverter.Convert*
  commentId: Overload:DrawnUi.Infrastructure.Xaml.NotConverter.Convert
  href: DrawnUi.Infrastructure.Xaml.NotConverter.html#DrawnUi_Infrastructure_Xaml_NotConverter_Convert_System_Object_System_Type_System_Object_System_Globalization_CultureInfo_
  name: Convert
  nameWithType: NotConverter.Convert
  fullName: DrawnUi.Infrastructure.Xaml.NotConverter.Convert
- uid: Microsoft.Maui.Controls.IValueConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)
  commentId: M:Microsoft.Maui.Controls.IValueConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)
  parent: Microsoft.Maui.Controls.IValueConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ivalueconverter.convert
  name: Convert(object, Type, object, CultureInfo)
  nameWithType: IValueConverter.Convert(object, Type, object, CultureInfo)
  fullName: Microsoft.Maui.Controls.IValueConverter.Convert(object, System.Type, object, System.Globalization.CultureInfo)
  nameWithType.vb: IValueConverter.Convert(Object, Type, Object, CultureInfo)
  fullName.vb: Microsoft.Maui.Controls.IValueConverter.Convert(Object, System.Type, Object, System.Globalization.CultureInfo)
  name.vb: Convert(Object, Type, Object, CultureInfo)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.IValueConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)
    name: Convert
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ivalueconverter.convert
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Globalization.CultureInfo
    name: CultureInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.globalization.cultureinfo
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.IValueConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)
    name: Convert
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ivalueconverter.convert
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Globalization.CultureInfo
    name: CultureInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.globalization.cultureinfo
  - name: )
- uid: System.Type
  commentId: T:System.Type
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.type
  name: Type
  nameWithType: Type
  fullName: System.Type
- uid: System.Globalization.CultureInfo
  commentId: T:System.Globalization.CultureInfo
  parent: System.Globalization
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.globalization.cultureinfo
  name: CultureInfo
  nameWithType: CultureInfo
  fullName: System.Globalization.CultureInfo
- uid: System.Globalization
  commentId: N:System.Globalization
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Globalization
  nameWithType: System.Globalization
  fullName: System.Globalization
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Globalization
    name: Globalization
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.globalization
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Globalization
    name: Globalization
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.globalization
- uid: DrawnUi.Infrastructure.Xaml.NotConverter.ConvertBack*
  commentId: Overload:DrawnUi.Infrastructure.Xaml.NotConverter.ConvertBack
  href: DrawnUi.Infrastructure.Xaml.NotConverter.html#DrawnUi_Infrastructure_Xaml_NotConverter_ConvertBack_System_Object_System_Type_System_Object_System_Globalization_CultureInfo_
  name: ConvertBack
  nameWithType: NotConverter.ConvertBack
  fullName: DrawnUi.Infrastructure.Xaml.NotConverter.ConvertBack
- uid: Microsoft.Maui.Controls.IValueConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)
  commentId: M:Microsoft.Maui.Controls.IValueConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)
  parent: Microsoft.Maui.Controls.IValueConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ivalueconverter.convertback
  name: ConvertBack(object, Type, object, CultureInfo)
  nameWithType: IValueConverter.ConvertBack(object, Type, object, CultureInfo)
  fullName: Microsoft.Maui.Controls.IValueConverter.ConvertBack(object, System.Type, object, System.Globalization.CultureInfo)
  nameWithType.vb: IValueConverter.ConvertBack(Object, Type, Object, CultureInfo)
  fullName.vb: Microsoft.Maui.Controls.IValueConverter.ConvertBack(Object, System.Type, Object, System.Globalization.CultureInfo)
  name.vb: ConvertBack(Object, Type, Object, CultureInfo)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.IValueConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)
    name: ConvertBack
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ivalueconverter.convertback
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Globalization.CultureInfo
    name: CultureInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.globalization.cultureinfo
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.IValueConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)
    name: ConvertBack
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ivalueconverter.convertback
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Globalization.CultureInfo
    name: CultureInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.globalization.cultureinfo
  - name: )
