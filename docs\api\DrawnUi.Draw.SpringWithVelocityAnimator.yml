### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.SpringWithVelocityAnimator
  commentId: T:DrawnUi.Draw.SpringWithVelocityAnimator
  id: SpringWithVelocityAnimator
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.SpringWithVelocityAnimator.#ctor(DrawnUi.Draw.IDrawnBase)
  - DrawnUi.Draw.SpringWithVelocityAnimator.Initialize(System.Single,System.Single,System.Single,DrawnUi.Infrastructure.Spring,System.Single)
  - DrawnUi.Draw.SpringWithVelocityAnimator.Parameters
  - DrawnUi.Draw.SpringWithVelocityAnimator.UpdateValue(System.Int64,System.Int64)
  langs:
  - csharp
  - vb
  name: SpringWithVelocityAnimator
  nameWithType: SpringWithVelocityAnimator
  fullName: DrawnUi.Draw.SpringWithVelocityAnimator
  type: Class
  source:
    remote:
      path: src/Shared/Features/Animators/SpringWithVelocityAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: SpringWithVelocityAnimator
    path: ../src/Shared/Features/Animators/SpringWithVelocityAnimator.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: 'public class SpringWithVelocityAnimator : SkiaValueAnimator, ISkiaAnimator, IDisposable'
    content.vb: Public Class SpringWithVelocityAnimator Inherits SkiaValueAnimator Implements ISkiaAnimator, IDisposable
  inheritance:
  - System.Object
  - DrawnUi.Draw.AnimatorBase
  - DrawnUi.Draw.SkiaValueAnimator
  implements:
  - DrawnUi.Draw.ISkiaAnimator
  - System.IDisposable
  inheritedMembers:
  - DrawnUi.Draw.SkiaValueAnimator.Dispose
  - DrawnUi.Draw.SkiaValueAnimator.Stop
  - DrawnUi.Draw.SkiaValueAnimator.RunAsync(System.Action,System.Threading.CancellationToken)
  - DrawnUi.Draw.SkiaValueAnimator.OnRunningStateChanged(System.Boolean)
  - DrawnUi.Draw.SkiaValueAnimator.Seek(System.Single)
  - DrawnUi.Draw.SkiaValueAnimator.CycleFInished
  - DrawnUi.Draw.SkiaValueAnimator.Finished
  - DrawnUi.Draw.SkiaValueAnimator.FinishedRunning
  - DrawnUi.Draw.SkiaValueAnimator.FrameTimeInterpolator
  - DrawnUi.Draw.SkiaValueAnimator.TickFrame(System.Int64)
  - DrawnUi.Draw.SkiaValueAnimator.Repeat
  - DrawnUi.Draw.SkiaValueAnimator.mValue
  - DrawnUi.Draw.SkiaValueAnimator.mStartValueIsSet
  - DrawnUi.Draw.SkiaValueAnimator.mMaxValue
  - DrawnUi.Draw.SkiaValueAnimator.mMinValue
  - DrawnUi.Draw.SkiaValueAnimator.Easing
  - DrawnUi.Draw.SkiaValueAnimator.Speed
  - DrawnUi.Draw.SkiaValueAnimator.TransformReportedValue(System.Int64)
  - DrawnUi.Draw.SkiaValueAnimator.Debug
  - DrawnUi.Draw.SkiaValueAnimator.GetNanoseconds
  - DrawnUi.Draw.SkiaValueAnimator.ElapsedMs
  - DrawnUi.Draw.SkiaValueAnimator.Progress
  - DrawnUi.Draw.SkiaValueAnimator.OnUpdated
  - DrawnUi.Draw.SkiaValueAnimator.ClampOnStart
  - DrawnUi.Draw.SkiaValueAnimator.Start(System.Double)
  - DrawnUi.Draw.SkiaValueAnimator.SetValue(System.Double)
  - DrawnUi.Draw.SkiaValueAnimator.SetSpeed(System.Double)
  - DrawnUi.Draw.AnimatorBase.IsPostAnimator
  - DrawnUi.Draw.AnimatorBase.IsHiddenInViewTree
  - DrawnUi.Draw.AnimatorBase.Radians(System.Double)
  - DrawnUi.Draw.AnimatorBase.runDelayMs
  - DrawnUi.Draw.AnimatorBase.Register
  - DrawnUi.Draw.AnimatorBase.Unregister
  - DrawnUi.Draw.AnimatorBase.Cancel
  - DrawnUi.Draw.AnimatorBase.Pause
  - DrawnUi.Draw.AnimatorBase.Resume
  - DrawnUi.Draw.AnimatorBase.IsPaused
  - DrawnUi.Draw.AnimatorBase.OnStop
  - DrawnUi.Draw.AnimatorBase.OnStart
  - DrawnUi.Draw.AnimatorBase.Parent
  - DrawnUi.Draw.AnimatorBase.IsDeactivated
  - DrawnUi.Draw.AnimatorBase.LastFrameTimeNanos
  - DrawnUi.Draw.AnimatorBase.StartFrameTimeNanos
  - DrawnUi.Draw.AnimatorBase.Uid
  - DrawnUi.Draw.AnimatorBase.IsRunning
  - DrawnUi.Draw.AnimatorBase.WasStarted
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.SpringWithVelocityAnimator.Initialize(System.Single,System.Single,System.Single,DrawnUi.Infrastructure.Spring,System.Single)
  commentId: M:DrawnUi.Draw.SpringWithVelocityAnimator.Initialize(System.Single,System.Single,System.Single,DrawnUi.Infrastructure.Spring,System.Single)
  id: Initialize(System.Single,System.Single,System.Single,DrawnUi.Infrastructure.Spring,System.Single)
  parent: DrawnUi.Draw.SpringWithVelocityAnimator
  langs:
  - csharp
  - vb
  name: Initialize(float, float, float, Spring, float)
  nameWithType: SpringWithVelocityAnimator.Initialize(float, float, float, Spring, float)
  fullName: DrawnUi.Draw.SpringWithVelocityAnimator.Initialize(float, float, float, DrawnUi.Infrastructure.Spring, float)
  type: Method
  source:
    remote:
      path: src/Shared/Features/Animators/SpringWithVelocityAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Initialize
    path: ../src/Shared/Features/Animators/SpringWithVelocityAnimator.cs
    startLine: 7
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public void Initialize(float restOffset, float position, float velocity, Spring spring, float thresholdStop = 0.5)
    parameters:
    - id: restOffset
      type: System.Single
    - id: position
      type: System.Single
    - id: velocity
      type: System.Single
    - id: spring
      type: DrawnUi.Infrastructure.Spring
    - id: thresholdStop
      type: System.Single
    content.vb: Public Sub Initialize(restOffset As Single, position As Single, velocity As Single, spring As Spring, thresholdStop As Single = 0.5)
  overload: DrawnUi.Draw.SpringWithVelocityAnimator.Initialize*
  nameWithType.vb: SpringWithVelocityAnimator.Initialize(Single, Single, Single, Spring, Single)
  fullName.vb: DrawnUi.Draw.SpringWithVelocityAnimator.Initialize(Single, Single, Single, DrawnUi.Infrastructure.Spring, Single)
  name.vb: Initialize(Single, Single, Single, Spring, Single)
- uid: DrawnUi.Draw.SpringWithVelocityAnimator.Parameters
  commentId: P:DrawnUi.Draw.SpringWithVelocityAnimator.Parameters
  id: Parameters
  parent: DrawnUi.Draw.SpringWithVelocityAnimator
  langs:
  - csharp
  - vb
  name: Parameters
  nameWithType: SpringWithVelocityAnimator.Parameters
  fullName: DrawnUi.Draw.SpringWithVelocityAnimator.Parameters
  type: Property
  source:
    remote:
      path: src/Shared/Features/Animators/SpringWithVelocityAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Parameters
    path: ../src/Shared/Features/Animators/SpringWithVelocityAnimator.cs
    startLine: 14
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public SpringTimingParameters Parameters { get; set; }
    parameters: []
    return:
      type: DrawnUi.Draw.SpringTimingParameters
    content.vb: Public Property Parameters As SpringTimingParameters
  overload: DrawnUi.Draw.SpringWithVelocityAnimator.Parameters*
- uid: DrawnUi.Draw.SpringWithVelocityAnimator.UpdateValue(System.Int64,System.Int64)
  commentId: M:DrawnUi.Draw.SpringWithVelocityAnimator.UpdateValue(System.Int64,System.Int64)
  id: UpdateValue(System.Int64,System.Int64)
  parent: DrawnUi.Draw.SpringWithVelocityAnimator
  langs:
  - csharp
  - vb
  name: UpdateValue(long, long)
  nameWithType: SpringWithVelocityAnimator.UpdateValue(long, long)
  fullName: DrawnUi.Draw.SpringWithVelocityAnimator.UpdateValue(long, long)
  type: Method
  source:
    remote:
      path: src/Shared/Features/Animators/SpringWithVelocityAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: UpdateValue
    path: ../src/Shared/Features/Animators/SpringWithVelocityAnimator.cs
    startLine: 16
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: >-
    Update mValue using time distance between rendered frames.

    Return true if anims is finished.
  example: []
  syntax:
    content: protected override bool UpdateValue(long deltaT, long deltaFromStart)
    parameters:
    - id: deltaT
      type: System.Int64
      description: ''
    - id: deltaFromStart
      type: System.Int64
    return:
      type: System.Boolean
      description: ''
    content.vb: Protected Overrides Function UpdateValue(deltaT As Long, deltaFromStart As Long) As Boolean
  overridden: DrawnUi.Draw.SkiaValueAnimator.UpdateValue(System.Int64,System.Int64)
  overload: DrawnUi.Draw.SpringWithVelocityAnimator.UpdateValue*
  nameWithType.vb: SpringWithVelocityAnimator.UpdateValue(Long, Long)
  fullName.vb: DrawnUi.Draw.SpringWithVelocityAnimator.UpdateValue(Long, Long)
  name.vb: UpdateValue(Long, Long)
- uid: DrawnUi.Draw.SpringWithVelocityAnimator.#ctor(DrawnUi.Draw.IDrawnBase)
  commentId: M:DrawnUi.Draw.SpringWithVelocityAnimator.#ctor(DrawnUi.Draw.IDrawnBase)
  id: '#ctor(DrawnUi.Draw.IDrawnBase)'
  parent: DrawnUi.Draw.SpringWithVelocityAnimator
  langs:
  - csharp
  - vb
  name: SpringWithVelocityAnimator(IDrawnBase)
  nameWithType: SpringWithVelocityAnimator.SpringWithVelocityAnimator(IDrawnBase)
  fullName: DrawnUi.Draw.SpringWithVelocityAnimator.SpringWithVelocityAnimator(DrawnUi.Draw.IDrawnBase)
  type: Constructor
  source:
    remote:
      path: src/Shared/Features/Animators/SpringWithVelocityAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Shared/Features/Animators/SpringWithVelocityAnimator.cs
    startLine: 30
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public SpringWithVelocityAnimator(IDrawnBase parent)
    parameters:
    - id: parent
      type: DrawnUi.Draw.IDrawnBase
    content.vb: Public Sub New(parent As IDrawnBase)
  overload: DrawnUi.Draw.SpringWithVelocityAnimator.#ctor*
  nameWithType.vb: SpringWithVelocityAnimator.New(IDrawnBase)
  fullName.vb: DrawnUi.Draw.SpringWithVelocityAnimator.New(DrawnUi.Draw.IDrawnBase)
  name.vb: New(IDrawnBase)
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: DrawnUi.Draw.AnimatorBase
  commentId: T:DrawnUi.Draw.AnimatorBase
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.AnimatorBase.html
  name: AnimatorBase
  nameWithType: AnimatorBase
  fullName: DrawnUi.Draw.AnimatorBase
- uid: DrawnUi.Draw.SkiaValueAnimator
  commentId: T:DrawnUi.Draw.SkiaValueAnimator
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaValueAnimator.html
  name: SkiaValueAnimator
  nameWithType: SkiaValueAnimator
  fullName: DrawnUi.Draw.SkiaValueAnimator
- uid: DrawnUi.Draw.ISkiaAnimator
  commentId: T:DrawnUi.Draw.ISkiaAnimator
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ISkiaAnimator.html
  name: ISkiaAnimator
  nameWithType: ISkiaAnimator
  fullName: DrawnUi.Draw.ISkiaAnimator
- uid: System.IDisposable
  commentId: T:System.IDisposable
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.idisposable
  name: IDisposable
  nameWithType: IDisposable
  fullName: System.IDisposable
- uid: DrawnUi.Draw.SkiaValueAnimator.Dispose
  commentId: M:DrawnUi.Draw.SkiaValueAnimator.Dispose
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Dispose
  name: Dispose()
  nameWithType: SkiaValueAnimator.Dispose()
  fullName: DrawnUi.Draw.SkiaValueAnimator.Dispose()
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaValueAnimator.Dispose
    name: Dispose
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Dispose
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SkiaValueAnimator.Dispose
    name: Dispose
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Dispose
  - name: (
  - name: )
- uid: DrawnUi.Draw.SkiaValueAnimator.Stop
  commentId: M:DrawnUi.Draw.SkiaValueAnimator.Stop
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Stop
  name: Stop()
  nameWithType: SkiaValueAnimator.Stop()
  fullName: DrawnUi.Draw.SkiaValueAnimator.Stop()
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaValueAnimator.Stop
    name: Stop
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Stop
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SkiaValueAnimator.Stop
    name: Stop
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Stop
  - name: (
  - name: )
- uid: DrawnUi.Draw.SkiaValueAnimator.RunAsync(System.Action,System.Threading.CancellationToken)
  commentId: M:DrawnUi.Draw.SkiaValueAnimator.RunAsync(System.Action,System.Threading.CancellationToken)
  parent: DrawnUi.Draw.SkiaValueAnimator
  isExternal: true
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_RunAsync_System_Action_System_Threading_CancellationToken_
  name: RunAsync(Action, CancellationToken)
  nameWithType: SkiaValueAnimator.RunAsync(Action, CancellationToken)
  fullName: DrawnUi.Draw.SkiaValueAnimator.RunAsync(System.Action, System.Threading.CancellationToken)
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaValueAnimator.RunAsync(System.Action,System.Threading.CancellationToken)
    name: RunAsync
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_RunAsync_System_Action_System_Threading_CancellationToken_
  - name: (
  - uid: System.Action
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action
  - name: ','
  - name: " "
  - uid: System.Threading.CancellationToken
    name: CancellationToken
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading.cancellationtoken
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SkiaValueAnimator.RunAsync(System.Action,System.Threading.CancellationToken)
    name: RunAsync
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_RunAsync_System_Action_System_Threading_CancellationToken_
  - name: (
  - uid: System.Action
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action
  - name: ','
  - name: " "
  - uid: System.Threading.CancellationToken
    name: CancellationToken
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading.cancellationtoken
  - name: )
- uid: DrawnUi.Draw.SkiaValueAnimator.OnRunningStateChanged(System.Boolean)
  commentId: M:DrawnUi.Draw.SkiaValueAnimator.OnRunningStateChanged(System.Boolean)
  parent: DrawnUi.Draw.SkiaValueAnimator
  isExternal: true
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_OnRunningStateChanged_System_Boolean_
  name: OnRunningStateChanged(bool)
  nameWithType: SkiaValueAnimator.OnRunningStateChanged(bool)
  fullName: DrawnUi.Draw.SkiaValueAnimator.OnRunningStateChanged(bool)
  nameWithType.vb: SkiaValueAnimator.OnRunningStateChanged(Boolean)
  fullName.vb: DrawnUi.Draw.SkiaValueAnimator.OnRunningStateChanged(Boolean)
  name.vb: OnRunningStateChanged(Boolean)
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaValueAnimator.OnRunningStateChanged(System.Boolean)
    name: OnRunningStateChanged
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_OnRunningStateChanged_System_Boolean_
  - name: (
  - uid: System.Boolean
    name: bool
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SkiaValueAnimator.OnRunningStateChanged(System.Boolean)
    name: OnRunningStateChanged
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_OnRunningStateChanged_System_Boolean_
  - name: (
  - uid: System.Boolean
    name: Boolean
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
- uid: DrawnUi.Draw.SkiaValueAnimator.Seek(System.Single)
  commentId: M:DrawnUi.Draw.SkiaValueAnimator.Seek(System.Single)
  parent: DrawnUi.Draw.SkiaValueAnimator
  isExternal: true
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Seek_System_Single_
  name: Seek(float)
  nameWithType: SkiaValueAnimator.Seek(float)
  fullName: DrawnUi.Draw.SkiaValueAnimator.Seek(float)
  nameWithType.vb: SkiaValueAnimator.Seek(Single)
  fullName.vb: DrawnUi.Draw.SkiaValueAnimator.Seek(Single)
  name.vb: Seek(Single)
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaValueAnimator.Seek(System.Single)
    name: Seek
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Seek_System_Single_
  - name: (
  - uid: System.Single
    name: float
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.single
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SkiaValueAnimator.Seek(System.Single)
    name: Seek
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Seek_System_Single_
  - name: (
  - uid: System.Single
    name: Single
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.single
  - name: )
- uid: DrawnUi.Draw.SkiaValueAnimator.CycleFInished
  commentId: P:DrawnUi.Draw.SkiaValueAnimator.CycleFInished
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_CycleFInished
  name: CycleFInished
  nameWithType: SkiaValueAnimator.CycleFInished
  fullName: DrawnUi.Draw.SkiaValueAnimator.CycleFInished
- uid: DrawnUi.Draw.SkiaValueAnimator.Finished
  commentId: P:DrawnUi.Draw.SkiaValueAnimator.Finished
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Finished
  name: Finished
  nameWithType: SkiaValueAnimator.Finished
  fullName: DrawnUi.Draw.SkiaValueAnimator.Finished
- uid: DrawnUi.Draw.SkiaValueAnimator.FinishedRunning
  commentId: M:DrawnUi.Draw.SkiaValueAnimator.FinishedRunning
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_FinishedRunning
  name: FinishedRunning()
  nameWithType: SkiaValueAnimator.FinishedRunning()
  fullName: DrawnUi.Draw.SkiaValueAnimator.FinishedRunning()
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaValueAnimator.FinishedRunning
    name: FinishedRunning
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_FinishedRunning
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SkiaValueAnimator.FinishedRunning
    name: FinishedRunning
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_FinishedRunning
  - name: (
  - name: )
- uid: DrawnUi.Draw.SkiaValueAnimator.FrameTimeInterpolator
  commentId: F:DrawnUi.Draw.SkiaValueAnimator.FrameTimeInterpolator
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_FrameTimeInterpolator
  name: FrameTimeInterpolator
  nameWithType: SkiaValueAnimator.FrameTimeInterpolator
  fullName: DrawnUi.Draw.SkiaValueAnimator.FrameTimeInterpolator
- uid: DrawnUi.Draw.SkiaValueAnimator.TickFrame(System.Int64)
  commentId: M:DrawnUi.Draw.SkiaValueAnimator.TickFrame(System.Int64)
  parent: DrawnUi.Draw.SkiaValueAnimator
  isExternal: true
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_TickFrame_System_Int64_
  name: TickFrame(long)
  nameWithType: SkiaValueAnimator.TickFrame(long)
  fullName: DrawnUi.Draw.SkiaValueAnimator.TickFrame(long)
  nameWithType.vb: SkiaValueAnimator.TickFrame(Long)
  fullName.vb: DrawnUi.Draw.SkiaValueAnimator.TickFrame(Long)
  name.vb: TickFrame(Long)
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaValueAnimator.TickFrame(System.Int64)
    name: TickFrame
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_TickFrame_System_Int64_
  - name: (
  - uid: System.Int64
    name: long
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int64
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SkiaValueAnimator.TickFrame(System.Int64)
    name: TickFrame
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_TickFrame_System_Int64_
  - name: (
  - uid: System.Int64
    name: Long
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int64
  - name: )
- uid: DrawnUi.Draw.SkiaValueAnimator.Repeat
  commentId: P:DrawnUi.Draw.SkiaValueAnimator.Repeat
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Repeat
  name: Repeat
  nameWithType: SkiaValueAnimator.Repeat
  fullName: DrawnUi.Draw.SkiaValueAnimator.Repeat
- uid: DrawnUi.Draw.SkiaValueAnimator.mValue
  commentId: P:DrawnUi.Draw.SkiaValueAnimator.mValue
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_mValue
  name: mValue
  nameWithType: SkiaValueAnimator.mValue
  fullName: DrawnUi.Draw.SkiaValueAnimator.mValue
- uid: DrawnUi.Draw.SkiaValueAnimator.mStartValueIsSet
  commentId: P:DrawnUi.Draw.SkiaValueAnimator.mStartValueIsSet
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_mStartValueIsSet
  name: mStartValueIsSet
  nameWithType: SkiaValueAnimator.mStartValueIsSet
  fullName: DrawnUi.Draw.SkiaValueAnimator.mStartValueIsSet
- uid: DrawnUi.Draw.SkiaValueAnimator.mMaxValue
  commentId: P:DrawnUi.Draw.SkiaValueAnimator.mMaxValue
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_mMaxValue
  name: mMaxValue
  nameWithType: SkiaValueAnimator.mMaxValue
  fullName: DrawnUi.Draw.SkiaValueAnimator.mMaxValue
- uid: DrawnUi.Draw.SkiaValueAnimator.mMinValue
  commentId: P:DrawnUi.Draw.SkiaValueAnimator.mMinValue
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_mMinValue
  name: mMinValue
  nameWithType: SkiaValueAnimator.mMinValue
  fullName: DrawnUi.Draw.SkiaValueAnimator.mMinValue
- uid: DrawnUi.Draw.SkiaValueAnimator.Easing
  commentId: P:DrawnUi.Draw.SkiaValueAnimator.Easing
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Easing
  name: Easing
  nameWithType: SkiaValueAnimator.Easing
  fullName: DrawnUi.Draw.SkiaValueAnimator.Easing
- uid: DrawnUi.Draw.SkiaValueAnimator.Speed
  commentId: P:DrawnUi.Draw.SkiaValueAnimator.Speed
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Speed
  name: Speed
  nameWithType: SkiaValueAnimator.Speed
  fullName: DrawnUi.Draw.SkiaValueAnimator.Speed
- uid: DrawnUi.Draw.SkiaValueAnimator.TransformReportedValue(System.Int64)
  commentId: M:DrawnUi.Draw.SkiaValueAnimator.TransformReportedValue(System.Int64)
  parent: DrawnUi.Draw.SkiaValueAnimator
  isExternal: true
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_TransformReportedValue_System_Int64_
  name: TransformReportedValue(long)
  nameWithType: SkiaValueAnimator.TransformReportedValue(long)
  fullName: DrawnUi.Draw.SkiaValueAnimator.TransformReportedValue(long)
  nameWithType.vb: SkiaValueAnimator.TransformReportedValue(Long)
  fullName.vb: DrawnUi.Draw.SkiaValueAnimator.TransformReportedValue(Long)
  name.vb: TransformReportedValue(Long)
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaValueAnimator.TransformReportedValue(System.Int64)
    name: TransformReportedValue
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_TransformReportedValue_System_Int64_
  - name: (
  - uid: System.Int64
    name: long
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int64
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SkiaValueAnimator.TransformReportedValue(System.Int64)
    name: TransformReportedValue
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_TransformReportedValue_System_Int64_
  - name: (
  - uid: System.Int64
    name: Long
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int64
  - name: )
- uid: DrawnUi.Draw.SkiaValueAnimator.Debug
  commentId: F:DrawnUi.Draw.SkiaValueAnimator.Debug
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Debug
  name: Debug
  nameWithType: SkiaValueAnimator.Debug
  fullName: DrawnUi.Draw.SkiaValueAnimator.Debug
- uid: DrawnUi.Draw.SkiaValueAnimator.GetNanoseconds
  commentId: M:DrawnUi.Draw.SkiaValueAnimator.GetNanoseconds
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_GetNanoseconds
  name: GetNanoseconds()
  nameWithType: SkiaValueAnimator.GetNanoseconds()
  fullName: DrawnUi.Draw.SkiaValueAnimator.GetNanoseconds()
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaValueAnimator.GetNanoseconds
    name: GetNanoseconds
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_GetNanoseconds
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SkiaValueAnimator.GetNanoseconds
    name: GetNanoseconds
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_GetNanoseconds
  - name: (
  - name: )
- uid: DrawnUi.Draw.SkiaValueAnimator.ElapsedMs
  commentId: P:DrawnUi.Draw.SkiaValueAnimator.ElapsedMs
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_ElapsedMs
  name: ElapsedMs
  nameWithType: SkiaValueAnimator.ElapsedMs
  fullName: DrawnUi.Draw.SkiaValueAnimator.ElapsedMs
- uid: DrawnUi.Draw.SkiaValueAnimator.Progress
  commentId: P:DrawnUi.Draw.SkiaValueAnimator.Progress
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Progress
  name: Progress
  nameWithType: SkiaValueAnimator.Progress
  fullName: DrawnUi.Draw.SkiaValueAnimator.Progress
- uid: DrawnUi.Draw.SkiaValueAnimator.OnUpdated
  commentId: P:DrawnUi.Draw.SkiaValueAnimator.OnUpdated
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_OnUpdated
  name: OnUpdated
  nameWithType: SkiaValueAnimator.OnUpdated
  fullName: DrawnUi.Draw.SkiaValueAnimator.OnUpdated
- uid: DrawnUi.Draw.SkiaValueAnimator.ClampOnStart
  commentId: M:DrawnUi.Draw.SkiaValueAnimator.ClampOnStart
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_ClampOnStart
  name: ClampOnStart()
  nameWithType: SkiaValueAnimator.ClampOnStart()
  fullName: DrawnUi.Draw.SkiaValueAnimator.ClampOnStart()
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaValueAnimator.ClampOnStart
    name: ClampOnStart
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_ClampOnStart
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SkiaValueAnimator.ClampOnStart
    name: ClampOnStart
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_ClampOnStart
  - name: (
  - name: )
- uid: DrawnUi.Draw.SkiaValueAnimator.Start(System.Double)
  commentId: M:DrawnUi.Draw.SkiaValueAnimator.Start(System.Double)
  parent: DrawnUi.Draw.SkiaValueAnimator
  isExternal: true
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Start_System_Double_
  name: Start(double)
  nameWithType: SkiaValueAnimator.Start(double)
  fullName: DrawnUi.Draw.SkiaValueAnimator.Start(double)
  nameWithType.vb: SkiaValueAnimator.Start(Double)
  fullName.vb: DrawnUi.Draw.SkiaValueAnimator.Start(Double)
  name.vb: Start(Double)
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaValueAnimator.Start(System.Double)
    name: Start
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Start_System_Double_
  - name: (
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SkiaValueAnimator.Start(System.Double)
    name: Start
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Start_System_Double_
  - name: (
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
- uid: DrawnUi.Draw.SkiaValueAnimator.SetValue(System.Double)
  commentId: M:DrawnUi.Draw.SkiaValueAnimator.SetValue(System.Double)
  parent: DrawnUi.Draw.SkiaValueAnimator
  isExternal: true
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_SetValue_System_Double_
  name: SetValue(double)
  nameWithType: SkiaValueAnimator.SetValue(double)
  fullName: DrawnUi.Draw.SkiaValueAnimator.SetValue(double)
  nameWithType.vb: SkiaValueAnimator.SetValue(Double)
  fullName.vb: DrawnUi.Draw.SkiaValueAnimator.SetValue(Double)
  name.vb: SetValue(Double)
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaValueAnimator.SetValue(System.Double)
    name: SetValue
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_SetValue_System_Double_
  - name: (
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SkiaValueAnimator.SetValue(System.Double)
    name: SetValue
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_SetValue_System_Double_
  - name: (
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
- uid: DrawnUi.Draw.SkiaValueAnimator.SetSpeed(System.Double)
  commentId: M:DrawnUi.Draw.SkiaValueAnimator.SetSpeed(System.Double)
  parent: DrawnUi.Draw.SkiaValueAnimator
  isExternal: true
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_SetSpeed_System_Double_
  name: SetSpeed(double)
  nameWithType: SkiaValueAnimator.SetSpeed(double)
  fullName: DrawnUi.Draw.SkiaValueAnimator.SetSpeed(double)
  nameWithType.vb: SkiaValueAnimator.SetSpeed(Double)
  fullName.vb: DrawnUi.Draw.SkiaValueAnimator.SetSpeed(Double)
  name.vb: SetSpeed(Double)
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaValueAnimator.SetSpeed(System.Double)
    name: SetSpeed
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_SetSpeed_System_Double_
  - name: (
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SkiaValueAnimator.SetSpeed(System.Double)
    name: SetSpeed
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_SetSpeed_System_Double_
  - name: (
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
- uid: DrawnUi.Draw.AnimatorBase.IsPostAnimator
  commentId: P:DrawnUi.Draw.AnimatorBase.IsPostAnimator
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_IsPostAnimator
  name: IsPostAnimator
  nameWithType: AnimatorBase.IsPostAnimator
  fullName: DrawnUi.Draw.AnimatorBase.IsPostAnimator
- uid: DrawnUi.Draw.AnimatorBase.IsHiddenInViewTree
  commentId: P:DrawnUi.Draw.AnimatorBase.IsHiddenInViewTree
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_IsHiddenInViewTree
  name: IsHiddenInViewTree
  nameWithType: AnimatorBase.IsHiddenInViewTree
  fullName: DrawnUi.Draw.AnimatorBase.IsHiddenInViewTree
- uid: DrawnUi.Draw.AnimatorBase.Radians(System.Double)
  commentId: M:DrawnUi.Draw.AnimatorBase.Radians(System.Double)
  parent: DrawnUi.Draw.AnimatorBase
  isExternal: true
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Radians_System_Double_
  name: Radians(double)
  nameWithType: AnimatorBase.Radians(double)
  fullName: DrawnUi.Draw.AnimatorBase.Radians(double)
  nameWithType.vb: AnimatorBase.Radians(Double)
  fullName.vb: DrawnUi.Draw.AnimatorBase.Radians(Double)
  name.vb: Radians(Double)
  spec.csharp:
  - uid: DrawnUi.Draw.AnimatorBase.Radians(System.Double)
    name: Radians
    href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Radians_System_Double_
  - name: (
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.AnimatorBase.Radians(System.Double)
    name: Radians
    href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Radians_System_Double_
  - name: (
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
- uid: DrawnUi.Draw.AnimatorBase.runDelayMs
  commentId: F:DrawnUi.Draw.AnimatorBase.runDelayMs
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_runDelayMs
  name: runDelayMs
  nameWithType: AnimatorBase.runDelayMs
  fullName: DrawnUi.Draw.AnimatorBase.runDelayMs
- uid: DrawnUi.Draw.AnimatorBase.Register
  commentId: M:DrawnUi.Draw.AnimatorBase.Register
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Register
  name: Register()
  nameWithType: AnimatorBase.Register()
  fullName: DrawnUi.Draw.AnimatorBase.Register()
  spec.csharp:
  - uid: DrawnUi.Draw.AnimatorBase.Register
    name: Register
    href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Register
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.AnimatorBase.Register
    name: Register
    href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Register
  - name: (
  - name: )
- uid: DrawnUi.Draw.AnimatorBase.Unregister
  commentId: M:DrawnUi.Draw.AnimatorBase.Unregister
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Unregister
  name: Unregister()
  nameWithType: AnimatorBase.Unregister()
  fullName: DrawnUi.Draw.AnimatorBase.Unregister()
  spec.csharp:
  - uid: DrawnUi.Draw.AnimatorBase.Unregister
    name: Unregister
    href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Unregister
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.AnimatorBase.Unregister
    name: Unregister
    href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Unregister
  - name: (
  - name: )
- uid: DrawnUi.Draw.AnimatorBase.Cancel
  commentId: M:DrawnUi.Draw.AnimatorBase.Cancel
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Cancel
  name: Cancel()
  nameWithType: AnimatorBase.Cancel()
  fullName: DrawnUi.Draw.AnimatorBase.Cancel()
  spec.csharp:
  - uid: DrawnUi.Draw.AnimatorBase.Cancel
    name: Cancel
    href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Cancel
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.AnimatorBase.Cancel
    name: Cancel
    href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Cancel
  - name: (
  - name: )
- uid: DrawnUi.Draw.AnimatorBase.Pause
  commentId: M:DrawnUi.Draw.AnimatorBase.Pause
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Pause
  name: Pause()
  nameWithType: AnimatorBase.Pause()
  fullName: DrawnUi.Draw.AnimatorBase.Pause()
  spec.csharp:
  - uid: DrawnUi.Draw.AnimatorBase.Pause
    name: Pause
    href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Pause
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.AnimatorBase.Pause
    name: Pause
    href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Pause
  - name: (
  - name: )
- uid: DrawnUi.Draw.AnimatorBase.Resume
  commentId: M:DrawnUi.Draw.AnimatorBase.Resume
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Resume
  name: Resume()
  nameWithType: AnimatorBase.Resume()
  fullName: DrawnUi.Draw.AnimatorBase.Resume()
  spec.csharp:
  - uid: DrawnUi.Draw.AnimatorBase.Resume
    name: Resume
    href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Resume
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.AnimatorBase.Resume
    name: Resume
    href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Resume
  - name: (
  - name: )
- uid: DrawnUi.Draw.AnimatorBase.IsPaused
  commentId: P:DrawnUi.Draw.AnimatorBase.IsPaused
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_IsPaused
  name: IsPaused
  nameWithType: AnimatorBase.IsPaused
  fullName: DrawnUi.Draw.AnimatorBase.IsPaused
- uid: DrawnUi.Draw.AnimatorBase.OnStop
  commentId: P:DrawnUi.Draw.AnimatorBase.OnStop
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_OnStop
  name: OnStop
  nameWithType: AnimatorBase.OnStop
  fullName: DrawnUi.Draw.AnimatorBase.OnStop
- uid: DrawnUi.Draw.AnimatorBase.OnStart
  commentId: P:DrawnUi.Draw.AnimatorBase.OnStart
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_OnStart
  name: OnStart
  nameWithType: AnimatorBase.OnStart
  fullName: DrawnUi.Draw.AnimatorBase.OnStart
- uid: DrawnUi.Draw.AnimatorBase.Parent
  commentId: P:DrawnUi.Draw.AnimatorBase.Parent
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Parent
  name: Parent
  nameWithType: AnimatorBase.Parent
  fullName: DrawnUi.Draw.AnimatorBase.Parent
- uid: DrawnUi.Draw.AnimatorBase.IsDeactivated
  commentId: P:DrawnUi.Draw.AnimatorBase.IsDeactivated
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_IsDeactivated
  name: IsDeactivated
  nameWithType: AnimatorBase.IsDeactivated
  fullName: DrawnUi.Draw.AnimatorBase.IsDeactivated
- uid: DrawnUi.Draw.AnimatorBase.LastFrameTimeNanos
  commentId: P:DrawnUi.Draw.AnimatorBase.LastFrameTimeNanos
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_LastFrameTimeNanos
  name: LastFrameTimeNanos
  nameWithType: AnimatorBase.LastFrameTimeNanos
  fullName: DrawnUi.Draw.AnimatorBase.LastFrameTimeNanos
- uid: DrawnUi.Draw.AnimatorBase.StartFrameTimeNanos
  commentId: P:DrawnUi.Draw.AnimatorBase.StartFrameTimeNanos
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_StartFrameTimeNanos
  name: StartFrameTimeNanos
  nameWithType: AnimatorBase.StartFrameTimeNanos
  fullName: DrawnUi.Draw.AnimatorBase.StartFrameTimeNanos
- uid: DrawnUi.Draw.AnimatorBase.Uid
  commentId: P:DrawnUi.Draw.AnimatorBase.Uid
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Uid
  name: Uid
  nameWithType: AnimatorBase.Uid
  fullName: DrawnUi.Draw.AnimatorBase.Uid
- uid: DrawnUi.Draw.AnimatorBase.IsRunning
  commentId: P:DrawnUi.Draw.AnimatorBase.IsRunning
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_IsRunning
  name: IsRunning
  nameWithType: AnimatorBase.IsRunning
  fullName: DrawnUi.Draw.AnimatorBase.IsRunning
- uid: DrawnUi.Draw.AnimatorBase.WasStarted
  commentId: P:DrawnUi.Draw.AnimatorBase.WasStarted
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_WasStarted
  name: WasStarted
  nameWithType: AnimatorBase.WasStarted
  fullName: DrawnUi.Draw.AnimatorBase.WasStarted
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.SpringWithVelocityAnimator.Initialize*
  commentId: Overload:DrawnUi.Draw.SpringWithVelocityAnimator.Initialize
  href: DrawnUi.Draw.SpringWithVelocityAnimator.html#DrawnUi_Draw_SpringWithVelocityAnimator_Initialize_System_Single_System_Single_System_Single_DrawnUi_Infrastructure_Spring_System_Single_
  name: Initialize
  nameWithType: SpringWithVelocityAnimator.Initialize
  fullName: DrawnUi.Draw.SpringWithVelocityAnimator.Initialize
- uid: System.Single
  commentId: T:System.Single
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.single
  name: float
  nameWithType: float
  fullName: float
  nameWithType.vb: Single
  fullName.vb: Single
  name.vb: Single
- uid: DrawnUi.Infrastructure.Spring
  commentId: T:DrawnUi.Infrastructure.Spring
  parent: DrawnUi.Infrastructure
  href: DrawnUi.Infrastructure.Spring.html
  name: Spring
  nameWithType: Spring
  fullName: DrawnUi.Infrastructure.Spring
- uid: DrawnUi.Infrastructure
  commentId: N:DrawnUi.Infrastructure
  href: DrawnUi.html
  name: DrawnUi.Infrastructure
  nameWithType: DrawnUi.Infrastructure
  fullName: DrawnUi.Infrastructure
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Infrastructure
    name: Infrastructure
    href: DrawnUi.Infrastructure.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Infrastructure
    name: Infrastructure
    href: DrawnUi.Infrastructure.html
- uid: DrawnUi.Draw.SpringWithVelocityAnimator.Parameters*
  commentId: Overload:DrawnUi.Draw.SpringWithVelocityAnimator.Parameters
  href: DrawnUi.Draw.SpringWithVelocityAnimator.html#DrawnUi_Draw_SpringWithVelocityAnimator_Parameters
  name: Parameters
  nameWithType: SpringWithVelocityAnimator.Parameters
  fullName: DrawnUi.Draw.SpringWithVelocityAnimator.Parameters
- uid: DrawnUi.Draw.SpringTimingParameters
  commentId: T:DrawnUi.Draw.SpringTimingParameters
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SpringTimingParameters.html
  name: SpringTimingParameters
  nameWithType: SpringTimingParameters
  fullName: DrawnUi.Draw.SpringTimingParameters
- uid: DrawnUi.Draw.SkiaValueAnimator.UpdateValue(System.Int64,System.Int64)
  commentId: M:DrawnUi.Draw.SkiaValueAnimator.UpdateValue(System.Int64,System.Int64)
  parent: DrawnUi.Draw.SkiaValueAnimator
  isExternal: true
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_UpdateValue_System_Int64_System_Int64_
  name: UpdateValue(long, long)
  nameWithType: SkiaValueAnimator.UpdateValue(long, long)
  fullName: DrawnUi.Draw.SkiaValueAnimator.UpdateValue(long, long)
  nameWithType.vb: SkiaValueAnimator.UpdateValue(Long, Long)
  fullName.vb: DrawnUi.Draw.SkiaValueAnimator.UpdateValue(Long, Long)
  name.vb: UpdateValue(Long, Long)
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaValueAnimator.UpdateValue(System.Int64,System.Int64)
    name: UpdateValue
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_UpdateValue_System_Int64_System_Int64_
  - name: (
  - uid: System.Int64
    name: long
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int64
  - name: ','
  - name: " "
  - uid: System.Int64
    name: long
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int64
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SkiaValueAnimator.UpdateValue(System.Int64,System.Int64)
    name: UpdateValue
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_UpdateValue_System_Int64_System_Int64_
  - name: (
  - uid: System.Int64
    name: Long
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int64
  - name: ','
  - name: " "
  - uid: System.Int64
    name: Long
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int64
  - name: )
- uid: DrawnUi.Draw.SpringWithVelocityAnimator.UpdateValue*
  commentId: Overload:DrawnUi.Draw.SpringWithVelocityAnimator.UpdateValue
  href: DrawnUi.Draw.SpringWithVelocityAnimator.html#DrawnUi_Draw_SpringWithVelocityAnimator_UpdateValue_System_Int64_System_Int64_
  name: UpdateValue
  nameWithType: SpringWithVelocityAnimator.UpdateValue
  fullName: DrawnUi.Draw.SpringWithVelocityAnimator.UpdateValue
- uid: System.Int64
  commentId: T:System.Int64
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.int64
  name: long
  nameWithType: long
  fullName: long
  nameWithType.vb: Long
  fullName.vb: Long
  name.vb: Long
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: DrawnUi.Draw.SpringWithVelocityAnimator.#ctor*
  commentId: Overload:DrawnUi.Draw.SpringWithVelocityAnimator.#ctor
  href: DrawnUi.Draw.SpringWithVelocityAnimator.html#DrawnUi_Draw_SpringWithVelocityAnimator__ctor_DrawnUi_Draw_IDrawnBase_
  name: SpringWithVelocityAnimator
  nameWithType: SpringWithVelocityAnimator.SpringWithVelocityAnimator
  fullName: DrawnUi.Draw.SpringWithVelocityAnimator.SpringWithVelocityAnimator
  nameWithType.vb: SpringWithVelocityAnimator.New
  fullName.vb: DrawnUi.Draw.SpringWithVelocityAnimator.New
  name.vb: New
- uid: DrawnUi.Draw.IDrawnBase
  commentId: T:DrawnUi.Draw.IDrawnBase
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.IDrawnBase.html
  name: IDrawnBase
  nameWithType: IDrawnBase
  fullName: DrawnUi.Draw.IDrawnBase
