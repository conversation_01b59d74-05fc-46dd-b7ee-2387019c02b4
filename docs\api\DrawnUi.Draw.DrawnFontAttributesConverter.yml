### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.DrawnFontAttributesConverter
  commentId: T:DrawnUi.Draw.DrawnFontAttributesConverter
  id: DrawnFontAttributesConverter
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.DrawnFontAttributesConverter.CanConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Type)
  - DrawnUi.Draw.DrawnFontAttributesConverter.CanConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Type)
  - DrawnUi.Draw.DrawnFontAttributesConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)
  - DrawnUi.Draw.DrawnFontAttributesConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)
  langs:
  - csharp
  - vb
  name: DrawnFontAttributesConverter
  nameWithType: DrawnFontAttributesConverter
  fullName: DrawnUi.Draw.DrawnFontAttributesConverter
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Xaml/DrawnFontAttributesConverter.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: DrawnFontAttributesConverter
    path: ../src/Maui/DrawnUi/Internals/Xaml/DrawnFontAttributesConverter.cs
    startLine: 8
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Forked from Microsoft.Maui.Controls as using original class was breaking XAML HotReload for some unknown reason
  example: []
  syntax:
    content: 'public class DrawnFontAttributesConverter : TypeConverter'
    content.vb: Public Class DrawnFontAttributesConverter Inherits TypeConverter
  inheritance:
  - System.Object
  - System.ComponentModel.TypeConverter
  inheritedMembers:
  - System.ComponentModel.TypeConverter.CanConvertFrom(System.Type)
  - System.ComponentModel.TypeConverter.CanConvertTo(System.Type)
  - System.ComponentModel.TypeConverter.ConvertFrom(System.Object)
  - System.ComponentModel.TypeConverter.ConvertFromInvariantString(System.ComponentModel.ITypeDescriptorContext,System.String)
  - System.ComponentModel.TypeConverter.ConvertFromInvariantString(System.String)
  - System.ComponentModel.TypeConverter.ConvertFromString(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.String)
  - System.ComponentModel.TypeConverter.ConvertFromString(System.ComponentModel.ITypeDescriptorContext,System.String)
  - System.ComponentModel.TypeConverter.ConvertFromString(System.String)
  - System.ComponentModel.TypeConverter.ConvertTo(System.Object,System.Type)
  - System.ComponentModel.TypeConverter.ConvertToInvariantString(System.ComponentModel.ITypeDescriptorContext,System.Object)
  - System.ComponentModel.TypeConverter.ConvertToInvariantString(System.Object)
  - System.ComponentModel.TypeConverter.ConvertToString(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)
  - System.ComponentModel.TypeConverter.ConvertToString(System.ComponentModel.ITypeDescriptorContext,System.Object)
  - System.ComponentModel.TypeConverter.ConvertToString(System.Object)
  - System.ComponentModel.TypeConverter.CreateInstance(System.Collections.IDictionary)
  - System.ComponentModel.TypeConverter.CreateInstance(System.ComponentModel.ITypeDescriptorContext,System.Collections.IDictionary)
  - System.ComponentModel.TypeConverter.GetConvertFromException(System.Object)
  - System.ComponentModel.TypeConverter.GetConvertToException(System.Object,System.Type)
  - System.ComponentModel.TypeConverter.GetCreateInstanceSupported
  - System.ComponentModel.TypeConverter.GetCreateInstanceSupported(System.ComponentModel.ITypeDescriptorContext)
  - System.ComponentModel.TypeConverter.GetProperties(System.ComponentModel.ITypeDescriptorContext,System.Object)
  - System.ComponentModel.TypeConverter.GetProperties(System.ComponentModel.ITypeDescriptorContext,System.Object,System.Attribute[])
  - System.ComponentModel.TypeConverter.GetProperties(System.Object)
  - System.ComponentModel.TypeConverter.GetPropertiesSupported
  - System.ComponentModel.TypeConverter.GetPropertiesSupported(System.ComponentModel.ITypeDescriptorContext)
  - System.ComponentModel.TypeConverter.GetStandardValues
  - System.ComponentModel.TypeConverter.GetStandardValues(System.ComponentModel.ITypeDescriptorContext)
  - System.ComponentModel.TypeConverter.GetStandardValuesExclusive
  - System.ComponentModel.TypeConverter.GetStandardValuesExclusive(System.ComponentModel.ITypeDescriptorContext)
  - System.ComponentModel.TypeConverter.GetStandardValuesSupported
  - System.ComponentModel.TypeConverter.GetStandardValuesSupported(System.ComponentModel.ITypeDescriptorContext)
  - System.ComponentModel.TypeConverter.IsValid(System.ComponentModel.ITypeDescriptorContext,System.Object)
  - System.ComponentModel.TypeConverter.IsValid(System.Object)
  - System.ComponentModel.TypeConverter.SortProperties(System.ComponentModel.PropertyDescriptorCollection,System.String[])
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.DrawnFontAttributesConverter.CanConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Type)
  commentId: M:DrawnUi.Draw.DrawnFontAttributesConverter.CanConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Type)
  id: CanConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Type)
  parent: DrawnUi.Draw.DrawnFontAttributesConverter
  langs:
  - csharp
  - vb
  name: CanConvertFrom(ITypeDescriptorContext, Type)
  nameWithType: DrawnFontAttributesConverter.CanConvertFrom(ITypeDescriptorContext, Type)
  fullName: DrawnUi.Draw.DrawnFontAttributesConverter.CanConvertFrom(System.ComponentModel.ITypeDescriptorContext, System.Type)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Xaml/DrawnFontAttributesConverter.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: CanConvertFrom
    path: ../src/Maui/DrawnUi/Internals/Xaml/DrawnFontAttributesConverter.cs
    startLine: 10
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Returns whether this converter can convert an object of the given type to the type of this converter, using the specified context.
  example: []
  syntax:
    content: public override bool CanConvertFrom(ITypeDescriptorContext context, Type sourceType)
    parameters:
    - id: context
      type: System.ComponentModel.ITypeDescriptorContext
      description: An <xref href="System.ComponentModel.ITypeDescriptorContext" data-throw-if-not-resolved="false"></xref> that provides a format context.
    - id: sourceType
      type: System.Type
      description: A <xref href="System.Type" data-throw-if-not-resolved="false"></xref> that represents the type you want to convert from.
    return:
      type: System.Boolean
      description: <a href="https://learn.microsoft.com/dotnet/csharp/language-reference/builtin-types/bool">true</a> if this converter can perform the conversion; otherwise, <a href="https://learn.microsoft.com/dotnet/csharp/language-reference/builtin-types/bool">false</a>.
    content.vb: Public Overrides Function CanConvertFrom(context As ITypeDescriptorContext, sourceType As Type) As Boolean
  overridden: System.ComponentModel.TypeConverter.CanConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Type)
  overload: DrawnUi.Draw.DrawnFontAttributesConverter.CanConvertFrom*
- uid: DrawnUi.Draw.DrawnFontAttributesConverter.CanConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Type)
  commentId: M:DrawnUi.Draw.DrawnFontAttributesConverter.CanConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Type)
  id: CanConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Type)
  parent: DrawnUi.Draw.DrawnFontAttributesConverter
  langs:
  - csharp
  - vb
  name: CanConvertTo(ITypeDescriptorContext, Type)
  nameWithType: DrawnFontAttributesConverter.CanConvertTo(ITypeDescriptorContext, Type)
  fullName: DrawnUi.Draw.DrawnFontAttributesConverter.CanConvertTo(System.ComponentModel.ITypeDescriptorContext, System.Type)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Xaml/DrawnFontAttributesConverter.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: CanConvertTo
    path: ../src/Maui/DrawnUi/Internals/Xaml/DrawnFontAttributesConverter.cs
    startLine: 13
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Returns whether this converter can convert the object to the specified type, using the specified context.
  example: []
  syntax:
    content: public override bool CanConvertTo(ITypeDescriptorContext context, Type destinationType)
    parameters:
    - id: context
      type: System.ComponentModel.ITypeDescriptorContext
      description: An <xref href="System.ComponentModel.ITypeDescriptorContext" data-throw-if-not-resolved="false"></xref> that provides a format context.
    - id: destinationType
      type: System.Type
      description: A <xref href="System.Type" data-throw-if-not-resolved="false"></xref> that represents the type you want to convert to.
    return:
      type: System.Boolean
      description: <a href="https://learn.microsoft.com/dotnet/csharp/language-reference/builtin-types/bool">true</a> if this converter can perform the conversion; otherwise, <a href="https://learn.microsoft.com/dotnet/csharp/language-reference/builtin-types/bool">false</a>.
    content.vb: Public Overrides Function CanConvertTo(context As ITypeDescriptorContext, destinationType As Type) As Boolean
  overridden: System.ComponentModel.TypeConverter.CanConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Type)
  overload: DrawnUi.Draw.DrawnFontAttributesConverter.CanConvertTo*
- uid: DrawnUi.Draw.DrawnFontAttributesConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)
  commentId: M:DrawnUi.Draw.DrawnFontAttributesConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)
  id: ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)
  parent: DrawnUi.Draw.DrawnFontAttributesConverter
  langs:
  - csharp
  - vb
  name: ConvertFrom(ITypeDescriptorContext, CultureInfo, object)
  nameWithType: DrawnFontAttributesConverter.ConvertFrom(ITypeDescriptorContext, CultureInfo, object)
  fullName: DrawnUi.Draw.DrawnFontAttributesConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext, System.Globalization.CultureInfo, object)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Xaml/DrawnFontAttributesConverter.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ConvertFrom
    path: ../src/Maui/DrawnUi/Internals/Xaml/DrawnFontAttributesConverter.cs
    startLine: 16
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Converts the given object to the type of this converter, using the specified context and culture information.
  example: []
  syntax:
    content: public override object ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, object value)
    parameters:
    - id: context
      type: System.ComponentModel.ITypeDescriptorContext
      description: An <xref href="System.ComponentModel.ITypeDescriptorContext" data-throw-if-not-resolved="false"></xref> that provides a format context.
    - id: culture
      type: System.Globalization.CultureInfo
      description: The <xref href="System.Globalization.CultureInfo" data-throw-if-not-resolved="false"></xref> to use as the current culture.
    - id: value
      type: System.Object
      description: The <xref href="System.Object" data-throw-if-not-resolved="false"></xref> to convert.
    return:
      type: System.Object
      description: An <xref href="System.Object" data-throw-if-not-resolved="false"></xref> that represents the converted value.
    content.vb: Public Overrides Function ConvertFrom(context As ITypeDescriptorContext, culture As CultureInfo, value As Object) As Object
  overridden: System.ComponentModel.TypeConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)
  overload: DrawnUi.Draw.DrawnFontAttributesConverter.ConvertFrom*
  exceptions:
  - type: System.NotSupportedException
    commentId: T:System.NotSupportedException
    description: The conversion cannot be performed.
  nameWithType.vb: DrawnFontAttributesConverter.ConvertFrom(ITypeDescriptorContext, CultureInfo, Object)
  fullName.vb: DrawnUi.Draw.DrawnFontAttributesConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext, System.Globalization.CultureInfo, Object)
  name.vb: ConvertFrom(ITypeDescriptorContext, CultureInfo, Object)
- uid: DrawnUi.Draw.DrawnFontAttributesConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)
  commentId: M:DrawnUi.Draw.DrawnFontAttributesConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)
  id: ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)
  parent: DrawnUi.Draw.DrawnFontAttributesConverter
  langs:
  - csharp
  - vb
  name: ConvertTo(ITypeDescriptorContext, CultureInfo, object, Type)
  nameWithType: DrawnFontAttributesConverter.ConvertTo(ITypeDescriptorContext, CultureInfo, object, Type)
  fullName: DrawnUi.Draw.DrawnFontAttributesConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext, System.Globalization.CultureInfo, object, System.Type)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Xaml/DrawnFontAttributesConverter.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ConvertTo
    path: ../src/Maui/DrawnUi/Internals/Xaml/DrawnFontAttributesConverter.cs
    startLine: 52
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Converts the given value object to the specified type, using the specified context and culture information.
  example: []
  syntax:
    content: public override object ConvertTo(ITypeDescriptorContext context, CultureInfo culture, object value, Type destinationType)
    parameters:
    - id: context
      type: System.ComponentModel.ITypeDescriptorContext
      description: An <xref href="System.ComponentModel.ITypeDescriptorContext" data-throw-if-not-resolved="false"></xref> that provides a format context.
    - id: culture
      type: System.Globalization.CultureInfo
      description: A <xref href="System.Globalization.CultureInfo" data-throw-if-not-resolved="false"></xref>. If <a href="https://learn.microsoft.com/dotnet/csharp/language-reference/keywords/null">null</a> is passed, the current culture is assumed.
    - id: value
      type: System.Object
      description: The <xref href="System.Object" data-throw-if-not-resolved="false"></xref> to convert.
    - id: destinationType
      type: System.Type
      description: The <xref href="System.Type" data-throw-if-not-resolved="false"></xref> to convert the <code class="paramref">value</code> parameter to.
    return:
      type: System.Object
      description: An <xref href="System.Object" data-throw-if-not-resolved="false"></xref> that represents the converted value.
    content.vb: Public Overrides Function ConvertTo(context As ITypeDescriptorContext, culture As CultureInfo, value As Object, destinationType As Type) As Object
  overridden: System.ComponentModel.TypeConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)
  overload: DrawnUi.Draw.DrawnFontAttributesConverter.ConvertTo*
  exceptions:
  - type: System.ArgumentNullException
    commentId: T:System.ArgumentNullException
    description: The <code class="paramref">destinationType</code> parameter is <a href="https://learn.microsoft.com/dotnet/csharp/language-reference/keywords/null">null</a>.
  - type: System.NotSupportedException
    commentId: T:System.NotSupportedException
    description: The conversion cannot be performed.
  nameWithType.vb: DrawnFontAttributesConverter.ConvertTo(ITypeDescriptorContext, CultureInfo, Object, Type)
  fullName.vb: DrawnUi.Draw.DrawnFontAttributesConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext, System.Globalization.CultureInfo, Object, System.Type)
  name.vb: ConvertTo(ITypeDescriptorContext, CultureInfo, Object, Type)
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.ComponentModel.TypeConverter
  commentId: T:System.ComponentModel.TypeConverter
  parent: System.ComponentModel
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter
  name: TypeConverter
  nameWithType: TypeConverter
  fullName: System.ComponentModel.TypeConverter
- uid: System.ComponentModel.TypeConverter.CanConvertFrom(System.Type)
  commentId: M:System.ComponentModel.TypeConverter.CanConvertFrom(System.Type)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.canconvertfrom#system-componentmodel-typeconverter-canconvertfrom(system-type)
  name: CanConvertFrom(Type)
  nameWithType: TypeConverter.CanConvertFrom(Type)
  fullName: System.ComponentModel.TypeConverter.CanConvertFrom(System.Type)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.CanConvertFrom(System.Type)
    name: CanConvertFrom
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.canconvertfrom#system-componentmodel-typeconverter-canconvertfrom(system-type)
  - name: (
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.CanConvertFrom(System.Type)
    name: CanConvertFrom
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.canconvertfrom#system-componentmodel-typeconverter-canconvertfrom(system-type)
  - name: (
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
- uid: System.ComponentModel.TypeConverter.CanConvertTo(System.Type)
  commentId: M:System.ComponentModel.TypeConverter.CanConvertTo(System.Type)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.canconvertto#system-componentmodel-typeconverter-canconvertto(system-type)
  name: CanConvertTo(Type)
  nameWithType: TypeConverter.CanConvertTo(Type)
  fullName: System.ComponentModel.TypeConverter.CanConvertTo(System.Type)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.CanConvertTo(System.Type)
    name: CanConvertTo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.canconvertto#system-componentmodel-typeconverter-canconvertto(system-type)
  - name: (
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.CanConvertTo(System.Type)
    name: CanConvertTo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.canconvertto#system-componentmodel-typeconverter-canconvertto(system-type)
  - name: (
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
- uid: System.ComponentModel.TypeConverter.ConvertFrom(System.Object)
  commentId: M:System.ComponentModel.TypeConverter.ConvertFrom(System.Object)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertfrom#system-componentmodel-typeconverter-convertfrom(system-object)
  name: ConvertFrom(object)
  nameWithType: TypeConverter.ConvertFrom(object)
  fullName: System.ComponentModel.TypeConverter.ConvertFrom(object)
  nameWithType.vb: TypeConverter.ConvertFrom(Object)
  fullName.vb: System.ComponentModel.TypeConverter.ConvertFrom(Object)
  name.vb: ConvertFrom(Object)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.ConvertFrom(System.Object)
    name: ConvertFrom
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertfrom#system-componentmodel-typeconverter-convertfrom(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.ConvertFrom(System.Object)
    name: ConvertFrom
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertfrom#system-componentmodel-typeconverter-convertfrom(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.ComponentModel.TypeConverter.ConvertFromInvariantString(System.ComponentModel.ITypeDescriptorContext,System.String)
  commentId: M:System.ComponentModel.TypeConverter.ConvertFromInvariantString(System.ComponentModel.ITypeDescriptorContext,System.String)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertfrominvariantstring#system-componentmodel-typeconverter-convertfrominvariantstring(system-componentmodel-itypedescriptorcontext-system-string)
  name: ConvertFromInvariantString(ITypeDescriptorContext, string)
  nameWithType: TypeConverter.ConvertFromInvariantString(ITypeDescriptorContext, string)
  fullName: System.ComponentModel.TypeConverter.ConvertFromInvariantString(System.ComponentModel.ITypeDescriptorContext, string)
  nameWithType.vb: TypeConverter.ConvertFromInvariantString(ITypeDescriptorContext, String)
  fullName.vb: System.ComponentModel.TypeConverter.ConvertFromInvariantString(System.ComponentModel.ITypeDescriptorContext, String)
  name.vb: ConvertFromInvariantString(ITypeDescriptorContext, String)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.ConvertFromInvariantString(System.ComponentModel.ITypeDescriptorContext,System.String)
    name: ConvertFromInvariantString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertfrominvariantstring#system-componentmodel-typeconverter-convertfrominvariantstring(system-componentmodel-itypedescriptorcontext-system-string)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.ConvertFromInvariantString(System.ComponentModel.ITypeDescriptorContext,System.String)
    name: ConvertFromInvariantString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertfrominvariantstring#system-componentmodel-typeconverter-convertfrominvariantstring(system-componentmodel-itypedescriptorcontext-system-string)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: System.ComponentModel.TypeConverter.ConvertFromInvariantString(System.String)
  commentId: M:System.ComponentModel.TypeConverter.ConvertFromInvariantString(System.String)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertfrominvariantstring#system-componentmodel-typeconverter-convertfrominvariantstring(system-string)
  name: ConvertFromInvariantString(string)
  nameWithType: TypeConverter.ConvertFromInvariantString(string)
  fullName: System.ComponentModel.TypeConverter.ConvertFromInvariantString(string)
  nameWithType.vb: TypeConverter.ConvertFromInvariantString(String)
  fullName.vb: System.ComponentModel.TypeConverter.ConvertFromInvariantString(String)
  name.vb: ConvertFromInvariantString(String)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.ConvertFromInvariantString(System.String)
    name: ConvertFromInvariantString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertfrominvariantstring#system-componentmodel-typeconverter-convertfrominvariantstring(system-string)
  - name: (
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.ConvertFromInvariantString(System.String)
    name: ConvertFromInvariantString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertfrominvariantstring#system-componentmodel-typeconverter-convertfrominvariantstring(system-string)
  - name: (
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: System.ComponentModel.TypeConverter.ConvertFromString(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.String)
  commentId: M:System.ComponentModel.TypeConverter.ConvertFromString(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.String)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertfromstring#system-componentmodel-typeconverter-convertfromstring(system-componentmodel-itypedescriptorcontext-system-globalization-cultureinfo-system-string)
  name: ConvertFromString(ITypeDescriptorContext, CultureInfo, string)
  nameWithType: TypeConverter.ConvertFromString(ITypeDescriptorContext, CultureInfo, string)
  fullName: System.ComponentModel.TypeConverter.ConvertFromString(System.ComponentModel.ITypeDescriptorContext, System.Globalization.CultureInfo, string)
  nameWithType.vb: TypeConverter.ConvertFromString(ITypeDescriptorContext, CultureInfo, String)
  fullName.vb: System.ComponentModel.TypeConverter.ConvertFromString(System.ComponentModel.ITypeDescriptorContext, System.Globalization.CultureInfo, String)
  name.vb: ConvertFromString(ITypeDescriptorContext, CultureInfo, String)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.ConvertFromString(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.String)
    name: ConvertFromString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertfromstring#system-componentmodel-typeconverter-convertfromstring(system-componentmodel-itypedescriptorcontext-system-globalization-cultureinfo-system-string)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.Globalization.CultureInfo
    name: CultureInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.globalization.cultureinfo
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.ConvertFromString(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.String)
    name: ConvertFromString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertfromstring#system-componentmodel-typeconverter-convertfromstring(system-componentmodel-itypedescriptorcontext-system-globalization-cultureinfo-system-string)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.Globalization.CultureInfo
    name: CultureInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.globalization.cultureinfo
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: System.ComponentModel.TypeConverter.ConvertFromString(System.ComponentModel.ITypeDescriptorContext,System.String)
  commentId: M:System.ComponentModel.TypeConverter.ConvertFromString(System.ComponentModel.ITypeDescriptorContext,System.String)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertfromstring#system-componentmodel-typeconverter-convertfromstring(system-componentmodel-itypedescriptorcontext-system-string)
  name: ConvertFromString(ITypeDescriptorContext, string)
  nameWithType: TypeConverter.ConvertFromString(ITypeDescriptorContext, string)
  fullName: System.ComponentModel.TypeConverter.ConvertFromString(System.ComponentModel.ITypeDescriptorContext, string)
  nameWithType.vb: TypeConverter.ConvertFromString(ITypeDescriptorContext, String)
  fullName.vb: System.ComponentModel.TypeConverter.ConvertFromString(System.ComponentModel.ITypeDescriptorContext, String)
  name.vb: ConvertFromString(ITypeDescriptorContext, String)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.ConvertFromString(System.ComponentModel.ITypeDescriptorContext,System.String)
    name: ConvertFromString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertfromstring#system-componentmodel-typeconverter-convertfromstring(system-componentmodel-itypedescriptorcontext-system-string)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.ConvertFromString(System.ComponentModel.ITypeDescriptorContext,System.String)
    name: ConvertFromString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertfromstring#system-componentmodel-typeconverter-convertfromstring(system-componentmodel-itypedescriptorcontext-system-string)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: System.ComponentModel.TypeConverter.ConvertFromString(System.String)
  commentId: M:System.ComponentModel.TypeConverter.ConvertFromString(System.String)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertfromstring#system-componentmodel-typeconverter-convertfromstring(system-string)
  name: ConvertFromString(string)
  nameWithType: TypeConverter.ConvertFromString(string)
  fullName: System.ComponentModel.TypeConverter.ConvertFromString(string)
  nameWithType.vb: TypeConverter.ConvertFromString(String)
  fullName.vb: System.ComponentModel.TypeConverter.ConvertFromString(String)
  name.vb: ConvertFromString(String)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.ConvertFromString(System.String)
    name: ConvertFromString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertfromstring#system-componentmodel-typeconverter-convertfromstring(system-string)
  - name: (
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.ConvertFromString(System.String)
    name: ConvertFromString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertfromstring#system-componentmodel-typeconverter-convertfromstring(system-string)
  - name: (
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: System.ComponentModel.TypeConverter.ConvertTo(System.Object,System.Type)
  commentId: M:System.ComponentModel.TypeConverter.ConvertTo(System.Object,System.Type)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertto#system-componentmodel-typeconverter-convertto(system-object-system-type)
  name: ConvertTo(object, Type)
  nameWithType: TypeConverter.ConvertTo(object, Type)
  fullName: System.ComponentModel.TypeConverter.ConvertTo(object, System.Type)
  nameWithType.vb: TypeConverter.ConvertTo(Object, Type)
  fullName.vb: System.ComponentModel.TypeConverter.ConvertTo(Object, System.Type)
  name.vb: ConvertTo(Object, Type)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.ConvertTo(System.Object,System.Type)
    name: ConvertTo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertto#system-componentmodel-typeconverter-convertto(system-object-system-type)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.ConvertTo(System.Object,System.Type)
    name: ConvertTo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertto#system-componentmodel-typeconverter-convertto(system-object-system-type)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
- uid: System.ComponentModel.TypeConverter.ConvertToInvariantString(System.ComponentModel.ITypeDescriptorContext,System.Object)
  commentId: M:System.ComponentModel.TypeConverter.ConvertToInvariantString(System.ComponentModel.ITypeDescriptorContext,System.Object)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.converttoinvariantstring#system-componentmodel-typeconverter-converttoinvariantstring(system-componentmodel-itypedescriptorcontext-system-object)
  name: ConvertToInvariantString(ITypeDescriptorContext, object)
  nameWithType: TypeConverter.ConvertToInvariantString(ITypeDescriptorContext, object)
  fullName: System.ComponentModel.TypeConverter.ConvertToInvariantString(System.ComponentModel.ITypeDescriptorContext, object)
  nameWithType.vb: TypeConverter.ConvertToInvariantString(ITypeDescriptorContext, Object)
  fullName.vb: System.ComponentModel.TypeConverter.ConvertToInvariantString(System.ComponentModel.ITypeDescriptorContext, Object)
  name.vb: ConvertToInvariantString(ITypeDescriptorContext, Object)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.ConvertToInvariantString(System.ComponentModel.ITypeDescriptorContext,System.Object)
    name: ConvertToInvariantString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.converttoinvariantstring#system-componentmodel-typeconverter-converttoinvariantstring(system-componentmodel-itypedescriptorcontext-system-object)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.ConvertToInvariantString(System.ComponentModel.ITypeDescriptorContext,System.Object)
    name: ConvertToInvariantString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.converttoinvariantstring#system-componentmodel-typeconverter-converttoinvariantstring(system-componentmodel-itypedescriptorcontext-system-object)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.ComponentModel.TypeConverter.ConvertToInvariantString(System.Object)
  commentId: M:System.ComponentModel.TypeConverter.ConvertToInvariantString(System.Object)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.converttoinvariantstring#system-componentmodel-typeconverter-converttoinvariantstring(system-object)
  name: ConvertToInvariantString(object)
  nameWithType: TypeConverter.ConvertToInvariantString(object)
  fullName: System.ComponentModel.TypeConverter.ConvertToInvariantString(object)
  nameWithType.vb: TypeConverter.ConvertToInvariantString(Object)
  fullName.vb: System.ComponentModel.TypeConverter.ConvertToInvariantString(Object)
  name.vb: ConvertToInvariantString(Object)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.ConvertToInvariantString(System.Object)
    name: ConvertToInvariantString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.converttoinvariantstring#system-componentmodel-typeconverter-converttoinvariantstring(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.ConvertToInvariantString(System.Object)
    name: ConvertToInvariantString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.converttoinvariantstring#system-componentmodel-typeconverter-converttoinvariantstring(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.ComponentModel.TypeConverter.ConvertToString(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)
  commentId: M:System.ComponentModel.TypeConverter.ConvertToString(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.converttostring#system-componentmodel-typeconverter-converttostring(system-componentmodel-itypedescriptorcontext-system-globalization-cultureinfo-system-object)
  name: ConvertToString(ITypeDescriptorContext, CultureInfo, object)
  nameWithType: TypeConverter.ConvertToString(ITypeDescriptorContext, CultureInfo, object)
  fullName: System.ComponentModel.TypeConverter.ConvertToString(System.ComponentModel.ITypeDescriptorContext, System.Globalization.CultureInfo, object)
  nameWithType.vb: TypeConverter.ConvertToString(ITypeDescriptorContext, CultureInfo, Object)
  fullName.vb: System.ComponentModel.TypeConverter.ConvertToString(System.ComponentModel.ITypeDescriptorContext, System.Globalization.CultureInfo, Object)
  name.vb: ConvertToString(ITypeDescriptorContext, CultureInfo, Object)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.ConvertToString(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)
    name: ConvertToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.converttostring#system-componentmodel-typeconverter-converttostring(system-componentmodel-itypedescriptorcontext-system-globalization-cultureinfo-system-object)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.Globalization.CultureInfo
    name: CultureInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.globalization.cultureinfo
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.ConvertToString(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)
    name: ConvertToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.converttostring#system-componentmodel-typeconverter-converttostring(system-componentmodel-itypedescriptorcontext-system-globalization-cultureinfo-system-object)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.Globalization.CultureInfo
    name: CultureInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.globalization.cultureinfo
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.ComponentModel.TypeConverter.ConvertToString(System.ComponentModel.ITypeDescriptorContext,System.Object)
  commentId: M:System.ComponentModel.TypeConverter.ConvertToString(System.ComponentModel.ITypeDescriptorContext,System.Object)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.converttostring#system-componentmodel-typeconverter-converttostring(system-componentmodel-itypedescriptorcontext-system-object)
  name: ConvertToString(ITypeDescriptorContext, object)
  nameWithType: TypeConverter.ConvertToString(ITypeDescriptorContext, object)
  fullName: System.ComponentModel.TypeConverter.ConvertToString(System.ComponentModel.ITypeDescriptorContext, object)
  nameWithType.vb: TypeConverter.ConvertToString(ITypeDescriptorContext, Object)
  fullName.vb: System.ComponentModel.TypeConverter.ConvertToString(System.ComponentModel.ITypeDescriptorContext, Object)
  name.vb: ConvertToString(ITypeDescriptorContext, Object)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.ConvertToString(System.ComponentModel.ITypeDescriptorContext,System.Object)
    name: ConvertToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.converttostring#system-componentmodel-typeconverter-converttostring(system-componentmodel-itypedescriptorcontext-system-object)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.ConvertToString(System.ComponentModel.ITypeDescriptorContext,System.Object)
    name: ConvertToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.converttostring#system-componentmodel-typeconverter-converttostring(system-componentmodel-itypedescriptorcontext-system-object)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.ComponentModel.TypeConverter.ConvertToString(System.Object)
  commentId: M:System.ComponentModel.TypeConverter.ConvertToString(System.Object)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.converttostring#system-componentmodel-typeconverter-converttostring(system-object)
  name: ConvertToString(object)
  nameWithType: TypeConverter.ConvertToString(object)
  fullName: System.ComponentModel.TypeConverter.ConvertToString(object)
  nameWithType.vb: TypeConverter.ConvertToString(Object)
  fullName.vb: System.ComponentModel.TypeConverter.ConvertToString(Object)
  name.vb: ConvertToString(Object)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.ConvertToString(System.Object)
    name: ConvertToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.converttostring#system-componentmodel-typeconverter-converttostring(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.ConvertToString(System.Object)
    name: ConvertToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.converttostring#system-componentmodel-typeconverter-converttostring(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.ComponentModel.TypeConverter.CreateInstance(System.Collections.IDictionary)
  commentId: M:System.ComponentModel.TypeConverter.CreateInstance(System.Collections.IDictionary)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.createinstance#system-componentmodel-typeconverter-createinstance(system-collections-idictionary)
  name: CreateInstance(IDictionary)
  nameWithType: TypeConverter.CreateInstance(IDictionary)
  fullName: System.ComponentModel.TypeConverter.CreateInstance(System.Collections.IDictionary)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.CreateInstance(System.Collections.IDictionary)
    name: CreateInstance
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.createinstance#system-componentmodel-typeconverter-createinstance(system-collections-idictionary)
  - name: (
  - uid: System.Collections.IDictionary
    name: IDictionary
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.idictionary
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.CreateInstance(System.Collections.IDictionary)
    name: CreateInstance
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.createinstance#system-componentmodel-typeconverter-createinstance(system-collections-idictionary)
  - name: (
  - uid: System.Collections.IDictionary
    name: IDictionary
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.idictionary
  - name: )
- uid: System.ComponentModel.TypeConverter.CreateInstance(System.ComponentModel.ITypeDescriptorContext,System.Collections.IDictionary)
  commentId: M:System.ComponentModel.TypeConverter.CreateInstance(System.ComponentModel.ITypeDescriptorContext,System.Collections.IDictionary)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.createinstance#system-componentmodel-typeconverter-createinstance(system-componentmodel-itypedescriptorcontext-system-collections-idictionary)
  name: CreateInstance(ITypeDescriptorContext, IDictionary)
  nameWithType: TypeConverter.CreateInstance(ITypeDescriptorContext, IDictionary)
  fullName: System.ComponentModel.TypeConverter.CreateInstance(System.ComponentModel.ITypeDescriptorContext, System.Collections.IDictionary)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.CreateInstance(System.ComponentModel.ITypeDescriptorContext,System.Collections.IDictionary)
    name: CreateInstance
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.createinstance#system-componentmodel-typeconverter-createinstance(system-componentmodel-itypedescriptorcontext-system-collections-idictionary)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.Collections.IDictionary
    name: IDictionary
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.idictionary
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.CreateInstance(System.ComponentModel.ITypeDescriptorContext,System.Collections.IDictionary)
    name: CreateInstance
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.createinstance#system-componentmodel-typeconverter-createinstance(system-componentmodel-itypedescriptorcontext-system-collections-idictionary)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.Collections.IDictionary
    name: IDictionary
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.idictionary
  - name: )
- uid: System.ComponentModel.TypeConverter.GetConvertFromException(System.Object)
  commentId: M:System.ComponentModel.TypeConverter.GetConvertFromException(System.Object)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getconvertfromexception
  name: GetConvertFromException(object)
  nameWithType: TypeConverter.GetConvertFromException(object)
  fullName: System.ComponentModel.TypeConverter.GetConvertFromException(object)
  nameWithType.vb: TypeConverter.GetConvertFromException(Object)
  fullName.vb: System.ComponentModel.TypeConverter.GetConvertFromException(Object)
  name.vb: GetConvertFromException(Object)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.GetConvertFromException(System.Object)
    name: GetConvertFromException
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getconvertfromexception
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.GetConvertFromException(System.Object)
    name: GetConvertFromException
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getconvertfromexception
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.ComponentModel.TypeConverter.GetConvertToException(System.Object,System.Type)
  commentId: M:System.ComponentModel.TypeConverter.GetConvertToException(System.Object,System.Type)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getconverttoexception
  name: GetConvertToException(object, Type)
  nameWithType: TypeConverter.GetConvertToException(object, Type)
  fullName: System.ComponentModel.TypeConverter.GetConvertToException(object, System.Type)
  nameWithType.vb: TypeConverter.GetConvertToException(Object, Type)
  fullName.vb: System.ComponentModel.TypeConverter.GetConvertToException(Object, System.Type)
  name.vb: GetConvertToException(Object, Type)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.GetConvertToException(System.Object,System.Type)
    name: GetConvertToException
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getconverttoexception
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.GetConvertToException(System.Object,System.Type)
    name: GetConvertToException
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getconverttoexception
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
- uid: System.ComponentModel.TypeConverter.GetCreateInstanceSupported
  commentId: M:System.ComponentModel.TypeConverter.GetCreateInstanceSupported
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getcreateinstancesupported#system-componentmodel-typeconverter-getcreateinstancesupported
  name: GetCreateInstanceSupported()
  nameWithType: TypeConverter.GetCreateInstanceSupported()
  fullName: System.ComponentModel.TypeConverter.GetCreateInstanceSupported()
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.GetCreateInstanceSupported
    name: GetCreateInstanceSupported
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getcreateinstancesupported#system-componentmodel-typeconverter-getcreateinstancesupported
  - name: (
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.GetCreateInstanceSupported
    name: GetCreateInstanceSupported
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getcreateinstancesupported#system-componentmodel-typeconverter-getcreateinstancesupported
  - name: (
  - name: )
- uid: System.ComponentModel.TypeConverter.GetCreateInstanceSupported(System.ComponentModel.ITypeDescriptorContext)
  commentId: M:System.ComponentModel.TypeConverter.GetCreateInstanceSupported(System.ComponentModel.ITypeDescriptorContext)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getcreateinstancesupported#system-componentmodel-typeconverter-getcreateinstancesupported(system-componentmodel-itypedescriptorcontext)
  name: GetCreateInstanceSupported(ITypeDescriptorContext)
  nameWithType: TypeConverter.GetCreateInstanceSupported(ITypeDescriptorContext)
  fullName: System.ComponentModel.TypeConverter.GetCreateInstanceSupported(System.ComponentModel.ITypeDescriptorContext)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.GetCreateInstanceSupported(System.ComponentModel.ITypeDescriptorContext)
    name: GetCreateInstanceSupported
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getcreateinstancesupported#system-componentmodel-typeconverter-getcreateinstancesupported(system-componentmodel-itypedescriptorcontext)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.GetCreateInstanceSupported(System.ComponentModel.ITypeDescriptorContext)
    name: GetCreateInstanceSupported
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getcreateinstancesupported#system-componentmodel-typeconverter-getcreateinstancesupported(system-componentmodel-itypedescriptorcontext)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: )
- uid: System.ComponentModel.TypeConverter.GetProperties(System.ComponentModel.ITypeDescriptorContext,System.Object)
  commentId: M:System.ComponentModel.TypeConverter.GetProperties(System.ComponentModel.ITypeDescriptorContext,System.Object)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getproperties#system-componentmodel-typeconverter-getproperties(system-componentmodel-itypedescriptorcontext-system-object)
  name: GetProperties(ITypeDescriptorContext, object)
  nameWithType: TypeConverter.GetProperties(ITypeDescriptorContext, object)
  fullName: System.ComponentModel.TypeConverter.GetProperties(System.ComponentModel.ITypeDescriptorContext, object)
  nameWithType.vb: TypeConverter.GetProperties(ITypeDescriptorContext, Object)
  fullName.vb: System.ComponentModel.TypeConverter.GetProperties(System.ComponentModel.ITypeDescriptorContext, Object)
  name.vb: GetProperties(ITypeDescriptorContext, Object)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.GetProperties(System.ComponentModel.ITypeDescriptorContext,System.Object)
    name: GetProperties
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getproperties#system-componentmodel-typeconverter-getproperties(system-componentmodel-itypedescriptorcontext-system-object)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.GetProperties(System.ComponentModel.ITypeDescriptorContext,System.Object)
    name: GetProperties
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getproperties#system-componentmodel-typeconverter-getproperties(system-componentmodel-itypedescriptorcontext-system-object)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.ComponentModel.TypeConverter.GetProperties(System.ComponentModel.ITypeDescriptorContext,System.Object,System.Attribute[])
  commentId: M:System.ComponentModel.TypeConverter.GetProperties(System.ComponentModel.ITypeDescriptorContext,System.Object,System.Attribute[])
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getproperties#system-componentmodel-typeconverter-getproperties(system-componentmodel-itypedescriptorcontext-system-object-system-attribute())
  name: GetProperties(ITypeDescriptorContext, object, Attribute[])
  nameWithType: TypeConverter.GetProperties(ITypeDescriptorContext, object, Attribute[])
  fullName: System.ComponentModel.TypeConverter.GetProperties(System.ComponentModel.ITypeDescriptorContext, object, System.Attribute[])
  nameWithType.vb: TypeConverter.GetProperties(ITypeDescriptorContext, Object, Attribute())
  fullName.vb: System.ComponentModel.TypeConverter.GetProperties(System.ComponentModel.ITypeDescriptorContext, Object, System.Attribute())
  name.vb: GetProperties(ITypeDescriptorContext, Object, Attribute())
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.GetProperties(System.ComponentModel.ITypeDescriptorContext,System.Object,System.Attribute[])
    name: GetProperties
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getproperties#system-componentmodel-typeconverter-getproperties(system-componentmodel-itypedescriptorcontext-system-object-system-attribute())
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Attribute
    name: Attribute
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute
  - name: '['
  - name: ']'
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.GetProperties(System.ComponentModel.ITypeDescriptorContext,System.Object,System.Attribute[])
    name: GetProperties
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getproperties#system-componentmodel-typeconverter-getproperties(system-componentmodel-itypedescriptorcontext-system-object-system-attribute())
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Attribute
    name: Attribute
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute
  - name: (
  - name: )
  - name: )
- uid: System.ComponentModel.TypeConverter.GetProperties(System.Object)
  commentId: M:System.ComponentModel.TypeConverter.GetProperties(System.Object)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getproperties#system-componentmodel-typeconverter-getproperties(system-object)
  name: GetProperties(object)
  nameWithType: TypeConverter.GetProperties(object)
  fullName: System.ComponentModel.TypeConverter.GetProperties(object)
  nameWithType.vb: TypeConverter.GetProperties(Object)
  fullName.vb: System.ComponentModel.TypeConverter.GetProperties(Object)
  name.vb: GetProperties(Object)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.GetProperties(System.Object)
    name: GetProperties
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getproperties#system-componentmodel-typeconverter-getproperties(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.GetProperties(System.Object)
    name: GetProperties
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getproperties#system-componentmodel-typeconverter-getproperties(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.ComponentModel.TypeConverter.GetPropertiesSupported
  commentId: M:System.ComponentModel.TypeConverter.GetPropertiesSupported
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getpropertiessupported#system-componentmodel-typeconverter-getpropertiessupported
  name: GetPropertiesSupported()
  nameWithType: TypeConverter.GetPropertiesSupported()
  fullName: System.ComponentModel.TypeConverter.GetPropertiesSupported()
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.GetPropertiesSupported
    name: GetPropertiesSupported
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getpropertiessupported#system-componentmodel-typeconverter-getpropertiessupported
  - name: (
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.GetPropertiesSupported
    name: GetPropertiesSupported
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getpropertiessupported#system-componentmodel-typeconverter-getpropertiessupported
  - name: (
  - name: )
- uid: System.ComponentModel.TypeConverter.GetPropertiesSupported(System.ComponentModel.ITypeDescriptorContext)
  commentId: M:System.ComponentModel.TypeConverter.GetPropertiesSupported(System.ComponentModel.ITypeDescriptorContext)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getpropertiessupported#system-componentmodel-typeconverter-getpropertiessupported(system-componentmodel-itypedescriptorcontext)
  name: GetPropertiesSupported(ITypeDescriptorContext)
  nameWithType: TypeConverter.GetPropertiesSupported(ITypeDescriptorContext)
  fullName: System.ComponentModel.TypeConverter.GetPropertiesSupported(System.ComponentModel.ITypeDescriptorContext)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.GetPropertiesSupported(System.ComponentModel.ITypeDescriptorContext)
    name: GetPropertiesSupported
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getpropertiessupported#system-componentmodel-typeconverter-getpropertiessupported(system-componentmodel-itypedescriptorcontext)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.GetPropertiesSupported(System.ComponentModel.ITypeDescriptorContext)
    name: GetPropertiesSupported
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getpropertiessupported#system-componentmodel-typeconverter-getpropertiessupported(system-componentmodel-itypedescriptorcontext)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: )
- uid: System.ComponentModel.TypeConverter.GetStandardValues
  commentId: M:System.ComponentModel.TypeConverter.GetStandardValues
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getstandardvalues#system-componentmodel-typeconverter-getstandardvalues
  name: GetStandardValues()
  nameWithType: TypeConverter.GetStandardValues()
  fullName: System.ComponentModel.TypeConverter.GetStandardValues()
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.GetStandardValues
    name: GetStandardValues
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getstandardvalues#system-componentmodel-typeconverter-getstandardvalues
  - name: (
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.GetStandardValues
    name: GetStandardValues
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getstandardvalues#system-componentmodel-typeconverter-getstandardvalues
  - name: (
  - name: )
- uid: System.ComponentModel.TypeConverter.GetStandardValues(System.ComponentModel.ITypeDescriptorContext)
  commentId: M:System.ComponentModel.TypeConverter.GetStandardValues(System.ComponentModel.ITypeDescriptorContext)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getstandardvalues#system-componentmodel-typeconverter-getstandardvalues(system-componentmodel-itypedescriptorcontext)
  name: GetStandardValues(ITypeDescriptorContext)
  nameWithType: TypeConverter.GetStandardValues(ITypeDescriptorContext)
  fullName: System.ComponentModel.TypeConverter.GetStandardValues(System.ComponentModel.ITypeDescriptorContext)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.GetStandardValues(System.ComponentModel.ITypeDescriptorContext)
    name: GetStandardValues
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getstandardvalues#system-componentmodel-typeconverter-getstandardvalues(system-componentmodel-itypedescriptorcontext)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.GetStandardValues(System.ComponentModel.ITypeDescriptorContext)
    name: GetStandardValues
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getstandardvalues#system-componentmodel-typeconverter-getstandardvalues(system-componentmodel-itypedescriptorcontext)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: )
- uid: System.ComponentModel.TypeConverter.GetStandardValuesExclusive
  commentId: M:System.ComponentModel.TypeConverter.GetStandardValuesExclusive
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getstandardvaluesexclusive#system-componentmodel-typeconverter-getstandardvaluesexclusive
  name: GetStandardValuesExclusive()
  nameWithType: TypeConverter.GetStandardValuesExclusive()
  fullName: System.ComponentModel.TypeConverter.GetStandardValuesExclusive()
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.GetStandardValuesExclusive
    name: GetStandardValuesExclusive
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getstandardvaluesexclusive#system-componentmodel-typeconverter-getstandardvaluesexclusive
  - name: (
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.GetStandardValuesExclusive
    name: GetStandardValuesExclusive
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getstandardvaluesexclusive#system-componentmodel-typeconverter-getstandardvaluesexclusive
  - name: (
  - name: )
- uid: System.ComponentModel.TypeConverter.GetStandardValuesExclusive(System.ComponentModel.ITypeDescriptorContext)
  commentId: M:System.ComponentModel.TypeConverter.GetStandardValuesExclusive(System.ComponentModel.ITypeDescriptorContext)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getstandardvaluesexclusive#system-componentmodel-typeconverter-getstandardvaluesexclusive(system-componentmodel-itypedescriptorcontext)
  name: GetStandardValuesExclusive(ITypeDescriptorContext)
  nameWithType: TypeConverter.GetStandardValuesExclusive(ITypeDescriptorContext)
  fullName: System.ComponentModel.TypeConverter.GetStandardValuesExclusive(System.ComponentModel.ITypeDescriptorContext)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.GetStandardValuesExclusive(System.ComponentModel.ITypeDescriptorContext)
    name: GetStandardValuesExclusive
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getstandardvaluesexclusive#system-componentmodel-typeconverter-getstandardvaluesexclusive(system-componentmodel-itypedescriptorcontext)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.GetStandardValuesExclusive(System.ComponentModel.ITypeDescriptorContext)
    name: GetStandardValuesExclusive
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getstandardvaluesexclusive#system-componentmodel-typeconverter-getstandardvaluesexclusive(system-componentmodel-itypedescriptorcontext)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: )
- uid: System.ComponentModel.TypeConverter.GetStandardValuesSupported
  commentId: M:System.ComponentModel.TypeConverter.GetStandardValuesSupported
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getstandardvaluessupported#system-componentmodel-typeconverter-getstandardvaluessupported
  name: GetStandardValuesSupported()
  nameWithType: TypeConverter.GetStandardValuesSupported()
  fullName: System.ComponentModel.TypeConverter.GetStandardValuesSupported()
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.GetStandardValuesSupported
    name: GetStandardValuesSupported
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getstandardvaluessupported#system-componentmodel-typeconverter-getstandardvaluessupported
  - name: (
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.GetStandardValuesSupported
    name: GetStandardValuesSupported
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getstandardvaluessupported#system-componentmodel-typeconverter-getstandardvaluessupported
  - name: (
  - name: )
- uid: System.ComponentModel.TypeConverter.GetStandardValuesSupported(System.ComponentModel.ITypeDescriptorContext)
  commentId: M:System.ComponentModel.TypeConverter.GetStandardValuesSupported(System.ComponentModel.ITypeDescriptorContext)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getstandardvaluessupported#system-componentmodel-typeconverter-getstandardvaluessupported(system-componentmodel-itypedescriptorcontext)
  name: GetStandardValuesSupported(ITypeDescriptorContext)
  nameWithType: TypeConverter.GetStandardValuesSupported(ITypeDescriptorContext)
  fullName: System.ComponentModel.TypeConverter.GetStandardValuesSupported(System.ComponentModel.ITypeDescriptorContext)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.GetStandardValuesSupported(System.ComponentModel.ITypeDescriptorContext)
    name: GetStandardValuesSupported
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getstandardvaluessupported#system-componentmodel-typeconverter-getstandardvaluessupported(system-componentmodel-itypedescriptorcontext)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.GetStandardValuesSupported(System.ComponentModel.ITypeDescriptorContext)
    name: GetStandardValuesSupported
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.getstandardvaluessupported#system-componentmodel-typeconverter-getstandardvaluessupported(system-componentmodel-itypedescriptorcontext)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: )
- uid: System.ComponentModel.TypeConverter.IsValid(System.ComponentModel.ITypeDescriptorContext,System.Object)
  commentId: M:System.ComponentModel.TypeConverter.IsValid(System.ComponentModel.ITypeDescriptorContext,System.Object)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.isvalid#system-componentmodel-typeconverter-isvalid(system-componentmodel-itypedescriptorcontext-system-object)
  name: IsValid(ITypeDescriptorContext, object)
  nameWithType: TypeConverter.IsValid(ITypeDescriptorContext, object)
  fullName: System.ComponentModel.TypeConverter.IsValid(System.ComponentModel.ITypeDescriptorContext, object)
  nameWithType.vb: TypeConverter.IsValid(ITypeDescriptorContext, Object)
  fullName.vb: System.ComponentModel.TypeConverter.IsValid(System.ComponentModel.ITypeDescriptorContext, Object)
  name.vb: IsValid(ITypeDescriptorContext, Object)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.IsValid(System.ComponentModel.ITypeDescriptorContext,System.Object)
    name: IsValid
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.isvalid#system-componentmodel-typeconverter-isvalid(system-componentmodel-itypedescriptorcontext-system-object)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.IsValid(System.ComponentModel.ITypeDescriptorContext,System.Object)
    name: IsValid
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.isvalid#system-componentmodel-typeconverter-isvalid(system-componentmodel-itypedescriptorcontext-system-object)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.ComponentModel.TypeConverter.IsValid(System.Object)
  commentId: M:System.ComponentModel.TypeConverter.IsValid(System.Object)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.isvalid#system-componentmodel-typeconverter-isvalid(system-object)
  name: IsValid(object)
  nameWithType: TypeConverter.IsValid(object)
  fullName: System.ComponentModel.TypeConverter.IsValid(object)
  nameWithType.vb: TypeConverter.IsValid(Object)
  fullName.vb: System.ComponentModel.TypeConverter.IsValid(Object)
  name.vb: IsValid(Object)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.IsValid(System.Object)
    name: IsValid
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.isvalid#system-componentmodel-typeconverter-isvalid(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.IsValid(System.Object)
    name: IsValid
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.isvalid#system-componentmodel-typeconverter-isvalid(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.ComponentModel.TypeConverter.SortProperties(System.ComponentModel.PropertyDescriptorCollection,System.String[])
  commentId: M:System.ComponentModel.TypeConverter.SortProperties(System.ComponentModel.PropertyDescriptorCollection,System.String[])
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.sortproperties
  name: SortProperties(PropertyDescriptorCollection, string[])
  nameWithType: TypeConverter.SortProperties(PropertyDescriptorCollection, string[])
  fullName: System.ComponentModel.TypeConverter.SortProperties(System.ComponentModel.PropertyDescriptorCollection, string[])
  nameWithType.vb: TypeConverter.SortProperties(PropertyDescriptorCollection, String())
  fullName.vb: System.ComponentModel.TypeConverter.SortProperties(System.ComponentModel.PropertyDescriptorCollection, String())
  name.vb: SortProperties(PropertyDescriptorCollection, String())
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.SortProperties(System.ComponentModel.PropertyDescriptorCollection,System.String[])
    name: SortProperties
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.sortproperties
  - name: (
  - uid: System.ComponentModel.PropertyDescriptorCollection
    name: PropertyDescriptorCollection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.propertydescriptorcollection
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: '['
  - name: ']'
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.SortProperties(System.ComponentModel.PropertyDescriptorCollection,System.String[])
    name: SortProperties
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.sortproperties
  - name: (
  - uid: System.ComponentModel.PropertyDescriptorCollection
    name: PropertyDescriptorCollection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.propertydescriptorcollection
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: (
  - name: )
  - name: )
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: System.ComponentModel
  commentId: N:System.ComponentModel
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.ComponentModel
  nameWithType: System.ComponentModel
  fullName: System.ComponentModel
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.ComponentModel
    name: ComponentModel
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.ComponentModel
    name: ComponentModel
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: System.ComponentModel.ITypeDescriptorContext
  commentId: T:System.ComponentModel.ITypeDescriptorContext
  parent: System.ComponentModel
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  name: ITypeDescriptorContext
  nameWithType: ITypeDescriptorContext
  fullName: System.ComponentModel.ITypeDescriptorContext
- uid: System.Type
  commentId: T:System.Type
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.type
  name: Type
  nameWithType: Type
  fullName: System.Type
- uid: System.ComponentModel.TypeConverter.CanConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Type)
  commentId: M:System.ComponentModel.TypeConverter.CanConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Type)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.canconvertfrom#system-componentmodel-typeconverter-canconvertfrom(system-componentmodel-itypedescriptorcontext-system-type)
  name: CanConvertFrom(ITypeDescriptorContext, Type)
  nameWithType: TypeConverter.CanConvertFrom(ITypeDescriptorContext, Type)
  fullName: System.ComponentModel.TypeConverter.CanConvertFrom(System.ComponentModel.ITypeDescriptorContext, System.Type)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.CanConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Type)
    name: CanConvertFrom
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.canconvertfrom#system-componentmodel-typeconverter-canconvertfrom(system-componentmodel-itypedescriptorcontext-system-type)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.CanConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Type)
    name: CanConvertFrom
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.canconvertfrom#system-componentmodel-typeconverter-canconvertfrom(system-componentmodel-itypedescriptorcontext-system-type)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
- uid: DrawnUi.Draw.DrawnFontAttributesConverter.CanConvertFrom*
  commentId: Overload:DrawnUi.Draw.DrawnFontAttributesConverter.CanConvertFrom
  href: DrawnUi.Draw.DrawnFontAttributesConverter.html#DrawnUi_Draw_DrawnFontAttributesConverter_CanConvertFrom_System_ComponentModel_ITypeDescriptorContext_System_Type_
  name: CanConvertFrom
  nameWithType: DrawnFontAttributesConverter.CanConvertFrom
  fullName: DrawnUi.Draw.DrawnFontAttributesConverter.CanConvertFrom
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: System.ComponentModel.TypeConverter.CanConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Type)
  commentId: M:System.ComponentModel.TypeConverter.CanConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Type)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.canconvertto#system-componentmodel-typeconverter-canconvertto(system-componentmodel-itypedescriptorcontext-system-type)
  name: CanConvertTo(ITypeDescriptorContext, Type)
  nameWithType: TypeConverter.CanConvertTo(ITypeDescriptorContext, Type)
  fullName: System.ComponentModel.TypeConverter.CanConvertTo(System.ComponentModel.ITypeDescriptorContext, System.Type)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.CanConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Type)
    name: CanConvertTo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.canconvertto#system-componentmodel-typeconverter-canconvertto(system-componentmodel-itypedescriptorcontext-system-type)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.CanConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Type)
    name: CanConvertTo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.canconvertto#system-componentmodel-typeconverter-canconvertto(system-componentmodel-itypedescriptorcontext-system-type)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
- uid: DrawnUi.Draw.DrawnFontAttributesConverter.CanConvertTo*
  commentId: Overload:DrawnUi.Draw.DrawnFontAttributesConverter.CanConvertTo
  href: DrawnUi.Draw.DrawnFontAttributesConverter.html#DrawnUi_Draw_DrawnFontAttributesConverter_CanConvertTo_System_ComponentModel_ITypeDescriptorContext_System_Type_
  name: CanConvertTo
  nameWithType: DrawnFontAttributesConverter.CanConvertTo
  fullName: DrawnUi.Draw.DrawnFontAttributesConverter.CanConvertTo
- uid: System.Globalization.CultureInfo
  commentId: T:System.Globalization.CultureInfo
  parent: System.Globalization
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.globalization.cultureinfo
  name: CultureInfo
  nameWithType: CultureInfo
  fullName: System.Globalization.CultureInfo
- uid: System.NotSupportedException
  commentId: T:System.NotSupportedException
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.notsupportedexception
  name: NotSupportedException
  nameWithType: NotSupportedException
  fullName: System.NotSupportedException
- uid: System.ComponentModel.TypeConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)
  commentId: M:System.ComponentModel.TypeConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertfrom#system-componentmodel-typeconverter-convertfrom(system-componentmodel-itypedescriptorcontext-system-globalization-cultureinfo-system-object)
  name: ConvertFrom(ITypeDescriptorContext, CultureInfo, object)
  nameWithType: TypeConverter.ConvertFrom(ITypeDescriptorContext, CultureInfo, object)
  fullName: System.ComponentModel.TypeConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext, System.Globalization.CultureInfo, object)
  nameWithType.vb: TypeConverter.ConvertFrom(ITypeDescriptorContext, CultureInfo, Object)
  fullName.vb: System.ComponentModel.TypeConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext, System.Globalization.CultureInfo, Object)
  name.vb: ConvertFrom(ITypeDescriptorContext, CultureInfo, Object)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)
    name: ConvertFrom
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertfrom#system-componentmodel-typeconverter-convertfrom(system-componentmodel-itypedescriptorcontext-system-globalization-cultureinfo-system-object)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.Globalization.CultureInfo
    name: CultureInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.globalization.cultureinfo
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)
    name: ConvertFrom
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertfrom#system-componentmodel-typeconverter-convertfrom(system-componentmodel-itypedescriptorcontext-system-globalization-cultureinfo-system-object)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.Globalization.CultureInfo
    name: CultureInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.globalization.cultureinfo
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Draw.DrawnFontAttributesConverter.ConvertFrom*
  commentId: Overload:DrawnUi.Draw.DrawnFontAttributesConverter.ConvertFrom
  href: DrawnUi.Draw.DrawnFontAttributesConverter.html#DrawnUi_Draw_DrawnFontAttributesConverter_ConvertFrom_System_ComponentModel_ITypeDescriptorContext_System_Globalization_CultureInfo_System_Object_
  name: ConvertFrom
  nameWithType: DrawnFontAttributesConverter.ConvertFrom
  fullName: DrawnUi.Draw.DrawnFontAttributesConverter.ConvertFrom
- uid: System.Globalization
  commentId: N:System.Globalization
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Globalization
  nameWithType: System.Globalization
  fullName: System.Globalization
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Globalization
    name: Globalization
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.globalization
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Globalization
    name: Globalization
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.globalization
- uid: System.ArgumentNullException
  commentId: T:System.ArgumentNullException
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.argumentnullexception
  name: ArgumentNullException
  nameWithType: ArgumentNullException
  fullName: System.ArgumentNullException
- uid: System.ComponentModel.TypeConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)
  commentId: M:System.ComponentModel.TypeConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)
  parent: System.ComponentModel.TypeConverter
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertto#system-componentmodel-typeconverter-convertto(system-componentmodel-itypedescriptorcontext-system-globalization-cultureinfo-system-object-system-type)
  name: ConvertTo(ITypeDescriptorContext, CultureInfo, object, Type)
  nameWithType: TypeConverter.ConvertTo(ITypeDescriptorContext, CultureInfo, object, Type)
  fullName: System.ComponentModel.TypeConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext, System.Globalization.CultureInfo, object, System.Type)
  nameWithType.vb: TypeConverter.ConvertTo(ITypeDescriptorContext, CultureInfo, Object, Type)
  fullName.vb: System.ComponentModel.TypeConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext, System.Globalization.CultureInfo, Object, System.Type)
  name.vb: ConvertTo(ITypeDescriptorContext, CultureInfo, Object, Type)
  spec.csharp:
  - uid: System.ComponentModel.TypeConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)
    name: ConvertTo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertto#system-componentmodel-typeconverter-convertto(system-componentmodel-itypedescriptorcontext-system-globalization-cultureinfo-system-object-system-type)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.Globalization.CultureInfo
    name: CultureInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.globalization.cultureinfo
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
  spec.vb:
  - uid: System.ComponentModel.TypeConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)
    name: ConvertTo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.typeconverter.convertto#system-componentmodel-typeconverter-convertto(system-componentmodel-itypedescriptorcontext-system-globalization-cultureinfo-system-object-system-type)
  - name: (
  - uid: System.ComponentModel.ITypeDescriptorContext
    name: ITypeDescriptorContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.itypedescriptorcontext
  - name: ','
  - name: " "
  - uid: System.Globalization.CultureInfo
    name: CultureInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.globalization.cultureinfo
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
- uid: DrawnUi.Draw.DrawnFontAttributesConverter.ConvertTo*
  commentId: Overload:DrawnUi.Draw.DrawnFontAttributesConverter.ConvertTo
  href: DrawnUi.Draw.DrawnFontAttributesConverter.html#DrawnUi_Draw_DrawnFontAttributesConverter_ConvertTo_System_ComponentModel_ITypeDescriptorContext_System_Globalization_CultureInfo_System_Object_System_Type_
  name: ConvertTo
  nameWithType: DrawnFontAttributesConverter.ConvertTo
  fullName: DrawnUi.Draw.DrawnFontAttributesConverter.ConvertTo
