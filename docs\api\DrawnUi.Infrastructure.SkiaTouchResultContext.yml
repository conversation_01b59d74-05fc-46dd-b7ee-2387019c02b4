### YamlMime:ManagedReference
items:
- uid: DrawnUi.Infrastructure.SkiaTouchResultContext
  commentId: T:DrawnUi.Infrastructure.SkiaTouchResultContext
  id: SkiaTouchResultContext
  parent: DrawnUi.Infrastructure
  children:
  - DrawnUi.Infrastructure.SkiaTouchResultContext.Context
  - DrawnUi.Infrastructure.SkiaTouchResultContext.Control
  - DrawnUi.Infrastructure.SkiaTouchResultContext.TouchAction
  - DrawnUi.Infrastructure.SkiaTouchResultContext.TouchArgs
  langs:
  - csharp
  - vb
  name: SkiaTouchResultContext
  nameWithType: SkiaTouchResultContext
  fullName: DrawnUi.Infrastructure.SkiaTouchResultContext
  type: Struct
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/SkiaTouchResultContext.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: SkiaTouchResultContext
    path: ../src/Shared/Draw/Internals/Models/SkiaTouchResultContext.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public struct SkiaTouchResultContext
    content.vb: Public Structure SkiaTouchResultContext
  inheritedMembers:
  - System.ValueType.Equals(System.Object)
  - System.ValueType.GetHashCode
  - System.ValueType.ToString
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetType
  - System.Object.ReferenceEquals(System.Object,System.Object)
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Infrastructure.SkiaTouchResultContext.Context
  commentId: P:DrawnUi.Infrastructure.SkiaTouchResultContext.Context
  id: Context
  parent: DrawnUi.Infrastructure.SkiaTouchResultContext
  langs:
  - csharp
  - vb
  name: Context
  nameWithType: SkiaTouchResultContext.Context
  fullName: DrawnUi.Infrastructure.SkiaTouchResultContext.Context
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/SkiaTouchResultContext.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Context
    path: ../src/Shared/Draw/Internals/Models/SkiaTouchResultContext.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public object Context { readonly get; set; }
    parameters: []
    return:
      type: System.Object
    content.vb: Public Property Context As Object
  overload: DrawnUi.Infrastructure.SkiaTouchResultContext.Context*
- uid: DrawnUi.Infrastructure.SkiaTouchResultContext.Control
  commentId: P:DrawnUi.Infrastructure.SkiaTouchResultContext.Control
  id: Control
  parent: DrawnUi.Infrastructure.SkiaTouchResultContext
  langs:
  - csharp
  - vb
  name: Control
  nameWithType: SkiaTouchResultContext.Control
  fullName: DrawnUi.Infrastructure.SkiaTouchResultContext.Control
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/SkiaTouchResultContext.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Control
    path: ../src/Shared/Draw/Internals/Models/SkiaTouchResultContext.cs
    startLine: 5
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public SkiaControl Control { readonly get; set; }
    parameters: []
    return:
      type: DrawnUi.Draw.SkiaControl
    content.vb: Public Property Control As SkiaControl
  overload: DrawnUi.Infrastructure.SkiaTouchResultContext.Control*
- uid: DrawnUi.Infrastructure.SkiaTouchResultContext.TouchArgs
  commentId: P:DrawnUi.Infrastructure.SkiaTouchResultContext.TouchArgs
  id: TouchArgs
  parent: DrawnUi.Infrastructure.SkiaTouchResultContext
  langs:
  - csharp
  - vb
  name: TouchArgs
  nameWithType: SkiaTouchResultContext.TouchArgs
  fullName: DrawnUi.Infrastructure.SkiaTouchResultContext.TouchArgs
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/SkiaTouchResultContext.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: TouchArgs
    path: ../src/Shared/Draw/Internals/Models/SkiaTouchResultContext.cs
    startLine: 6
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public TouchActionEventArgs TouchArgs { readonly get; set; }
    parameters: []
    return:
      type: AppoMobi.Maui.Gestures.TouchActionEventArgs
    content.vb: Public Property TouchArgs As TouchActionEventArgs
  overload: DrawnUi.Infrastructure.SkiaTouchResultContext.TouchArgs*
- uid: DrawnUi.Infrastructure.SkiaTouchResultContext.TouchAction
  commentId: P:DrawnUi.Infrastructure.SkiaTouchResultContext.TouchAction
  id: TouchAction
  parent: DrawnUi.Infrastructure.SkiaTouchResultContext
  langs:
  - csharp
  - vb
  name: TouchAction
  nameWithType: SkiaTouchResultContext.TouchAction
  fullName: DrawnUi.Infrastructure.SkiaTouchResultContext.TouchAction
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/SkiaTouchResultContext.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: TouchAction
    path: ../src/Shared/Draw/Internals/Models/SkiaTouchResultContext.cs
    startLine: 7
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public TouchActionResult TouchAction { readonly get; set; }
    parameters: []
    return:
      type: AppoMobi.Maui.Gestures.TouchActionResult
    content.vb: Public Property TouchAction As TouchActionResult
  overload: DrawnUi.Infrastructure.SkiaTouchResultContext.TouchAction*
references:
- uid: DrawnUi.Infrastructure
  commentId: N:DrawnUi.Infrastructure
  href: DrawnUi.html
  name: DrawnUi.Infrastructure
  nameWithType: DrawnUi.Infrastructure
  fullName: DrawnUi.Infrastructure
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Infrastructure
    name: Infrastructure
    href: DrawnUi.Infrastructure.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Infrastructure
    name: Infrastructure
    href: DrawnUi.Infrastructure.html
- uid: System.ValueType.Equals(System.Object)
  commentId: M:System.ValueType.Equals(System.Object)
  parent: System.ValueType
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype.equals
  name: Equals(object)
  nameWithType: ValueType.Equals(object)
  fullName: System.ValueType.Equals(object)
  nameWithType.vb: ValueType.Equals(Object)
  fullName.vb: System.ValueType.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.ValueType.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.equals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.ValueType.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.equals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.ValueType.GetHashCode
  commentId: M:System.ValueType.GetHashCode
  parent: System.ValueType
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype.gethashcode
  name: GetHashCode()
  nameWithType: ValueType.GetHashCode()
  fullName: System.ValueType.GetHashCode()
  spec.csharp:
  - uid: System.ValueType.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.ValueType.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.gethashcode
  - name: (
  - name: )
- uid: System.ValueType.ToString
  commentId: M:System.ValueType.ToString
  parent: System.ValueType
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype.tostring
  name: ToString()
  nameWithType: ValueType.ToString()
  fullName: System.ValueType.ToString()
  spec.csharp:
  - uid: System.ValueType.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.ValueType.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.tostring
  - name: (
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.ValueType
  commentId: T:System.ValueType
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype
  name: ValueType
  nameWithType: ValueType
  fullName: System.ValueType
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Infrastructure.SkiaTouchResultContext.Context*
  commentId: Overload:DrawnUi.Infrastructure.SkiaTouchResultContext.Context
  href: DrawnUi.Infrastructure.SkiaTouchResultContext.html#DrawnUi_Infrastructure_SkiaTouchResultContext_Context
  name: Context
  nameWithType: SkiaTouchResultContext.Context
  fullName: DrawnUi.Infrastructure.SkiaTouchResultContext.Context
- uid: DrawnUi.Infrastructure.SkiaTouchResultContext.Control*
  commentId: Overload:DrawnUi.Infrastructure.SkiaTouchResultContext.Control
  href: DrawnUi.Infrastructure.SkiaTouchResultContext.html#DrawnUi_Infrastructure_SkiaTouchResultContext_Control
  name: Control
  nameWithType: SkiaTouchResultContext.Control
  fullName: DrawnUi.Infrastructure.SkiaTouchResultContext.Control
- uid: DrawnUi.Draw.SkiaControl
  commentId: T:DrawnUi.Draw.SkiaControl
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaControl.html
  name: SkiaControl
  nameWithType: SkiaControl
  fullName: DrawnUi.Draw.SkiaControl
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: DrawnUi.Infrastructure.SkiaTouchResultContext.TouchArgs*
  commentId: Overload:DrawnUi.Infrastructure.SkiaTouchResultContext.TouchArgs
  href: DrawnUi.Infrastructure.SkiaTouchResultContext.html#DrawnUi_Infrastructure_SkiaTouchResultContext_TouchArgs
  name: TouchArgs
  nameWithType: SkiaTouchResultContext.TouchArgs
  fullName: DrawnUi.Infrastructure.SkiaTouchResultContext.TouchArgs
- uid: AppoMobi.Maui.Gestures.TouchActionEventArgs
  commentId: T:AppoMobi.Maui.Gestures.TouchActionEventArgs
  parent: AppoMobi.Maui.Gestures
  isExternal: true
  name: TouchActionEventArgs
  nameWithType: TouchActionEventArgs
  fullName: AppoMobi.Maui.Gestures.TouchActionEventArgs
- uid: AppoMobi.Maui.Gestures
  commentId: N:AppoMobi.Maui.Gestures
  isExternal: true
  name: AppoMobi.Maui.Gestures
  nameWithType: AppoMobi.Maui.Gestures
  fullName: AppoMobi.Maui.Gestures
  spec.csharp:
  - uid: AppoMobi
    name: AppoMobi
    isExternal: true
  - name: .
  - uid: AppoMobi.Maui
    name: Maui
    isExternal: true
  - name: .
  - uid: AppoMobi.Maui.Gestures
    name: Gestures
    isExternal: true
  spec.vb:
  - uid: AppoMobi
    name: AppoMobi
    isExternal: true
  - name: .
  - uid: AppoMobi.Maui
    name: Maui
    isExternal: true
  - name: .
  - uid: AppoMobi.Maui.Gestures
    name: Gestures
    isExternal: true
- uid: DrawnUi.Infrastructure.SkiaTouchResultContext.TouchAction*
  commentId: Overload:DrawnUi.Infrastructure.SkiaTouchResultContext.TouchAction
  href: DrawnUi.Infrastructure.SkiaTouchResultContext.html#DrawnUi_Infrastructure_SkiaTouchResultContext_TouchAction
  name: TouchAction
  nameWithType: SkiaTouchResultContext.TouchAction
  fullName: DrawnUi.Infrastructure.SkiaTouchResultContext.TouchAction
- uid: AppoMobi.Maui.Gestures.TouchActionResult
  commentId: T:AppoMobi.Maui.Gestures.TouchActionResult
  parent: AppoMobi.Maui.Gestures
  isExternal: true
  name: TouchActionResult
  nameWithType: TouchActionResult
  fullName: AppoMobi.Maui.Gestures.TouchActionResult
