### YamlMime:ManagedReference
items:
- uid: DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry
  commentId: T:DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry
  id: SkiaViewSwitcher.NavigationStackEntry
  parent: DrawnUi.Controls
  children:
  - DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry.#ctor(DrawnUi.Draw.SkiaControl,System.Boolean,System.Boolean)
  - DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry.Animated
  - DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry.Preserve
  - DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry.View
  langs:
  - csharp
  - vb
  name: SkiaViewSwitcher.NavigationStackEntry
  nameWithType: SkiaViewSwitcher.NavigationStackEntry
  fullName: DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/ViewSwitcher/SkiaViewSwitcher.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: NavigationStackEntry
    path: ../src/Maui/DrawnUi/Controls/ViewSwitcher/SkiaViewSwitcher.cs
    startLine: 315
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: 'public record SkiaViewSwitcher.NavigationStackEntry : IEquatable<SkiaViewSwitcher.NavigationStackEntry>'
    content.vb: Public Class SkiaViewSwitcher.NavigationStackEntry Implements IEquatable(Of SkiaViewSwitcher.NavigationStackEntry)
  inheritance:
  - System.Object
  implements:
  - System.IEquatable{DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry}
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry.#ctor(DrawnUi.Draw.SkiaControl,System.Boolean,System.Boolean)
  commentId: M:DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry.#ctor(DrawnUi.Draw.SkiaControl,System.Boolean,System.Boolean)
  id: '#ctor(DrawnUi.Draw.SkiaControl,System.Boolean,System.Boolean)'
  parent: DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry
  langs:
  - csharp
  - vb
  name: NavigationStackEntry(SkiaControl, bool, bool)
  nameWithType: SkiaViewSwitcher.NavigationStackEntry.NavigationStackEntry(SkiaControl, bool, bool)
  fullName: DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry.NavigationStackEntry(DrawnUi.Draw.SkiaControl, bool, bool)
  type: Constructor
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/ViewSwitcher/SkiaViewSwitcher.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Maui/DrawnUi/Controls/ViewSwitcher/SkiaViewSwitcher.cs
    startLine: 315
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: public NavigationStackEntry(SkiaControl View, bool Animated, bool Preserve)
    parameters:
    - id: View
      type: DrawnUi.Draw.SkiaControl
    - id: Animated
      type: System.Boolean
    - id: Preserve
      type: System.Boolean
    content.vb: Public Sub New(View As SkiaControl, Animated As Boolean, Preserve As Boolean)
  overload: DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry.#ctor*
  nameWithType.vb: SkiaViewSwitcher.NavigationStackEntry.New(SkiaControl, Boolean, Boolean)
  fullName.vb: DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry.New(DrawnUi.Draw.SkiaControl, Boolean, Boolean)
  name.vb: New(SkiaControl, Boolean, Boolean)
- uid: DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry.View
  commentId: P:DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry.View
  id: View
  parent: DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry
  langs:
  - csharp
  - vb
  name: View
  nameWithType: SkiaViewSwitcher.NavigationStackEntry.View
  fullName: DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry.View
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/ViewSwitcher/SkiaViewSwitcher.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: View
    path: ../src/Maui/DrawnUi/Controls/ViewSwitcher/SkiaViewSwitcher.cs
    startLine: 315
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: public SkiaControl View { get; init; }
    parameters: []
    return:
      type: DrawnUi.Draw.SkiaControl
    content.vb: Public Property View As SkiaControl
  overload: DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry.View*
- uid: DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry.Animated
  commentId: P:DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry.Animated
  id: Animated
  parent: DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry
  langs:
  - csharp
  - vb
  name: Animated
  nameWithType: SkiaViewSwitcher.NavigationStackEntry.Animated
  fullName: DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry.Animated
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/ViewSwitcher/SkiaViewSwitcher.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Animated
    path: ../src/Maui/DrawnUi/Controls/ViewSwitcher/SkiaViewSwitcher.cs
    startLine: 315
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: public bool Animated { get; init; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property Animated As Boolean
  overload: DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry.Animated*
- uid: DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry.Preserve
  commentId: P:DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry.Preserve
  id: Preserve
  parent: DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry
  langs:
  - csharp
  - vb
  name: Preserve
  nameWithType: SkiaViewSwitcher.NavigationStackEntry.Preserve
  fullName: DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry.Preserve
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/ViewSwitcher/SkiaViewSwitcher.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Preserve
    path: ../src/Maui/DrawnUi/Controls/ViewSwitcher/SkiaViewSwitcher.cs
    startLine: 315
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: public bool Preserve { get; init; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property Preserve As Boolean
  overload: DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry.Preserve*
references:
- uid: DrawnUi.Controls
  commentId: N:DrawnUi.Controls
  href: DrawnUi.html
  name: DrawnUi.Controls
  nameWithType: DrawnUi.Controls
  fullName: DrawnUi.Controls
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Controls
    name: Controls
    href: DrawnUi.Controls.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Controls
    name: Controls
    href: DrawnUi.Controls.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.IEquatable{DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry}
  commentId: T:System.IEquatable{DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry}
  parent: System
  definition: System.IEquatable`1
  href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  name: IEquatable<SkiaViewSwitcher.NavigationStackEntry>
  nameWithType: IEquatable<SkiaViewSwitcher.NavigationStackEntry>
  fullName: System.IEquatable<DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry>
  nameWithType.vb: IEquatable(Of SkiaViewSwitcher.NavigationStackEntry)
  fullName.vb: System.IEquatable(Of DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry)
  name.vb: IEquatable(Of SkiaViewSwitcher.NavigationStackEntry)
  spec.csharp:
  - uid: System.IEquatable`1
    name: IEquatable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  - name: <
  - uid: DrawnUi.Controls.SkiaViewSwitcher
    name: SkiaViewSwitcher
    href: DrawnUi.Controls.SkiaViewSwitcher.html
  - name: .
  - uid: DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry
    name: NavigationStackEntry
    href: DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry.html
  - name: '>'
  spec.vb:
  - uid: System.IEquatable`1
    name: IEquatable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Controls.SkiaViewSwitcher
    name: SkiaViewSwitcher
    href: DrawnUi.Controls.SkiaViewSwitcher.html
  - name: .
  - uid: DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry
    name: NavigationStackEntry
    href: DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry.html
  - name: )
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: System.IEquatable`1
  commentId: T:System.IEquatable`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  name: IEquatable<T>
  nameWithType: IEquatable<T>
  fullName: System.IEquatable<T>
  nameWithType.vb: IEquatable(Of T)
  fullName.vb: System.IEquatable(Of T)
  name.vb: IEquatable(Of T)
  spec.csharp:
  - uid: System.IEquatable`1
    name: IEquatable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.IEquatable`1
    name: IEquatable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry.#ctor*
  commentId: Overload:DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry.#ctor
  href: DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry.html#DrawnUi_Controls_SkiaViewSwitcher_NavigationStackEntry__ctor_DrawnUi_Draw_SkiaControl_System_Boolean_System_Boolean_
  name: NavigationStackEntry
  nameWithType: SkiaViewSwitcher.NavigationStackEntry.NavigationStackEntry
  fullName: DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry.NavigationStackEntry
  nameWithType.vb: SkiaViewSwitcher.NavigationStackEntry.New
  fullName.vb: DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry.New
  name.vb: New
- uid: DrawnUi.Draw.SkiaControl
  commentId: T:DrawnUi.Draw.SkiaControl
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaControl.html
  name: SkiaControl
  nameWithType: SkiaControl
  fullName: DrawnUi.Draw.SkiaControl
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry.View*
  commentId: Overload:DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry.View
  href: DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry.html#DrawnUi_Controls_SkiaViewSwitcher_NavigationStackEntry_View
  name: View
  nameWithType: SkiaViewSwitcher.NavigationStackEntry.View
  fullName: DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry.View
- uid: DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry.Animated*
  commentId: Overload:DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry.Animated
  href: DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry.html#DrawnUi_Controls_SkiaViewSwitcher_NavigationStackEntry_Animated
  name: Animated
  nameWithType: SkiaViewSwitcher.NavigationStackEntry.Animated
  fullName: DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry.Animated
- uid: DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry.Preserve*
  commentId: Overload:DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry.Preserve
  href: DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry.html#DrawnUi_Controls_SkiaViewSwitcher_NavigationStackEntry_Preserve
  name: Preserve
  nameWithType: SkiaViewSwitcher.NavigationStackEntry.Preserve
  fullName: DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry.Preserve
