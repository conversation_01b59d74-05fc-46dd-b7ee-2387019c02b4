### YamlMime:ManagedReference
items:
- uid: DrawnUi.Controls
  commentId: N:DrawnUi.Controls
  id: DrawnUi.Controls
  children:
  - DrawnUi.Controls.AnimatedFramesRenderer
  - DrawnUi.Controls.ContentWithBackdrop
  - DrawnUi.Controls.DrawerDirection
  - DrawnUi.Controls.GifAnimation
  - DrawnUi.Controls.GridLayout
  - DrawnUi.Controls.ISkiaRadioButton
  - DrawnUi.Controls.ISmartNative
  - DrawnUi.Controls.MauiEditor
  - DrawnUi.Controls.MauiEditorHandler
  - DrawnUi.Controls.MauiEntry
  - DrawnUi.Controls.MauiEntryHandler
  - DrawnUi.Controls.NavigationSource
  - DrawnUi.Controls.RadioButtonGroupManager
  - DrawnUi.Controls.ScrollPickerLabelContainer
  - DrawnUi.Controls.ScrollPickerWheel
  - DrawnUi.Controls.SkiaCarousel
  - DrawnUi.Controls.SkiaDecoratedGrid
  - DrawnUi.Controls.SkiaDrawer
  - DrawnUi.Controls.SkiaDrawnCell
  - DrawnUi.Controls.SkiaDynamicDrawnCell
  - DrawnUi.Controls.SkiaGif
  - DrawnUi.Controls.SkiaLottie
  - DrawnUi.Controls.SkiaLottie.ColorEqualityComparer
  - DrawnUi.Controls.SkiaMauiEditor
  - DrawnUi.Controls.SkiaMauiEntry
  - DrawnUi.Controls.SkiaMediaImage
  - DrawnUi.Controls.SkiaRadioButton
  - DrawnUi.Controls.SkiaShell
  - DrawnUi.Controls.SkiaShell.IHandleGoBack
  - DrawnUi.Controls.SkiaShell.ModalWrapper
  - DrawnUi.Controls.SkiaShell.NavigationLayer`1
  - DrawnUi.Controls.SkiaShell.PageInStack
  - DrawnUi.Controls.SkiaShell.ParsedRoute
  - DrawnUi.Controls.SkiaShell.PopupWrapper
  - DrawnUi.Controls.SkiaShell.ShellCurrentRoute
  - DrawnUi.Controls.SkiaShell.ShellStackChild
  - DrawnUi.Controls.SkiaShell.TypeRouteFactory
  - DrawnUi.Controls.SkiaShellNavigatedArgs
  - DrawnUi.Controls.SkiaShellNavigatingArgs
  - DrawnUi.Controls.SkiaSprite
  - DrawnUi.Controls.SkiaTabsSelector
  - DrawnUi.Controls.SkiaTabsSelector.TabEntry
  - DrawnUi.Controls.SkiaViewSwitcher
  - DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry
  langs:
  - csharp
  - vb
  name: DrawnUi.Controls
  nameWithType: DrawnUi.Controls
  fullName: DrawnUi.Controls
  type: Namespace
  assemblies:
  - DrawnUi.Maui
references:
- uid: DrawnUi.Controls.SkiaCarousel
  commentId: T:DrawnUi.Controls.SkiaCarousel
  href: DrawnUi.Controls.SkiaCarousel.html
  name: SkiaCarousel
  nameWithType: SkiaCarousel
  fullName: DrawnUi.Controls.SkiaCarousel
- uid: DrawnUi.Controls.SkiaDrawnCell
  commentId: T:DrawnUi.Controls.SkiaDrawnCell
  parent: DrawnUi.Controls
  href: DrawnUi.Controls.SkiaDrawnCell.html
  name: SkiaDrawnCell
  nameWithType: SkiaDrawnCell
  fullName: DrawnUi.Controls.SkiaDrawnCell
- uid: DrawnUi.Controls.SkiaDynamicDrawnCell
  commentId: T:DrawnUi.Controls.SkiaDynamicDrawnCell
  href: DrawnUi.Controls.SkiaDynamicDrawnCell.html
  name: SkiaDynamicDrawnCell
  nameWithType: SkiaDynamicDrawnCell
  fullName: DrawnUi.Controls.SkiaDynamicDrawnCell
- uid: DrawnUi.Controls.DrawerDirection
  commentId: T:DrawnUi.Controls.DrawerDirection
  parent: DrawnUi.Controls
  href: DrawnUi.Controls.DrawerDirection.html
  name: DrawerDirection
  nameWithType: DrawerDirection
  fullName: DrawnUi.Controls.DrawerDirection
- uid: DrawnUi.Controls.SkiaDrawer
  commentId: T:DrawnUi.Controls.SkiaDrawer
  parent: DrawnUi.Controls
  href: DrawnUi.Controls.SkiaDrawer.html
  name: SkiaDrawer
  nameWithType: SkiaDrawer
  fullName: DrawnUi.Controls.SkiaDrawer
- uid: DrawnUi.Controls.ISmartNative
  commentId: T:DrawnUi.Controls.ISmartNative
  parent: DrawnUi.Controls
  href: DrawnUi.Controls.ISmartNative.html
  name: ISmartNative
  nameWithType: ISmartNative
  fullName: DrawnUi.Controls.ISmartNative
- uid: DrawnUi.Controls.MauiEditor
  commentId: T:DrawnUi.Controls.MauiEditor
  parent: DrawnUi.Controls
  href: DrawnUi.Controls.MauiEditor.html
  name: MauiEditor
  nameWithType: MauiEditor
  fullName: DrawnUi.Controls.MauiEditor
- uid: DrawnUi.Controls.MauiEditorHandler
  commentId: T:DrawnUi.Controls.MauiEditorHandler
  href: DrawnUi.Controls.MauiEditorHandler.html
  name: MauiEditorHandler
  nameWithType: MauiEditorHandler
  fullName: DrawnUi.Controls.MauiEditorHandler
- uid: DrawnUi.Controls.MauiEntry
  commentId: T:DrawnUi.Controls.MauiEntry
  parent: DrawnUi.Controls
  href: DrawnUi.Controls.MauiEntry.html
  name: MauiEntry
  nameWithType: MauiEntry
  fullName: DrawnUi.Controls.MauiEntry
- uid: DrawnUi.Controls.MauiEntryHandler
  commentId: T:DrawnUi.Controls.MauiEntryHandler
  href: DrawnUi.Controls.MauiEntryHandler.html
  name: MauiEntryHandler
  nameWithType: MauiEntryHandler
  fullName: DrawnUi.Controls.MauiEntryHandler
- uid: DrawnUi.Controls.SkiaMauiEditor
  commentId: T:DrawnUi.Controls.SkiaMauiEditor
  parent: DrawnUi.Controls
  href: DrawnUi.Controls.SkiaMauiEditor.html
  name: SkiaMauiEditor
  nameWithType: SkiaMauiEditor
  fullName: DrawnUi.Controls.SkiaMauiEditor
- uid: DrawnUi.Controls.SkiaMauiEntry
  commentId: T:DrawnUi.Controls.SkiaMauiEntry
  parent: DrawnUi.Controls
  href: DrawnUi.Controls.SkiaMauiEntry.html
  name: SkiaMauiEntry
  nameWithType: SkiaMauiEntry
  fullName: DrawnUi.Controls.SkiaMauiEntry
- uid: DrawnUi.Controls.ContentWithBackdrop
  commentId: T:DrawnUi.Controls.ContentWithBackdrop
  parent: DrawnUi.Controls
  href: DrawnUi.Controls.ContentWithBackdrop.html
  name: ContentWithBackdrop
  nameWithType: ContentWithBackdrop
  fullName: DrawnUi.Controls.ContentWithBackdrop
- uid: DrawnUi.Controls.GridLayout
  commentId: T:DrawnUi.Controls.GridLayout
  href: DrawnUi.Controls.GridLayout.html
  name: GridLayout
  nameWithType: GridLayout
  fullName: DrawnUi.Controls.GridLayout
- uid: DrawnUi.Controls.SkiaDecoratedGrid
  commentId: T:DrawnUi.Controls.SkiaDecoratedGrid
  href: DrawnUi.Controls.SkiaDecoratedGrid.html
  name: SkiaDecoratedGrid
  nameWithType: SkiaDecoratedGrid
  fullName: DrawnUi.Controls.SkiaDecoratedGrid
- uid: DrawnUi.Controls.NavigationSource
  commentId: T:DrawnUi.Controls.NavigationSource
  parent: DrawnUi.Controls
  href: DrawnUi.Controls.NavigationSource.html
  name: NavigationSource
  nameWithType: NavigationSource
  fullName: DrawnUi.Controls.NavigationSource
- uid: DrawnUi.Controls.SkiaShell
  commentId: T:DrawnUi.Controls.SkiaShell
  parent: DrawnUi.Controls
  href: DrawnUi.Controls.SkiaShell.html
  name: SkiaShell
  nameWithType: SkiaShell
  fullName: DrawnUi.Controls.SkiaShell
- uid: DrawnUi.Controls.SkiaShell.IHandleGoBack
  commentId: T:DrawnUi.Controls.SkiaShell.IHandleGoBack
  parent: DrawnUi.Controls
  href: DrawnUi.Controls.SkiaShell.html
  name: SkiaShell.IHandleGoBack
  nameWithType: SkiaShell.IHandleGoBack
  fullName: DrawnUi.Controls.SkiaShell.IHandleGoBack
  spec.csharp:
  - uid: DrawnUi.Controls.SkiaShell
    name: SkiaShell
    href: DrawnUi.Controls.SkiaShell.html
  - name: .
  - uid: DrawnUi.Controls.SkiaShell.IHandleGoBack
    name: IHandleGoBack
    href: DrawnUi.Controls.SkiaShell.IHandleGoBack.html
  spec.vb:
  - uid: DrawnUi.Controls.SkiaShell
    name: SkiaShell
    href: DrawnUi.Controls.SkiaShell.html
  - name: .
  - uid: DrawnUi.Controls.SkiaShell.IHandleGoBack
    name: IHandleGoBack
    href: DrawnUi.Controls.SkiaShell.IHandleGoBack.html
- uid: DrawnUi.Controls.SkiaShell.TypeRouteFactory
  commentId: T:DrawnUi.Controls.SkiaShell.TypeRouteFactory
  parent: DrawnUi.Controls
  href: DrawnUi.Controls.SkiaShell.html
  name: SkiaShell.TypeRouteFactory
  nameWithType: SkiaShell.TypeRouteFactory
  fullName: DrawnUi.Controls.SkiaShell.TypeRouteFactory
  spec.csharp:
  - uid: DrawnUi.Controls.SkiaShell
    name: SkiaShell
    href: DrawnUi.Controls.SkiaShell.html
  - name: .
  - uid: DrawnUi.Controls.SkiaShell.TypeRouteFactory
    name: TypeRouteFactory
    href: DrawnUi.Controls.SkiaShell.TypeRouteFactory.html
  spec.vb:
  - uid: DrawnUi.Controls.SkiaShell
    name: SkiaShell
    href: DrawnUi.Controls.SkiaShell.html
  - name: .
  - uid: DrawnUi.Controls.SkiaShell.TypeRouteFactory
    name: TypeRouteFactory
    href: DrawnUi.Controls.SkiaShell.TypeRouteFactory.html
- uid: DrawnUi.Controls.SkiaShell.PageInStack
  commentId: T:DrawnUi.Controls.SkiaShell.PageInStack
  parent: DrawnUi.Controls
  href: DrawnUi.Controls.SkiaShell.html
  name: SkiaShell.PageInStack
  nameWithType: SkiaShell.PageInStack
  fullName: DrawnUi.Controls.SkiaShell.PageInStack
  spec.csharp:
  - uid: DrawnUi.Controls.SkiaShell
    name: SkiaShell
    href: DrawnUi.Controls.SkiaShell.html
  - name: .
  - uid: DrawnUi.Controls.SkiaShell.PageInStack
    name: PageInStack
    href: DrawnUi.Controls.SkiaShell.PageInStack.html
  spec.vb:
  - uid: DrawnUi.Controls.SkiaShell
    name: SkiaShell
    href: DrawnUi.Controls.SkiaShell.html
  - name: .
  - uid: DrawnUi.Controls.SkiaShell.PageInStack
    name: PageInStack
    href: DrawnUi.Controls.SkiaShell.PageInStack.html
- uid: DrawnUi.Controls.SkiaShell.ParsedRoute
  commentId: T:DrawnUi.Controls.SkiaShell.ParsedRoute
  parent: DrawnUi.Controls
  href: DrawnUi.Controls.SkiaShell.html
  name: SkiaShell.ParsedRoute
  nameWithType: SkiaShell.ParsedRoute
  fullName: DrawnUi.Controls.SkiaShell.ParsedRoute
  spec.csharp:
  - uid: DrawnUi.Controls.SkiaShell
    name: SkiaShell
    href: DrawnUi.Controls.SkiaShell.html
  - name: .
  - uid: DrawnUi.Controls.SkiaShell.ParsedRoute
    name: ParsedRoute
    href: DrawnUi.Controls.SkiaShell.ParsedRoute.html
  spec.vb:
  - uid: DrawnUi.Controls.SkiaShell
    name: SkiaShell
    href: DrawnUi.Controls.SkiaShell.html
  - name: .
  - uid: DrawnUi.Controls.SkiaShell.ParsedRoute
    name: ParsedRoute
    href: DrawnUi.Controls.SkiaShell.ParsedRoute.html
- uid: DrawnUi.Controls.SkiaShell.ShellStackChild
  commentId: T:DrawnUi.Controls.SkiaShell.ShellStackChild
  href: DrawnUi.Controls.SkiaShell.html
  name: SkiaShell.ShellStackChild
  nameWithType: SkiaShell.ShellStackChild
  fullName: DrawnUi.Controls.SkiaShell.ShellStackChild
  spec.csharp:
  - uid: DrawnUi.Controls.SkiaShell
    name: SkiaShell
    href: DrawnUi.Controls.SkiaShell.html
  - name: .
  - uid: DrawnUi.Controls.SkiaShell.ShellStackChild
    name: ShellStackChild
    href: DrawnUi.Controls.SkiaShell.ShellStackChild.html
  spec.vb:
  - uid: DrawnUi.Controls.SkiaShell
    name: SkiaShell
    href: DrawnUi.Controls.SkiaShell.html
  - name: .
  - uid: DrawnUi.Controls.SkiaShell.ShellStackChild
    name: ShellStackChild
    href: DrawnUi.Controls.SkiaShell.ShellStackChild.html
- uid: DrawnUi.Controls.SkiaShell.ShellCurrentRoute
  commentId: T:DrawnUi.Controls.SkiaShell.ShellCurrentRoute
  parent: DrawnUi.Controls
  href: DrawnUi.Controls.SkiaShell.html
  name: SkiaShell.ShellCurrentRoute
  nameWithType: SkiaShell.ShellCurrentRoute
  fullName: DrawnUi.Controls.SkiaShell.ShellCurrentRoute
  spec.csharp:
  - uid: DrawnUi.Controls.SkiaShell
    name: SkiaShell
    href: DrawnUi.Controls.SkiaShell.html
  - name: .
  - uid: DrawnUi.Controls.SkiaShell.ShellCurrentRoute
    name: ShellCurrentRoute
    href: DrawnUi.Controls.SkiaShell.ShellCurrentRoute.html
  spec.vb:
  - uid: DrawnUi.Controls.SkiaShell
    name: SkiaShell
    href: DrawnUi.Controls.SkiaShell.html
  - name: .
  - uid: DrawnUi.Controls.SkiaShell.ShellCurrentRoute
    name: ShellCurrentRoute
    href: DrawnUi.Controls.SkiaShell.ShellCurrentRoute.html
- uid: DrawnUi.Controls.SkiaShell.ModalWrapper
  commentId: T:DrawnUi.Controls.SkiaShell.ModalWrapper
  parent: DrawnUi.Controls
  href: DrawnUi.Controls.SkiaShell.html
  name: SkiaShell.ModalWrapper
  nameWithType: SkiaShell.ModalWrapper
  fullName: DrawnUi.Controls.SkiaShell.ModalWrapper
  spec.csharp:
  - uid: DrawnUi.Controls.SkiaShell
    name: SkiaShell
    href: DrawnUi.Controls.SkiaShell.html
  - name: .
  - uid: DrawnUi.Controls.SkiaShell.ModalWrapper
    name: ModalWrapper
    href: DrawnUi.Controls.SkiaShell.ModalWrapper.html
  spec.vb:
  - uid: DrawnUi.Controls.SkiaShell
    name: SkiaShell
    href: DrawnUi.Controls.SkiaShell.html
  - name: .
  - uid: DrawnUi.Controls.SkiaShell.ModalWrapper
    name: ModalWrapper
    href: DrawnUi.Controls.SkiaShell.ModalWrapper.html
- uid: DrawnUi.Controls.SkiaShell.NavigationLayer`1
  commentId: T:DrawnUi.Controls.SkiaShell.NavigationLayer`1
  href: DrawnUi.Controls.SkiaShell.html
  name: SkiaShell.NavigationLayer<T>
  nameWithType: SkiaShell.NavigationLayer<T>
  fullName: DrawnUi.Controls.SkiaShell.NavigationLayer<T>
  nameWithType.vb: SkiaShell.NavigationLayer(Of T)
  fullName.vb: DrawnUi.Controls.SkiaShell.NavigationLayer(Of T)
  name.vb: SkiaShell.NavigationLayer(Of T)
  spec.csharp:
  - uid: DrawnUi.Controls.SkiaShell
    name: SkiaShell
    href: DrawnUi.Controls.SkiaShell.html
  - name: .
  - uid: DrawnUi.Controls.SkiaShell.NavigationLayer`1
    name: NavigationLayer
    href: DrawnUi.Controls.SkiaShell.NavigationLayer-1.html
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: DrawnUi.Controls.SkiaShell
    name: SkiaShell
    href: DrawnUi.Controls.SkiaShell.html
  - name: .
  - uid: DrawnUi.Controls.SkiaShell.NavigationLayer`1
    name: NavigationLayer
    href: DrawnUi.Controls.SkiaShell.NavigationLayer-1.html
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: DrawnUi.Controls.SkiaShell.PopupWrapper
  commentId: T:DrawnUi.Controls.SkiaShell.PopupWrapper
  href: DrawnUi.Controls.SkiaShell.html
  name: SkiaShell.PopupWrapper
  nameWithType: SkiaShell.PopupWrapper
  fullName: DrawnUi.Controls.SkiaShell.PopupWrapper
  spec.csharp:
  - uid: DrawnUi.Controls.SkiaShell
    name: SkiaShell
    href: DrawnUi.Controls.SkiaShell.html
  - name: .
  - uid: DrawnUi.Controls.SkiaShell.PopupWrapper
    name: PopupWrapper
    href: DrawnUi.Controls.SkiaShell.PopupWrapper.html
  spec.vb:
  - uid: DrawnUi.Controls.SkiaShell
    name: SkiaShell
    href: DrawnUi.Controls.SkiaShell.html
  - name: .
  - uid: DrawnUi.Controls.SkiaShell.PopupWrapper
    name: PopupWrapper
    href: DrawnUi.Controls.SkiaShell.PopupWrapper.html
- uid: DrawnUi.Controls.SkiaShellNavigatedArgs
  commentId: T:DrawnUi.Controls.SkiaShellNavigatedArgs
  parent: DrawnUi.Controls
  href: DrawnUi.Controls.SkiaShellNavigatedArgs.html
  name: SkiaShellNavigatedArgs
  nameWithType: SkiaShellNavigatedArgs
  fullName: DrawnUi.Controls.SkiaShellNavigatedArgs
- uid: DrawnUi.Controls.SkiaShellNavigatingArgs
  commentId: T:DrawnUi.Controls.SkiaShellNavigatingArgs
  parent: DrawnUi.Controls
  href: DrawnUi.Controls.SkiaShellNavigatingArgs.html
  name: SkiaShellNavigatingArgs
  nameWithType: SkiaShellNavigatingArgs
  fullName: DrawnUi.Controls.SkiaShellNavigatingArgs
- uid: DrawnUi.Controls.ScrollPickerLabelContainer
  commentId: T:DrawnUi.Controls.ScrollPickerLabelContainer
  href: DrawnUi.Controls.ScrollPickerLabelContainer.html
  name: ScrollPickerLabelContainer
  nameWithType: ScrollPickerLabelContainer
  fullName: DrawnUi.Controls.ScrollPickerLabelContainer
- uid: DrawnUi.Controls.ScrollPickerWheel
  commentId: T:DrawnUi.Controls.ScrollPickerWheel
  href: DrawnUi.Controls.ScrollPickerWheel.html
  name: ScrollPickerWheel
  nameWithType: ScrollPickerWheel
  fullName: DrawnUi.Controls.ScrollPickerWheel
- uid: DrawnUi.Controls.AnimatedFramesRenderer
  commentId: T:DrawnUi.Controls.AnimatedFramesRenderer
  parent: DrawnUi.Controls
  href: DrawnUi.Controls.AnimatedFramesRenderer.html
  name: AnimatedFramesRenderer
  nameWithType: AnimatedFramesRenderer
  fullName: DrawnUi.Controls.AnimatedFramesRenderer
- uid: DrawnUi.Controls.GifAnimation
  commentId: T:DrawnUi.Controls.GifAnimation
  parent: DrawnUi.Controls
  href: DrawnUi.Controls.GifAnimation.html
  name: GifAnimation
  nameWithType: GifAnimation
  fullName: DrawnUi.Controls.GifAnimation
- uid: DrawnUi.Controls.SkiaGif
  commentId: T:DrawnUi.Controls.SkiaGif
  parent: DrawnUi.Controls
  href: DrawnUi.Controls.SkiaGif.html
  name: SkiaGif
  nameWithType: SkiaGif
  fullName: DrawnUi.Controls.SkiaGif
- uid: DrawnUi.Controls.SkiaLottie
  commentId: T:DrawnUi.Controls.SkiaLottie
  parent: DrawnUi.Controls
  href: DrawnUi.Controls.SkiaLottie.html
  name: SkiaLottie
  nameWithType: SkiaLottie
  fullName: DrawnUi.Controls.SkiaLottie
- uid: DrawnUi.Controls.SkiaLottie.ColorEqualityComparer
  commentId: T:DrawnUi.Controls.SkiaLottie.ColorEqualityComparer
  href: DrawnUi.Controls.SkiaLottie.html
  name: SkiaLottie.ColorEqualityComparer
  nameWithType: SkiaLottie.ColorEqualityComparer
  fullName: DrawnUi.Controls.SkiaLottie.ColorEqualityComparer
  spec.csharp:
  - uid: DrawnUi.Controls.SkiaLottie
    name: SkiaLottie
    href: DrawnUi.Controls.SkiaLottie.html
  - name: .
  - uid: DrawnUi.Controls.SkiaLottie.ColorEqualityComparer
    name: ColorEqualityComparer
    href: DrawnUi.Controls.SkiaLottie.ColorEqualityComparer.html
  spec.vb:
  - uid: DrawnUi.Controls.SkiaLottie
    name: SkiaLottie
    href: DrawnUi.Controls.SkiaLottie.html
  - name: .
  - uid: DrawnUi.Controls.SkiaLottie.ColorEqualityComparer
    name: ColorEqualityComparer
    href: DrawnUi.Controls.SkiaLottie.ColorEqualityComparer.html
- uid: DrawnUi.Controls.SkiaMediaImage
  commentId: T:DrawnUi.Controls.SkiaMediaImage
  href: DrawnUi.Controls.SkiaMediaImage.html
  name: SkiaMediaImage
  nameWithType: SkiaMediaImage
  fullName: DrawnUi.Controls.SkiaMediaImage
- uid: DrawnUi.Controls.SkiaSprite
  commentId: T:DrawnUi.Controls.SkiaSprite
  href: DrawnUi.Controls.SkiaSprite.html
  name: SkiaSprite
  nameWithType: SkiaSprite
  fullName: DrawnUi.Controls.SkiaSprite
- uid: DrawnUi.Controls.ISkiaRadioButton
  commentId: T:DrawnUi.Controls.ISkiaRadioButton
  parent: DrawnUi.Controls
  href: DrawnUi.Controls.ISkiaRadioButton.html
  name: ISkiaRadioButton
  nameWithType: ISkiaRadioButton
  fullName: DrawnUi.Controls.ISkiaRadioButton
- uid: DrawnUi.Controls.RadioButtonGroupManager
  commentId: T:DrawnUi.Controls.RadioButtonGroupManager
  parent: DrawnUi.Controls
  href: DrawnUi.Controls.RadioButtonGroupManager.html
  name: RadioButtonGroupManager
  nameWithType: RadioButtonGroupManager
  fullName: DrawnUi.Controls.RadioButtonGroupManager
- uid: DrawnUi.Controls.SkiaRadioButton
  commentId: T:DrawnUi.Controls.SkiaRadioButton
  href: DrawnUi.Controls.SkiaRadioButton.html
  name: SkiaRadioButton
  nameWithType: SkiaRadioButton
  fullName: DrawnUi.Controls.SkiaRadioButton
- uid: DrawnUi.Controls.SkiaTabsSelector
  commentId: T:DrawnUi.Controls.SkiaTabsSelector
  href: DrawnUi.Controls.SkiaTabsSelector.html
  name: SkiaTabsSelector
  nameWithType: SkiaTabsSelector
  fullName: DrawnUi.Controls.SkiaTabsSelector
- uid: DrawnUi.Controls.SkiaTabsSelector.TabEntry
  commentId: T:DrawnUi.Controls.SkiaTabsSelector.TabEntry
  href: DrawnUi.Controls.SkiaTabsSelector.html
  name: SkiaTabsSelector.TabEntry
  nameWithType: SkiaTabsSelector.TabEntry
  fullName: DrawnUi.Controls.SkiaTabsSelector.TabEntry
  spec.csharp:
  - uid: DrawnUi.Controls.SkiaTabsSelector
    name: SkiaTabsSelector
    href: DrawnUi.Controls.SkiaTabsSelector.html
  - name: .
  - uid: DrawnUi.Controls.SkiaTabsSelector.TabEntry
    name: TabEntry
    href: DrawnUi.Controls.SkiaTabsSelector.TabEntry.html
  spec.vb:
  - uid: DrawnUi.Controls.SkiaTabsSelector
    name: SkiaTabsSelector
    href: DrawnUi.Controls.SkiaTabsSelector.html
  - name: .
  - uid: DrawnUi.Controls.SkiaTabsSelector.TabEntry
    name: TabEntry
    href: DrawnUi.Controls.SkiaTabsSelector.TabEntry.html
- uid: DrawnUi.Controls.SkiaViewSwitcher
  commentId: T:DrawnUi.Controls.SkiaViewSwitcher
  parent: DrawnUi.Controls
  href: DrawnUi.Controls.SkiaViewSwitcher.html
  name: SkiaViewSwitcher
  nameWithType: SkiaViewSwitcher
  fullName: DrawnUi.Controls.SkiaViewSwitcher
- uid: DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry
  commentId: T:DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry
  parent: DrawnUi.Controls
  href: DrawnUi.Controls.SkiaViewSwitcher.html
  name: SkiaViewSwitcher.NavigationStackEntry
  nameWithType: SkiaViewSwitcher.NavigationStackEntry
  fullName: DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry
  spec.csharp:
  - uid: DrawnUi.Controls.SkiaViewSwitcher
    name: SkiaViewSwitcher
    href: DrawnUi.Controls.SkiaViewSwitcher.html
  - name: .
  - uid: DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry
    name: NavigationStackEntry
    href: DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry.html
  spec.vb:
  - uid: DrawnUi.Controls.SkiaViewSwitcher
    name: SkiaViewSwitcher
    href: DrawnUi.Controls.SkiaViewSwitcher.html
  - name: .
  - uid: DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry
    name: NavigationStackEntry
    href: DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry.html
- uid: DrawnUi.Controls
  commentId: N:DrawnUi.Controls
  href: DrawnUi.html
  name: DrawnUi.Controls
  nameWithType: DrawnUi.Controls
  fullName: DrawnUi.Controls
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Controls
    name: Controls
    href: DrawnUi.Controls.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Controls
    name: Controls
    href: DrawnUi.Controls.html
