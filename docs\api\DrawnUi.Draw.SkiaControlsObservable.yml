### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.SkiaControlsObservable
  commentId: T:DrawnUi.Draw.SkiaControlsObservable
  id: SkiaControlsObservable
  parent: DrawnUi.Draw
  children: []
  langs:
  - csharp
  - vb
  name: SkiaControlsObservable
  nameWithType: SkiaControlsObservable
  fullName: DrawnUi.Draw.SkiaControlsObservable
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/SkiaControlsObservable.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: SkiaControlsObservable
    path: ../src/Maui/DrawnUi/Draw/SkiaControlsObservable.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: 'public class SkiaControlsObservable : ObservableCollection<SkiaControl>, IList<SkiaControl>, ICollection<SkiaControl>, IReadOnlyList<SkiaControl>, IReadOnlyCollection<SkiaControl>, IEnumerable<SkiaControl>, IList, ICollection, IEnumerable, INotifyCollectionChanged, INotifyPropertyChanged'
    content.vb: Public Class SkiaControlsObservable Inherits ObservableCollection(Of SkiaControl) Implements IList(Of SkiaControl), ICollection(Of SkiaControl), IReadOnlyList(Of SkiaControl), IReadOnlyCollection(Of SkiaControl), IEnumerable(Of SkiaControl), IList, ICollection, IEnumerable, INotifyCollectionChanged, INotifyPropertyChanged
  inheritance:
  - System.Object
  - System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}
  - System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}
  implements:
  - System.Collections.Generic.IList{DrawnUi.Draw.SkiaControl}
  - System.Collections.Generic.ICollection{DrawnUi.Draw.SkiaControl}
  - System.Collections.Generic.IReadOnlyList{DrawnUi.Draw.SkiaControl}
  - System.Collections.Generic.IReadOnlyCollection{DrawnUi.Draw.SkiaControl}
  - System.Collections.Generic.IEnumerable{DrawnUi.Draw.SkiaControl}
  - System.Collections.IList
  - System.Collections.ICollection
  - System.Collections.IEnumerable
  - System.Collections.Specialized.INotifyCollectionChanged
  - System.ComponentModel.INotifyPropertyChanged
  inheritedMembers:
  - System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.BlockReentrancy
  - System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.CheckReentrancy
  - System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.ClearItems
  - System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.InsertItem(System.Int32,DrawnUi.Draw.SkiaControl)
  - System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.Move(System.Int32,System.Int32)
  - System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.MoveItem(System.Int32,System.Int32)
  - System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.OnCollectionChanged(System.Collections.Specialized.NotifyCollectionChangedEventArgs)
  - System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.OnPropertyChanged(System.ComponentModel.PropertyChangedEventArgs)
  - System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.RemoveItem(System.Int32)
  - System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.SetItem(System.Int32,DrawnUi.Draw.SkiaControl)
  - System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.CollectionChanged
  - System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.PropertyChanged
  - System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.Add(DrawnUi.Draw.SkiaControl)
  - System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.Clear
  - System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.Contains(DrawnUi.Draw.SkiaControl)
  - System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.CopyTo(DrawnUi.Draw.SkiaControl[],System.Int32)
  - System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.GetEnumerator
  - System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.IndexOf(DrawnUi.Draw.SkiaControl)
  - System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.Insert(System.Int32,DrawnUi.Draw.SkiaControl)
  - System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.Remove(DrawnUi.Draw.SkiaControl)
  - System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.RemoveAt(System.Int32)
  - System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.Count
  - System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.Item(System.Int32)
  - System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.Items
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}
  commentId: T:System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}
  parent: System.Collections.ObjectModel
  definition: System.Collections.ObjectModel.Collection`1
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1
  name: Collection<SkiaControl>
  nameWithType: Collection<SkiaControl>
  fullName: System.Collections.ObjectModel.Collection<DrawnUi.Draw.SkiaControl>
  nameWithType.vb: Collection(Of SkiaControl)
  fullName.vb: System.Collections.ObjectModel.Collection(Of DrawnUi.Draw.SkiaControl)
  name.vb: Collection(Of SkiaControl)
  spec.csharp:
  - uid: System.Collections.ObjectModel.Collection`1
    name: Collection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1
  - name: <
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: '>'
  spec.vb:
  - uid: System.Collections.ObjectModel.Collection`1
    name: Collection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
- uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}
  commentId: T:System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}
  parent: System.Collections.ObjectModel
  definition: System.Collections.ObjectModel.ObservableCollection`1
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1
  name: ObservableCollection<SkiaControl>
  nameWithType: ObservableCollection<SkiaControl>
  fullName: System.Collections.ObjectModel.ObservableCollection<DrawnUi.Draw.SkiaControl>
  nameWithType.vb: ObservableCollection(Of SkiaControl)
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of DrawnUi.Draw.SkiaControl)
  name.vb: ObservableCollection(Of SkiaControl)
  spec.csharp:
  - uid: System.Collections.ObjectModel.ObservableCollection`1
    name: ObservableCollection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1
  - name: <
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: '>'
  spec.vb:
  - uid: System.Collections.ObjectModel.ObservableCollection`1
    name: ObservableCollection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
- uid: System.Collections.Generic.IList{DrawnUi.Draw.SkiaControl}
  commentId: T:System.Collections.Generic.IList{DrawnUi.Draw.SkiaControl}
  parent: System.Collections.Generic
  definition: System.Collections.Generic.IList`1
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ilist-1
  name: IList<SkiaControl>
  nameWithType: IList<SkiaControl>
  fullName: System.Collections.Generic.IList<DrawnUi.Draw.SkiaControl>
  nameWithType.vb: IList(Of SkiaControl)
  fullName.vb: System.Collections.Generic.IList(Of DrawnUi.Draw.SkiaControl)
  name.vb: IList(Of SkiaControl)
  spec.csharp:
  - uid: System.Collections.Generic.IList`1
    name: IList
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ilist-1
  - name: <
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.IList`1
    name: IList
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ilist-1
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
- uid: System.Collections.Generic.ICollection{DrawnUi.Draw.SkiaControl}
  commentId: T:System.Collections.Generic.ICollection{DrawnUi.Draw.SkiaControl}
  parent: System.Collections.Generic
  definition: System.Collections.Generic.ICollection`1
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.icollection-1
  name: ICollection<SkiaControl>
  nameWithType: ICollection<SkiaControl>
  fullName: System.Collections.Generic.ICollection<DrawnUi.Draw.SkiaControl>
  nameWithType.vb: ICollection(Of SkiaControl)
  fullName.vb: System.Collections.Generic.ICollection(Of DrawnUi.Draw.SkiaControl)
  name.vb: ICollection(Of SkiaControl)
  spec.csharp:
  - uid: System.Collections.Generic.ICollection`1
    name: ICollection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.icollection-1
  - name: <
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.ICollection`1
    name: ICollection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.icollection-1
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
- uid: System.Collections.Generic.IReadOnlyList{DrawnUi.Draw.SkiaControl}
  commentId: T:System.Collections.Generic.IReadOnlyList{DrawnUi.Draw.SkiaControl}
  parent: System.Collections.Generic
  definition: System.Collections.Generic.IReadOnlyList`1
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlylist-1
  name: IReadOnlyList<SkiaControl>
  nameWithType: IReadOnlyList<SkiaControl>
  fullName: System.Collections.Generic.IReadOnlyList<DrawnUi.Draw.SkiaControl>
  nameWithType.vb: IReadOnlyList(Of SkiaControl)
  fullName.vb: System.Collections.Generic.IReadOnlyList(Of DrawnUi.Draw.SkiaControl)
  name.vb: IReadOnlyList(Of SkiaControl)
  spec.csharp:
  - uid: System.Collections.Generic.IReadOnlyList`1
    name: IReadOnlyList
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlylist-1
  - name: <
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.IReadOnlyList`1
    name: IReadOnlyList
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlylist-1
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
- uid: System.Collections.Generic.IReadOnlyCollection{DrawnUi.Draw.SkiaControl}
  commentId: T:System.Collections.Generic.IReadOnlyCollection{DrawnUi.Draw.SkiaControl}
  parent: System.Collections.Generic
  definition: System.Collections.Generic.IReadOnlyCollection`1
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlycollection-1
  name: IReadOnlyCollection<SkiaControl>
  nameWithType: IReadOnlyCollection<SkiaControl>
  fullName: System.Collections.Generic.IReadOnlyCollection<DrawnUi.Draw.SkiaControl>
  nameWithType.vb: IReadOnlyCollection(Of SkiaControl)
  fullName.vb: System.Collections.Generic.IReadOnlyCollection(Of DrawnUi.Draw.SkiaControl)
  name.vb: IReadOnlyCollection(Of SkiaControl)
  spec.csharp:
  - uid: System.Collections.Generic.IReadOnlyCollection`1
    name: IReadOnlyCollection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlycollection-1
  - name: <
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.IReadOnlyCollection`1
    name: IReadOnlyCollection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlycollection-1
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
- uid: System.Collections.Generic.IEnumerable{DrawnUi.Draw.SkiaControl}
  commentId: T:System.Collections.Generic.IEnumerable{DrawnUi.Draw.SkiaControl}
  parent: System.Collections.Generic
  definition: System.Collections.Generic.IEnumerable`1
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1
  name: IEnumerable<SkiaControl>
  nameWithType: IEnumerable<SkiaControl>
  fullName: System.Collections.Generic.IEnumerable<DrawnUi.Draw.SkiaControl>
  nameWithType.vb: IEnumerable(Of SkiaControl)
  fullName.vb: System.Collections.Generic.IEnumerable(Of DrawnUi.Draw.SkiaControl)
  name.vb: IEnumerable(Of SkiaControl)
  spec.csharp:
  - uid: System.Collections.Generic.IEnumerable`1
    name: IEnumerable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1
  - name: <
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.IEnumerable`1
    name: IEnumerable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
- uid: System.Collections.IList
  commentId: T:System.Collections.IList
  parent: System.Collections
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.ilist
  name: IList
  nameWithType: IList
  fullName: System.Collections.IList
- uid: System.Collections.ICollection
  commentId: T:System.Collections.ICollection
  parent: System.Collections
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.icollection
  name: ICollection
  nameWithType: ICollection
  fullName: System.Collections.ICollection
- uid: System.Collections.IEnumerable
  commentId: T:System.Collections.IEnumerable
  parent: System.Collections
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.ienumerable
  name: IEnumerable
  nameWithType: IEnumerable
  fullName: System.Collections.IEnumerable
- uid: System.Collections.Specialized.INotifyCollectionChanged
  commentId: T:System.Collections.Specialized.INotifyCollectionChanged
  parent: System.Collections.Specialized
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.specialized.inotifycollectionchanged
  name: INotifyCollectionChanged
  nameWithType: INotifyCollectionChanged
  fullName: System.Collections.Specialized.INotifyCollectionChanged
- uid: System.ComponentModel.INotifyPropertyChanged
  commentId: T:System.ComponentModel.INotifyPropertyChanged
  parent: System.ComponentModel
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.inotifypropertychanged
  name: INotifyPropertyChanged
  nameWithType: INotifyPropertyChanged
  fullName: System.ComponentModel.INotifyPropertyChanged
- uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.BlockReentrancy
  commentId: M:System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.BlockReentrancy
  parent: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}
  definition: System.Collections.ObjectModel.ObservableCollection`1.BlockReentrancy
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.blockreentrancy
  name: BlockReentrancy()
  nameWithType: ObservableCollection<SkiaControl>.BlockReentrancy()
  fullName: System.Collections.ObjectModel.ObservableCollection<DrawnUi.Draw.SkiaControl>.BlockReentrancy()
  nameWithType.vb: ObservableCollection(Of SkiaControl).BlockReentrancy()
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of DrawnUi.Draw.SkiaControl).BlockReentrancy()
  spec.csharp:
  - uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.BlockReentrancy
    name: BlockReentrancy
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.blockreentrancy
  - name: (
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.BlockReentrancy
    name: BlockReentrancy
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.blockreentrancy
  - name: (
  - name: )
- uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.CheckReentrancy
  commentId: M:System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.CheckReentrancy
  parent: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}
  definition: System.Collections.ObjectModel.ObservableCollection`1.CheckReentrancy
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.checkreentrancy
  name: CheckReentrancy()
  nameWithType: ObservableCollection<SkiaControl>.CheckReentrancy()
  fullName: System.Collections.ObjectModel.ObservableCollection<DrawnUi.Draw.SkiaControl>.CheckReentrancy()
  nameWithType.vb: ObservableCollection(Of SkiaControl).CheckReentrancy()
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of DrawnUi.Draw.SkiaControl).CheckReentrancy()
  spec.csharp:
  - uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.CheckReentrancy
    name: CheckReentrancy
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.checkreentrancy
  - name: (
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.CheckReentrancy
    name: CheckReentrancy
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.checkreentrancy
  - name: (
  - name: )
- uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.ClearItems
  commentId: M:System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.ClearItems
  parent: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}
  definition: System.Collections.ObjectModel.ObservableCollection`1.ClearItems
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.clearitems
  name: ClearItems()
  nameWithType: ObservableCollection<SkiaControl>.ClearItems()
  fullName: System.Collections.ObjectModel.ObservableCollection<DrawnUi.Draw.SkiaControl>.ClearItems()
  nameWithType.vb: ObservableCollection(Of SkiaControl).ClearItems()
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of DrawnUi.Draw.SkiaControl).ClearItems()
  spec.csharp:
  - uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.ClearItems
    name: ClearItems
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.clearitems
  - name: (
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.ClearItems
    name: ClearItems
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.clearitems
  - name: (
  - name: )
- uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.InsertItem(System.Int32,DrawnUi.Draw.SkiaControl)
  commentId: M:System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.InsertItem(System.Int32,DrawnUi.Draw.SkiaControl)
  parent: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}
  definition: System.Collections.ObjectModel.ObservableCollection`1.InsertItem(System.Int32,`0)
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.insertitem
  name: InsertItem(int, SkiaControl)
  nameWithType: ObservableCollection<SkiaControl>.InsertItem(int, SkiaControl)
  fullName: System.Collections.ObjectModel.ObservableCollection<DrawnUi.Draw.SkiaControl>.InsertItem(int, DrawnUi.Draw.SkiaControl)
  nameWithType.vb: ObservableCollection(Of SkiaControl).InsertItem(Integer, SkiaControl)
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of DrawnUi.Draw.SkiaControl).InsertItem(Integer, DrawnUi.Draw.SkiaControl)
  name.vb: InsertItem(Integer, SkiaControl)
  spec.csharp:
  - uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.InsertItem(System.Int32,DrawnUi.Draw.SkiaControl)
    name: InsertItem
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.insertitem
  - name: (
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.InsertItem(System.Int32,DrawnUi.Draw.SkiaControl)
    name: InsertItem
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.insertitem
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
- uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.Move(System.Int32,System.Int32)
  commentId: M:System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.Move(System.Int32,System.Int32)
  parent: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}
  definition: System.Collections.ObjectModel.ObservableCollection`1.Move(System.Int32,System.Int32)
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.move
  name: Move(int, int)
  nameWithType: ObservableCollection<SkiaControl>.Move(int, int)
  fullName: System.Collections.ObjectModel.ObservableCollection<DrawnUi.Draw.SkiaControl>.Move(int, int)
  nameWithType.vb: ObservableCollection(Of SkiaControl).Move(Integer, Integer)
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of DrawnUi.Draw.SkiaControl).Move(Integer, Integer)
  name.vb: Move(Integer, Integer)
  spec.csharp:
  - uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.Move(System.Int32,System.Int32)
    name: Move
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.move
  - name: (
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.Move(System.Int32,System.Int32)
    name: Move
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.move
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.MoveItem(System.Int32,System.Int32)
  commentId: M:System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.MoveItem(System.Int32,System.Int32)
  parent: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}
  definition: System.Collections.ObjectModel.ObservableCollection`1.MoveItem(System.Int32,System.Int32)
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.moveitem
  name: MoveItem(int, int)
  nameWithType: ObservableCollection<SkiaControl>.MoveItem(int, int)
  fullName: System.Collections.ObjectModel.ObservableCollection<DrawnUi.Draw.SkiaControl>.MoveItem(int, int)
  nameWithType.vb: ObservableCollection(Of SkiaControl).MoveItem(Integer, Integer)
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of DrawnUi.Draw.SkiaControl).MoveItem(Integer, Integer)
  name.vb: MoveItem(Integer, Integer)
  spec.csharp:
  - uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.MoveItem(System.Int32,System.Int32)
    name: MoveItem
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.moveitem
  - name: (
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.MoveItem(System.Int32,System.Int32)
    name: MoveItem
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.moveitem
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.OnCollectionChanged(System.Collections.Specialized.NotifyCollectionChangedEventArgs)
  commentId: M:System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.OnCollectionChanged(System.Collections.Specialized.NotifyCollectionChangedEventArgs)
  parent: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}
  definition: System.Collections.ObjectModel.ObservableCollection`1.OnCollectionChanged(System.Collections.Specialized.NotifyCollectionChangedEventArgs)
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.oncollectionchanged
  name: OnCollectionChanged(NotifyCollectionChangedEventArgs)
  nameWithType: ObservableCollection<SkiaControl>.OnCollectionChanged(NotifyCollectionChangedEventArgs)
  fullName: System.Collections.ObjectModel.ObservableCollection<DrawnUi.Draw.SkiaControl>.OnCollectionChanged(System.Collections.Specialized.NotifyCollectionChangedEventArgs)
  nameWithType.vb: ObservableCollection(Of SkiaControl).OnCollectionChanged(NotifyCollectionChangedEventArgs)
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of DrawnUi.Draw.SkiaControl).OnCollectionChanged(System.Collections.Specialized.NotifyCollectionChangedEventArgs)
  spec.csharp:
  - uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.OnCollectionChanged(System.Collections.Specialized.NotifyCollectionChangedEventArgs)
    name: OnCollectionChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.oncollectionchanged
  - name: (
  - uid: System.Collections.Specialized.NotifyCollectionChangedEventArgs
    name: NotifyCollectionChangedEventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.specialized.notifycollectionchangedeventargs
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.OnCollectionChanged(System.Collections.Specialized.NotifyCollectionChangedEventArgs)
    name: OnCollectionChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.oncollectionchanged
  - name: (
  - uid: System.Collections.Specialized.NotifyCollectionChangedEventArgs
    name: NotifyCollectionChangedEventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.specialized.notifycollectionchangedeventargs
  - name: )
- uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.OnPropertyChanged(System.ComponentModel.PropertyChangedEventArgs)
  commentId: M:System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.OnPropertyChanged(System.ComponentModel.PropertyChangedEventArgs)
  parent: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}
  definition: System.Collections.ObjectModel.ObservableCollection`1.OnPropertyChanged(System.ComponentModel.PropertyChangedEventArgs)
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.onpropertychanged
  name: OnPropertyChanged(PropertyChangedEventArgs)
  nameWithType: ObservableCollection<SkiaControl>.OnPropertyChanged(PropertyChangedEventArgs)
  fullName: System.Collections.ObjectModel.ObservableCollection<DrawnUi.Draw.SkiaControl>.OnPropertyChanged(System.ComponentModel.PropertyChangedEventArgs)
  nameWithType.vb: ObservableCollection(Of SkiaControl).OnPropertyChanged(PropertyChangedEventArgs)
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of DrawnUi.Draw.SkiaControl).OnPropertyChanged(System.ComponentModel.PropertyChangedEventArgs)
  spec.csharp:
  - uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.OnPropertyChanged(System.ComponentModel.PropertyChangedEventArgs)
    name: OnPropertyChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.onpropertychanged
  - name: (
  - uid: System.ComponentModel.PropertyChangedEventArgs
    name: PropertyChangedEventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.propertychangedeventargs
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.OnPropertyChanged(System.ComponentModel.PropertyChangedEventArgs)
    name: OnPropertyChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.onpropertychanged
  - name: (
  - uid: System.ComponentModel.PropertyChangedEventArgs
    name: PropertyChangedEventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.propertychangedeventargs
  - name: )
- uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.RemoveItem(System.Int32)
  commentId: M:System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.RemoveItem(System.Int32)
  parent: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}
  definition: System.Collections.ObjectModel.ObservableCollection`1.RemoveItem(System.Int32)
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.removeitem
  name: RemoveItem(int)
  nameWithType: ObservableCollection<SkiaControl>.RemoveItem(int)
  fullName: System.Collections.ObjectModel.ObservableCollection<DrawnUi.Draw.SkiaControl>.RemoveItem(int)
  nameWithType.vb: ObservableCollection(Of SkiaControl).RemoveItem(Integer)
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of DrawnUi.Draw.SkiaControl).RemoveItem(Integer)
  name.vb: RemoveItem(Integer)
  spec.csharp:
  - uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.RemoveItem(System.Int32)
    name: RemoveItem
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.removeitem
  - name: (
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.RemoveItem(System.Int32)
    name: RemoveItem
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.removeitem
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.SetItem(System.Int32,DrawnUi.Draw.SkiaControl)
  commentId: M:System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.SetItem(System.Int32,DrawnUi.Draw.SkiaControl)
  parent: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}
  definition: System.Collections.ObjectModel.ObservableCollection`1.SetItem(System.Int32,`0)
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.setitem
  name: SetItem(int, SkiaControl)
  nameWithType: ObservableCollection<SkiaControl>.SetItem(int, SkiaControl)
  fullName: System.Collections.ObjectModel.ObservableCollection<DrawnUi.Draw.SkiaControl>.SetItem(int, DrawnUi.Draw.SkiaControl)
  nameWithType.vb: ObservableCollection(Of SkiaControl).SetItem(Integer, SkiaControl)
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of DrawnUi.Draw.SkiaControl).SetItem(Integer, DrawnUi.Draw.SkiaControl)
  name.vb: SetItem(Integer, SkiaControl)
  spec.csharp:
  - uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.SetItem(System.Int32,DrawnUi.Draw.SkiaControl)
    name: SetItem
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.setitem
  - name: (
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.SetItem(System.Int32,DrawnUi.Draw.SkiaControl)
    name: SetItem
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.setitem
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
- uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.CollectionChanged
  commentId: E:System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.CollectionChanged
  parent: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}
  definition: System.Collections.ObjectModel.ObservableCollection`1.CollectionChanged
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.collectionchanged
  name: CollectionChanged
  nameWithType: ObservableCollection<SkiaControl>.CollectionChanged
  fullName: System.Collections.ObjectModel.ObservableCollection<DrawnUi.Draw.SkiaControl>.CollectionChanged
  nameWithType.vb: ObservableCollection(Of SkiaControl).CollectionChanged
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of DrawnUi.Draw.SkiaControl).CollectionChanged
- uid: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.PropertyChanged
  commentId: E:System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}.PropertyChanged
  parent: System.Collections.ObjectModel.ObservableCollection{DrawnUi.Draw.SkiaControl}
  definition: System.Collections.ObjectModel.ObservableCollection`1.PropertyChanged
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.propertychanged
  name: PropertyChanged
  nameWithType: ObservableCollection<SkiaControl>.PropertyChanged
  fullName: System.Collections.ObjectModel.ObservableCollection<DrawnUi.Draw.SkiaControl>.PropertyChanged
  nameWithType.vb: ObservableCollection(Of SkiaControl).PropertyChanged
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of DrawnUi.Draw.SkiaControl).PropertyChanged
- uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.Add(DrawnUi.Draw.SkiaControl)
  commentId: M:System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.Add(DrawnUi.Draw.SkiaControl)
  parent: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}
  definition: System.Collections.ObjectModel.Collection`1.Add(`0)
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.add
  name: Add(SkiaControl)
  nameWithType: Collection<SkiaControl>.Add(SkiaControl)
  fullName: System.Collections.ObjectModel.Collection<DrawnUi.Draw.SkiaControl>.Add(DrawnUi.Draw.SkiaControl)
  nameWithType.vb: Collection(Of SkiaControl).Add(SkiaControl)
  fullName.vb: System.Collections.ObjectModel.Collection(Of DrawnUi.Draw.SkiaControl).Add(DrawnUi.Draw.SkiaControl)
  spec.csharp:
  - uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.Add(DrawnUi.Draw.SkiaControl)
    name: Add
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.add
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.Add(DrawnUi.Draw.SkiaControl)
    name: Add
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.add
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
- uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.Clear
  commentId: M:System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.Clear
  parent: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}
  definition: System.Collections.ObjectModel.Collection`1.Clear
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.clear
  name: Clear()
  nameWithType: Collection<SkiaControl>.Clear()
  fullName: System.Collections.ObjectModel.Collection<DrawnUi.Draw.SkiaControl>.Clear()
  nameWithType.vb: Collection(Of SkiaControl).Clear()
  fullName.vb: System.Collections.ObjectModel.Collection(Of DrawnUi.Draw.SkiaControl).Clear()
  spec.csharp:
  - uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.Clear
    name: Clear
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.clear
  - name: (
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.Clear
    name: Clear
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.clear
  - name: (
  - name: )
- uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.Contains(DrawnUi.Draw.SkiaControl)
  commentId: M:System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.Contains(DrawnUi.Draw.SkiaControl)
  parent: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}
  definition: System.Collections.ObjectModel.Collection`1.Contains(`0)
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.contains
  name: Contains(SkiaControl)
  nameWithType: Collection<SkiaControl>.Contains(SkiaControl)
  fullName: System.Collections.ObjectModel.Collection<DrawnUi.Draw.SkiaControl>.Contains(DrawnUi.Draw.SkiaControl)
  nameWithType.vb: Collection(Of SkiaControl).Contains(SkiaControl)
  fullName.vb: System.Collections.ObjectModel.Collection(Of DrawnUi.Draw.SkiaControl).Contains(DrawnUi.Draw.SkiaControl)
  spec.csharp:
  - uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.Contains(DrawnUi.Draw.SkiaControl)
    name: Contains
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.contains
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.Contains(DrawnUi.Draw.SkiaControl)
    name: Contains
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.contains
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
- uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.CopyTo(DrawnUi.Draw.SkiaControl[],System.Int32)
  commentId: M:System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.CopyTo(DrawnUi.Draw.SkiaControl[],System.Int32)
  parent: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}
  definition: System.Collections.ObjectModel.Collection`1.CopyTo(`0[],System.Int32)
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.copyto
  name: CopyTo(SkiaControl[], int)
  nameWithType: Collection<SkiaControl>.CopyTo(SkiaControl[], int)
  fullName: System.Collections.ObjectModel.Collection<DrawnUi.Draw.SkiaControl>.CopyTo(DrawnUi.Draw.SkiaControl[], int)
  nameWithType.vb: Collection(Of SkiaControl).CopyTo(SkiaControl(), Integer)
  fullName.vb: System.Collections.ObjectModel.Collection(Of DrawnUi.Draw.SkiaControl).CopyTo(DrawnUi.Draw.SkiaControl(), Integer)
  name.vb: CopyTo(SkiaControl(), Integer)
  spec.csharp:
  - uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.CopyTo(DrawnUi.Draw.SkiaControl[],System.Int32)
    name: CopyTo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.copyto
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: '['
  - name: ']'
  - name: ','
  - name: " "
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.CopyTo(DrawnUi.Draw.SkiaControl[],System.Int32)
    name: CopyTo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.copyto
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: (
  - name: )
  - name: ','
  - name: " "
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.GetEnumerator
  commentId: M:System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.GetEnumerator
  parent: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}
  definition: System.Collections.ObjectModel.Collection`1.GetEnumerator
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.getenumerator
  name: GetEnumerator()
  nameWithType: Collection<SkiaControl>.GetEnumerator()
  fullName: System.Collections.ObjectModel.Collection<DrawnUi.Draw.SkiaControl>.GetEnumerator()
  nameWithType.vb: Collection(Of SkiaControl).GetEnumerator()
  fullName.vb: System.Collections.ObjectModel.Collection(Of DrawnUi.Draw.SkiaControl).GetEnumerator()
  spec.csharp:
  - uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.GetEnumerator
    name: GetEnumerator
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.getenumerator
  - name: (
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.GetEnumerator
    name: GetEnumerator
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.getenumerator
  - name: (
  - name: )
- uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.IndexOf(DrawnUi.Draw.SkiaControl)
  commentId: M:System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.IndexOf(DrawnUi.Draw.SkiaControl)
  parent: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}
  definition: System.Collections.ObjectModel.Collection`1.IndexOf(`0)
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.indexof
  name: IndexOf(SkiaControl)
  nameWithType: Collection<SkiaControl>.IndexOf(SkiaControl)
  fullName: System.Collections.ObjectModel.Collection<DrawnUi.Draw.SkiaControl>.IndexOf(DrawnUi.Draw.SkiaControl)
  nameWithType.vb: Collection(Of SkiaControl).IndexOf(SkiaControl)
  fullName.vb: System.Collections.ObjectModel.Collection(Of DrawnUi.Draw.SkiaControl).IndexOf(DrawnUi.Draw.SkiaControl)
  spec.csharp:
  - uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.IndexOf(DrawnUi.Draw.SkiaControl)
    name: IndexOf
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.indexof
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.IndexOf(DrawnUi.Draw.SkiaControl)
    name: IndexOf
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.indexof
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
- uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.Insert(System.Int32,DrawnUi.Draw.SkiaControl)
  commentId: M:System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.Insert(System.Int32,DrawnUi.Draw.SkiaControl)
  parent: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}
  definition: System.Collections.ObjectModel.Collection`1.Insert(System.Int32,`0)
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.insert
  name: Insert(int, SkiaControl)
  nameWithType: Collection<SkiaControl>.Insert(int, SkiaControl)
  fullName: System.Collections.ObjectModel.Collection<DrawnUi.Draw.SkiaControl>.Insert(int, DrawnUi.Draw.SkiaControl)
  nameWithType.vb: Collection(Of SkiaControl).Insert(Integer, SkiaControl)
  fullName.vb: System.Collections.ObjectModel.Collection(Of DrawnUi.Draw.SkiaControl).Insert(Integer, DrawnUi.Draw.SkiaControl)
  name.vb: Insert(Integer, SkiaControl)
  spec.csharp:
  - uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.Insert(System.Int32,DrawnUi.Draw.SkiaControl)
    name: Insert
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.insert
  - name: (
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.Insert(System.Int32,DrawnUi.Draw.SkiaControl)
    name: Insert
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.insert
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
- uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.Remove(DrawnUi.Draw.SkiaControl)
  commentId: M:System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.Remove(DrawnUi.Draw.SkiaControl)
  parent: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}
  definition: System.Collections.ObjectModel.Collection`1.Remove(`0)
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.remove
  name: Remove(SkiaControl)
  nameWithType: Collection<SkiaControl>.Remove(SkiaControl)
  fullName: System.Collections.ObjectModel.Collection<DrawnUi.Draw.SkiaControl>.Remove(DrawnUi.Draw.SkiaControl)
  nameWithType.vb: Collection(Of SkiaControl).Remove(SkiaControl)
  fullName.vb: System.Collections.ObjectModel.Collection(Of DrawnUi.Draw.SkiaControl).Remove(DrawnUi.Draw.SkiaControl)
  spec.csharp:
  - uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.Remove(DrawnUi.Draw.SkiaControl)
    name: Remove
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.remove
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.Remove(DrawnUi.Draw.SkiaControl)
    name: Remove
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.remove
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
- uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.RemoveAt(System.Int32)
  commentId: M:System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.RemoveAt(System.Int32)
  parent: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}
  definition: System.Collections.ObjectModel.Collection`1.RemoveAt(System.Int32)
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.removeat
  name: RemoveAt(int)
  nameWithType: Collection<SkiaControl>.RemoveAt(int)
  fullName: System.Collections.ObjectModel.Collection<DrawnUi.Draw.SkiaControl>.RemoveAt(int)
  nameWithType.vb: Collection(Of SkiaControl).RemoveAt(Integer)
  fullName.vb: System.Collections.ObjectModel.Collection(Of DrawnUi.Draw.SkiaControl).RemoveAt(Integer)
  name.vb: RemoveAt(Integer)
  spec.csharp:
  - uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.RemoveAt(System.Int32)
    name: RemoveAt
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.removeat
  - name: (
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.RemoveAt(System.Int32)
    name: RemoveAt
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.removeat
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.Count
  commentId: P:System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.Count
  parent: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}
  definition: System.Collections.ObjectModel.Collection`1.Count
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.count
  name: Count
  nameWithType: Collection<SkiaControl>.Count
  fullName: System.Collections.ObjectModel.Collection<DrawnUi.Draw.SkiaControl>.Count
  nameWithType.vb: Collection(Of SkiaControl).Count
  fullName.vb: System.Collections.ObjectModel.Collection(Of DrawnUi.Draw.SkiaControl).Count
- uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.Item(System.Int32)
  commentId: P:System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.Item(System.Int32)
  parent: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}
  definition: System.Collections.ObjectModel.Collection`1.Item(System.Int32)
  href: https://learn.microsoft.com/dotnet/api/system.int32
  name: this[int]
  nameWithType: Collection<SkiaControl>.this[int]
  fullName: System.Collections.ObjectModel.Collection<DrawnUi.Draw.SkiaControl>.this[int]
  nameWithType.vb: Collection(Of SkiaControl).this[](Integer)
  fullName.vb: System.Collections.ObjectModel.Collection(Of DrawnUi.Draw.SkiaControl).this[](Integer)
  name.vb: this[](Integer)
  spec.csharp:
  - name: this
  - name: '['
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ']'
  spec.vb:
  - uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.Item(System.Int32)
    name: this[]
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.item
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.Items
  commentId: P:System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}.Items
  parent: System.Collections.ObjectModel.Collection{DrawnUi.Draw.SkiaControl}
  definition: System.Collections.ObjectModel.Collection`1.Items
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.items
  name: Items
  nameWithType: Collection<SkiaControl>.Items
  fullName: System.Collections.ObjectModel.Collection<DrawnUi.Draw.SkiaControl>.Items
  nameWithType.vb: Collection(Of SkiaControl).Items
  fullName.vb: System.Collections.ObjectModel.Collection(Of DrawnUi.Draw.SkiaControl).Items
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: System.Collections.ObjectModel.Collection`1
  commentId: T:System.Collections.ObjectModel.Collection`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1
  name: Collection<T>
  nameWithType: Collection<T>
  fullName: System.Collections.ObjectModel.Collection<T>
  nameWithType.vb: Collection(Of T)
  fullName.vb: System.Collections.ObjectModel.Collection(Of T)
  name.vb: Collection(Of T)
  spec.csharp:
  - uid: System.Collections.ObjectModel.Collection`1
    name: Collection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Collections.ObjectModel.Collection`1
    name: Collection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: System.Collections.ObjectModel
  commentId: N:System.Collections.ObjectModel
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Collections.ObjectModel
  nameWithType: System.Collections.ObjectModel
  fullName: System.Collections.ObjectModel
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.ObjectModel
    name: ObjectModel
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.ObjectModel
    name: ObjectModel
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel
- uid: System.Collections.ObjectModel.ObservableCollection`1
  commentId: T:System.Collections.ObjectModel.ObservableCollection`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1
  name: ObservableCollection<T>
  nameWithType: ObservableCollection<T>
  fullName: System.Collections.ObjectModel.ObservableCollection<T>
  nameWithType.vb: ObservableCollection(Of T)
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of T)
  name.vb: ObservableCollection(Of T)
  spec.csharp:
  - uid: System.Collections.ObjectModel.ObservableCollection`1
    name: ObservableCollection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Collections.ObjectModel.ObservableCollection`1
    name: ObservableCollection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: System.Collections.Generic.IList`1
  commentId: T:System.Collections.Generic.IList`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ilist-1
  name: IList<T>
  nameWithType: IList<T>
  fullName: System.Collections.Generic.IList<T>
  nameWithType.vb: IList(Of T)
  fullName.vb: System.Collections.Generic.IList(Of T)
  name.vb: IList(Of T)
  spec.csharp:
  - uid: System.Collections.Generic.IList`1
    name: IList
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ilist-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.IList`1
    name: IList
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ilist-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: System.Collections.Generic
  commentId: N:System.Collections.Generic
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Collections.Generic
  nameWithType: System.Collections.Generic
  fullName: System.Collections.Generic
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Generic
    name: Generic
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Generic
    name: Generic
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic
- uid: System.Collections.Generic.ICollection`1
  commentId: T:System.Collections.Generic.ICollection`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.icollection-1
  name: ICollection<T>
  nameWithType: ICollection<T>
  fullName: System.Collections.Generic.ICollection<T>
  nameWithType.vb: ICollection(Of T)
  fullName.vb: System.Collections.Generic.ICollection(Of T)
  name.vb: ICollection(Of T)
  spec.csharp:
  - uid: System.Collections.Generic.ICollection`1
    name: ICollection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.icollection-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.ICollection`1
    name: ICollection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.icollection-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: System.Collections.Generic.IReadOnlyList`1
  commentId: T:System.Collections.Generic.IReadOnlyList`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlylist-1
  name: IReadOnlyList<T>
  nameWithType: IReadOnlyList<T>
  fullName: System.Collections.Generic.IReadOnlyList<T>
  nameWithType.vb: IReadOnlyList(Of T)
  fullName.vb: System.Collections.Generic.IReadOnlyList(Of T)
  name.vb: IReadOnlyList(Of T)
  spec.csharp:
  - uid: System.Collections.Generic.IReadOnlyList`1
    name: IReadOnlyList
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlylist-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.IReadOnlyList`1
    name: IReadOnlyList
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlylist-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: System.Collections.Generic.IReadOnlyCollection`1
  commentId: T:System.Collections.Generic.IReadOnlyCollection`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlycollection-1
  name: IReadOnlyCollection<T>
  nameWithType: IReadOnlyCollection<T>
  fullName: System.Collections.Generic.IReadOnlyCollection<T>
  nameWithType.vb: IReadOnlyCollection(Of T)
  fullName.vb: System.Collections.Generic.IReadOnlyCollection(Of T)
  name.vb: IReadOnlyCollection(Of T)
  spec.csharp:
  - uid: System.Collections.Generic.IReadOnlyCollection`1
    name: IReadOnlyCollection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlycollection-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.IReadOnlyCollection`1
    name: IReadOnlyCollection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlycollection-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: System.Collections.Generic.IEnumerable`1
  commentId: T:System.Collections.Generic.IEnumerable`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1
  name: IEnumerable<T>
  nameWithType: IEnumerable<T>
  fullName: System.Collections.Generic.IEnumerable<T>
  nameWithType.vb: IEnumerable(Of T)
  fullName.vb: System.Collections.Generic.IEnumerable(Of T)
  name.vb: IEnumerable(Of T)
  spec.csharp:
  - uid: System.Collections.Generic.IEnumerable`1
    name: IEnumerable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.IEnumerable`1
    name: IEnumerable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: System.Collections
  commentId: N:System.Collections
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Collections
  nameWithType: System.Collections
  fullName: System.Collections
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
- uid: System.Collections.Specialized
  commentId: N:System.Collections.Specialized
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Collections.Specialized
  nameWithType: System.Collections.Specialized
  fullName: System.Collections.Specialized
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Specialized
    name: Specialized
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.specialized
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Specialized
    name: Specialized
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.specialized
- uid: System.ComponentModel
  commentId: N:System.ComponentModel
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.ComponentModel
  nameWithType: System.ComponentModel
  fullName: System.ComponentModel
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.ComponentModel
    name: ComponentModel
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.ComponentModel
    name: ComponentModel
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel
- uid: System.Collections.ObjectModel.ObservableCollection`1.BlockReentrancy
  commentId: M:System.Collections.ObjectModel.ObservableCollection`1.BlockReentrancy
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.blockreentrancy
  name: BlockReentrancy()
  nameWithType: ObservableCollection<T>.BlockReentrancy()
  fullName: System.Collections.ObjectModel.ObservableCollection<T>.BlockReentrancy()
  nameWithType.vb: ObservableCollection(Of T).BlockReentrancy()
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of T).BlockReentrancy()
  spec.csharp:
  - uid: System.Collections.ObjectModel.ObservableCollection`1.BlockReentrancy
    name: BlockReentrancy
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.blockreentrancy
  - name: (
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.ObservableCollection`1.BlockReentrancy
    name: BlockReentrancy
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.blockreentrancy
  - name: (
  - name: )
- uid: System.Collections.ObjectModel.ObservableCollection`1.CheckReentrancy
  commentId: M:System.Collections.ObjectModel.ObservableCollection`1.CheckReentrancy
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.checkreentrancy
  name: CheckReentrancy()
  nameWithType: ObservableCollection<T>.CheckReentrancy()
  fullName: System.Collections.ObjectModel.ObservableCollection<T>.CheckReentrancy()
  nameWithType.vb: ObservableCollection(Of T).CheckReentrancy()
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of T).CheckReentrancy()
  spec.csharp:
  - uid: System.Collections.ObjectModel.ObservableCollection`1.CheckReentrancy
    name: CheckReentrancy
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.checkreentrancy
  - name: (
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.ObservableCollection`1.CheckReentrancy
    name: CheckReentrancy
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.checkreentrancy
  - name: (
  - name: )
- uid: System.Collections.ObjectModel.ObservableCollection`1.ClearItems
  commentId: M:System.Collections.ObjectModel.ObservableCollection`1.ClearItems
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.clearitems
  name: ClearItems()
  nameWithType: ObservableCollection<T>.ClearItems()
  fullName: System.Collections.ObjectModel.ObservableCollection<T>.ClearItems()
  nameWithType.vb: ObservableCollection(Of T).ClearItems()
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of T).ClearItems()
  spec.csharp:
  - uid: System.Collections.ObjectModel.ObservableCollection`1.ClearItems
    name: ClearItems
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.clearitems
  - name: (
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.ObservableCollection`1.ClearItems
    name: ClearItems
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.clearitems
  - name: (
  - name: )
- uid: System.Collections.ObjectModel.ObservableCollection`1.InsertItem(System.Int32,`0)
  commentId: M:System.Collections.ObjectModel.ObservableCollection`1.InsertItem(System.Int32,`0)
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.insertitem
  name: InsertItem(int, T)
  nameWithType: ObservableCollection<T>.InsertItem(int, T)
  fullName: System.Collections.ObjectModel.ObservableCollection<T>.InsertItem(int, T)
  nameWithType.vb: ObservableCollection(Of T).InsertItem(Integer, T)
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of T).InsertItem(Integer, T)
  name.vb: InsertItem(Integer, T)
  spec.csharp:
  - uid: System.Collections.ObjectModel.ObservableCollection`1.InsertItem(System.Int32,`0)
    name: InsertItem
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.insertitem
  - name: (
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - name: T
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.ObservableCollection`1.InsertItem(System.Int32,`0)
    name: InsertItem
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.insertitem
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - name: T
  - name: )
- uid: System.Collections.ObjectModel.ObservableCollection`1.Move(System.Int32,System.Int32)
  commentId: M:System.Collections.ObjectModel.ObservableCollection`1.Move(System.Int32,System.Int32)
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.move
  name: Move(int, int)
  nameWithType: ObservableCollection<T>.Move(int, int)
  fullName: System.Collections.ObjectModel.ObservableCollection<T>.Move(int, int)
  nameWithType.vb: ObservableCollection(Of T).Move(Integer, Integer)
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of T).Move(Integer, Integer)
  name.vb: Move(Integer, Integer)
  spec.csharp:
  - uid: System.Collections.ObjectModel.ObservableCollection`1.Move(System.Int32,System.Int32)
    name: Move
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.move
  - name: (
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.ObservableCollection`1.Move(System.Int32,System.Int32)
    name: Move
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.move
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: System.Collections.ObjectModel.ObservableCollection`1.MoveItem(System.Int32,System.Int32)
  commentId: M:System.Collections.ObjectModel.ObservableCollection`1.MoveItem(System.Int32,System.Int32)
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.moveitem
  name: MoveItem(int, int)
  nameWithType: ObservableCollection<T>.MoveItem(int, int)
  fullName: System.Collections.ObjectModel.ObservableCollection<T>.MoveItem(int, int)
  nameWithType.vb: ObservableCollection(Of T).MoveItem(Integer, Integer)
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of T).MoveItem(Integer, Integer)
  name.vb: MoveItem(Integer, Integer)
  spec.csharp:
  - uid: System.Collections.ObjectModel.ObservableCollection`1.MoveItem(System.Int32,System.Int32)
    name: MoveItem
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.moveitem
  - name: (
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.ObservableCollection`1.MoveItem(System.Int32,System.Int32)
    name: MoveItem
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.moveitem
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: System.Collections.ObjectModel.ObservableCollection`1.OnCollectionChanged(System.Collections.Specialized.NotifyCollectionChangedEventArgs)
  commentId: M:System.Collections.ObjectModel.ObservableCollection`1.OnCollectionChanged(System.Collections.Specialized.NotifyCollectionChangedEventArgs)
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.oncollectionchanged
  name: OnCollectionChanged(NotifyCollectionChangedEventArgs)
  nameWithType: ObservableCollection<T>.OnCollectionChanged(NotifyCollectionChangedEventArgs)
  fullName: System.Collections.ObjectModel.ObservableCollection<T>.OnCollectionChanged(System.Collections.Specialized.NotifyCollectionChangedEventArgs)
  nameWithType.vb: ObservableCollection(Of T).OnCollectionChanged(NotifyCollectionChangedEventArgs)
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of T).OnCollectionChanged(System.Collections.Specialized.NotifyCollectionChangedEventArgs)
  spec.csharp:
  - uid: System.Collections.ObjectModel.ObservableCollection`1.OnCollectionChanged(System.Collections.Specialized.NotifyCollectionChangedEventArgs)
    name: OnCollectionChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.oncollectionchanged
  - name: (
  - uid: System.Collections.Specialized.NotifyCollectionChangedEventArgs
    name: NotifyCollectionChangedEventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.specialized.notifycollectionchangedeventargs
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.ObservableCollection`1.OnCollectionChanged(System.Collections.Specialized.NotifyCollectionChangedEventArgs)
    name: OnCollectionChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.oncollectionchanged
  - name: (
  - uid: System.Collections.Specialized.NotifyCollectionChangedEventArgs
    name: NotifyCollectionChangedEventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.specialized.notifycollectionchangedeventargs
  - name: )
- uid: System.Collections.ObjectModel.ObservableCollection`1.OnPropertyChanged(System.ComponentModel.PropertyChangedEventArgs)
  commentId: M:System.Collections.ObjectModel.ObservableCollection`1.OnPropertyChanged(System.ComponentModel.PropertyChangedEventArgs)
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.onpropertychanged
  name: OnPropertyChanged(PropertyChangedEventArgs)
  nameWithType: ObservableCollection<T>.OnPropertyChanged(PropertyChangedEventArgs)
  fullName: System.Collections.ObjectModel.ObservableCollection<T>.OnPropertyChanged(System.ComponentModel.PropertyChangedEventArgs)
  nameWithType.vb: ObservableCollection(Of T).OnPropertyChanged(PropertyChangedEventArgs)
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of T).OnPropertyChanged(System.ComponentModel.PropertyChangedEventArgs)
  spec.csharp:
  - uid: System.Collections.ObjectModel.ObservableCollection`1.OnPropertyChanged(System.ComponentModel.PropertyChangedEventArgs)
    name: OnPropertyChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.onpropertychanged
  - name: (
  - uid: System.ComponentModel.PropertyChangedEventArgs
    name: PropertyChangedEventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.propertychangedeventargs
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.ObservableCollection`1.OnPropertyChanged(System.ComponentModel.PropertyChangedEventArgs)
    name: OnPropertyChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.onpropertychanged
  - name: (
  - uid: System.ComponentModel.PropertyChangedEventArgs
    name: PropertyChangedEventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel.propertychangedeventargs
  - name: )
- uid: System.Collections.ObjectModel.ObservableCollection`1.RemoveItem(System.Int32)
  commentId: M:System.Collections.ObjectModel.ObservableCollection`1.RemoveItem(System.Int32)
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.removeitem
  name: RemoveItem(int)
  nameWithType: ObservableCollection<T>.RemoveItem(int)
  fullName: System.Collections.ObjectModel.ObservableCollection<T>.RemoveItem(int)
  nameWithType.vb: ObservableCollection(Of T).RemoveItem(Integer)
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of T).RemoveItem(Integer)
  name.vb: RemoveItem(Integer)
  spec.csharp:
  - uid: System.Collections.ObjectModel.ObservableCollection`1.RemoveItem(System.Int32)
    name: RemoveItem
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.removeitem
  - name: (
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.ObservableCollection`1.RemoveItem(System.Int32)
    name: RemoveItem
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.removeitem
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: System.Collections.ObjectModel.ObservableCollection`1.SetItem(System.Int32,`0)
  commentId: M:System.Collections.ObjectModel.ObservableCollection`1.SetItem(System.Int32,`0)
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.setitem
  name: SetItem(int, T)
  nameWithType: ObservableCollection<T>.SetItem(int, T)
  fullName: System.Collections.ObjectModel.ObservableCollection<T>.SetItem(int, T)
  nameWithType.vb: ObservableCollection(Of T).SetItem(Integer, T)
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of T).SetItem(Integer, T)
  name.vb: SetItem(Integer, T)
  spec.csharp:
  - uid: System.Collections.ObjectModel.ObservableCollection`1.SetItem(System.Int32,`0)
    name: SetItem
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.setitem
  - name: (
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - name: T
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.ObservableCollection`1.SetItem(System.Int32,`0)
    name: SetItem
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.setitem
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - name: T
  - name: )
- uid: System.Collections.ObjectModel.ObservableCollection`1.CollectionChanged
  commentId: E:System.Collections.ObjectModel.ObservableCollection`1.CollectionChanged
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.collectionchanged
  name: CollectionChanged
  nameWithType: ObservableCollection<T>.CollectionChanged
  fullName: System.Collections.ObjectModel.ObservableCollection<T>.CollectionChanged
  nameWithType.vb: ObservableCollection(Of T).CollectionChanged
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of T).CollectionChanged
- uid: System.Collections.ObjectModel.ObservableCollection`1.PropertyChanged
  commentId: E:System.Collections.ObjectModel.ObservableCollection`1.PropertyChanged
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.propertychanged
  name: PropertyChanged
  nameWithType: ObservableCollection<T>.PropertyChanged
  fullName: System.Collections.ObjectModel.ObservableCollection<T>.PropertyChanged
  nameWithType.vb: ObservableCollection(Of T).PropertyChanged
  fullName.vb: System.Collections.ObjectModel.ObservableCollection(Of T).PropertyChanged
- uid: System.Collections.ObjectModel.Collection`1.Add(`0)
  commentId: M:System.Collections.ObjectModel.Collection`1.Add(`0)
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.add
  name: Add(T)
  nameWithType: Collection<T>.Add(T)
  fullName: System.Collections.ObjectModel.Collection<T>.Add(T)
  nameWithType.vb: Collection(Of T).Add(T)
  fullName.vb: System.Collections.ObjectModel.Collection(Of T).Add(T)
  spec.csharp:
  - uid: System.Collections.ObjectModel.Collection`1.Add(`0)
    name: Add
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.add
  - name: (
  - name: T
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.Collection`1.Add(`0)
    name: Add
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.add
  - name: (
  - name: T
  - name: )
- uid: System.Collections.ObjectModel.Collection`1.Clear
  commentId: M:System.Collections.ObjectModel.Collection`1.Clear
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.clear
  name: Clear()
  nameWithType: Collection<T>.Clear()
  fullName: System.Collections.ObjectModel.Collection<T>.Clear()
  nameWithType.vb: Collection(Of T).Clear()
  fullName.vb: System.Collections.ObjectModel.Collection(Of T).Clear()
  spec.csharp:
  - uid: System.Collections.ObjectModel.Collection`1.Clear
    name: Clear
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.clear
  - name: (
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.Collection`1.Clear
    name: Clear
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.clear
  - name: (
  - name: )
- uid: System.Collections.ObjectModel.Collection`1.Contains(`0)
  commentId: M:System.Collections.ObjectModel.Collection`1.Contains(`0)
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.contains
  name: Contains(T)
  nameWithType: Collection<T>.Contains(T)
  fullName: System.Collections.ObjectModel.Collection<T>.Contains(T)
  nameWithType.vb: Collection(Of T).Contains(T)
  fullName.vb: System.Collections.ObjectModel.Collection(Of T).Contains(T)
  spec.csharp:
  - uid: System.Collections.ObjectModel.Collection`1.Contains(`0)
    name: Contains
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.contains
  - name: (
  - name: T
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.Collection`1.Contains(`0)
    name: Contains
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.contains
  - name: (
  - name: T
  - name: )
- uid: System.Collections.ObjectModel.Collection`1.CopyTo(`0[],System.Int32)
  commentId: M:System.Collections.ObjectModel.Collection`1.CopyTo(`0[],System.Int32)
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.copyto
  name: CopyTo(T[], int)
  nameWithType: Collection<T>.CopyTo(T[], int)
  fullName: System.Collections.ObjectModel.Collection<T>.CopyTo(T[], int)
  nameWithType.vb: Collection(Of T).CopyTo(T(), Integer)
  fullName.vb: System.Collections.ObjectModel.Collection(Of T).CopyTo(T(), Integer)
  name.vb: CopyTo(T(), Integer)
  spec.csharp:
  - uid: System.Collections.ObjectModel.Collection`1.CopyTo(`0[],System.Int32)
    name: CopyTo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.copyto
  - name: (
  - name: T
  - name: '['
  - name: ']'
  - name: ','
  - name: " "
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.Collection`1.CopyTo(`0[],System.Int32)
    name: CopyTo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.copyto
  - name: (
  - name: T
  - name: (
  - name: )
  - name: ','
  - name: " "
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: System.Collections.ObjectModel.Collection`1.GetEnumerator
  commentId: M:System.Collections.ObjectModel.Collection`1.GetEnumerator
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.getenumerator
  name: GetEnumerator()
  nameWithType: Collection<T>.GetEnumerator()
  fullName: System.Collections.ObjectModel.Collection<T>.GetEnumerator()
  nameWithType.vb: Collection(Of T).GetEnumerator()
  fullName.vb: System.Collections.ObjectModel.Collection(Of T).GetEnumerator()
  spec.csharp:
  - uid: System.Collections.ObjectModel.Collection`1.GetEnumerator
    name: GetEnumerator
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.getenumerator
  - name: (
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.Collection`1.GetEnumerator
    name: GetEnumerator
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.getenumerator
  - name: (
  - name: )
- uid: System.Collections.ObjectModel.Collection`1.IndexOf(`0)
  commentId: M:System.Collections.ObjectModel.Collection`1.IndexOf(`0)
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.indexof
  name: IndexOf(T)
  nameWithType: Collection<T>.IndexOf(T)
  fullName: System.Collections.ObjectModel.Collection<T>.IndexOf(T)
  nameWithType.vb: Collection(Of T).IndexOf(T)
  fullName.vb: System.Collections.ObjectModel.Collection(Of T).IndexOf(T)
  spec.csharp:
  - uid: System.Collections.ObjectModel.Collection`1.IndexOf(`0)
    name: IndexOf
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.indexof
  - name: (
  - name: T
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.Collection`1.IndexOf(`0)
    name: IndexOf
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.indexof
  - name: (
  - name: T
  - name: )
- uid: System.Collections.ObjectModel.Collection`1.Insert(System.Int32,`0)
  commentId: M:System.Collections.ObjectModel.Collection`1.Insert(System.Int32,`0)
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.insert
  name: Insert(int, T)
  nameWithType: Collection<T>.Insert(int, T)
  fullName: System.Collections.ObjectModel.Collection<T>.Insert(int, T)
  nameWithType.vb: Collection(Of T).Insert(Integer, T)
  fullName.vb: System.Collections.ObjectModel.Collection(Of T).Insert(Integer, T)
  name.vb: Insert(Integer, T)
  spec.csharp:
  - uid: System.Collections.ObjectModel.Collection`1.Insert(System.Int32,`0)
    name: Insert
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.insert
  - name: (
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - name: T
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.Collection`1.Insert(System.Int32,`0)
    name: Insert
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.insert
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - name: T
  - name: )
- uid: System.Collections.ObjectModel.Collection`1.Remove(`0)
  commentId: M:System.Collections.ObjectModel.Collection`1.Remove(`0)
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.remove
  name: Remove(T)
  nameWithType: Collection<T>.Remove(T)
  fullName: System.Collections.ObjectModel.Collection<T>.Remove(T)
  nameWithType.vb: Collection(Of T).Remove(T)
  fullName.vb: System.Collections.ObjectModel.Collection(Of T).Remove(T)
  spec.csharp:
  - uid: System.Collections.ObjectModel.Collection`1.Remove(`0)
    name: Remove
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.remove
  - name: (
  - name: T
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.Collection`1.Remove(`0)
    name: Remove
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.remove
  - name: (
  - name: T
  - name: )
- uid: System.Collections.ObjectModel.Collection`1.RemoveAt(System.Int32)
  commentId: M:System.Collections.ObjectModel.Collection`1.RemoveAt(System.Int32)
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.removeat
  name: RemoveAt(int)
  nameWithType: Collection<T>.RemoveAt(int)
  fullName: System.Collections.ObjectModel.Collection<T>.RemoveAt(int)
  nameWithType.vb: Collection(Of T).RemoveAt(Integer)
  fullName.vb: System.Collections.ObjectModel.Collection(Of T).RemoveAt(Integer)
  name.vb: RemoveAt(Integer)
  spec.csharp:
  - uid: System.Collections.ObjectModel.Collection`1.RemoveAt(System.Int32)
    name: RemoveAt
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.removeat
  - name: (
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.Collection`1.RemoveAt(System.Int32)
    name: RemoveAt
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.removeat
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: System.Collections.ObjectModel.Collection`1.Count
  commentId: P:System.Collections.ObjectModel.Collection`1.Count
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.count
  name: Count
  nameWithType: Collection<T>.Count
  fullName: System.Collections.ObjectModel.Collection<T>.Count
  nameWithType.vb: Collection(Of T).Count
  fullName.vb: System.Collections.ObjectModel.Collection(Of T).Count
- uid: System.Collections.ObjectModel.Collection`1.Item(System.Int32)
  commentId: P:System.Collections.ObjectModel.Collection`1.Item(System.Int32)
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.int32
  name: this[int]
  nameWithType: Collection<T>.this[int]
  fullName: System.Collections.ObjectModel.Collection<T>.this[int]
  nameWithType.vb: Collection(Of T).this[](Integer)
  fullName.vb: System.Collections.ObjectModel.Collection(Of T).this[](Integer)
  name.vb: this[](Integer)
  spec.csharp:
  - name: this
  - name: '['
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ']'
  spec.vb:
  - uid: System.Collections.ObjectModel.Collection`1.Item(System.Int32)
    name: this[]
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.item
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: System.Collections.ObjectModel.Collection`1.Items
  commentId: P:System.Collections.ObjectModel.Collection`1.Items
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.items
  name: Items
  nameWithType: Collection<T>.Items
  fullName: System.Collections.ObjectModel.Collection<T>.Items
  nameWithType.vb: Collection(Of T).Items
  fullName.vb: System.Collections.ObjectModel.Collection(Of T).Items
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
