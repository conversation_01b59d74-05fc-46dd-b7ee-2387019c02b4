### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.ViewsIterator
  commentId: T:DrawnUi.Draw.ViewsIterator
  id: ViewsIterator
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.ViewsIterator.#ctor(DrawnUi.Draw.TemplatedViewsPool,System.Collections.IList,System.Nullable{DrawnUi.Draw.LayoutType})
  - DrawnUi.Draw.ViewsIterator.#ctor(System.Collections.Generic.IEnumerable{DrawnUi.Draw.SkiaControl})
  - DrawnUi.Draw.ViewsIterator.DataContexts
  - DrawnUi.Draw.ViewsIterator.Dispose
  - DrawnUi.Draw.ViewsIterator.GetEnumerator
  - DrawnUi.Draw.ViewsIterator.IsTemplated
  - DrawnUi.Draw.ViewsIterator.SetViews(System.Collections.Generic.IEnumerable{DrawnUi.Draw.SkiaControl})
  - DrawnUi.Draw.ViewsIterator.TemplatedViewsPool
  - DrawnUi.Draw.ViewsIterator.Views
  langs:
  - csharp
  - vb
  name: ViewsIterator
  nameWithType: ViewsIterator
  fullName: DrawnUi.Draw.ViewsIterator
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ViewsIterator
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs
    startLine: 1237
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: To iterate over virtual views
  example: []
  syntax:
    content: 'public class ViewsIterator : IEnumerable<SkiaControl>, IEnumerable, IDisposable'
    content.vb: Public Class ViewsIterator Implements IEnumerable(Of SkiaControl), IEnumerable, IDisposable
  inheritance:
  - System.Object
  implements:
  - System.Collections.Generic.IEnumerable{DrawnUi.Draw.SkiaControl}
  - System.Collections.IEnumerable
  - System.IDisposable
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.ViewsIterator.IsTemplated
  commentId: P:DrawnUi.Draw.ViewsIterator.IsTemplated
  id: IsTemplated
  parent: DrawnUi.Draw.ViewsIterator
  langs:
  - csharp
  - vb
  name: IsTemplated
  nameWithType: ViewsIterator.IsTemplated
  fullName: DrawnUi.Draw.ViewsIterator.IsTemplated
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IsTemplated
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs
    startLine: 1245
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public bool IsTemplated { get; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public ReadOnly Property IsTemplated As Boolean
  overload: DrawnUi.Draw.ViewsIterator.IsTemplated*
- uid: DrawnUi.Draw.ViewsIterator.TemplatedViewsPool
  commentId: P:DrawnUi.Draw.ViewsIterator.TemplatedViewsPool
  id: TemplatedViewsPool
  parent: DrawnUi.Draw.ViewsIterator
  langs:
  - csharp
  - vb
  name: TemplatedViewsPool
  nameWithType: ViewsIterator.TemplatedViewsPool
  fullName: DrawnUi.Draw.ViewsIterator.TemplatedViewsPool
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: TemplatedViewsPool
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs
    startLine: 1247
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public TemplatedViewsPool TemplatedViewsPool { get; }
    parameters: []
    return:
      type: DrawnUi.Draw.TemplatedViewsPool
    content.vb: Public ReadOnly Property TemplatedViewsPool As TemplatedViewsPool
  overload: DrawnUi.Draw.ViewsIterator.TemplatedViewsPool*
- uid: DrawnUi.Draw.ViewsIterator.DataContexts
  commentId: P:DrawnUi.Draw.ViewsIterator.DataContexts
  id: DataContexts
  parent: DrawnUi.Draw.ViewsIterator
  langs:
  - csharp
  - vb
  name: DataContexts
  nameWithType: ViewsIterator.DataContexts
  fullName: DrawnUi.Draw.ViewsIterator.DataContexts
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: DataContexts
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs
    startLine: 1248
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public IList DataContexts { get; }
    parameters: []
    return:
      type: System.Collections.IList
    content.vb: Public ReadOnly Property DataContexts As IList
  overload: DrawnUi.Draw.ViewsIterator.DataContexts*
- uid: DrawnUi.Draw.ViewsIterator.Views
  commentId: P:DrawnUi.Draw.ViewsIterator.Views
  id: Views
  parent: DrawnUi.Draw.ViewsIterator
  langs:
  - csharp
  - vb
  name: Views
  nameWithType: ViewsIterator.Views
  fullName: DrawnUi.Draw.ViewsIterator.Views
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Views
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs
    startLine: 1249
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public IEnumerable<SkiaControl> Views { get; }
    parameters: []
    return:
      type: System.Collections.Generic.IEnumerable{DrawnUi.Draw.SkiaControl}
    content.vb: Public ReadOnly Property Views As IEnumerable(Of SkiaControl)
  overload: DrawnUi.Draw.ViewsIterator.Views*
- uid: DrawnUi.Draw.ViewsIterator.#ctor(DrawnUi.Draw.TemplatedViewsPool,System.Collections.IList,System.Nullable{DrawnUi.Draw.LayoutType})
  commentId: M:DrawnUi.Draw.ViewsIterator.#ctor(DrawnUi.Draw.TemplatedViewsPool,System.Collections.IList,System.Nullable{DrawnUi.Draw.LayoutType})
  id: '#ctor(DrawnUi.Draw.TemplatedViewsPool,System.Collections.IList,System.Nullable{DrawnUi.Draw.LayoutType})'
  parent: DrawnUi.Draw.ViewsIterator
  langs:
  - csharp
  - vb
  name: ViewsIterator(TemplatedViewsPool, IList, LayoutType?)
  nameWithType: ViewsIterator.ViewsIterator(TemplatedViewsPool, IList, LayoutType?)
  fullName: DrawnUi.Draw.ViewsIterator.ViewsIterator(DrawnUi.Draw.TemplatedViewsPool, System.Collections.IList, DrawnUi.Draw.LayoutType?)
  type: Constructor
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs
    startLine: 1251
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public ViewsIterator(TemplatedViewsPool templatedViewsPool, IList dataContexts, LayoutType? layoutType)
    parameters:
    - id: templatedViewsPool
      type: DrawnUi.Draw.TemplatedViewsPool
    - id: dataContexts
      type: System.Collections.IList
    - id: layoutType
      type: System.Nullable{DrawnUi.Draw.LayoutType}
    content.vb: Public Sub New(templatedViewsPool As TemplatedViewsPool, dataContexts As IList, layoutType As LayoutType?)
  overload: DrawnUi.Draw.ViewsIterator.#ctor*
  nameWithType.vb: ViewsIterator.New(TemplatedViewsPool, IList, LayoutType?)
  fullName.vb: DrawnUi.Draw.ViewsIterator.New(DrawnUi.Draw.TemplatedViewsPool, System.Collections.IList, DrawnUi.Draw.LayoutType?)
  name.vb: New(TemplatedViewsPool, IList, LayoutType?)
- uid: DrawnUi.Draw.ViewsIterator.SetViews(System.Collections.Generic.IEnumerable{DrawnUi.Draw.SkiaControl})
  commentId: M:DrawnUi.Draw.ViewsIterator.SetViews(System.Collections.Generic.IEnumerable{DrawnUi.Draw.SkiaControl})
  id: SetViews(System.Collections.Generic.IEnumerable{DrawnUi.Draw.SkiaControl})
  parent: DrawnUi.Draw.ViewsIterator
  langs:
  - csharp
  - vb
  name: SetViews(IEnumerable<SkiaControl>)
  nameWithType: ViewsIterator.SetViews(IEnumerable<SkiaControl>)
  fullName: DrawnUi.Draw.ViewsIterator.SetViews(System.Collections.Generic.IEnumerable<DrawnUi.Draw.SkiaControl>)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: SetViews
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs
    startLine: 1259
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public void SetViews(IEnumerable<SkiaControl> views)
    parameters:
    - id: views
      type: System.Collections.Generic.IEnumerable{DrawnUi.Draw.SkiaControl}
    content.vb: Public Sub SetViews(views As IEnumerable(Of SkiaControl))
  overload: DrawnUi.Draw.ViewsIterator.SetViews*
  nameWithType.vb: ViewsIterator.SetViews(IEnumerable(Of SkiaControl))
  fullName.vb: DrawnUi.Draw.ViewsIterator.SetViews(System.Collections.Generic.IEnumerable(Of DrawnUi.Draw.SkiaControl))
  name.vb: SetViews(IEnumerable(Of SkiaControl))
- uid: DrawnUi.Draw.ViewsIterator.#ctor(System.Collections.Generic.IEnumerable{DrawnUi.Draw.SkiaControl})
  commentId: M:DrawnUi.Draw.ViewsIterator.#ctor(System.Collections.Generic.IEnumerable{DrawnUi.Draw.SkiaControl})
  id: '#ctor(System.Collections.Generic.IEnumerable{DrawnUi.Draw.SkiaControl})'
  parent: DrawnUi.Draw.ViewsIterator
  langs:
  - csharp
  - vb
  name: ViewsIterator(IEnumerable<SkiaControl>)
  nameWithType: ViewsIterator.ViewsIterator(IEnumerable<SkiaControl>)
  fullName: DrawnUi.Draw.ViewsIterator.ViewsIterator(System.Collections.Generic.IEnumerable<DrawnUi.Draw.SkiaControl>)
  type: Constructor
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs
    startLine: 1264
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public ViewsIterator(IEnumerable<SkiaControl> views)
    parameters:
    - id: views
      type: System.Collections.Generic.IEnumerable{DrawnUi.Draw.SkiaControl}
    content.vb: Public Sub New(views As IEnumerable(Of SkiaControl))
  overload: DrawnUi.Draw.ViewsIterator.#ctor*
  nameWithType.vb: ViewsIterator.New(IEnumerable(Of SkiaControl))
  fullName.vb: DrawnUi.Draw.ViewsIterator.New(System.Collections.Generic.IEnumerable(Of DrawnUi.Draw.SkiaControl))
  name.vb: New(IEnumerable(Of SkiaControl))
- uid: DrawnUi.Draw.ViewsIterator.GetEnumerator
  commentId: M:DrawnUi.Draw.ViewsIterator.GetEnumerator
  id: GetEnumerator
  parent: DrawnUi.Draw.ViewsIterator
  langs:
  - csharp
  - vb
  name: GetEnumerator()
  nameWithType: ViewsIterator.GetEnumerator()
  fullName: DrawnUi.Draw.ViewsIterator.GetEnumerator()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: GetEnumerator
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs
    startLine: 1270
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Returns an enumerator that iterates through the collection.
  example: []
  syntax:
    content: public IEnumerator<SkiaControl> GetEnumerator()
    return:
      type: System.Collections.Generic.IEnumerator{DrawnUi.Draw.SkiaControl}
      description: An enumerator that can be used to iterate through the collection.
    content.vb: Public Function GetEnumerator() As IEnumerator(Of SkiaControl)
  overload: DrawnUi.Draw.ViewsIterator.GetEnumerator*
  implements:
  - System.Collections.Generic.IEnumerable{DrawnUi.Draw.SkiaControl}.GetEnumerator
- uid: DrawnUi.Draw.ViewsIterator.Dispose
  commentId: M:DrawnUi.Draw.ViewsIterator.Dispose
  id: Dispose
  parent: DrawnUi.Draw.ViewsIterator
  langs:
  - csharp
  - vb
  name: Dispose()
  nameWithType: ViewsIterator.Dispose()
  fullName: DrawnUi.Draw.ViewsIterator.Dispose()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Dispose
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs
    startLine: 1281
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
  example: []
  syntax:
    content: public void Dispose()
    content.vb: Public Sub Dispose()
  overload: DrawnUi.Draw.ViewsIterator.Dispose*
  implements:
  - System.IDisposable.Dispose
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Collections.Generic.IEnumerable{DrawnUi.Draw.SkiaControl}
  commentId: T:System.Collections.Generic.IEnumerable{DrawnUi.Draw.SkiaControl}
  parent: System.Collections.Generic
  definition: System.Collections.Generic.IEnumerable`1
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1
  name: IEnumerable<SkiaControl>
  nameWithType: IEnumerable<SkiaControl>
  fullName: System.Collections.Generic.IEnumerable<DrawnUi.Draw.SkiaControl>
  nameWithType.vb: IEnumerable(Of SkiaControl)
  fullName.vb: System.Collections.Generic.IEnumerable(Of DrawnUi.Draw.SkiaControl)
  name.vb: IEnumerable(Of SkiaControl)
  spec.csharp:
  - uid: System.Collections.Generic.IEnumerable`1
    name: IEnumerable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1
  - name: <
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.IEnumerable`1
    name: IEnumerable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
- uid: System.Collections.IEnumerable
  commentId: T:System.Collections.IEnumerable
  parent: System.Collections
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.ienumerable
  name: IEnumerable
  nameWithType: IEnumerable
  fullName: System.Collections.IEnumerable
- uid: System.IDisposable
  commentId: T:System.IDisposable
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.idisposable
  name: IDisposable
  nameWithType: IDisposable
  fullName: System.IDisposable
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: System.Collections.Generic.IEnumerable`1
  commentId: T:System.Collections.Generic.IEnumerable`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1
  name: IEnumerable<T>
  nameWithType: IEnumerable<T>
  fullName: System.Collections.Generic.IEnumerable<T>
  nameWithType.vb: IEnumerable(Of T)
  fullName.vb: System.Collections.Generic.IEnumerable(Of T)
  name.vb: IEnumerable(Of T)
  spec.csharp:
  - uid: System.Collections.Generic.IEnumerable`1
    name: IEnumerable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.IEnumerable`1
    name: IEnumerable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: System.Collections.Generic
  commentId: N:System.Collections.Generic
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Collections.Generic
  nameWithType: System.Collections.Generic
  fullName: System.Collections.Generic
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Generic
    name: Generic
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Generic
    name: Generic
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic
- uid: System.Collections
  commentId: N:System.Collections
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Collections
  nameWithType: System.Collections
  fullName: System.Collections
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.ViewsIterator.IsTemplated*
  commentId: Overload:DrawnUi.Draw.ViewsIterator.IsTemplated
  href: DrawnUi.Draw.ViewsIterator.html#DrawnUi_Draw_ViewsIterator_IsTemplated
  name: IsTemplated
  nameWithType: ViewsIterator.IsTemplated
  fullName: DrawnUi.Draw.ViewsIterator.IsTemplated
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: DrawnUi.Draw.ViewsIterator.TemplatedViewsPool*
  commentId: Overload:DrawnUi.Draw.ViewsIterator.TemplatedViewsPool
  href: DrawnUi.Draw.ViewsIterator.html#DrawnUi_Draw_ViewsIterator_TemplatedViewsPool
  name: TemplatedViewsPool
  nameWithType: ViewsIterator.TemplatedViewsPool
  fullName: DrawnUi.Draw.ViewsIterator.TemplatedViewsPool
- uid: DrawnUi.Draw.TemplatedViewsPool
  commentId: T:DrawnUi.Draw.TemplatedViewsPool
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.TemplatedViewsPool.html
  name: TemplatedViewsPool
  nameWithType: TemplatedViewsPool
  fullName: DrawnUi.Draw.TemplatedViewsPool
- uid: DrawnUi.Draw.ViewsIterator.DataContexts*
  commentId: Overload:DrawnUi.Draw.ViewsIterator.DataContexts
  href: DrawnUi.Draw.ViewsIterator.html#DrawnUi_Draw_ViewsIterator_DataContexts
  name: DataContexts
  nameWithType: ViewsIterator.DataContexts
  fullName: DrawnUi.Draw.ViewsIterator.DataContexts
- uid: System.Collections.IList
  commentId: T:System.Collections.IList
  parent: System.Collections
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.ilist
  name: IList
  nameWithType: IList
  fullName: System.Collections.IList
- uid: DrawnUi.Draw.ViewsIterator.Views*
  commentId: Overload:DrawnUi.Draw.ViewsIterator.Views
  href: DrawnUi.Draw.ViewsIterator.html#DrawnUi_Draw_ViewsIterator_Views
  name: Views
  nameWithType: ViewsIterator.Views
  fullName: DrawnUi.Draw.ViewsIterator.Views
- uid: DrawnUi.Draw.ViewsIterator.#ctor*
  commentId: Overload:DrawnUi.Draw.ViewsIterator.#ctor
  href: DrawnUi.Draw.ViewsIterator.html#DrawnUi_Draw_ViewsIterator__ctor_DrawnUi_Draw_TemplatedViewsPool_System_Collections_IList_System_Nullable_DrawnUi_Draw_LayoutType__
  name: ViewsIterator
  nameWithType: ViewsIterator.ViewsIterator
  fullName: DrawnUi.Draw.ViewsIterator.ViewsIterator
  nameWithType.vb: ViewsIterator.New
  fullName.vb: DrawnUi.Draw.ViewsIterator.New
  name.vb: New
- uid: System.Nullable{DrawnUi.Draw.LayoutType}
  commentId: T:System.Nullable{DrawnUi.Draw.LayoutType}
  parent: System
  definition: System.Nullable`1
  href: DrawnUi.Draw.LayoutType.html
  name: LayoutType?
  nameWithType: LayoutType?
  fullName: DrawnUi.Draw.LayoutType?
  spec.csharp:
  - uid: DrawnUi.Draw.LayoutType
    name: LayoutType
    href: DrawnUi.Draw.LayoutType.html
  - name: '?'
  spec.vb:
  - uid: DrawnUi.Draw.LayoutType
    name: LayoutType
    href: DrawnUi.Draw.LayoutType.html
  - name: '?'
- uid: System.Nullable`1
  commentId: T:System.Nullable`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.nullable-1
  name: Nullable<T>
  nameWithType: Nullable<T>
  fullName: System.Nullable<T>
  nameWithType.vb: Nullable(Of T)
  fullName.vb: System.Nullable(Of T)
  name.vb: Nullable(Of T)
  spec.csharp:
  - uid: System.Nullable`1
    name: Nullable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.nullable-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Nullable`1
    name: Nullable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.nullable-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: DrawnUi.Draw.ViewsIterator.SetViews*
  commentId: Overload:DrawnUi.Draw.ViewsIterator.SetViews
  href: DrawnUi.Draw.ViewsIterator.html#DrawnUi_Draw_ViewsIterator_SetViews_System_Collections_Generic_IEnumerable_DrawnUi_Draw_SkiaControl__
  name: SetViews
  nameWithType: ViewsIterator.SetViews
  fullName: DrawnUi.Draw.ViewsIterator.SetViews
- uid: DrawnUi.Draw.ViewsIterator.GetEnumerator*
  commentId: Overload:DrawnUi.Draw.ViewsIterator.GetEnumerator
  href: DrawnUi.Draw.ViewsIterator.html#DrawnUi_Draw_ViewsIterator_GetEnumerator
  name: GetEnumerator
  nameWithType: ViewsIterator.GetEnumerator
  fullName: DrawnUi.Draw.ViewsIterator.GetEnumerator
- uid: System.Collections.Generic.IEnumerable{DrawnUi.Draw.SkiaControl}.GetEnumerator
  commentId: M:System.Collections.Generic.IEnumerable{DrawnUi.Draw.SkiaControl}.GetEnumerator
  parent: System.Collections.Generic.IEnumerable{DrawnUi.Draw.SkiaControl}
  definition: System.Collections.Generic.IEnumerable`1.GetEnumerator
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1.getenumerator
  name: GetEnumerator()
  nameWithType: IEnumerable<SkiaControl>.GetEnumerator()
  fullName: System.Collections.Generic.IEnumerable<DrawnUi.Draw.SkiaControl>.GetEnumerator()
  nameWithType.vb: IEnumerable(Of SkiaControl).GetEnumerator()
  fullName.vb: System.Collections.Generic.IEnumerable(Of DrawnUi.Draw.SkiaControl).GetEnumerator()
  spec.csharp:
  - uid: System.Collections.Generic.IEnumerable{DrawnUi.Draw.SkiaControl}.GetEnumerator
    name: GetEnumerator
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1.getenumerator
  - name: (
  - name: )
  spec.vb:
  - uid: System.Collections.Generic.IEnumerable{DrawnUi.Draw.SkiaControl}.GetEnumerator
    name: GetEnumerator
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1.getenumerator
  - name: (
  - name: )
- uid: System.Collections.Generic.IEnumerator{DrawnUi.Draw.SkiaControl}
  commentId: T:System.Collections.Generic.IEnumerator{DrawnUi.Draw.SkiaControl}
  parent: System.Collections.Generic
  definition: System.Collections.Generic.IEnumerator`1
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerator-1
  name: IEnumerator<SkiaControl>
  nameWithType: IEnumerator<SkiaControl>
  fullName: System.Collections.Generic.IEnumerator<DrawnUi.Draw.SkiaControl>
  nameWithType.vb: IEnumerator(Of SkiaControl)
  fullName.vb: System.Collections.Generic.IEnumerator(Of DrawnUi.Draw.SkiaControl)
  name.vb: IEnumerator(Of SkiaControl)
  spec.csharp:
  - uid: System.Collections.Generic.IEnumerator`1
    name: IEnumerator
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerator-1
  - name: <
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.IEnumerator`1
    name: IEnumerator
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerator-1
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
- uid: System.Collections.Generic.IEnumerable`1.GetEnumerator
  commentId: M:System.Collections.Generic.IEnumerable`1.GetEnumerator
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1.getenumerator
  name: GetEnumerator()
  nameWithType: IEnumerable<T>.GetEnumerator()
  fullName: System.Collections.Generic.IEnumerable<T>.GetEnumerator()
  nameWithType.vb: IEnumerable(Of T).GetEnumerator()
  fullName.vb: System.Collections.Generic.IEnumerable(Of T).GetEnumerator()
  spec.csharp:
  - uid: System.Collections.Generic.IEnumerable`1.GetEnumerator
    name: GetEnumerator
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1.getenumerator
  - name: (
  - name: )
  spec.vb:
  - uid: System.Collections.Generic.IEnumerable`1.GetEnumerator
    name: GetEnumerator
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1.getenumerator
  - name: (
  - name: )
- uid: System.Collections.Generic.IEnumerator`1
  commentId: T:System.Collections.Generic.IEnumerator`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerator-1
  name: IEnumerator<T>
  nameWithType: IEnumerator<T>
  fullName: System.Collections.Generic.IEnumerator<T>
  nameWithType.vb: IEnumerator(Of T)
  fullName.vb: System.Collections.Generic.IEnumerator(Of T)
  name.vb: IEnumerator(Of T)
  spec.csharp:
  - uid: System.Collections.Generic.IEnumerator`1
    name: IEnumerator
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerator-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.IEnumerator`1
    name: IEnumerator
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerator-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: DrawnUi.Draw.ViewsIterator.Dispose*
  commentId: Overload:DrawnUi.Draw.ViewsIterator.Dispose
  href: DrawnUi.Draw.ViewsIterator.html#DrawnUi_Draw_ViewsIterator_Dispose
  name: Dispose
  nameWithType: ViewsIterator.Dispose
  fullName: DrawnUi.Draw.ViewsIterator.Dispose
- uid: System.IDisposable.Dispose
  commentId: M:System.IDisposable.Dispose
  parent: System.IDisposable
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.idisposable.dispose
  name: Dispose()
  nameWithType: IDisposable.Dispose()
  fullName: System.IDisposable.Dispose()
  spec.csharp:
  - uid: System.IDisposable.Dispose
    name: Dispose
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.idisposable.dispose
  - name: (
  - name: )
  spec.vb:
  - uid: System.IDisposable.Dispose
    name: Dispose
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.idisposable.dispose
  - name: (
  - name: )
