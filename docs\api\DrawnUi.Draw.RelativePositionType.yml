### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.RelativePositionType
  commentId: T:DrawnUi.Draw.RelativePositionType
  id: RelativePositionType
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.RelativePositionType.Center
  - DrawnUi.Draw.RelativePositionType.End
  - DrawnUi.Draw.RelativePositionType.None
  - DrawnUi.Draw.RelativePositionType.Start
  langs:
  - csharp
  - vb
  name: RelativePositionType
  nameWithType: RelativePositionType
  fullName: DrawnUi.Draw.RelativePositionType
  type: Enum
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/RelativePositionType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: RelativePositionType
    path: ../src/Shared/Draw/Internals/Enums/RelativePositionType.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public enum RelativePositionType
    content.vb: Public Enum RelativePositionType
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.RelativePositionType.None
  commentId: F:DrawnUi.Draw.RelativePositionType.None
  id: None
  parent: DrawnUi.Draw.RelativePositionType
  langs:
  - csharp
  - vb
  name: None
  nameWithType: RelativePositionType.None
  fullName: DrawnUi.Draw.RelativePositionType.None
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/RelativePositionType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: None
    path: ../src/Shared/Draw/Internals/Enums/RelativePositionType.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: None = 0
    return:
      type: DrawnUi.Draw.RelativePositionType
- uid: DrawnUi.Draw.RelativePositionType.Start
  commentId: F:DrawnUi.Draw.RelativePositionType.Start
  id: Start
  parent: DrawnUi.Draw.RelativePositionType
  langs:
  - csharp
  - vb
  name: Start
  nameWithType: RelativePositionType.Start
  fullName: DrawnUi.Draw.RelativePositionType.Start
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/RelativePositionType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Start
    path: ../src/Shared/Draw/Internals/Enums/RelativePositionType.cs
    startLine: 5
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: Start = 1
    return:
      type: DrawnUi.Draw.RelativePositionType
- uid: DrawnUi.Draw.RelativePositionType.Center
  commentId: F:DrawnUi.Draw.RelativePositionType.Center
  id: Center
  parent: DrawnUi.Draw.RelativePositionType
  langs:
  - csharp
  - vb
  name: Center
  nameWithType: RelativePositionType.Center
  fullName: DrawnUi.Draw.RelativePositionType.Center
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/RelativePositionType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Center
    path: ../src/Shared/Draw/Internals/Enums/RelativePositionType.cs
    startLine: 6
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: Center = 2
    return:
      type: DrawnUi.Draw.RelativePositionType
- uid: DrawnUi.Draw.RelativePositionType.End
  commentId: F:DrawnUi.Draw.RelativePositionType.End
  id: End
  parent: DrawnUi.Draw.RelativePositionType
  langs:
  - csharp
  - vb
  name: End
  nameWithType: RelativePositionType.End
  fullName: DrawnUi.Draw.RelativePositionType.End
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/RelativePositionType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: End
    path: ../src/Shared/Draw/Internals/Enums/RelativePositionType.cs
    startLine: 7
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: End = 3
    return:
      type: DrawnUi.Draw.RelativePositionType
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.RelativePositionType
  commentId: T:DrawnUi.Draw.RelativePositionType
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.RelativePositionType.html
  name: RelativePositionType
  nameWithType: RelativePositionType
  fullName: DrawnUi.Draw.RelativePositionType
