### YamlMime:ManagedReference
items:
- uid: DrawnUi.Infrastructure.VisualTransform
  commentId: T:DrawnUi.Infrastructure.VisualTransform
  id: VisualTransform
  parent: DrawnUi.Infrastructure
  children:
  - DrawnUi.Infrastructure.VisualTransform.#ctor
  - DrawnUi.Infrastructure.VisualTransform.Frame
  - DrawnUi.Infrastructure.VisualTransform.IsVisible
  - DrawnUi.Infrastructure.VisualTransform.Logs
  - DrawnUi.Infrastructure.VisualTransform.Opacity
  - DrawnUi.Infrastructure.VisualTransform.RenderedNodes
  - DrawnUi.Infrastructure.VisualTransform.Rotation
  - DrawnUi.Infrastructure.VisualTransform.Scale
  - DrawnUi.Infrastructure.VisualTransform.ToNative(SkiaSharp.SKRect,System.Single)
  - DrawnUi.Infrastructure.VisualTransform.Translation
  langs:
  - csharp
  - vb
  name: VisualTransform
  nameWithType: VisualTransform
  fullName: DrawnUi.Infrastructure.VisualTransform
  type: Class
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/VisualTransform.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: VisualTransform
    path: ../src/Shared/Draw/Internals/Models/VisualTransform.cs
    startLine: 5
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  summary: Will enhance this in the future to include more properties
  example: []
  syntax:
    content: public class VisualTransform
    content.vb: Public Class VisualTransform
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Infrastructure.VisualTransform.#ctor
  commentId: M:DrawnUi.Infrastructure.VisualTransform.#ctor
  id: '#ctor'
  parent: DrawnUi.Infrastructure.VisualTransform
  langs:
  - csharp
  - vb
  name: VisualTransform()
  nameWithType: VisualTransform.VisualTransform()
  fullName: DrawnUi.Infrastructure.VisualTransform.VisualTransform()
  type: Constructor
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/VisualTransform.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Shared/Draw/Internals/Models/VisualTransform.cs
    startLine: 7
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public VisualTransform()
    content.vb: Public Sub New()
  overload: DrawnUi.Infrastructure.VisualTransform.#ctor*
  nameWithType.vb: VisualTransform.New()
  fullName.vb: DrawnUi.Infrastructure.VisualTransform.New()
  name.vb: New()
- uid: DrawnUi.Infrastructure.VisualTransform.Frame
  commentId: P:DrawnUi.Infrastructure.VisualTransform.Frame
  id: Frame
  parent: DrawnUi.Infrastructure.VisualTransform
  langs:
  - csharp
  - vb
  name: Frame
  nameWithType: VisualTransform.Frame
  fullName: DrawnUi.Infrastructure.VisualTransform.Frame
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/VisualTransform.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Frame
    path: ../src/Shared/Draw/Internals/Models/VisualTransform.cs
    startLine: 14
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public ScaledRect Frame { get; set; }
    parameters: []
    return:
      type: DrawnUi.Draw.ScaledRect
    content.vb: Public Property Frame As ScaledRect
  overload: DrawnUi.Infrastructure.VisualTransform.Frame*
- uid: DrawnUi.Infrastructure.VisualTransform.IsVisible
  commentId: P:DrawnUi.Infrastructure.VisualTransform.IsVisible
  id: IsVisible
  parent: DrawnUi.Infrastructure.VisualTransform
  langs:
  - csharp
  - vb
  name: IsVisible
  nameWithType: VisualTransform.IsVisible
  fullName: DrawnUi.Infrastructure.VisualTransform.IsVisible
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/VisualTransform.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IsVisible
    path: ../src/Shared/Draw/Internals/Models/VisualTransform.cs
    startLine: 16
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public bool IsVisible { get; set; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property IsVisible As Boolean
  overload: DrawnUi.Infrastructure.VisualTransform.IsVisible*
- uid: DrawnUi.Infrastructure.VisualTransform.Opacity
  commentId: P:DrawnUi.Infrastructure.VisualTransform.Opacity
  id: Opacity
  parent: DrawnUi.Infrastructure.VisualTransform
  langs:
  - csharp
  - vb
  name: Opacity
  nameWithType: VisualTransform.Opacity
  fullName: DrawnUi.Infrastructure.VisualTransform.Opacity
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/VisualTransform.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Opacity
    path: ../src/Shared/Draw/Internals/Models/VisualTransform.cs
    startLine: 18
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public float Opacity { get; set; }
    parameters: []
    return:
      type: System.Single
    content.vb: Public Property Opacity As Single
  overload: DrawnUi.Infrastructure.VisualTransform.Opacity*
- uid: DrawnUi.Infrastructure.VisualTransform.Rotation
  commentId: P:DrawnUi.Infrastructure.VisualTransform.Rotation
  id: Rotation
  parent: DrawnUi.Infrastructure.VisualTransform
  langs:
  - csharp
  - vb
  name: Rotation
  nameWithType: VisualTransform.Rotation
  fullName: DrawnUi.Infrastructure.VisualTransform.Rotation
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/VisualTransform.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Rotation
    path: ../src/Shared/Draw/Internals/Models/VisualTransform.cs
    startLine: 20
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public float Rotation { get; set; }
    parameters: []
    return:
      type: System.Single
    content.vb: Public Property Rotation As Single
  overload: DrawnUi.Infrastructure.VisualTransform.Rotation*
- uid: DrawnUi.Infrastructure.VisualTransform.Translation
  commentId: P:DrawnUi.Infrastructure.VisualTransform.Translation
  id: Translation
  parent: DrawnUi.Infrastructure.VisualTransform
  langs:
  - csharp
  - vb
  name: Translation
  nameWithType: VisualTransform.Translation
  fullName: DrawnUi.Infrastructure.VisualTransform.Translation
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/VisualTransform.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Translation
    path: ../src/Shared/Draw/Internals/Models/VisualTransform.cs
    startLine: 25
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  summary: Units as from TranslationX and TranslationY
  example: []
  syntax:
    content: public SKPoint Translation { get; set; }
    parameters: []
    return:
      type: SkiaSharp.SKPoint
    content.vb: Public Property Translation As SKPoint
  overload: DrawnUi.Infrastructure.VisualTransform.Translation*
- uid: DrawnUi.Infrastructure.VisualTransform.Scale
  commentId: P:DrawnUi.Infrastructure.VisualTransform.Scale
  id: Scale
  parent: DrawnUi.Infrastructure.VisualTransform
  langs:
  - csharp
  - vb
  name: Scale
  nameWithType: VisualTransform.Scale
  fullName: DrawnUi.Infrastructure.VisualTransform.Scale
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/VisualTransform.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Scale
    path: ../src/Shared/Draw/Internals/Models/VisualTransform.cs
    startLine: 30
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  summary: Units as from ScaleX and ScaleY
  example: []
  syntax:
    content: public SKPoint Scale { get; set; }
    parameters: []
    return:
      type: SkiaSharp.SKPoint
    content.vb: Public Property Scale As SKPoint
  overload: DrawnUi.Infrastructure.VisualTransform.Scale*
- uid: DrawnUi.Infrastructure.VisualTransform.Logs
  commentId: P:DrawnUi.Infrastructure.VisualTransform.Logs
  id: Logs
  parent: DrawnUi.Infrastructure.VisualTransform
  langs:
  - csharp
  - vb
  name: Logs
  nameWithType: VisualTransform.Logs
  fullName: DrawnUi.Infrastructure.VisualTransform.Logs
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/VisualTransform.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Logs
    path: ../src/Shared/Draw/Internals/Models/VisualTransform.cs
    startLine: 32
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public string Logs { get; set; }
    parameters: []
    return:
      type: System.String
    content.vb: Public Property Logs As String
  overload: DrawnUi.Infrastructure.VisualTransform.Logs*
- uid: DrawnUi.Infrastructure.VisualTransform.RenderedNodes
  commentId: P:DrawnUi.Infrastructure.VisualTransform.RenderedNodes
  id: RenderedNodes
  parent: DrawnUi.Infrastructure.VisualTransform
  langs:
  - csharp
  - vb
  name: RenderedNodes
  nameWithType: VisualTransform.RenderedNodes
  fullName: DrawnUi.Infrastructure.VisualTransform.RenderedNodes
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/VisualTransform.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: RenderedNodes
    path: ../src/Shared/Draw/Internals/Models/VisualTransform.cs
    startLine: 34
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public int RenderedNodes { get; set; }
    parameters: []
    return:
      type: System.Int32
    content.vb: Public Property RenderedNodes As Integer
  overload: DrawnUi.Infrastructure.VisualTransform.RenderedNodes*
- uid: DrawnUi.Infrastructure.VisualTransform.ToNative(SkiaSharp.SKRect,System.Single)
  commentId: M:DrawnUi.Infrastructure.VisualTransform.ToNative(SkiaSharp.SKRect,System.Single)
  id: ToNative(SkiaSharp.SKRect,System.Single)
  parent: DrawnUi.Infrastructure.VisualTransform
  langs:
  - csharp
  - vb
  name: ToNative(SKRect, float)
  nameWithType: VisualTransform.ToNative(SKRect, float)
  fullName: DrawnUi.Infrastructure.VisualTransform.ToNative(SkiaSharp.SKRect, float)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/VisualTransform.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ToNative
    path: ../src/Shared/Draw/Internals/Models/VisualTransform.cs
    startLine: 43
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  summary: All input rects are in pixels
  example: []
  syntax:
    content: public VisualTransformNative ToNative(SKRect rect, float scale)
    parameters:
    - id: rect
      type: SkiaSharp.SKRect
      description: ''
    - id: scale
      type: System.Single
      description: ''
    return:
      type: DrawnUi.Infrastructure.VisualTransformNative
      description: ''
    content.vb: Public Function ToNative(rect As SKRect, scale As Single) As VisualTransformNative
  overload: DrawnUi.Infrastructure.VisualTransform.ToNative*
  nameWithType.vb: VisualTransform.ToNative(SKRect, Single)
  fullName.vb: DrawnUi.Infrastructure.VisualTransform.ToNative(SkiaSharp.SKRect, Single)
  name.vb: ToNative(SKRect, Single)
references:
- uid: DrawnUi.Infrastructure
  commentId: N:DrawnUi.Infrastructure
  href: DrawnUi.html
  name: DrawnUi.Infrastructure
  nameWithType: DrawnUi.Infrastructure
  fullName: DrawnUi.Infrastructure
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Infrastructure
    name: Infrastructure
    href: DrawnUi.Infrastructure.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Infrastructure
    name: Infrastructure
    href: DrawnUi.Infrastructure.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Infrastructure.VisualTransform.#ctor*
  commentId: Overload:DrawnUi.Infrastructure.VisualTransform.#ctor
  href: DrawnUi.Infrastructure.VisualTransform.html#DrawnUi_Infrastructure_VisualTransform__ctor
  name: VisualTransform
  nameWithType: VisualTransform.VisualTransform
  fullName: DrawnUi.Infrastructure.VisualTransform.VisualTransform
  nameWithType.vb: VisualTransform.New
  fullName.vb: DrawnUi.Infrastructure.VisualTransform.New
  name.vb: New
- uid: DrawnUi.Infrastructure.VisualTransform.Frame*
  commentId: Overload:DrawnUi.Infrastructure.VisualTransform.Frame
  href: DrawnUi.Infrastructure.VisualTransform.html#DrawnUi_Infrastructure_VisualTransform_Frame
  name: Frame
  nameWithType: VisualTransform.Frame
  fullName: DrawnUi.Infrastructure.VisualTransform.Frame
- uid: DrawnUi.Draw.ScaledRect
  commentId: T:DrawnUi.Draw.ScaledRect
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ScaledRect.html
  name: ScaledRect
  nameWithType: ScaledRect
  fullName: DrawnUi.Draw.ScaledRect
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: DrawnUi.Infrastructure.VisualTransform.IsVisible*
  commentId: Overload:DrawnUi.Infrastructure.VisualTransform.IsVisible
  href: DrawnUi.Infrastructure.VisualTransform.html#DrawnUi_Infrastructure_VisualTransform_IsVisible
  name: IsVisible
  nameWithType: VisualTransform.IsVisible
  fullName: DrawnUi.Infrastructure.VisualTransform.IsVisible
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: DrawnUi.Infrastructure.VisualTransform.Opacity*
  commentId: Overload:DrawnUi.Infrastructure.VisualTransform.Opacity
  href: DrawnUi.Infrastructure.VisualTransform.html#DrawnUi_Infrastructure_VisualTransform_Opacity
  name: Opacity
  nameWithType: VisualTransform.Opacity
  fullName: DrawnUi.Infrastructure.VisualTransform.Opacity
- uid: System.Single
  commentId: T:System.Single
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.single
  name: float
  nameWithType: float
  fullName: float
  nameWithType.vb: Single
  fullName.vb: Single
  name.vb: Single
- uid: DrawnUi.Infrastructure.VisualTransform.Rotation*
  commentId: Overload:DrawnUi.Infrastructure.VisualTransform.Rotation
  href: DrawnUi.Infrastructure.VisualTransform.html#DrawnUi_Infrastructure_VisualTransform_Rotation
  name: Rotation
  nameWithType: VisualTransform.Rotation
  fullName: DrawnUi.Infrastructure.VisualTransform.Rotation
- uid: DrawnUi.Infrastructure.VisualTransform.Translation*
  commentId: Overload:DrawnUi.Infrastructure.VisualTransform.Translation
  href: DrawnUi.Infrastructure.VisualTransform.html#DrawnUi_Infrastructure_VisualTransform_Translation
  name: Translation
  nameWithType: VisualTransform.Translation
  fullName: DrawnUi.Infrastructure.VisualTransform.Translation
- uid: SkiaSharp.SKPoint
  commentId: T:SkiaSharp.SKPoint
  parent: SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.skpoint
  name: SKPoint
  nameWithType: SKPoint
  fullName: SkiaSharp.SKPoint
- uid: SkiaSharp
  commentId: N:SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp
  name: SkiaSharp
  nameWithType: SkiaSharp
  fullName: SkiaSharp
- uid: DrawnUi.Infrastructure.VisualTransform.Scale*
  commentId: Overload:DrawnUi.Infrastructure.VisualTransform.Scale
  href: DrawnUi.Infrastructure.VisualTransform.html#DrawnUi_Infrastructure_VisualTransform_Scale
  name: Scale
  nameWithType: VisualTransform.Scale
  fullName: DrawnUi.Infrastructure.VisualTransform.Scale
- uid: DrawnUi.Infrastructure.VisualTransform.Logs*
  commentId: Overload:DrawnUi.Infrastructure.VisualTransform.Logs
  href: DrawnUi.Infrastructure.VisualTransform.html#DrawnUi_Infrastructure_VisualTransform_Logs
  name: Logs
  nameWithType: VisualTransform.Logs
  fullName: DrawnUi.Infrastructure.VisualTransform.Logs
- uid: System.String
  commentId: T:System.String
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.string
  name: string
  nameWithType: string
  fullName: string
  nameWithType.vb: String
  fullName.vb: String
  name.vb: String
- uid: DrawnUi.Infrastructure.VisualTransform.RenderedNodes*
  commentId: Overload:DrawnUi.Infrastructure.VisualTransform.RenderedNodes
  href: DrawnUi.Infrastructure.VisualTransform.html#DrawnUi_Infrastructure_VisualTransform_RenderedNodes
  name: RenderedNodes
  nameWithType: VisualTransform.RenderedNodes
  fullName: DrawnUi.Infrastructure.VisualTransform.RenderedNodes
- uid: System.Int32
  commentId: T:System.Int32
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.int32
  name: int
  nameWithType: int
  fullName: int
  nameWithType.vb: Integer
  fullName.vb: Integer
  name.vb: Integer
- uid: DrawnUi.Infrastructure.VisualTransform.ToNative*
  commentId: Overload:DrawnUi.Infrastructure.VisualTransform.ToNative
  href: DrawnUi.Infrastructure.VisualTransform.html#DrawnUi_Infrastructure_VisualTransform_ToNative_SkiaSharp_SKRect_System_Single_
  name: ToNative
  nameWithType: VisualTransform.ToNative
  fullName: DrawnUi.Infrastructure.VisualTransform.ToNative
- uid: SkiaSharp.SKRect
  commentId: T:SkiaSharp.SKRect
  parent: SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.skrect
  name: SKRect
  nameWithType: SKRect
  fullName: SkiaSharp.SKRect
- uid: DrawnUi.Infrastructure.VisualTransformNative
  commentId: T:DrawnUi.Infrastructure.VisualTransformNative
  parent: DrawnUi.Infrastructure
  href: DrawnUi.Infrastructure.VisualTransformNative.html
  name: VisualTransformNative
  nameWithType: VisualTransformNative
  fullName: DrawnUi.Infrastructure.VisualTransformNative
