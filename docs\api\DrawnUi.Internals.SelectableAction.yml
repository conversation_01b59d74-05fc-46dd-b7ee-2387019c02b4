### YamlMime:ManagedReference
items:
- uid: DrawnUi.Internals.SelectableAction
  commentId: T:DrawnUi.Internals.SelectableAction
  id: SelectableAction
  parent: DrawnUi.Internals
  children:
  - DrawnUi.Internals.SelectableAction.#ctor
  - DrawnUi.Internals.SelectableAction.#ctor(System.String,System.Action)
  - DrawnUi.Internals.SelectableAction.#ctor(System.String,System.String,System.Action)
  - DrawnUi.Internals.SelectableAction.Action
  - DrawnUi.Internals.SelectableAction.IsReadOnly
  - DrawnUi.Internals.SelectableAction.Selected
  langs:
  - csharp
  - vb
  name: SelectableAction
  nameWithType: SelectableAction
  fullName: DrawnUi.Internals.SelectableAction
  type: Class
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/SelectableAction.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: SelectableAction
    path: ../src/Shared/Draw/Internals/Models/SelectableAction.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Internals
  syntax:
    content: 'public class SelectableAction : TitleWithStringId, IHasTitleWithId, IHasStringTitle, IHasStringId'
    content.vb: Public Class SelectableAction Inherits TitleWithStringId Implements IHasTitleWithId, IHasStringTitle, IHasStringId
  inheritance:
  - System.Object
  - DrawnUi.Internals.TitleWithStringId
  implements:
  - AppoMobi.Specials.IHasTitleWithId
  - AppoMobi.Specials.IHasStringTitle
  - AppoMobi.Specials.IHasStringId
  inheritedMembers:
  - DrawnUi.Internals.TitleWithStringId.Id
  - DrawnUi.Internals.TitleWithStringId.Title
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Internals.SelectableAction.#ctor
  commentId: M:DrawnUi.Internals.SelectableAction.#ctor
  id: '#ctor'
  parent: DrawnUi.Internals.SelectableAction
  langs:
  - csharp
  - vb
  name: SelectableAction()
  nameWithType: SelectableAction.SelectableAction()
  fullName: DrawnUi.Internals.SelectableAction.SelectableAction()
  type: Constructor
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/SelectableAction.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Shared/Draw/Internals/Models/SelectableAction.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Internals
  syntax:
    content: public SelectableAction()
    content.vb: Public Sub New()
  overload: DrawnUi.Internals.SelectableAction.#ctor*
  nameWithType.vb: SelectableAction.New()
  fullName.vb: DrawnUi.Internals.SelectableAction.New()
  name.vb: New()
- uid: DrawnUi.Internals.SelectableAction.#ctor(System.String,System.Action)
  commentId: M:DrawnUi.Internals.SelectableAction.#ctor(System.String,System.Action)
  id: '#ctor(System.String,System.Action)'
  parent: DrawnUi.Internals.SelectableAction
  langs:
  - csharp
  - vb
  name: SelectableAction(string, Action)
  nameWithType: SelectableAction.SelectableAction(string, Action)
  fullName: DrawnUi.Internals.SelectableAction.SelectableAction(string, System.Action)
  type: Constructor
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/SelectableAction.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Shared/Draw/Internals/Models/SelectableAction.cs
    startLine: 8
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Internals
  syntax:
    content: public SelectableAction(string title, Action action)
    parameters:
    - id: title
      type: System.String
    - id: action
      type: System.Action
    content.vb: Public Sub New(title As String, action As Action)
  overload: DrawnUi.Internals.SelectableAction.#ctor*
  nameWithType.vb: SelectableAction.New(String, Action)
  fullName.vb: DrawnUi.Internals.SelectableAction.New(String, System.Action)
  name.vb: New(String, Action)
- uid: DrawnUi.Internals.SelectableAction.#ctor(System.String,System.String,System.Action)
  commentId: M:DrawnUi.Internals.SelectableAction.#ctor(System.String,System.String,System.Action)
  id: '#ctor(System.String,System.String,System.Action)'
  parent: DrawnUi.Internals.SelectableAction
  langs:
  - csharp
  - vb
  name: SelectableAction(string, string, Action)
  nameWithType: SelectableAction.SelectableAction(string, string, Action)
  fullName: DrawnUi.Internals.SelectableAction.SelectableAction(string, string, System.Action)
  type: Constructor
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/SelectableAction.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Shared/Draw/Internals/Models/SelectableAction.cs
    startLine: 15
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Internals
  syntax:
    content: public SelectableAction(string id, string title, Action action)
    parameters:
    - id: id
      type: System.String
    - id: title
      type: System.String
    - id: action
      type: System.Action
    content.vb: Public Sub New(id As String, title As String, action As Action)
  overload: DrawnUi.Internals.SelectableAction.#ctor*
  nameWithType.vb: SelectableAction.New(String, String, Action)
  fullName.vb: DrawnUi.Internals.SelectableAction.New(String, String, System.Action)
  name.vb: New(String, String, Action)
- uid: DrawnUi.Internals.SelectableAction.Action
  commentId: P:DrawnUi.Internals.SelectableAction.Action
  id: Action
  parent: DrawnUi.Internals.SelectableAction
  langs:
  - csharp
  - vb
  name: Action
  nameWithType: SelectableAction.Action
  fullName: DrawnUi.Internals.SelectableAction.Action
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/SelectableAction.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Action
    path: ../src/Shared/Draw/Internals/Models/SelectableAction.cs
    startLine: 23
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Internals
  syntax:
    content: public Action Action { get; set; }
    parameters: []
    return:
      type: System.Action
    content.vb: Public Property Action As Action
  overload: DrawnUi.Internals.SelectableAction.Action*
- uid: DrawnUi.Internals.SelectableAction.Selected
  commentId: P:DrawnUi.Internals.SelectableAction.Selected
  id: Selected
  parent: DrawnUi.Internals.SelectableAction
  langs:
  - csharp
  - vb
  name: Selected
  nameWithType: SelectableAction.Selected
  fullName: DrawnUi.Internals.SelectableAction.Selected
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/SelectableAction.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Selected
    path: ../src/Shared/Draw/Internals/Models/SelectableAction.cs
    startLine: 24
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Internals
  syntax:
    content: public bool Selected { get; set; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property Selected As Boolean
  overload: DrawnUi.Internals.SelectableAction.Selected*
- uid: DrawnUi.Internals.SelectableAction.IsReadOnly
  commentId: P:DrawnUi.Internals.SelectableAction.IsReadOnly
  id: IsReadOnly
  parent: DrawnUi.Internals.SelectableAction
  langs:
  - csharp
  - vb
  name: IsReadOnly
  nameWithType: SelectableAction.IsReadOnly
  fullName: DrawnUi.Internals.SelectableAction.IsReadOnly
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/SelectableAction.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IsReadOnly
    path: ../src/Shared/Draw/Internals/Models/SelectableAction.cs
    startLine: 26
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Internals
  syntax:
    content: public bool IsReadOnly { get; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public ReadOnly Property IsReadOnly As Boolean
  overload: DrawnUi.Internals.SelectableAction.IsReadOnly*
references:
- uid: DrawnUi.Internals
  commentId: N:DrawnUi.Internals
  href: DrawnUi.html
  name: DrawnUi.Internals
  nameWithType: DrawnUi.Internals
  fullName: DrawnUi.Internals
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Internals
    name: Internals
    href: DrawnUi.Internals.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Internals
    name: Internals
    href: DrawnUi.Internals.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: DrawnUi.Internals.TitleWithStringId
  commentId: T:DrawnUi.Internals.TitleWithStringId
  parent: DrawnUi.Internals
  href: DrawnUi.Internals.TitleWithStringId.html
  name: TitleWithStringId
  nameWithType: TitleWithStringId
  fullName: DrawnUi.Internals.TitleWithStringId
- uid: AppoMobi.Specials.IHasTitleWithId
  commentId: T:AppoMobi.Specials.IHasTitleWithId
  parent: AppoMobi.Specials
  isExternal: true
  name: IHasTitleWithId
  nameWithType: IHasTitleWithId
  fullName: AppoMobi.Specials.IHasTitleWithId
- uid: AppoMobi.Specials.IHasStringTitle
  commentId: T:AppoMobi.Specials.IHasStringTitle
  parent: AppoMobi.Specials
  isExternal: true
  name: IHasStringTitle
  nameWithType: IHasStringTitle
  fullName: AppoMobi.Specials.IHasStringTitle
- uid: AppoMobi.Specials.IHasStringId
  commentId: T:AppoMobi.Specials.IHasStringId
  parent: AppoMobi.Specials
  isExternal: true
  name: IHasStringId
  nameWithType: IHasStringId
  fullName: AppoMobi.Specials.IHasStringId
- uid: DrawnUi.Internals.TitleWithStringId.Id
  commentId: P:DrawnUi.Internals.TitleWithStringId.Id
  parent: DrawnUi.Internals.TitleWithStringId
  href: DrawnUi.Internals.TitleWithStringId.html#DrawnUi_Internals_TitleWithStringId_Id
  name: Id
  nameWithType: TitleWithStringId.Id
  fullName: DrawnUi.Internals.TitleWithStringId.Id
- uid: DrawnUi.Internals.TitleWithStringId.Title
  commentId: P:DrawnUi.Internals.TitleWithStringId.Title
  parent: DrawnUi.Internals.TitleWithStringId
  href: DrawnUi.Internals.TitleWithStringId.html#DrawnUi_Internals_TitleWithStringId_Title
  name: Title
  nameWithType: TitleWithStringId.Title
  fullName: DrawnUi.Internals.TitleWithStringId.Title
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: AppoMobi.Specials
  commentId: N:AppoMobi.Specials
  isExternal: true
  name: AppoMobi.Specials
  nameWithType: AppoMobi.Specials
  fullName: AppoMobi.Specials
  spec.csharp:
  - uid: AppoMobi
    name: AppoMobi
    isExternal: true
  - name: .
  - uid: AppoMobi.Specials
    name: Specials
    isExternal: true
  spec.vb:
  - uid: AppoMobi
    name: AppoMobi
    isExternal: true
  - name: .
  - uid: AppoMobi.Specials
    name: Specials
    isExternal: true
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Internals.SelectableAction.#ctor*
  commentId: Overload:DrawnUi.Internals.SelectableAction.#ctor
  href: DrawnUi.Internals.SelectableAction.html#DrawnUi_Internals_SelectableAction__ctor
  name: SelectableAction
  nameWithType: SelectableAction.SelectableAction
  fullName: DrawnUi.Internals.SelectableAction.SelectableAction
  nameWithType.vb: SelectableAction.New
  fullName.vb: DrawnUi.Internals.SelectableAction.New
  name.vb: New
- uid: System.String
  commentId: T:System.String
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.string
  name: string
  nameWithType: string
  fullName: string
  nameWithType.vb: String
  fullName.vb: String
  name.vb: String
- uid: System.Action
  commentId: T:System.Action
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.action
  name: Action
  nameWithType: Action
  fullName: System.Action
- uid: DrawnUi.Internals.SelectableAction.Action*
  commentId: Overload:DrawnUi.Internals.SelectableAction.Action
  href: DrawnUi.Internals.SelectableAction.html#DrawnUi_Internals_SelectableAction_Action
  name: Action
  nameWithType: SelectableAction.Action
  fullName: DrawnUi.Internals.SelectableAction.Action
- uid: DrawnUi.Internals.SelectableAction.Selected*
  commentId: Overload:DrawnUi.Internals.SelectableAction.Selected
  href: DrawnUi.Internals.SelectableAction.html#DrawnUi_Internals_SelectableAction_Selected
  name: Selected
  nameWithType: SelectableAction.Selected
  fullName: DrawnUi.Internals.SelectableAction.Selected
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: DrawnUi.Internals.SelectableAction.IsReadOnly*
  commentId: Overload:DrawnUi.Internals.SelectableAction.IsReadOnly
  href: DrawnUi.Internals.SelectableAction.html#DrawnUi_Internals_SelectableAction_IsReadOnly
  name: IsReadOnly
  nameWithType: SelectableAction.IsReadOnly
  fullName: DrawnUi.Internals.SelectableAction.IsReadOnly
