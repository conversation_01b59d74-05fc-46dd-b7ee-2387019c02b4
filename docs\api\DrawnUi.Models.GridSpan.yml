### YamlMime:ManagedReference
items:
- uid: DrawnUi.Models.GridSpan
  commentId: T:DrawnUi.Models.GridSpan
  id: GridSpan
  parent: DrawnUi.Models
  children:
  - DrawnUi.Models.GridSpan.#ctor(System.Int32,System.Int32,System.<PERSON>an,System.Double)
  - DrawnUi.Models.GridSpan.IsColumn
  - DrawnUi.Models.GridSpan.Key
  - DrawnUi.Models.GridSpan.Length
  - DrawnUi.Models.GridSpan.Requested
  - DrawnUi.Models.GridSpan.Start
  langs:
  - csharp
  - vb
  name: GridSpan
  nameWithType: GridSpan
  fullName: DrawnUi.Models.GridSpan
  type: Class
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/GridSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: GridSpan
    path: ../src/Shared/Draw/Internals/Models/GridSpan.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Models
  syntax:
    content: public class GridSpan
    content.vb: Public Class GridSpan
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Models.GridSpan.Start
  commentId: P:DrawnUi.Models.GridSpan.Start
  id: Start
  parent: DrawnUi.Models.GridSpan
  langs:
  - csharp
  - vb
  name: Start
  nameWithType: GridSpan.Start
  fullName: DrawnUi.Models.GridSpan.Start
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/GridSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Start
    path: ../src/Shared/Draw/Internals/Models/GridSpan.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Models
  syntax:
    content: public int Start { get; }
    parameters: []
    return:
      type: System.Int32
    content.vb: Public ReadOnly Property Start As Integer
  overload: DrawnUi.Models.GridSpan.Start*
- uid: DrawnUi.Models.GridSpan.Length
  commentId: P:DrawnUi.Models.GridSpan.Length
  id: Length
  parent: DrawnUi.Models.GridSpan
  langs:
  - csharp
  - vb
  name: Length
  nameWithType: GridSpan.Length
  fullName: DrawnUi.Models.GridSpan.Length
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/GridSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Length
    path: ../src/Shared/Draw/Internals/Models/GridSpan.cs
    startLine: 5
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Models
  syntax:
    content: public int Length { get; }
    parameters: []
    return:
      type: System.Int32
    content.vb: Public ReadOnly Property Length As Integer
  overload: DrawnUi.Models.GridSpan.Length*
- uid: DrawnUi.Models.GridSpan.IsColumn
  commentId: P:DrawnUi.Models.GridSpan.IsColumn
  id: IsColumn
  parent: DrawnUi.Models.GridSpan
  langs:
  - csharp
  - vb
  name: IsColumn
  nameWithType: GridSpan.IsColumn
  fullName: DrawnUi.Models.GridSpan.IsColumn
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/GridSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IsColumn
    path: ../src/Shared/Draw/Internals/Models/GridSpan.cs
    startLine: 6
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Models
  syntax:
    content: public bool IsColumn { get; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public ReadOnly Property IsColumn As Boolean
  overload: DrawnUi.Models.GridSpan.IsColumn*
- uid: DrawnUi.Models.GridSpan.Requested
  commentId: P:DrawnUi.Models.GridSpan.Requested
  id: Requested
  parent: DrawnUi.Models.GridSpan
  langs:
  - csharp
  - vb
  name: Requested
  nameWithType: GridSpan.Requested
  fullName: DrawnUi.Models.GridSpan.Requested
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/GridSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Requested
    path: ../src/Shared/Draw/Internals/Models/GridSpan.cs
    startLine: 7
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Models
  syntax:
    content: public double Requested { get; }
    parameters: []
    return:
      type: System.Double
    content.vb: Public ReadOnly Property Requested As Double
  overload: DrawnUi.Models.GridSpan.Requested*
- uid: DrawnUi.Models.GridSpan.Key
  commentId: P:DrawnUi.Models.GridSpan.Key
  id: Key
  parent: DrawnUi.Models.GridSpan
  langs:
  - csharp
  - vb
  name: Key
  nameWithType: GridSpan.Key
  fullName: DrawnUi.Models.GridSpan.Key
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/GridSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Key
    path: ../src/Shared/Draw/Internals/Models/GridSpan.cs
    startLine: 9
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Models
  syntax:
    content: public SpanKey Key { get; }
    parameters: []
    return:
      type: DrawnUi.Models.SpanKey
    content.vb: Public ReadOnly Property Key As SpanKey
  overload: DrawnUi.Models.GridSpan.Key*
- uid: DrawnUi.Models.GridSpan.#ctor(System.Int32,System.Int32,System.Boolean,System.Double)
  commentId: M:DrawnUi.Models.GridSpan.#ctor(System.Int32,System.Int32,System.Boolean,System.Double)
  id: '#ctor(System.Int32,System.Int32,System.Boolean,System.Double)'
  parent: DrawnUi.Models.GridSpan
  langs:
  - csharp
  - vb
  name: GridSpan(int, int, bool, double)
  nameWithType: GridSpan.GridSpan(int, int, bool, double)
  fullName: DrawnUi.Models.GridSpan.GridSpan(int, int, bool, double)
  type: Constructor
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/GridSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Shared/Draw/Internals/Models/GridSpan.cs
    startLine: 11
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Models
  syntax:
    content: public GridSpan(int start, int length, bool isColumn, double requestedLength)
    parameters:
    - id: start
      type: System.Int32
    - id: length
      type: System.Int32
    - id: isColumn
      type: System.Boolean
    - id: requestedLength
      type: System.Double
    content.vb: Public Sub New(start As Integer, length As Integer, isColumn As Boolean, requestedLength As Double)
  overload: DrawnUi.Models.GridSpan.#ctor*
  nameWithType.vb: GridSpan.New(Integer, Integer, Boolean, Double)
  fullName.vb: DrawnUi.Models.GridSpan.New(Integer, Integer, Boolean, Double)
  name.vb: New(Integer, Integer, Boolean, Double)
references:
- uid: DrawnUi.Models
  commentId: N:DrawnUi.Models
  href: DrawnUi.html
  name: DrawnUi.Models
  nameWithType: DrawnUi.Models
  fullName: DrawnUi.Models
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Models
    name: Models
    href: DrawnUi.Models.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Models
    name: Models
    href: DrawnUi.Models.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Models.GridSpan.Start*
  commentId: Overload:DrawnUi.Models.GridSpan.Start
  href: DrawnUi.Models.GridSpan.html#DrawnUi_Models_GridSpan_Start
  name: Start
  nameWithType: GridSpan.Start
  fullName: DrawnUi.Models.GridSpan.Start
- uid: System.Int32
  commentId: T:System.Int32
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.int32
  name: int
  nameWithType: int
  fullName: int
  nameWithType.vb: Integer
  fullName.vb: Integer
  name.vb: Integer
- uid: DrawnUi.Models.GridSpan.Length*
  commentId: Overload:DrawnUi.Models.GridSpan.Length
  href: DrawnUi.Models.GridSpan.html#DrawnUi_Models_GridSpan_Length
  name: Length
  nameWithType: GridSpan.Length
  fullName: DrawnUi.Models.GridSpan.Length
- uid: DrawnUi.Models.GridSpan.IsColumn*
  commentId: Overload:DrawnUi.Models.GridSpan.IsColumn
  href: DrawnUi.Models.GridSpan.html#DrawnUi_Models_GridSpan_IsColumn
  name: IsColumn
  nameWithType: GridSpan.IsColumn
  fullName: DrawnUi.Models.GridSpan.IsColumn
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: DrawnUi.Models.GridSpan.Requested*
  commentId: Overload:DrawnUi.Models.GridSpan.Requested
  href: DrawnUi.Models.GridSpan.html#DrawnUi_Models_GridSpan_Requested
  name: Requested
  nameWithType: GridSpan.Requested
  fullName: DrawnUi.Models.GridSpan.Requested
- uid: System.Double
  commentId: T:System.Double
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.double
  name: double
  nameWithType: double
  fullName: double
  nameWithType.vb: Double
  fullName.vb: Double
  name.vb: Double
- uid: DrawnUi.Models.GridSpan.Key*
  commentId: Overload:DrawnUi.Models.GridSpan.Key
  href: DrawnUi.Models.GridSpan.html#DrawnUi_Models_GridSpan_Key
  name: Key
  nameWithType: GridSpan.Key
  fullName: DrawnUi.Models.GridSpan.Key
- uid: DrawnUi.Models.SpanKey
  commentId: T:DrawnUi.Models.SpanKey
  parent: DrawnUi.Models
  href: DrawnUi.Models.SpanKey.html
  name: SpanKey
  nameWithType: SpanKey
  fullName: DrawnUi.Models.SpanKey
- uid: DrawnUi.Models.GridSpan.#ctor*
  commentId: Overload:DrawnUi.Models.GridSpan.#ctor
  href: DrawnUi.Models.GridSpan.html#DrawnUi_Models_GridSpan__ctor_System_Int32_System_Int32_System_Boolean_System_Double_
  name: GridSpan
  nameWithType: GridSpan.GridSpan
  fullName: DrawnUi.Models.GridSpan.GridSpan
  nameWithType.vb: GridSpan.New
  fullName.vb: DrawnUi.Models.GridSpan.New
  name.vb: New
