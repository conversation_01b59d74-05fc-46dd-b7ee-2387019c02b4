### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.DescendingZIndexGestureListenerComparer
  commentId: T:DrawnUi.Draw.DescendingZIndexGestureListenerComparer
  id: DescendingZIndexGestureListenerComparer
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.DescendingZIndexGestureListenerComparer.Compare(DrawnUi.Draw.ISkiaGestureListener,DrawnUi.Draw.ISkiaGestureListener)
  langs:
  - csharp
  - vb
  name: DescendingZIndexGestureListenerComparer
  nameWithType: DescendingZIndexGestureListenerComparer
  fullName: DrawnUi.Draw.DescendingZIndexGestureListenerComparer
  type: Class
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/DescendingZIndexGestureListenerComparer.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: DescendingZIndexGestureListenerComparer
    path: ../src/Shared/Draw/Internals/Models/DescendingZIndexGestureListenerComparer.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: 'public class DescendingZIndexGestureListenerComparer : IComparer<ISkiaGestureListener>'
    content.vb: Public Class DescendingZIndexGestureListenerComparer Implements IComparer(Of ISkiaGestureListener)
  inheritance:
  - System.Object
  implements:
  - System.Collections.Generic.IComparer{DrawnUi.Draw.ISkiaGestureListener}
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.DescendingZIndexGestureListenerComparer.Compare(DrawnUi.Draw.ISkiaGestureListener,DrawnUi.Draw.ISkiaGestureListener)
  commentId: M:DrawnUi.Draw.DescendingZIndexGestureListenerComparer.Compare(DrawnUi.Draw.ISkiaGestureListener,DrawnUi.Draw.ISkiaGestureListener)
  id: Compare(DrawnUi.Draw.ISkiaGestureListener,DrawnUi.Draw.ISkiaGestureListener)
  parent: DrawnUi.Draw.DescendingZIndexGestureListenerComparer
  langs:
  - csharp
  - vb
  name: Compare(ISkiaGestureListener, ISkiaGestureListener)
  nameWithType: DescendingZIndexGestureListenerComparer.Compare(ISkiaGestureListener, ISkiaGestureListener)
  fullName: DrawnUi.Draw.DescendingZIndexGestureListenerComparer.Compare(DrawnUi.Draw.ISkiaGestureListener, DrawnUi.Draw.ISkiaGestureListener)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/DescendingZIndexGestureListenerComparer.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Compare
    path: ../src/Shared/Draw/Internals/Models/DescendingZIndexGestureListenerComparer.cs
    startLine: 6
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Compares two objects and returns a value indicating whether one is less than, equal to, or greater than the other.
  example: []
  syntax:
    content: public int Compare(ISkiaGestureListener x, ISkiaGestureListener y)
    parameters:
    - id: x
      type: DrawnUi.Draw.ISkiaGestureListener
      description: The first object to compare.
    - id: y
      type: DrawnUi.Draw.ISkiaGestureListener
      description: The second object to compare.
    return:
      type: System.Int32
      description: >-
        A signed integer that indicates the relative values of <code class="paramref">x</code> and <code class="paramref">y</code>, as shown in the following table.  

         <table><thead><tr><th class="term"> Value</th><th class="description"> Meaning</th></tr></thead><tbody><tr><td class="term"> Less than zero</td><td class="description"><code class="paramref">x</code> is less than <code class="paramref">y</code>.</td></tr><tr><td class="term"> Zero</td><td class="description"><code class="paramref">x</code> equals <code class="paramref">y</code>.</td></tr><tr><td class="term"> Greater than zero</td><td class="description"><code class="paramref">x</code> is greater than <code class="paramref">y</code>.</td></tr></tbody></table>
    content.vb: Public Function Compare(x As ISkiaGestureListener, y As ISkiaGestureListener) As Integer
  overload: DrawnUi.Draw.DescendingZIndexGestureListenerComparer.Compare*
  implements:
  - System.Collections.Generic.IComparer{DrawnUi.Draw.ISkiaGestureListener}.Compare(DrawnUi.Draw.ISkiaGestureListener,DrawnUi.Draw.ISkiaGestureListener)
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Collections.Generic.IComparer{DrawnUi.Draw.ISkiaGestureListener}
  commentId: T:System.Collections.Generic.IComparer{DrawnUi.Draw.ISkiaGestureListener}
  parent: System.Collections.Generic
  definition: System.Collections.Generic.IComparer`1
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.icomparer-1
  name: IComparer<ISkiaGestureListener>
  nameWithType: IComparer<ISkiaGestureListener>
  fullName: System.Collections.Generic.IComparer<DrawnUi.Draw.ISkiaGestureListener>
  nameWithType.vb: IComparer(Of ISkiaGestureListener)
  fullName.vb: System.Collections.Generic.IComparer(Of DrawnUi.Draw.ISkiaGestureListener)
  name.vb: IComparer(Of ISkiaGestureListener)
  spec.csharp:
  - uid: System.Collections.Generic.IComparer`1
    name: IComparer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.icomparer-1
  - name: <
  - uid: DrawnUi.Draw.ISkiaGestureListener
    name: ISkiaGestureListener
    href: DrawnUi.Draw.ISkiaGestureListener.html
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.IComparer`1
    name: IComparer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.icomparer-1
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Draw.ISkiaGestureListener
    name: ISkiaGestureListener
    href: DrawnUi.Draw.ISkiaGestureListener.html
  - name: )
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: System.Collections.Generic.IComparer`1
  commentId: T:System.Collections.Generic.IComparer`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.icomparer-1
  name: IComparer<T>
  nameWithType: IComparer<T>
  fullName: System.Collections.Generic.IComparer<T>
  nameWithType.vb: IComparer(Of T)
  fullName.vb: System.Collections.Generic.IComparer(Of T)
  name.vb: IComparer(Of T)
  spec.csharp:
  - uid: System.Collections.Generic.IComparer`1
    name: IComparer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.icomparer-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.IComparer`1
    name: IComparer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.icomparer-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: System.Collections.Generic
  commentId: N:System.Collections.Generic
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Collections.Generic
  nameWithType: System.Collections.Generic
  fullName: System.Collections.Generic
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Generic
    name: Generic
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Generic
    name: Generic
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.DescendingZIndexGestureListenerComparer.Compare*
  commentId: Overload:DrawnUi.Draw.DescendingZIndexGestureListenerComparer.Compare
  href: DrawnUi.Draw.DescendingZIndexGestureListenerComparer.html#DrawnUi_Draw_DescendingZIndexGestureListenerComparer_Compare_DrawnUi_Draw_ISkiaGestureListener_DrawnUi_Draw_ISkiaGestureListener_
  name: Compare
  nameWithType: DescendingZIndexGestureListenerComparer.Compare
  fullName: DrawnUi.Draw.DescendingZIndexGestureListenerComparer.Compare
- uid: System.Collections.Generic.IComparer{DrawnUi.Draw.ISkiaGestureListener}.Compare(DrawnUi.Draw.ISkiaGestureListener,DrawnUi.Draw.ISkiaGestureListener)
  commentId: M:System.Collections.Generic.IComparer{DrawnUi.Draw.ISkiaGestureListener}.Compare(DrawnUi.Draw.ISkiaGestureListener,DrawnUi.Draw.ISkiaGestureListener)
  parent: System.Collections.Generic.IComparer{DrawnUi.Draw.ISkiaGestureListener}
  definition: System.Collections.Generic.IComparer`1.Compare(`0,`0)
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.icomparer-1.compare
  name: Compare(ISkiaGestureListener, ISkiaGestureListener)
  nameWithType: IComparer<ISkiaGestureListener>.Compare(ISkiaGestureListener, ISkiaGestureListener)
  fullName: System.Collections.Generic.IComparer<DrawnUi.Draw.ISkiaGestureListener>.Compare(DrawnUi.Draw.ISkiaGestureListener, DrawnUi.Draw.ISkiaGestureListener)
  nameWithType.vb: IComparer(Of ISkiaGestureListener).Compare(ISkiaGestureListener, ISkiaGestureListener)
  fullName.vb: System.Collections.Generic.IComparer(Of DrawnUi.Draw.ISkiaGestureListener).Compare(DrawnUi.Draw.ISkiaGestureListener, DrawnUi.Draw.ISkiaGestureListener)
  spec.csharp:
  - uid: System.Collections.Generic.IComparer{DrawnUi.Draw.ISkiaGestureListener}.Compare(DrawnUi.Draw.ISkiaGestureListener,DrawnUi.Draw.ISkiaGestureListener)
    name: Compare
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.icomparer-1.compare
  - name: (
  - uid: DrawnUi.Draw.ISkiaGestureListener
    name: ISkiaGestureListener
    href: DrawnUi.Draw.ISkiaGestureListener.html
  - name: ','
  - name: " "
  - uid: DrawnUi.Draw.ISkiaGestureListener
    name: ISkiaGestureListener
    href: DrawnUi.Draw.ISkiaGestureListener.html
  - name: )
  spec.vb:
  - uid: System.Collections.Generic.IComparer{DrawnUi.Draw.ISkiaGestureListener}.Compare(DrawnUi.Draw.ISkiaGestureListener,DrawnUi.Draw.ISkiaGestureListener)
    name: Compare
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.icomparer-1.compare
  - name: (
  - uid: DrawnUi.Draw.ISkiaGestureListener
    name: ISkiaGestureListener
    href: DrawnUi.Draw.ISkiaGestureListener.html
  - name: ','
  - name: " "
  - uid: DrawnUi.Draw.ISkiaGestureListener
    name: ISkiaGestureListener
    href: DrawnUi.Draw.ISkiaGestureListener.html
  - name: )
- uid: DrawnUi.Draw.ISkiaGestureListener
  commentId: T:DrawnUi.Draw.ISkiaGestureListener
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ISkiaGestureListener.html
  name: ISkiaGestureListener
  nameWithType: ISkiaGestureListener
  fullName: DrawnUi.Draw.ISkiaGestureListener
- uid: System.Int32
  commentId: T:System.Int32
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.int32
  name: int
  nameWithType: int
  fullName: int
  nameWithType.vb: Integer
  fullName.vb: Integer
  name.vb: Integer
- uid: System.Collections.Generic.IComparer`1.Compare(`0,`0)
  commentId: M:System.Collections.Generic.IComparer`1.Compare(`0,`0)
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.icomparer-1.compare
  name: Compare(T, T)
  nameWithType: IComparer<T>.Compare(T, T)
  fullName: System.Collections.Generic.IComparer<T>.Compare(T, T)
  nameWithType.vb: IComparer(Of T).Compare(T, T)
  fullName.vb: System.Collections.Generic.IComparer(Of T).Compare(T, T)
  spec.csharp:
  - uid: System.Collections.Generic.IComparer`1.Compare(`0,`0)
    name: Compare
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.icomparer-1.compare
  - name: (
  - name: T
  - name: ','
  - name: " "
  - name: T
  - name: )
  spec.vb:
  - uid: System.Collections.Generic.IComparer`1.Compare(`0,`0)
    name: Compare
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.icomparer-1.compare
  - name: (
  - name: T
  - name: ','
  - name: " "
  - name: T
  - name: )
