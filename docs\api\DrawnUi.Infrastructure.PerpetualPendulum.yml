### YamlMime:ManagedReference
items:
- uid: DrawnUi.Infrastructure.PerpetualPendulum
  commentId: T:DrawnUi.Infrastructure.PerpetualPendulum
  id: PerpetualPendulum
  parent: DrawnUi.Infrastructure
  children:
  - DrawnUi.Infrastructure.PerpetualPendulum.Update(System.Double)
  langs:
  - csharp
  - vb
  name: PerpetualPendulum
  nameWithType: PerpetualPendulum
  fullName: DrawnUi.Infrastructure.PerpetualPendulum
  type: Class
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/Pendulum.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: PerpetualPendulum
    path: ../src/Shared/Draw/Internals/Helpers/Pendulum.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: 'public class PerpetualPendulum : Pendulum'
    content.vb: Public Class PerpetualPendulum Inherits Pendulum
  inheritance:
  - System.Object
  - DrawnUi.Infrastructure.Pendulum
  inheritedMembers:
  - DrawnUi.Infrastructure.Pendulum.wireVector
  - DrawnUi.Infrastructure.Pendulum.accelerationVector
  - DrawnUi.Infrastructure.Pendulum.velocityVector
  - DrawnUi.Infrastructure.Pendulum.angularAcceleration
  - DrawnUi.Infrastructure.Pendulum.AngularVelocity
  - DrawnUi.Infrastructure.Pendulum.RodLength
  - DrawnUi.Infrastructure.Pendulum.AirResistance
  - DrawnUi.Infrastructure.Pendulum.Gravity
  - DrawnUi.Infrastructure.Pendulum.angle
  - DrawnUi.Infrastructure.Pendulum.Reset
  - DrawnUi.Infrastructure.Pendulum.SetAmplitude(System.Double)
  - DrawnUi.Infrastructure.Pendulum.getInitialAngle
  - DrawnUi.Infrastructure.Pendulum.setInitialAngle(System.Double)
  - DrawnUi.Infrastructure.Pendulum.getInitialVelocity
  - DrawnUi.Infrastructure.Pendulum.setInitialVelocity(System.Double)
  - DrawnUi.Infrastructure.Pendulum.getAngle
  - DrawnUi.Infrastructure.Pendulum.getAirResistence
  - DrawnUi.Infrastructure.Pendulum.setAirResistance(System.Double)
  - DrawnUi.Infrastructure.Pendulum.getWireVector
  - DrawnUi.Infrastructure.Pendulum.getAccelerationVector
  - DrawnUi.Infrastructure.Pendulum.getVelocityVector
  - DrawnUi.Infrastructure.Pendulum.getAngularAcceleration
  - DrawnUi.Infrastructure.Pendulum.getAngularVelocity
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Infrastructure.PerpetualPendulum.Update(System.Double)
  commentId: M:DrawnUi.Infrastructure.PerpetualPendulum.Update(System.Double)
  id: Update(System.Double)
  parent: DrawnUi.Infrastructure.PerpetualPendulum
  langs:
  - csharp
  - vb
  name: Update(double)
  nameWithType: PerpetualPendulum.Update(double)
  fullName: DrawnUi.Infrastructure.PerpetualPendulum.Update(double)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/Pendulum.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Update
    path: ../src/Shared/Draw/Internals/Helpers/Pendulum.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  example: []
  syntax:
    content: public override void Update(double timeStep)
    parameters:
    - id: timeStep
      type: System.Double
    content.vb: Public Overrides Sub Update(timeStep As Double)
  overridden: DrawnUi.Infrastructure.Pendulum.Update(System.Double)
  overload: DrawnUi.Infrastructure.PerpetualPendulum.Update*
  nameWithType.vb: PerpetualPendulum.Update(Double)
  fullName.vb: DrawnUi.Infrastructure.PerpetualPendulum.Update(Double)
  name.vb: Update(Double)
references:
- uid: DrawnUi.Infrastructure
  commentId: N:DrawnUi.Infrastructure
  href: DrawnUi.html
  name: DrawnUi.Infrastructure
  nameWithType: DrawnUi.Infrastructure
  fullName: DrawnUi.Infrastructure
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Infrastructure
    name: Infrastructure
    href: DrawnUi.Infrastructure.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Infrastructure
    name: Infrastructure
    href: DrawnUi.Infrastructure.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: DrawnUi.Infrastructure.Pendulum
  commentId: T:DrawnUi.Infrastructure.Pendulum
  parent: DrawnUi.Infrastructure
  href: DrawnUi.Infrastructure.Pendulum.html
  name: Pendulum
  nameWithType: Pendulum
  fullName: DrawnUi.Infrastructure.Pendulum
- uid: DrawnUi.Infrastructure.Pendulum.wireVector
  commentId: F:DrawnUi.Infrastructure.Pendulum.wireVector
  parent: DrawnUi.Infrastructure.Pendulum
  href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_wireVector
  name: wireVector
  nameWithType: Pendulum.wireVector
  fullName: DrawnUi.Infrastructure.Pendulum.wireVector
- uid: DrawnUi.Infrastructure.Pendulum.accelerationVector
  commentId: F:DrawnUi.Infrastructure.Pendulum.accelerationVector
  parent: DrawnUi.Infrastructure.Pendulum
  href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_accelerationVector
  name: accelerationVector
  nameWithType: Pendulum.accelerationVector
  fullName: DrawnUi.Infrastructure.Pendulum.accelerationVector
- uid: DrawnUi.Infrastructure.Pendulum.velocityVector
  commentId: F:DrawnUi.Infrastructure.Pendulum.velocityVector
  parent: DrawnUi.Infrastructure.Pendulum
  href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_velocityVector
  name: velocityVector
  nameWithType: Pendulum.velocityVector
  fullName: DrawnUi.Infrastructure.Pendulum.velocityVector
- uid: DrawnUi.Infrastructure.Pendulum.angularAcceleration
  commentId: F:DrawnUi.Infrastructure.Pendulum.angularAcceleration
  parent: DrawnUi.Infrastructure.Pendulum
  href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_angularAcceleration
  name: angularAcceleration
  nameWithType: Pendulum.angularAcceleration
  fullName: DrawnUi.Infrastructure.Pendulum.angularAcceleration
- uid: DrawnUi.Infrastructure.Pendulum.AngularVelocity
  commentId: P:DrawnUi.Infrastructure.Pendulum.AngularVelocity
  parent: DrawnUi.Infrastructure.Pendulum
  href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_AngularVelocity
  name: AngularVelocity
  nameWithType: Pendulum.AngularVelocity
  fullName: DrawnUi.Infrastructure.Pendulum.AngularVelocity
- uid: DrawnUi.Infrastructure.Pendulum.RodLength
  commentId: P:DrawnUi.Infrastructure.Pendulum.RodLength
  parent: DrawnUi.Infrastructure.Pendulum
  href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_RodLength
  name: RodLength
  nameWithType: Pendulum.RodLength
  fullName: DrawnUi.Infrastructure.Pendulum.RodLength
- uid: DrawnUi.Infrastructure.Pendulum.AirResistance
  commentId: P:DrawnUi.Infrastructure.Pendulum.AirResistance
  parent: DrawnUi.Infrastructure.Pendulum
  href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_AirResistance
  name: AirResistance
  nameWithType: Pendulum.AirResistance
  fullName: DrawnUi.Infrastructure.Pendulum.AirResistance
- uid: DrawnUi.Infrastructure.Pendulum.Gravity
  commentId: P:DrawnUi.Infrastructure.Pendulum.Gravity
  parent: DrawnUi.Infrastructure.Pendulum
  href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_Gravity
  name: Gravity
  nameWithType: Pendulum.Gravity
  fullName: DrawnUi.Infrastructure.Pendulum.Gravity
- uid: DrawnUi.Infrastructure.Pendulum.angle
  commentId: F:DrawnUi.Infrastructure.Pendulum.angle
  parent: DrawnUi.Infrastructure.Pendulum
  href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_angle
  name: angle
  nameWithType: Pendulum.angle
  fullName: DrawnUi.Infrastructure.Pendulum.angle
- uid: DrawnUi.Infrastructure.Pendulum.Reset
  commentId: M:DrawnUi.Infrastructure.Pendulum.Reset
  parent: DrawnUi.Infrastructure.Pendulum
  href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_Reset
  name: Reset()
  nameWithType: Pendulum.Reset()
  fullName: DrawnUi.Infrastructure.Pendulum.Reset()
  spec.csharp:
  - uid: DrawnUi.Infrastructure.Pendulum.Reset
    name: Reset
    href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_Reset
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Infrastructure.Pendulum.Reset
    name: Reset
    href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_Reset
  - name: (
  - name: )
- uid: DrawnUi.Infrastructure.Pendulum.SetAmplitude(System.Double)
  commentId: M:DrawnUi.Infrastructure.Pendulum.SetAmplitude(System.Double)
  parent: DrawnUi.Infrastructure.Pendulum
  isExternal: true
  href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_SetAmplitude_System_Double_
  name: SetAmplitude(double)
  nameWithType: Pendulum.SetAmplitude(double)
  fullName: DrawnUi.Infrastructure.Pendulum.SetAmplitude(double)
  nameWithType.vb: Pendulum.SetAmplitude(Double)
  fullName.vb: DrawnUi.Infrastructure.Pendulum.SetAmplitude(Double)
  name.vb: SetAmplitude(Double)
  spec.csharp:
  - uid: DrawnUi.Infrastructure.Pendulum.SetAmplitude(System.Double)
    name: SetAmplitude
    href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_SetAmplitude_System_Double_
  - name: (
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
  spec.vb:
  - uid: DrawnUi.Infrastructure.Pendulum.SetAmplitude(System.Double)
    name: SetAmplitude
    href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_SetAmplitude_System_Double_
  - name: (
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
- uid: DrawnUi.Infrastructure.Pendulum.getInitialAngle
  commentId: M:DrawnUi.Infrastructure.Pendulum.getInitialAngle
  parent: DrawnUi.Infrastructure.Pendulum
  href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_getInitialAngle
  name: getInitialAngle()
  nameWithType: Pendulum.getInitialAngle()
  fullName: DrawnUi.Infrastructure.Pendulum.getInitialAngle()
  spec.csharp:
  - uid: DrawnUi.Infrastructure.Pendulum.getInitialAngle
    name: getInitialAngle
    href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_getInitialAngle
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Infrastructure.Pendulum.getInitialAngle
    name: getInitialAngle
    href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_getInitialAngle
  - name: (
  - name: )
- uid: DrawnUi.Infrastructure.Pendulum.setInitialAngle(System.Double)
  commentId: M:DrawnUi.Infrastructure.Pendulum.setInitialAngle(System.Double)
  parent: DrawnUi.Infrastructure.Pendulum
  isExternal: true
  href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_setInitialAngle_System_Double_
  name: setInitialAngle(double)
  nameWithType: Pendulum.setInitialAngle(double)
  fullName: DrawnUi.Infrastructure.Pendulum.setInitialAngle(double)
  nameWithType.vb: Pendulum.setInitialAngle(Double)
  fullName.vb: DrawnUi.Infrastructure.Pendulum.setInitialAngle(Double)
  name.vb: setInitialAngle(Double)
  spec.csharp:
  - uid: DrawnUi.Infrastructure.Pendulum.setInitialAngle(System.Double)
    name: setInitialAngle
    href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_setInitialAngle_System_Double_
  - name: (
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
  spec.vb:
  - uid: DrawnUi.Infrastructure.Pendulum.setInitialAngle(System.Double)
    name: setInitialAngle
    href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_setInitialAngle_System_Double_
  - name: (
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
- uid: DrawnUi.Infrastructure.Pendulum.getInitialVelocity
  commentId: M:DrawnUi.Infrastructure.Pendulum.getInitialVelocity
  parent: DrawnUi.Infrastructure.Pendulum
  href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_getInitialVelocity
  name: getInitialVelocity()
  nameWithType: Pendulum.getInitialVelocity()
  fullName: DrawnUi.Infrastructure.Pendulum.getInitialVelocity()
  spec.csharp:
  - uid: DrawnUi.Infrastructure.Pendulum.getInitialVelocity
    name: getInitialVelocity
    href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_getInitialVelocity
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Infrastructure.Pendulum.getInitialVelocity
    name: getInitialVelocity
    href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_getInitialVelocity
  - name: (
  - name: )
- uid: DrawnUi.Infrastructure.Pendulum.setInitialVelocity(System.Double)
  commentId: M:DrawnUi.Infrastructure.Pendulum.setInitialVelocity(System.Double)
  parent: DrawnUi.Infrastructure.Pendulum
  isExternal: true
  href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_setInitialVelocity_System_Double_
  name: setInitialVelocity(double)
  nameWithType: Pendulum.setInitialVelocity(double)
  fullName: DrawnUi.Infrastructure.Pendulum.setInitialVelocity(double)
  nameWithType.vb: Pendulum.setInitialVelocity(Double)
  fullName.vb: DrawnUi.Infrastructure.Pendulum.setInitialVelocity(Double)
  name.vb: setInitialVelocity(Double)
  spec.csharp:
  - uid: DrawnUi.Infrastructure.Pendulum.setInitialVelocity(System.Double)
    name: setInitialVelocity
    href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_setInitialVelocity_System_Double_
  - name: (
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
  spec.vb:
  - uid: DrawnUi.Infrastructure.Pendulum.setInitialVelocity(System.Double)
    name: setInitialVelocity
    href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_setInitialVelocity_System_Double_
  - name: (
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
- uid: DrawnUi.Infrastructure.Pendulum.getAngle
  commentId: M:DrawnUi.Infrastructure.Pendulum.getAngle
  parent: DrawnUi.Infrastructure.Pendulum
  href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_getAngle
  name: getAngle()
  nameWithType: Pendulum.getAngle()
  fullName: DrawnUi.Infrastructure.Pendulum.getAngle()
  spec.csharp:
  - uid: DrawnUi.Infrastructure.Pendulum.getAngle
    name: getAngle
    href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_getAngle
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Infrastructure.Pendulum.getAngle
    name: getAngle
    href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_getAngle
  - name: (
  - name: )
- uid: DrawnUi.Infrastructure.Pendulum.getAirResistence
  commentId: M:DrawnUi.Infrastructure.Pendulum.getAirResistence
  parent: DrawnUi.Infrastructure.Pendulum
  href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_getAirResistence
  name: getAirResistence()
  nameWithType: Pendulum.getAirResistence()
  fullName: DrawnUi.Infrastructure.Pendulum.getAirResistence()
  spec.csharp:
  - uid: DrawnUi.Infrastructure.Pendulum.getAirResistence
    name: getAirResistence
    href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_getAirResistence
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Infrastructure.Pendulum.getAirResistence
    name: getAirResistence
    href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_getAirResistence
  - name: (
  - name: )
- uid: DrawnUi.Infrastructure.Pendulum.setAirResistance(System.Double)
  commentId: M:DrawnUi.Infrastructure.Pendulum.setAirResistance(System.Double)
  parent: DrawnUi.Infrastructure.Pendulum
  isExternal: true
  href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_setAirResistance_System_Double_
  name: setAirResistance(double)
  nameWithType: Pendulum.setAirResistance(double)
  fullName: DrawnUi.Infrastructure.Pendulum.setAirResistance(double)
  nameWithType.vb: Pendulum.setAirResistance(Double)
  fullName.vb: DrawnUi.Infrastructure.Pendulum.setAirResistance(Double)
  name.vb: setAirResistance(Double)
  spec.csharp:
  - uid: DrawnUi.Infrastructure.Pendulum.setAirResistance(System.Double)
    name: setAirResistance
    href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_setAirResistance_System_Double_
  - name: (
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
  spec.vb:
  - uid: DrawnUi.Infrastructure.Pendulum.setAirResistance(System.Double)
    name: setAirResistance
    href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_setAirResistance_System_Double_
  - name: (
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
- uid: DrawnUi.Infrastructure.Pendulum.getWireVector
  commentId: M:DrawnUi.Infrastructure.Pendulum.getWireVector
  parent: DrawnUi.Infrastructure.Pendulum
  href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_getWireVector
  name: getWireVector()
  nameWithType: Pendulum.getWireVector()
  fullName: DrawnUi.Infrastructure.Pendulum.getWireVector()
  spec.csharp:
  - uid: DrawnUi.Infrastructure.Pendulum.getWireVector
    name: getWireVector
    href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_getWireVector
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Infrastructure.Pendulum.getWireVector
    name: getWireVector
    href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_getWireVector
  - name: (
  - name: )
- uid: DrawnUi.Infrastructure.Pendulum.getAccelerationVector
  commentId: M:DrawnUi.Infrastructure.Pendulum.getAccelerationVector
  parent: DrawnUi.Infrastructure.Pendulum
  href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_getAccelerationVector
  name: getAccelerationVector()
  nameWithType: Pendulum.getAccelerationVector()
  fullName: DrawnUi.Infrastructure.Pendulum.getAccelerationVector()
  spec.csharp:
  - uid: DrawnUi.Infrastructure.Pendulum.getAccelerationVector
    name: getAccelerationVector
    href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_getAccelerationVector
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Infrastructure.Pendulum.getAccelerationVector
    name: getAccelerationVector
    href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_getAccelerationVector
  - name: (
  - name: )
- uid: DrawnUi.Infrastructure.Pendulum.getVelocityVector
  commentId: M:DrawnUi.Infrastructure.Pendulum.getVelocityVector
  parent: DrawnUi.Infrastructure.Pendulum
  href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_getVelocityVector
  name: getVelocityVector()
  nameWithType: Pendulum.getVelocityVector()
  fullName: DrawnUi.Infrastructure.Pendulum.getVelocityVector()
  spec.csharp:
  - uid: DrawnUi.Infrastructure.Pendulum.getVelocityVector
    name: getVelocityVector
    href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_getVelocityVector
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Infrastructure.Pendulum.getVelocityVector
    name: getVelocityVector
    href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_getVelocityVector
  - name: (
  - name: )
- uid: DrawnUi.Infrastructure.Pendulum.getAngularAcceleration
  commentId: M:DrawnUi.Infrastructure.Pendulum.getAngularAcceleration
  parent: DrawnUi.Infrastructure.Pendulum
  href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_getAngularAcceleration
  name: getAngularAcceleration()
  nameWithType: Pendulum.getAngularAcceleration()
  fullName: DrawnUi.Infrastructure.Pendulum.getAngularAcceleration()
  spec.csharp:
  - uid: DrawnUi.Infrastructure.Pendulum.getAngularAcceleration
    name: getAngularAcceleration
    href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_getAngularAcceleration
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Infrastructure.Pendulum.getAngularAcceleration
    name: getAngularAcceleration
    href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_getAngularAcceleration
  - name: (
  - name: )
- uid: DrawnUi.Infrastructure.Pendulum.getAngularVelocity
  commentId: M:DrawnUi.Infrastructure.Pendulum.getAngularVelocity
  parent: DrawnUi.Infrastructure.Pendulum
  href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_getAngularVelocity
  name: getAngularVelocity()
  nameWithType: Pendulum.getAngularVelocity()
  fullName: DrawnUi.Infrastructure.Pendulum.getAngularVelocity()
  spec.csharp:
  - uid: DrawnUi.Infrastructure.Pendulum.getAngularVelocity
    name: getAngularVelocity
    href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_getAngularVelocity
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Infrastructure.Pendulum.getAngularVelocity
    name: getAngularVelocity
    href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_getAngularVelocity
  - name: (
  - name: )
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Infrastructure.Pendulum.Update(System.Double)
  commentId: M:DrawnUi.Infrastructure.Pendulum.Update(System.Double)
  parent: DrawnUi.Infrastructure.Pendulum
  isExternal: true
  href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_Update_System_Double_
  name: Update(double)
  nameWithType: Pendulum.Update(double)
  fullName: DrawnUi.Infrastructure.Pendulum.Update(double)
  nameWithType.vb: Pendulum.Update(Double)
  fullName.vb: DrawnUi.Infrastructure.Pendulum.Update(Double)
  name.vb: Update(Double)
  spec.csharp:
  - uid: DrawnUi.Infrastructure.Pendulum.Update(System.Double)
    name: Update
    href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_Update_System_Double_
  - name: (
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
  spec.vb:
  - uid: DrawnUi.Infrastructure.Pendulum.Update(System.Double)
    name: Update
    href: DrawnUi.Infrastructure.Pendulum.html#DrawnUi_Infrastructure_Pendulum_Update_System_Double_
  - name: (
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
- uid: DrawnUi.Infrastructure.PerpetualPendulum.Update*
  commentId: Overload:DrawnUi.Infrastructure.PerpetualPendulum.Update
  href: DrawnUi.Infrastructure.PerpetualPendulum.html#DrawnUi_Infrastructure_PerpetualPendulum_Update_System_Double_
  name: Update
  nameWithType: PerpetualPendulum.Update
  fullName: DrawnUi.Infrastructure.PerpetualPendulum.Update
- uid: System.Double
  commentId: T:System.Double
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.double
  name: double
  nameWithType: double
  fullName: double
  nameWithType.vb: Double
  fullName.vb: Double
  name.vb: Double
