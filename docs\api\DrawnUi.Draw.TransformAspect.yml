### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.TransformAspect
  commentId: T:DrawnUi.Draw.TransformAspect
  id: TransformAspect
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.TransformAspect.AspectCover
  - DrawnUi.Draw.TransformAspect.AspectFill
  - DrawnUi.Draw.TransformAspect.AspectFit
  - DrawnUi.Draw.TransformAspect.AspectFitFill
  - DrawnUi.Draw.TransformAspect.Cover
  - DrawnUi.Draw.TransformAspect.Fill
  - DrawnUi.Draw.TransformAspect.Fit
  - DrawnUi.Draw.TransformAspect.FitFill
  - DrawnUi.Draw.TransformAspect.None
  - DrawnUi.Draw.TransformAspect.Tile
  langs:
  - csharp
  - vb
  name: TransformAspect
  nameWithType: TransformAspect
  fullName: DrawnUi.Draw.TransformAspect
  type: Enum
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/TransformAspect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: TransformAspect
    path: ../src/Shared/Draw/Internals/Enums/TransformAspect.cs
    startLine: 3
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public enum TransformAspect
    content.vb: Public Enum TransformAspect
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.TransformAspect.None
  commentId: F:DrawnUi.Draw.TransformAspect.None
  id: None
  parent: DrawnUi.Draw.TransformAspect
  langs:
  - csharp
  - vb
  name: None
  nameWithType: TransformAspect.None
  fullName: DrawnUi.Draw.TransformAspect.None
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/TransformAspect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: None
    path: ../src/Shared/Draw/Internals/Enums/TransformAspect.cs
    startLine: 5
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: None = 0
    return:
      type: DrawnUi.Draw.TransformAspect
- uid: DrawnUi.Draw.TransformAspect.Fill
  commentId: F:DrawnUi.Draw.TransformAspect.Fill
  id: Fill
  parent: DrawnUi.Draw.TransformAspect
  langs:
  - csharp
  - vb
  name: Fill
  nameWithType: TransformAspect.Fill
  fullName: DrawnUi.Draw.TransformAspect.Fill
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/TransformAspect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Fill
    path: ../src/Shared/Draw/Internals/Enums/TransformAspect.cs
    startLine: 10
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Enlarges to fill the viewport without maintaining aspect ratio if smaller, but does not scale down if larger
  example: []
  syntax:
    content: Fill = 1
    return:
      type: DrawnUi.Draw.TransformAspect
- uid: DrawnUi.Draw.TransformAspect.Fit
  commentId: F:DrawnUi.Draw.TransformAspect.Fit
  id: Fit
  parent: DrawnUi.Draw.TransformAspect
  langs:
  - csharp
  - vb
  name: Fit
  nameWithType: TransformAspect.Fit
  fullName: DrawnUi.Draw.TransformAspect.Fit
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/TransformAspect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Fit
    path: ../src/Shared/Draw/Internals/Enums/TransformAspect.cs
    startLine: 15
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Fit without maintaining aspect ratio and without enlarging if smaller
  example: []
  syntax:
    content: Fit = 2
    return:
      type: DrawnUi.Draw.TransformAspect
- uid: DrawnUi.Draw.TransformAspect.AspectFit
  commentId: F:DrawnUi.Draw.TransformAspect.AspectFit
  id: AspectFit
  parent: DrawnUi.Draw.TransformAspect
  langs:
  - csharp
  - vb
  name: AspectFit
  nameWithType: TransformAspect.AspectFit
  fullName: DrawnUi.Draw.TransformAspect.AspectFit
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/TransformAspect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: AspectFit
    path: ../src/Shared/Draw/Internals/Enums/TransformAspect.cs
    startLine: 20
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Fit inside viewport respecting aspect without enlarging if smaller, could result in the image having some blank space around
  example: []
  syntax:
    content: AspectFit = 3
    return:
      type: DrawnUi.Draw.TransformAspect
- uid: DrawnUi.Draw.TransformAspect.AspectFill
  commentId: F:DrawnUi.Draw.TransformAspect.AspectFill
  id: AspectFill
  parent: DrawnUi.Draw.TransformAspect
  langs:
  - csharp
  - vb
  name: AspectFill
  nameWithType: TransformAspect.AspectFill
  fullName: DrawnUi.Draw.TransformAspect.AspectFill
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/TransformAspect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: AspectFill
    path: ../src/Shared/Draw/Internals/Enums/TransformAspect.cs
    startLine: 25
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Covers viewport respecting aspect without scaling down if bigger, could result in the image being cropped
  example: []
  syntax:
    content: AspectFill = 4
    return:
      type: DrawnUi.Draw.TransformAspect
- uid: DrawnUi.Draw.TransformAspect.AspectFitFill
  commentId: F:DrawnUi.Draw.TransformAspect.AspectFitFill
  id: AspectFitFill
  parent: DrawnUi.Draw.TransformAspect
  langs:
  - csharp
  - vb
  name: AspectFitFill
  nameWithType: TransformAspect.AspectFitFill
  fullName: DrawnUi.Draw.TransformAspect.AspectFitFill
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/TransformAspect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: AspectFitFill
    path: ../src/Shared/Draw/Internals/Enums/TransformAspect.cs
    startLine: 30
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: AspectFit + AspectFill. Enlarges to cover the viewport or reduces size to fit inside the viewport both respecting aspect ratio, ensuring the entire image is always visible, potentially leaving some parts of the viewport uncovered.
  example: []
  syntax:
    content: AspectFitFill = 5
    return:
      type: DrawnUi.Draw.TransformAspect
- uid: DrawnUi.Draw.TransformAspect.FitFill
  commentId: F:DrawnUi.Draw.TransformAspect.FitFill
  id: FitFill
  parent: DrawnUi.Draw.TransformAspect
  langs:
  - csharp
  - vb
  name: FitFill
  nameWithType: TransformAspect.FitFill
  fullName: DrawnUi.Draw.TransformAspect.FitFill
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/TransformAspect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: FitFill
    path: ../src/Shared/Draw/Internals/Enums/TransformAspect.cs
    startLine: 35
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Fit + Fill. Enlarges to cover the viewport or reduces size to fit inside the viewport without respecting aspect ratio, ensuring the entire image is always visible, potentially leaving some parts of the viewport uncovered.
  example: []
  syntax:
    content: FitFill = 6
    return:
      type: DrawnUi.Draw.TransformAspect
- uid: DrawnUi.Draw.TransformAspect.Cover
  commentId: F:DrawnUi.Draw.TransformAspect.Cover
  id: Cover
  parent: DrawnUi.Draw.TransformAspect
  langs:
  - csharp
  - vb
  name: Cover
  nameWithType: TransformAspect.Cover
  fullName: DrawnUi.Draw.TransformAspect.Cover
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/TransformAspect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Cover
    path: ../src/Shared/Draw/Internals/Enums/TransformAspect.cs
    startLine: 40
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Enlarges to cover the viewport if smaller and reduces size if larger, all without respecting aspect ratio. Same as AspectFitFill but will crop the image to fill entire viewport.
  example: []
  syntax:
    content: Cover = 7
    return:
      type: DrawnUi.Draw.TransformAspect
- uid: DrawnUi.Draw.TransformAspect.AspectCover
  commentId: F:DrawnUi.Draw.TransformAspect.AspectCover
  id: AspectCover
  parent: DrawnUi.Draw.TransformAspect
  langs:
  - csharp
  - vb
  name: AspectCover
  nameWithType: TransformAspect.AspectCover
  fullName: DrawnUi.Draw.TransformAspect.AspectCover
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/TransformAspect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: AspectCover
    path: ../src/Shared/Draw/Internals/Enums/TransformAspect.cs
    startLine: 46
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: >-
    Enlarges to cover the viewport or reduces size to fit inside the viewport both respecting aspect ratio. Always covers the entire viewport, potentially cropping the image if it's larger,

    never leaves empty space inside viewport.
  example: []
  syntax:
    content: AspectCover = 8
    return:
      type: DrawnUi.Draw.TransformAspect
- uid: DrawnUi.Draw.TransformAspect.Tile
  commentId: F:DrawnUi.Draw.TransformAspect.Tile
  id: Tile
  parent: DrawnUi.Draw.TransformAspect
  langs:
  - csharp
  - vb
  name: Tile
  nameWithType: TransformAspect.Tile
  fullName: DrawnUi.Draw.TransformAspect.Tile
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/TransformAspect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Tile
    path: ../src/Shared/Draw/Internals/Enums/TransformAspect.cs
    startLine: 51
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: TODO very soon!
  example: []
  syntax:
    content: Tile = 9
    return:
      type: DrawnUi.Draw.TransformAspect
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.TransformAspect
  commentId: T:DrawnUi.Draw.TransformAspect
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.TransformAspect.html
  name: TransformAspect
  nameWithType: TransformAspect
  fullName: DrawnUi.Draw.TransformAspect
