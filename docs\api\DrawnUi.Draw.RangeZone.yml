### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.RangeZone
  commentId: T:DrawnUi.Draw.RangeZone
  id: RangeZone
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.RangeZone.End
  - DrawnUi.Draw.RangeZone.Start
  - DrawnUi.Draw.RangeZone.Unknown
  langs:
  - csharp
  - vb
  name: RangeZone
  nameWithType: RangeZone
  fullName: DrawnUi.Draw.RangeZone
  type: Enum
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/RangeZone.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: RangeZone
    path: ../src/Shared/Draw/Internals/Enums/RangeZone.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public enum RangeZone
    content.vb: Public Enum RangeZone
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.RangeZone.Unknown
  commentId: F:DrawnUi.Draw.RangeZone.Unknown
  id: Unknown
  parent: DrawnUi.Draw.RangeZone
  langs:
  - csharp
  - vb
  name: Unknown
  nameWithType: RangeZone.Unknown
  fullName: DrawnUi.Draw.RangeZone.Unknown
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/RangeZone.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Unknown
    path: ../src/Shared/Draw/Internals/Enums/RangeZone.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: Unknown = 0
    return:
      type: DrawnUi.Draw.RangeZone
- uid: DrawnUi.Draw.RangeZone.Start
  commentId: F:DrawnUi.Draw.RangeZone.Start
  id: Start
  parent: DrawnUi.Draw.RangeZone
  langs:
  - csharp
  - vb
  name: Start
  nameWithType: RangeZone.Start
  fullName: DrawnUi.Draw.RangeZone.Start
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/RangeZone.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Start
    path: ../src/Shared/Draw/Internals/Enums/RangeZone.cs
    startLine: 5
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: Start = 1
    return:
      type: DrawnUi.Draw.RangeZone
- uid: DrawnUi.Draw.RangeZone.End
  commentId: F:DrawnUi.Draw.RangeZone.End
  id: End
  parent: DrawnUi.Draw.RangeZone
  langs:
  - csharp
  - vb
  name: End
  nameWithType: RangeZone.End
  fullName: DrawnUi.Draw.RangeZone.End
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/RangeZone.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: End
    path: ../src/Shared/Draw/Internals/Enums/RangeZone.cs
    startLine: 6
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: End = 2
    return:
      type: DrawnUi.Draw.RangeZone
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.RangeZone
  commentId: T:DrawnUi.Draw.RangeZone
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.RangeZone.html
  name: RangeZone
  nameWithType: RangeZone
  fullName: DrawnUi.Draw.RangeZone
