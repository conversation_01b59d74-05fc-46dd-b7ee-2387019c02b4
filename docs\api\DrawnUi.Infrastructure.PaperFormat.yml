### YamlMime:ManagedReference
items:
- uid: DrawnUi.Infrastructure.PaperFormat
  commentId: T:DrawnUi.Infrastructure.PaperFormat
  id: PaperFormat
  parent: DrawnUi.Infrastructure
  children:
  - DrawnUi.Infrastructure.PaperFormat.A4
  - DrawnUi.Infrastructure.PaperFormat.A5
  - DrawnUi.Infrastructure.PaperFormat.A6
  - DrawnUi.Infrastructure.PaperFormat.Custom
  - DrawnUi.Infrastructure.PaperFormat.Legal
  - DrawnUi.Infrastructure.PaperFormat.Letter
  langs:
  - csharp
  - vb
  name: PaperFormat
  nameWithType: PaperFormat
  fullName: DrawnUi.Infrastructure.PaperFormat
  type: Enum
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Pdf/Pdf.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: PaperFormat
    path: ../src/Maui/DrawnUi/Features/Pdf/Pdf.cs
    startLine: 5
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  summary: Standard paper formats
  example: []
  syntax:
    content: public enum PaperFormat
    content.vb: Public Enum PaperFormat
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Infrastructure.PaperFormat.Custom
  commentId: F:DrawnUi.Infrastructure.PaperFormat.Custom
  id: Custom
  parent: DrawnUi.Infrastructure.PaperFormat
  langs:
  - csharp
  - vb
  name: Custom
  nameWithType: PaperFormat.Custom
  fullName: DrawnUi.Infrastructure.PaperFormat.Custom
  type: Field
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Pdf/Pdf.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Custom
    path: ../src/Maui/DrawnUi/Features/Pdf/Pdf.cs
    startLine: 7
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: Custom = 0
    return:
      type: DrawnUi.Infrastructure.PaperFormat
- uid: DrawnUi.Infrastructure.PaperFormat.A4
  commentId: F:DrawnUi.Infrastructure.PaperFormat.A4
  id: A4
  parent: DrawnUi.Infrastructure.PaperFormat
  langs:
  - csharp
  - vb
  name: A4
  nameWithType: PaperFormat.A4
  fullName: DrawnUi.Infrastructure.PaperFormat.A4
  type: Field
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Pdf/Pdf.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: A4
    path: ../src/Maui/DrawnUi/Features/Pdf/Pdf.cs
    startLine: 8
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: A4 = 1
    return:
      type: DrawnUi.Infrastructure.PaperFormat
- uid: DrawnUi.Infrastructure.PaperFormat.A5
  commentId: F:DrawnUi.Infrastructure.PaperFormat.A5
  id: A5
  parent: DrawnUi.Infrastructure.PaperFormat
  langs:
  - csharp
  - vb
  name: A5
  nameWithType: PaperFormat.A5
  fullName: DrawnUi.Infrastructure.PaperFormat.A5
  type: Field
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Pdf/Pdf.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: A5
    path: ../src/Maui/DrawnUi/Features/Pdf/Pdf.cs
    startLine: 9
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: A5 = 2
    return:
      type: DrawnUi.Infrastructure.PaperFormat
- uid: DrawnUi.Infrastructure.PaperFormat.A6
  commentId: F:DrawnUi.Infrastructure.PaperFormat.A6
  id: A6
  parent: DrawnUi.Infrastructure.PaperFormat
  langs:
  - csharp
  - vb
  name: A6
  nameWithType: PaperFormat.A6
  fullName: DrawnUi.Infrastructure.PaperFormat.A6
  type: Field
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Pdf/Pdf.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: A6
    path: ../src/Maui/DrawnUi/Features/Pdf/Pdf.cs
    startLine: 10
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: A6 = 3
    return:
      type: DrawnUi.Infrastructure.PaperFormat
- uid: DrawnUi.Infrastructure.PaperFormat.Letter
  commentId: F:DrawnUi.Infrastructure.PaperFormat.Letter
  id: Letter
  parent: DrawnUi.Infrastructure.PaperFormat
  langs:
  - csharp
  - vb
  name: Letter
  nameWithType: PaperFormat.Letter
  fullName: DrawnUi.Infrastructure.PaperFormat.Letter
  type: Field
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Pdf/Pdf.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Letter
    path: ../src/Maui/DrawnUi/Features/Pdf/Pdf.cs
    startLine: 11
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: Letter = 4
    return:
      type: DrawnUi.Infrastructure.PaperFormat
- uid: DrawnUi.Infrastructure.PaperFormat.Legal
  commentId: F:DrawnUi.Infrastructure.PaperFormat.Legal
  id: Legal
  parent: DrawnUi.Infrastructure.PaperFormat
  langs:
  - csharp
  - vb
  name: Legal
  nameWithType: PaperFormat.Legal
  fullName: DrawnUi.Infrastructure.PaperFormat.Legal
  type: Field
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Pdf/Pdf.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Legal
    path: ../src/Maui/DrawnUi/Features/Pdf/Pdf.cs
    startLine: 12
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: Legal = 5
    return:
      type: DrawnUi.Infrastructure.PaperFormat
references:
- uid: DrawnUi.Infrastructure
  commentId: N:DrawnUi.Infrastructure
  href: DrawnUi.html
  name: DrawnUi.Infrastructure
  nameWithType: DrawnUi.Infrastructure
  fullName: DrawnUi.Infrastructure
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Infrastructure
    name: Infrastructure
    href: DrawnUi.Infrastructure.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Infrastructure
    name: Infrastructure
    href: DrawnUi.Infrastructure.html
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Infrastructure.PaperFormat
  commentId: T:DrawnUi.Infrastructure.PaperFormat
  parent: DrawnUi.Infrastructure
  href: DrawnUi.Infrastructure.PaperFormat.html
  name: PaperFormat
  nameWithType: PaperFormat
  fullName: DrawnUi.Infrastructure.PaperFormat
