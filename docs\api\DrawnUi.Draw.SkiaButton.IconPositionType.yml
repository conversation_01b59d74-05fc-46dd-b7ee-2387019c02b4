### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.SkiaButton.IconPositionType
  commentId: T:DrawnUi.Draw.SkiaButton.IconPositionType
  id: SkiaButton.IconPositionType
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.SkiaButton.IconPositionType.Left
  - DrawnUi.Draw.SkiaButton.IconPositionType.Right
  langs:
  - csharp
  - vb
  name: SkiaButton.IconPositionType
  nameWithType: SkiaButton.IconPositionType
  fullName: DrawnUi.Draw.SkiaButton.IconPositionType
  type: Enum
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/Button/SkiaButton.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IconPositionType
    path: ../src/Maui/DrawnUi/Controls/Button/SkiaButton.cs
    startLine: 779
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Defines the position of an icon relative to the button text
  example: []
  syntax:
    content: public enum SkiaButton.IconPositionType
    content.vb: Public Enum SkiaButton.IconPositionType
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.SkiaButton.IconPositionType.Left
  commentId: F:DrawnUi.Draw.SkiaButton.IconPositionType.Left
  id: Left
  parent: DrawnUi.Draw.SkiaButton.IconPositionType
  langs:
  - csharp
  - vb
  name: Left
  nameWithType: SkiaButton.IconPositionType.Left
  fullName: DrawnUi.Draw.SkiaButton.IconPositionType.Left
  type: Field
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/Button/SkiaButton.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Left
    path: ../src/Maui/DrawnUi/Controls/Button/SkiaButton.cs
    startLine: 784
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Icon appears before the text
  example: []
  syntax:
    content: Left = 0
    return:
      type: DrawnUi.Draw.SkiaButton.IconPositionType
- uid: DrawnUi.Draw.SkiaButton.IconPositionType.Right
  commentId: F:DrawnUi.Draw.SkiaButton.IconPositionType.Right
  id: Right
  parent: DrawnUi.Draw.SkiaButton.IconPositionType
  langs:
  - csharp
  - vb
  name: Right
  nameWithType: SkiaButton.IconPositionType.Right
  fullName: DrawnUi.Draw.SkiaButton.IconPositionType.Right
  type: Field
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/Button/SkiaButton.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Right
    path: ../src/Maui/DrawnUi/Controls/Button/SkiaButton.cs
    startLine: 789
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Icon appears after the text
  example: []
  syntax:
    content: Right = 1
    return:
      type: DrawnUi.Draw.SkiaButton.IconPositionType
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.SkiaButton.IconPositionType
  commentId: T:DrawnUi.Draw.SkiaButton.IconPositionType
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaButton.html
  name: SkiaButton.IconPositionType
  nameWithType: SkiaButton.IconPositionType
  fullName: DrawnUi.Draw.SkiaButton.IconPositionType
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaButton
    name: SkiaButton
    href: DrawnUi.Draw.SkiaButton.html
  - name: .
  - uid: DrawnUi.Draw.SkiaButton.IconPositionType
    name: IconPositionType
    href: DrawnUi.Draw.SkiaButton.IconPositionType.html
  spec.vb:
  - uid: DrawnUi.Draw.SkiaButton
    name: SkiaButton
    href: DrawnUi.Draw.SkiaButton.html
  - name: .
  - uid: DrawnUi.Draw.SkiaButton.IconPositionType
    name: IconPositionType
    href: DrawnUi.Draw.SkiaButton.IconPositionType.html
