### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.SkiaImageManager.QueueItem
  commentId: T:DrawnUi.Draw.SkiaImageManager.QueueItem
  id: SkiaImageManager.QueueItem
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.SkiaImageManager.QueueItem.#ctor(Microsoft.Maui.Controls.ImageSource,System.Threading.CancellationTokenSource,System.Threading.Tasks.TaskCompletionSource{SkiaSharp.SKBitmap},System.Boolean)
  - DrawnUi.Draw.SkiaImageManager.QueueItem.Cancel
  - DrawnUi.Draw.SkiaImageManager.QueueItem.Dispose
  - DrawnUi.Draw.SkiaImageManager.QueueItem.Source
  - DrawnUi.Draw.SkiaImageManager.QueueItem.Task
  langs:
  - csharp
  - vb
  name: SkiaImageManager.QueueItem
  nameWithType: SkiaImageManager.QueueItem
  fullName: DrawnUi.Draw.SkiaImageManager.QueueItem
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Images/SkiaImageManager.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: QueueItem
    path: ../src/Maui/DrawnUi/Features/Images/SkiaImageManager.cs
    startLine: 274
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: 'public record SkiaImageManager.QueueItem : IDisposable, IEquatable<SkiaImageManager.QueueItem>'
    content.vb: Public Class SkiaImageManager.QueueItem Implements IDisposable, IEquatable(Of SkiaImageManager.QueueItem)
  inheritance:
  - System.Object
  implements:
  - System.IDisposable
  - System.IEquatable{DrawnUi.Draw.SkiaImageManager.QueueItem}
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.SkiaImageManager.QueueItem.#ctor(Microsoft.Maui.Controls.ImageSource,System.Threading.CancellationTokenSource,System.Threading.Tasks.TaskCompletionSource{SkiaSharp.SKBitmap},System.Boolean)
  commentId: M:DrawnUi.Draw.SkiaImageManager.QueueItem.#ctor(Microsoft.Maui.Controls.ImageSource,System.Threading.CancellationTokenSource,System.Threading.Tasks.TaskCompletionSource{SkiaSharp.SKBitmap},System.Boolean)
  id: '#ctor(Microsoft.Maui.Controls.ImageSource,System.Threading.CancellationTokenSource,System.Threading.Tasks.TaskCompletionSource{SkiaSharp.SKBitmap},System.Boolean)'
  parent: DrawnUi.Draw.SkiaImageManager.QueueItem
  langs:
  - csharp
  - vb
  name: QueueItem(ImageSource, CancellationTokenSource, TaskCompletionSource<SKBitmap>, bool)
  nameWithType: SkiaImageManager.QueueItem.QueueItem(ImageSource, CancellationTokenSource, TaskCompletionSource<SKBitmap>, bool)
  fullName: DrawnUi.Draw.SkiaImageManager.QueueItem.QueueItem(Microsoft.Maui.Controls.ImageSource, System.Threading.CancellationTokenSource, System.Threading.Tasks.TaskCompletionSource<SkiaSharp.SKBitmap>, bool)
  type: Constructor
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Images/SkiaImageManager.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Maui/DrawnUi/Features/Images/SkiaImageManager.cs
    startLine: 278
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public QueueItem(ImageSource source, CancellationTokenSource cancel, TaskCompletionSource<SKBitmap> task, bool ownsCancel = false)
    parameters:
    - id: source
      type: Microsoft.Maui.Controls.ImageSource
    - id: cancel
      type: System.Threading.CancellationTokenSource
    - id: task
      type: System.Threading.Tasks.TaskCompletionSource{SkiaSharp.SKBitmap}
    - id: ownsCancel
      type: System.Boolean
    content.vb: Public Sub New(source As ImageSource, cancel As CancellationTokenSource, task As TaskCompletionSource(Of SKBitmap), ownsCancel As Boolean = False)
  overload: DrawnUi.Draw.SkiaImageManager.QueueItem.#ctor*
  nameWithType.vb: SkiaImageManager.QueueItem.New(ImageSource, CancellationTokenSource, TaskCompletionSource(Of SKBitmap), Boolean)
  fullName.vb: DrawnUi.Draw.SkiaImageManager.QueueItem.New(Microsoft.Maui.Controls.ImageSource, System.Threading.CancellationTokenSource, System.Threading.Tasks.TaskCompletionSource(Of SkiaSharp.SKBitmap), Boolean)
  name.vb: New(ImageSource, CancellationTokenSource, TaskCompletionSource(Of SKBitmap), Boolean)
- uid: DrawnUi.Draw.SkiaImageManager.QueueItem.Source
  commentId: P:DrawnUi.Draw.SkiaImageManager.QueueItem.Source
  id: Source
  parent: DrawnUi.Draw.SkiaImageManager.QueueItem
  langs:
  - csharp
  - vb
  name: Source
  nameWithType: SkiaImageManager.QueueItem.Source
  fullName: DrawnUi.Draw.SkiaImageManager.QueueItem.Source
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Images/SkiaImageManager.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Source
    path: ../src/Maui/DrawnUi/Features/Images/SkiaImageManager.cs
    startLine: 286
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public ImageSource Source { get; init; }
    parameters: []
    return:
      type: Microsoft.Maui.Controls.ImageSource
    content.vb: Public Property Source As ImageSource
  overload: DrawnUi.Draw.SkiaImageManager.QueueItem.Source*
- uid: DrawnUi.Draw.SkiaImageManager.QueueItem.Cancel
  commentId: P:DrawnUi.Draw.SkiaImageManager.QueueItem.Cancel
  id: Cancel
  parent: DrawnUi.Draw.SkiaImageManager.QueueItem
  langs:
  - csharp
  - vb
  name: Cancel
  nameWithType: SkiaImageManager.QueueItem.Cancel
  fullName: DrawnUi.Draw.SkiaImageManager.QueueItem.Cancel
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Images/SkiaImageManager.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Cancel
    path: ../src/Maui/DrawnUi/Features/Images/SkiaImageManager.cs
    startLine: 287
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public CancellationTokenSource Cancel { get; init; }
    parameters: []
    return:
      type: System.Threading.CancellationTokenSource
    content.vb: Public Property Cancel As CancellationTokenSource
  overload: DrawnUi.Draw.SkiaImageManager.QueueItem.Cancel*
- uid: DrawnUi.Draw.SkiaImageManager.QueueItem.Task
  commentId: P:DrawnUi.Draw.SkiaImageManager.QueueItem.Task
  id: Task
  parent: DrawnUi.Draw.SkiaImageManager.QueueItem
  langs:
  - csharp
  - vb
  name: Task
  nameWithType: SkiaImageManager.QueueItem.Task
  fullName: DrawnUi.Draw.SkiaImageManager.QueueItem.Task
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Images/SkiaImageManager.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Task
    path: ../src/Maui/DrawnUi/Features/Images/SkiaImageManager.cs
    startLine: 288
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public TaskCompletionSource<SKBitmap> Task { get; init; }
    parameters: []
    return:
      type: System.Threading.Tasks.TaskCompletionSource{SkiaSharp.SKBitmap}
    content.vb: Public Property Task As TaskCompletionSource(Of SKBitmap)
  overload: DrawnUi.Draw.SkiaImageManager.QueueItem.Task*
- uid: DrawnUi.Draw.SkiaImageManager.QueueItem.Dispose
  commentId: M:DrawnUi.Draw.SkiaImageManager.QueueItem.Dispose
  id: Dispose
  parent: DrawnUi.Draw.SkiaImageManager.QueueItem
  langs:
  - csharp
  - vb
  name: Dispose()
  nameWithType: SkiaImageManager.QueueItem.Dispose()
  fullName: DrawnUi.Draw.SkiaImageManager.QueueItem.Dispose()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Images/SkiaImageManager.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Dispose
    path: ../src/Maui/DrawnUi/Features/Images/SkiaImageManager.cs
    startLine: 290
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
  example: []
  syntax:
    content: public void Dispose()
    content.vb: Public Sub Dispose()
  overload: DrawnUi.Draw.SkiaImageManager.QueueItem.Dispose*
  implements:
  - System.IDisposable.Dispose
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.IDisposable
  commentId: T:System.IDisposable
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.idisposable
  name: IDisposable
  nameWithType: IDisposable
  fullName: System.IDisposable
- uid: System.IEquatable{DrawnUi.Draw.SkiaImageManager.QueueItem}
  commentId: T:System.IEquatable{DrawnUi.Draw.SkiaImageManager.QueueItem}
  parent: System
  definition: System.IEquatable`1
  href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  name: IEquatable<SkiaImageManager.QueueItem>
  nameWithType: IEquatable<SkiaImageManager.QueueItem>
  fullName: System.IEquatable<DrawnUi.Draw.SkiaImageManager.QueueItem>
  nameWithType.vb: IEquatable(Of SkiaImageManager.QueueItem)
  fullName.vb: System.IEquatable(Of DrawnUi.Draw.SkiaImageManager.QueueItem)
  name.vb: IEquatable(Of SkiaImageManager.QueueItem)
  spec.csharp:
  - uid: System.IEquatable`1
    name: IEquatable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  - name: <
  - uid: DrawnUi.Draw.SkiaImageManager
    name: SkiaImageManager
    href: DrawnUi.Draw.SkiaImageManager.html
  - name: .
  - uid: DrawnUi.Draw.SkiaImageManager.QueueItem
    name: QueueItem
    href: DrawnUi.Draw.SkiaImageManager.QueueItem.html
  - name: '>'
  spec.vb:
  - uid: System.IEquatable`1
    name: IEquatable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Draw.SkiaImageManager
    name: SkiaImageManager
    href: DrawnUi.Draw.SkiaImageManager.html
  - name: .
  - uid: DrawnUi.Draw.SkiaImageManager.QueueItem
    name: QueueItem
    href: DrawnUi.Draw.SkiaImageManager.QueueItem.html
  - name: )
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: System.IEquatable`1
  commentId: T:System.IEquatable`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  name: IEquatable<T>
  nameWithType: IEquatable<T>
  fullName: System.IEquatable<T>
  nameWithType.vb: IEquatable(Of T)
  fullName.vb: System.IEquatable(Of T)
  name.vb: IEquatable(Of T)
  spec.csharp:
  - uid: System.IEquatable`1
    name: IEquatable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.IEquatable`1
    name: IEquatable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.SkiaImageManager.QueueItem.#ctor*
  commentId: Overload:DrawnUi.Draw.SkiaImageManager.QueueItem.#ctor
  href: DrawnUi.Draw.SkiaImageManager.QueueItem.html#DrawnUi_Draw_SkiaImageManager_QueueItem__ctor_Microsoft_Maui_Controls_ImageSource_System_Threading_CancellationTokenSource_System_Threading_Tasks_TaskCompletionSource_SkiaSharp_SKBitmap__System_Boolean_
  name: QueueItem
  nameWithType: SkiaImageManager.QueueItem.QueueItem
  fullName: DrawnUi.Draw.SkiaImageManager.QueueItem.QueueItem
  nameWithType.vb: SkiaImageManager.QueueItem.New
  fullName.vb: DrawnUi.Draw.SkiaImageManager.QueueItem.New
  name.vb: New
- uid: Microsoft.Maui.Controls.ImageSource
  commentId: T:Microsoft.Maui.Controls.ImageSource
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.imagesource
  name: ImageSource
  nameWithType: ImageSource
  fullName: Microsoft.Maui.Controls.ImageSource
- uid: System.Threading.CancellationTokenSource
  commentId: T:System.Threading.CancellationTokenSource
  parent: System.Threading
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.threading.cancellationtokensource
  name: CancellationTokenSource
  nameWithType: CancellationTokenSource
  fullName: System.Threading.CancellationTokenSource
- uid: System.Threading.Tasks.TaskCompletionSource{SkiaSharp.SKBitmap}
  commentId: T:System.Threading.Tasks.TaskCompletionSource{SkiaSharp.SKBitmap}
  parent: System.Threading.Tasks
  definition: System.Threading.Tasks.TaskCompletionSource`1
  href: https://learn.microsoft.com/dotnet/api/system.threading.tasks.taskcompletionsource-1
  name: TaskCompletionSource<SKBitmap>
  nameWithType: TaskCompletionSource<SKBitmap>
  fullName: System.Threading.Tasks.TaskCompletionSource<SkiaSharp.SKBitmap>
  nameWithType.vb: TaskCompletionSource(Of SKBitmap)
  fullName.vb: System.Threading.Tasks.TaskCompletionSource(Of SkiaSharp.SKBitmap)
  name.vb: TaskCompletionSource(Of SKBitmap)
  spec.csharp:
  - uid: System.Threading.Tasks.TaskCompletionSource`1
    name: TaskCompletionSource
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading.tasks.taskcompletionsource-1
  - name: <
  - uid: SkiaSharp.SKBitmap
    name: SKBitmap
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skbitmap
  - name: '>'
  spec.vb:
  - uid: System.Threading.Tasks.TaskCompletionSource`1
    name: TaskCompletionSource
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading.tasks.taskcompletionsource-1
  - name: (
  - name: Of
  - name: " "
  - uid: SkiaSharp.SKBitmap
    name: SKBitmap
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skbitmap
  - name: )
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: Microsoft.Maui.Controls
  commentId: N:Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft
  name: Microsoft.Maui.Controls
  nameWithType: Microsoft.Maui.Controls
  fullName: Microsoft.Maui.Controls
  spec.csharp:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Controls
    name: Controls
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls
  spec.vb:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Controls
    name: Controls
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls
- uid: System.Threading
  commentId: N:System.Threading
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Threading
  nameWithType: System.Threading
  fullName: System.Threading
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Threading
    name: Threading
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Threading
    name: Threading
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading
- uid: System.Threading.Tasks.TaskCompletionSource`1
  commentId: T:System.Threading.Tasks.TaskCompletionSource`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.threading.tasks.taskcompletionsource-1
  name: TaskCompletionSource<TResult>
  nameWithType: TaskCompletionSource<TResult>
  fullName: System.Threading.Tasks.TaskCompletionSource<TResult>
  nameWithType.vb: TaskCompletionSource(Of TResult)
  fullName.vb: System.Threading.Tasks.TaskCompletionSource(Of TResult)
  name.vb: TaskCompletionSource(Of TResult)
  spec.csharp:
  - uid: System.Threading.Tasks.TaskCompletionSource`1
    name: TaskCompletionSource
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading.tasks.taskcompletionsource-1
  - name: <
  - name: TResult
  - name: '>'
  spec.vb:
  - uid: System.Threading.Tasks.TaskCompletionSource`1
    name: TaskCompletionSource
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading.tasks.taskcompletionsource-1
  - name: (
  - name: Of
  - name: " "
  - name: TResult
  - name: )
- uid: System.Threading.Tasks
  commentId: N:System.Threading.Tasks
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Threading.Tasks
  nameWithType: System.Threading.Tasks
  fullName: System.Threading.Tasks
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Threading
    name: Threading
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading
  - name: .
  - uid: System.Threading.Tasks
    name: Tasks
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading.tasks
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Threading
    name: Threading
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading
  - name: .
  - uid: System.Threading.Tasks
    name: Tasks
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading.tasks
- uid: DrawnUi.Draw.SkiaImageManager.QueueItem.Source*
  commentId: Overload:DrawnUi.Draw.SkiaImageManager.QueueItem.Source
  href: DrawnUi.Draw.SkiaImageManager.QueueItem.html#DrawnUi_Draw_SkiaImageManager_QueueItem_Source
  name: Source
  nameWithType: SkiaImageManager.QueueItem.Source
  fullName: DrawnUi.Draw.SkiaImageManager.QueueItem.Source
- uid: DrawnUi.Draw.SkiaImageManager.QueueItem.Cancel*
  commentId: Overload:DrawnUi.Draw.SkiaImageManager.QueueItem.Cancel
  href: DrawnUi.Draw.SkiaImageManager.QueueItem.html#DrawnUi_Draw_SkiaImageManager_QueueItem_Cancel
  name: Cancel
  nameWithType: SkiaImageManager.QueueItem.Cancel
  fullName: DrawnUi.Draw.SkiaImageManager.QueueItem.Cancel
- uid: DrawnUi.Draw.SkiaImageManager.QueueItem.Task*
  commentId: Overload:DrawnUi.Draw.SkiaImageManager.QueueItem.Task
  href: DrawnUi.Draw.SkiaImageManager.QueueItem.html#DrawnUi_Draw_SkiaImageManager_QueueItem_Task
  name: Task
  nameWithType: SkiaImageManager.QueueItem.Task
  fullName: DrawnUi.Draw.SkiaImageManager.QueueItem.Task
- uid: DrawnUi.Draw.SkiaImageManager.QueueItem.Dispose*
  commentId: Overload:DrawnUi.Draw.SkiaImageManager.QueueItem.Dispose
  href: DrawnUi.Draw.SkiaImageManager.QueueItem.html#DrawnUi_Draw_SkiaImageManager_QueueItem_Dispose
  name: Dispose
  nameWithType: SkiaImageManager.QueueItem.Dispose
  fullName: DrawnUi.Draw.SkiaImageManager.QueueItem.Dispose
- uid: System.IDisposable.Dispose
  commentId: M:System.IDisposable.Dispose
  parent: System.IDisposable
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.idisposable.dispose
  name: Dispose()
  nameWithType: IDisposable.Dispose()
  fullName: System.IDisposable.Dispose()
  spec.csharp:
  - uid: System.IDisposable.Dispose
    name: Dispose
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.idisposable.dispose
  - name: (
  - name: )
  spec.vb:
  - uid: System.IDisposable.Dispose
    name: Dispose
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.idisposable.dispose
  - name: (
  - name: )
