### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.SkCamera3D2
  commentId: T:DrawnUi.Draw.SkCamera3D2
  id: SkCamera3D2
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.SkCamera3D2.#ctor
  - DrawnUi.Draw.SkCamera3D2.Forward
  - DrawnUi.Draw.SkCamera3D2.GetViewMatrix44
  - DrawnUi.Draw.SkCamera3D2.Position
  - DrawnUi.Draw.SkCamera3D2.Reset
  - DrawnUi.Draw.SkCamera3D2.RotateXDegrees(System.Single)
  - DrawnUi.Draw.SkCamera3D2.RotateYDegrees(System.Single)
  - DrawnUi.Draw.SkCamera3D2.RotateZDegrees(System.Single)
  - DrawnUi.Draw.SkCamera3D2.Up
  - DrawnUi.Draw.SkCamera3D2.Update
  langs:
  - csharp
  - vb
  name: SkCamera3D2
  nameWithType: SkCamera3D2
  fullName: DrawnUi.Draw.SkCamera3D2
  type: Class
  source:
    remote:
      path: src/Shared/Internals/Helpers/3D/SkCamera3D.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: SkCamera3D2
    path: ../src/Shared/Internals/Helpers/3D/SkCamera3D.cs
    startLine: 182
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public class SkCamera3D2
    content.vb: Public Class SkCamera3D2
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.SkCamera3D2.Position
  commentId: F:DrawnUi.Draw.SkCamera3D2.Position
  id: Position
  parent: DrawnUi.Draw.SkCamera3D2
  langs:
  - csharp
  - vb
  name: Position
  nameWithType: SkCamera3D2.Position
  fullName: DrawnUi.Draw.SkCamera3D2.Position
  type: Field
  source:
    remote:
      path: src/Shared/Internals/Helpers/3D/SkCamera3D.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Position
    path: ../src/Shared/Internals/Helpers/3D/SkCamera3D.cs
    startLine: 184
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public Vector3 Position
    return:
      type: System.Numerics.Vector3
    content.vb: Public Position As Vector3
- uid: DrawnUi.Draw.SkCamera3D2.Forward
  commentId: F:DrawnUi.Draw.SkCamera3D2.Forward
  id: Forward
  parent: DrawnUi.Draw.SkCamera3D2
  langs:
  - csharp
  - vb
  name: Forward
  nameWithType: SkCamera3D2.Forward
  fullName: DrawnUi.Draw.SkCamera3D2.Forward
  type: Field
  source:
    remote:
      path: src/Shared/Internals/Helpers/3D/SkCamera3D.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Forward
    path: ../src/Shared/Internals/Helpers/3D/SkCamera3D.cs
    startLine: 185
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public Vector3 Forward
    return:
      type: System.Numerics.Vector3
    content.vb: Public Forward As Vector3
- uid: DrawnUi.Draw.SkCamera3D2.Up
  commentId: F:DrawnUi.Draw.SkCamera3D2.Up
  id: Up
  parent: DrawnUi.Draw.SkCamera3D2
  langs:
  - csharp
  - vb
  name: Up
  nameWithType: SkCamera3D2.Up
  fullName: DrawnUi.Draw.SkCamera3D2.Up
  type: Field
  source:
    remote:
      path: src/Shared/Internals/Helpers/3D/SkCamera3D.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Up
    path: ../src/Shared/Internals/Helpers/3D/SkCamera3D.cs
    startLine: 186
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public Vector3 Up
    return:
      type: System.Numerics.Vector3
    content.vb: Public Up As Vector3
- uid: DrawnUi.Draw.SkCamera3D2.#ctor
  commentId: M:DrawnUi.Draw.SkCamera3D2.#ctor
  id: '#ctor'
  parent: DrawnUi.Draw.SkCamera3D2
  langs:
  - csharp
  - vb
  name: SkCamera3D2()
  nameWithType: SkCamera3D2.SkCamera3D2()
  fullName: DrawnUi.Draw.SkCamera3D2.SkCamera3D2()
  type: Constructor
  source:
    remote:
      path: src/Shared/Internals/Helpers/3D/SkCamera3D.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Shared/Internals/Helpers/3D/SkCamera3D.cs
    startLine: 191
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public SkCamera3D2()
    content.vb: Public Sub New()
  overload: DrawnUi.Draw.SkCamera3D2.#ctor*
  nameWithType.vb: SkCamera3D2.New()
  fullName.vb: DrawnUi.Draw.SkCamera3D2.New()
  name.vb: New()
- uid: DrawnUi.Draw.SkCamera3D2.Reset
  commentId: M:DrawnUi.Draw.SkCamera3D2.Reset
  id: Reset
  parent: DrawnUi.Draw.SkCamera3D2
  langs:
  - csharp
  - vb
  name: Reset()
  nameWithType: SkCamera3D2.Reset()
  fullName: DrawnUi.Draw.SkCamera3D2.Reset()
  type: Method
  source:
    remote:
      path: src/Shared/Internals/Helpers/3D/SkCamera3D.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Reset
    path: ../src/Shared/Internals/Helpers/3D/SkCamera3D.cs
    startLine: 196
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public void Reset()
    content.vb: Public Sub Reset()
  overload: DrawnUi.Draw.SkCamera3D2.Reset*
- uid: DrawnUi.Draw.SkCamera3D2.RotateXDegrees(System.Single)
  commentId: M:DrawnUi.Draw.SkCamera3D2.RotateXDegrees(System.Single)
  id: RotateXDegrees(System.Single)
  parent: DrawnUi.Draw.SkCamera3D2
  langs:
  - csharp
  - vb
  name: RotateXDegrees(float)
  nameWithType: SkCamera3D2.RotateXDegrees(float)
  fullName: DrawnUi.Draw.SkCamera3D2.RotateXDegrees(float)
  type: Method
  source:
    remote:
      path: src/Shared/Internals/Helpers/3D/SkCamera3D.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: RotateXDegrees
    path: ../src/Shared/Internals/Helpers/3D/SkCamera3D.cs
    startLine: 205
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public void RotateXDegrees(float degrees)
    parameters:
    - id: degrees
      type: System.Single
    content.vb: Public Sub RotateXDegrees(degrees As Single)
  overload: DrawnUi.Draw.SkCamera3D2.RotateXDegrees*
  nameWithType.vb: SkCamera3D2.RotateXDegrees(Single)
  fullName.vb: DrawnUi.Draw.SkCamera3D2.RotateXDegrees(Single)
  name.vb: RotateXDegrees(Single)
- uid: DrawnUi.Draw.SkCamera3D2.RotateYDegrees(System.Single)
  commentId: M:DrawnUi.Draw.SkCamera3D2.RotateYDegrees(System.Single)
  id: RotateYDegrees(System.Single)
  parent: DrawnUi.Draw.SkCamera3D2
  langs:
  - csharp
  - vb
  name: RotateYDegrees(float)
  nameWithType: SkCamera3D2.RotateYDegrees(float)
  fullName: DrawnUi.Draw.SkCamera3D2.RotateYDegrees(float)
  type: Method
  source:
    remote:
      path: src/Shared/Internals/Helpers/3D/SkCamera3D.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: RotateYDegrees
    path: ../src/Shared/Internals/Helpers/3D/SkCamera3D.cs
    startLine: 210
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public void RotateYDegrees(float degrees)
    parameters:
    - id: degrees
      type: System.Single
    content.vb: Public Sub RotateYDegrees(degrees As Single)
  overload: DrawnUi.Draw.SkCamera3D2.RotateYDegrees*
  nameWithType.vb: SkCamera3D2.RotateYDegrees(Single)
  fullName.vb: DrawnUi.Draw.SkCamera3D2.RotateYDegrees(Single)
  name.vb: RotateYDegrees(Single)
- uid: DrawnUi.Draw.SkCamera3D2.RotateZDegrees(System.Single)
  commentId: M:DrawnUi.Draw.SkCamera3D2.RotateZDegrees(System.Single)
  id: RotateZDegrees(System.Single)
  parent: DrawnUi.Draw.SkCamera3D2
  langs:
  - csharp
  - vb
  name: RotateZDegrees(float)
  nameWithType: SkCamera3D2.RotateZDegrees(float)
  fullName: DrawnUi.Draw.SkCamera3D2.RotateZDegrees(float)
  type: Method
  source:
    remote:
      path: src/Shared/Internals/Helpers/3D/SkCamera3D.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: RotateZDegrees
    path: ../src/Shared/Internals/Helpers/3D/SkCamera3D.cs
    startLine: 215
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public void RotateZDegrees(float degrees)
    parameters:
    - id: degrees
      type: System.Single
    content.vb: Public Sub RotateZDegrees(degrees As Single)
  overload: DrawnUi.Draw.SkCamera3D2.RotateZDegrees*
  nameWithType.vb: SkCamera3D2.RotateZDegrees(Single)
  fullName.vb: DrawnUi.Draw.SkCamera3D2.RotateZDegrees(Single)
  name.vb: RotateZDegrees(Single)
- uid: DrawnUi.Draw.SkCamera3D2.Update
  commentId: M:DrawnUi.Draw.SkCamera3D2.Update
  id: Update
  parent: DrawnUi.Draw.SkCamera3D2
  langs:
  - csharp
  - vb
  name: Update()
  nameWithType: SkCamera3D2.Update()
  fullName: DrawnUi.Draw.SkCamera3D2.Update()
  type: Method
  source:
    remote:
      path: src/Shared/Internals/Helpers/3D/SkCamera3D.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Update
    path: ../src/Shared/Internals/Helpers/3D/SkCamera3D.cs
    startLine: 242
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public void Update()
    content.vb: Public Sub Update()
  overload: DrawnUi.Draw.SkCamera3D2.Update*
- uid: DrawnUi.Draw.SkCamera3D2.GetViewMatrix44
  commentId: M:DrawnUi.Draw.SkCamera3D2.GetViewMatrix44
  id: GetViewMatrix44
  parent: DrawnUi.Draw.SkCamera3D2
  langs:
  - csharp
  - vb
  name: GetViewMatrix44()
  nameWithType: SkCamera3D2.GetViewMatrix44()
  fullName: DrawnUi.Draw.SkCamera3D2.GetViewMatrix44()
  type: Method
  source:
    remote:
      path: src/Shared/Internals/Helpers/3D/SkCamera3D.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: GetViewMatrix44
    path: ../src/Shared/Internals/Helpers/3D/SkCamera3D.cs
    startLine: 266
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public SKMatrix44 GetViewMatrix44()
    return:
      type: SkiaSharp.SKMatrix44
    content.vb: Public Function GetViewMatrix44() As SKMatrix44
  overload: DrawnUi.Draw.SkCamera3D2.GetViewMatrix44*
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: System.Numerics.Vector3
  commentId: T:System.Numerics.Vector3
  parent: System.Numerics
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.numerics.vector3
  name: Vector3
  nameWithType: Vector3
  fullName: System.Numerics.Vector3
- uid: System.Numerics
  commentId: N:System.Numerics
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Numerics
  nameWithType: System.Numerics
  fullName: System.Numerics
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Numerics
    name: Numerics
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.numerics
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Numerics
    name: Numerics
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.numerics
- uid: DrawnUi.Draw.SkCamera3D2.#ctor*
  commentId: Overload:DrawnUi.Draw.SkCamera3D2.#ctor
  href: DrawnUi.Draw.SkCamera3D2.html#DrawnUi_Draw_SkCamera3D2__ctor
  name: SkCamera3D2
  nameWithType: SkCamera3D2.SkCamera3D2
  fullName: DrawnUi.Draw.SkCamera3D2.SkCamera3D2
  nameWithType.vb: SkCamera3D2.New
  fullName.vb: DrawnUi.Draw.SkCamera3D2.New
  name.vb: New
- uid: DrawnUi.Draw.SkCamera3D2.Reset*
  commentId: Overload:DrawnUi.Draw.SkCamera3D2.Reset
  href: DrawnUi.Draw.SkCamera3D2.html#DrawnUi_Draw_SkCamera3D2_Reset
  name: Reset
  nameWithType: SkCamera3D2.Reset
  fullName: DrawnUi.Draw.SkCamera3D2.Reset
- uid: DrawnUi.Draw.SkCamera3D2.RotateXDegrees*
  commentId: Overload:DrawnUi.Draw.SkCamera3D2.RotateXDegrees
  href: DrawnUi.Draw.SkCamera3D2.html#DrawnUi_Draw_SkCamera3D2_RotateXDegrees_System_Single_
  name: RotateXDegrees
  nameWithType: SkCamera3D2.RotateXDegrees
  fullName: DrawnUi.Draw.SkCamera3D2.RotateXDegrees
- uid: System.Single
  commentId: T:System.Single
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.single
  name: float
  nameWithType: float
  fullName: float
  nameWithType.vb: Single
  fullName.vb: Single
  name.vb: Single
- uid: DrawnUi.Draw.SkCamera3D2.RotateYDegrees*
  commentId: Overload:DrawnUi.Draw.SkCamera3D2.RotateYDegrees
  href: DrawnUi.Draw.SkCamera3D2.html#DrawnUi_Draw_SkCamera3D2_RotateYDegrees_System_Single_
  name: RotateYDegrees
  nameWithType: SkCamera3D2.RotateYDegrees
  fullName: DrawnUi.Draw.SkCamera3D2.RotateYDegrees
- uid: DrawnUi.Draw.SkCamera3D2.RotateZDegrees*
  commentId: Overload:DrawnUi.Draw.SkCamera3D2.RotateZDegrees
  href: DrawnUi.Draw.SkCamera3D2.html#DrawnUi_Draw_SkCamera3D2_RotateZDegrees_System_Single_
  name: RotateZDegrees
  nameWithType: SkCamera3D2.RotateZDegrees
  fullName: DrawnUi.Draw.SkCamera3D2.RotateZDegrees
- uid: DrawnUi.Draw.SkCamera3D2.Update*
  commentId: Overload:DrawnUi.Draw.SkCamera3D2.Update
  href: DrawnUi.Draw.SkCamera3D2.html#DrawnUi_Draw_SkCamera3D2_Update
  name: Update
  nameWithType: SkCamera3D2.Update
  fullName: DrawnUi.Draw.SkCamera3D2.Update
- uid: DrawnUi.Draw.SkCamera3D2.GetViewMatrix44*
  commentId: Overload:DrawnUi.Draw.SkCamera3D2.GetViewMatrix44
  href: DrawnUi.Draw.SkCamera3D2.html#DrawnUi_Draw_SkCamera3D2_GetViewMatrix44
  name: GetViewMatrix44
  nameWithType: SkCamera3D2.GetViewMatrix44
  fullName: DrawnUi.Draw.SkCamera3D2.GetViewMatrix44
- uid: SkiaSharp.SKMatrix44
  commentId: T:SkiaSharp.SKMatrix44
  parent: SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.skmatrix44
  name: SKMatrix44
  nameWithType: SKMatrix44
  fullName: SkiaSharp.SKMatrix44
- uid: SkiaSharp
  commentId: N:SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp
  name: SkiaSharp
  nameWithType: SkiaSharp
  fullName: SkiaSharp
