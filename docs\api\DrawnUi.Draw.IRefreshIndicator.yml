### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.IRefreshIndicator
  commentId: T:DrawnUi.Draw.IRefreshIndicator
  id: IRefreshIndicator
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.IRefreshIndicator.SetDragRatio(System.Single,System.Single,System.Double)
  langs:
  - csharp
  - vb
  name: IRefreshIndicator
  nameWithType: IRefreshIndicator
  fullName: DrawnUi.Draw.IRefreshIndicator
  type: Interface
  source:
    remote:
      path: src/Shared/Draw/Internals/Interfaces/IRefreshIndicator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IRefreshIndicator
    path: ../src/Shared/Draw/Internals/Interfaces/IRefreshIndicator.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: 'public interface IRefreshIndicator : IDrawnBase, IDisposable, ICanBeUpdatedWithContext, ICanBeUpdated'
    content.vb: Public Interface IRefreshIndicator Inherits IDrawnBase, IDisposable, ICanBeUpdatedWithContext, ICanBeUpdated
  inheritedMembers:
  - DrawnUi.Draw.IDrawnBase.DrawingRect
  - DrawnUi.Draw.IDrawnBase.Tag
  - DrawnUi.Draw.IDrawnBase.IsVisible
  - DrawnUi.Draw.IDrawnBase.IsDisposed
  - DrawnUi.Draw.IDrawnBase.IsDisposing
  - DrawnUi.Draw.IDrawnBase.IsVisibleInViewTree
  - DrawnUi.Draw.IDrawnBase.GetOnScreenVisibleArea(DrawnUi.Draw.DrawingContext,System.Numerics.Vector2)
  - DrawnUi.Draw.IDrawnBase.Invalidate
  - DrawnUi.Draw.IDrawnBase.InvalidateParents
  - DrawnUi.Draw.IDrawnBase.ClipSmart(SkiaSharp.SKCanvas,SkiaSharp.SKPath,SkiaSharp.SKClipOperation)
  - DrawnUi.Draw.IDrawnBase.CreateClip(System.Object,System.Boolean,SkiaSharp.SKPath)
  - DrawnUi.Draw.IDrawnBase.RegisterAnimator(DrawnUi.Draw.ISkiaAnimator)
  - DrawnUi.Draw.IDrawnBase.UnregisterAnimator(System.Guid)
  - DrawnUi.Draw.IDrawnBase.UnregisterAllAnimatorsByType(System.Type)
  - DrawnUi.Draw.IDrawnBase.RegisterGestureListener(DrawnUi.Draw.ISkiaGestureListener)
  - DrawnUi.Draw.IDrawnBase.UnregisterGestureListener(DrawnUi.Draw.ISkiaGestureListener)
  - DrawnUi.Draw.IDrawnBase.PostAnimators
  - DrawnUi.Draw.IDrawnBase.Views
  - DrawnUi.Draw.IDrawnBase.AddSubView(DrawnUi.Draw.SkiaControl)
  - DrawnUi.Draw.IDrawnBase.RemoveSubView(DrawnUi.Draw.SkiaControl)
  - DrawnUi.Draw.IDrawnBase.MeasuredSize
  - DrawnUi.Draw.IDrawnBase.Destination
  - DrawnUi.Draw.IDrawnBase.HeightRequest
  - DrawnUi.Draw.IDrawnBase.WidthRequest
  - DrawnUi.Draw.IDrawnBase.Height
  - DrawnUi.Draw.IDrawnBase.Width
  - DrawnUi.Draw.IDrawnBase.TranslationX
  - DrawnUi.Draw.IDrawnBase.TranslationY
  - DrawnUi.Draw.IDrawnBase.InputTransparent
  - DrawnUi.Draw.IDrawnBase.IsClippedToBounds
  - DrawnUi.Draw.IDrawnBase.ClipEffects
  - DrawnUi.Draw.IDrawnBase.UpdateLocks
  - DrawnUi.Draw.IDrawnBase.InvalidateByChild(DrawnUi.Draw.SkiaControl)
  - DrawnUi.Draw.IDrawnBase.UpdateByChild(DrawnUi.Draw.SkiaControl)
  - DrawnUi.Draw.IDrawnBase.ShouldInvalidateByChildren
  - DrawnUi.Draw.IDrawnBase.RenderingScale
  - DrawnUi.Draw.IDrawnBase.X
  - DrawnUi.Draw.IDrawnBase.Y
  - DrawnUi.Draw.IDrawnBase.InvalidateViewport
  - DrawnUi.Draw.IDrawnBase.Repaint
  - DrawnUi.Draw.IDrawnBase.InvalidateViewsList
  - DrawnUi.Draw.IDrawnBase.DisposeObject(System.IDisposable)
  - System.IDisposable.Dispose
  - DrawnUi.Draw.ICanBeUpdatedWithContext.BindingContext
  - DrawnUi.Draw.ICanBeUpdated.Update
  extensionMethods:
  - DrawnUi.Draw.IDrawnBase.DrawnUi.Draw.DrawnExtensions.GetVelocityRatioForChild(DrawnUi.Draw.ISkiaControl)
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.IRefreshIndicator.SetDragRatio(System.Single,System.Single,System.Double)
  commentId: M:DrawnUi.Draw.IRefreshIndicator.SetDragRatio(System.Single,System.Single,System.Double)
  id: SetDragRatio(System.Single,System.Single,System.Double)
  parent: DrawnUi.Draw.IRefreshIndicator
  langs:
  - csharp
  - vb
  name: SetDragRatio(float, float, double)
  nameWithType: IRefreshIndicator.SetDragRatio(float, float, double)
  fullName: DrawnUi.Draw.IRefreshIndicator.SetDragRatio(float, float, double)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Interfaces/IRefreshIndicator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: SetDragRatio
    path: ../src/Shared/Draw/Internals/Interfaces/IRefreshIndicator.cs
    startLine: 9
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: 0 - 1 ratio between overscroll and RefreshShowDistance.1 means control must be totally shown.
  example: []
  syntax:
    content: void SetDragRatio(float ratio, float ptsScrollOffset, double ptsLimit)
    parameters:
    - id: ratio
      type: System.Single
      description: ''
    - id: ptsScrollOffset
      type: System.Single
    - id: ptsLimit
      type: System.Double
    content.vb: Sub SetDragRatio(ratio As Single, ptsScrollOffset As Single, ptsLimit As Double)
  overload: DrawnUi.Draw.IRefreshIndicator.SetDragRatio*
  nameWithType.vb: IRefreshIndicator.SetDragRatio(Single, Single, Double)
  fullName.vb: DrawnUi.Draw.IRefreshIndicator.SetDragRatio(Single, Single, Double)
  name.vb: SetDragRatio(Single, Single, Double)
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: DrawnUi.Draw.IDrawnBase.DrawingRect
  commentId: P:DrawnUi.Draw.IDrawnBase.DrawingRect
  parent: DrawnUi.Draw.IDrawnBase
  href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_DrawingRect
  name: DrawingRect
  nameWithType: IDrawnBase.DrawingRect
  fullName: DrawnUi.Draw.IDrawnBase.DrawingRect
- uid: DrawnUi.Draw.IDrawnBase.Tag
  commentId: P:DrawnUi.Draw.IDrawnBase.Tag
  parent: DrawnUi.Draw.IDrawnBase
  href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_Tag
  name: Tag
  nameWithType: IDrawnBase.Tag
  fullName: DrawnUi.Draw.IDrawnBase.Tag
- uid: DrawnUi.Draw.IDrawnBase.IsVisible
  commentId: P:DrawnUi.Draw.IDrawnBase.IsVisible
  parent: DrawnUi.Draw.IDrawnBase
  href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_IsVisible
  name: IsVisible
  nameWithType: IDrawnBase.IsVisible
  fullName: DrawnUi.Draw.IDrawnBase.IsVisible
- uid: DrawnUi.Draw.IDrawnBase.IsDisposed
  commentId: P:DrawnUi.Draw.IDrawnBase.IsDisposed
  parent: DrawnUi.Draw.IDrawnBase
  href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_IsDisposed
  name: IsDisposed
  nameWithType: IDrawnBase.IsDisposed
  fullName: DrawnUi.Draw.IDrawnBase.IsDisposed
- uid: DrawnUi.Draw.IDrawnBase.IsDisposing
  commentId: P:DrawnUi.Draw.IDrawnBase.IsDisposing
  parent: DrawnUi.Draw.IDrawnBase
  href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_IsDisposing
  name: IsDisposing
  nameWithType: IDrawnBase.IsDisposing
  fullName: DrawnUi.Draw.IDrawnBase.IsDisposing
- uid: DrawnUi.Draw.IDrawnBase.IsVisibleInViewTree
  commentId: M:DrawnUi.Draw.IDrawnBase.IsVisibleInViewTree
  parent: DrawnUi.Draw.IDrawnBase
  href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_IsVisibleInViewTree
  name: IsVisibleInViewTree()
  nameWithType: IDrawnBase.IsVisibleInViewTree()
  fullName: DrawnUi.Draw.IDrawnBase.IsVisibleInViewTree()
  spec.csharp:
  - uid: DrawnUi.Draw.IDrawnBase.IsVisibleInViewTree
    name: IsVisibleInViewTree
    href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_IsVisibleInViewTree
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.IDrawnBase.IsVisibleInViewTree
    name: IsVisibleInViewTree
    href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_IsVisibleInViewTree
  - name: (
  - name: )
- uid: DrawnUi.Draw.IDrawnBase.GetOnScreenVisibleArea(DrawnUi.Draw.DrawingContext,System.Numerics.Vector2)
  commentId: M:DrawnUi.Draw.IDrawnBase.GetOnScreenVisibleArea(DrawnUi.Draw.DrawingContext,System.Numerics.Vector2)
  parent: DrawnUi.Draw.IDrawnBase
  isExternal: true
  href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_GetOnScreenVisibleArea_DrawnUi_Draw_DrawingContext_System_Numerics_Vector2_
  name: GetOnScreenVisibleArea(DrawingContext, Vector2)
  nameWithType: IDrawnBase.GetOnScreenVisibleArea(DrawingContext, Vector2)
  fullName: DrawnUi.Draw.IDrawnBase.GetOnScreenVisibleArea(DrawnUi.Draw.DrawingContext, System.Numerics.Vector2)
  spec.csharp:
  - uid: DrawnUi.Draw.IDrawnBase.GetOnScreenVisibleArea(DrawnUi.Draw.DrawingContext,System.Numerics.Vector2)
    name: GetOnScreenVisibleArea
    href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_GetOnScreenVisibleArea_DrawnUi_Draw_DrawingContext_System_Numerics_Vector2_
  - name: (
  - uid: DrawnUi.Draw.DrawingContext
    name: DrawingContext
    href: DrawnUi.Draw.DrawingContext.html
  - name: ','
  - name: " "
  - uid: System.Numerics.Vector2
    name: Vector2
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.numerics.vector2
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.IDrawnBase.GetOnScreenVisibleArea(DrawnUi.Draw.DrawingContext,System.Numerics.Vector2)
    name: GetOnScreenVisibleArea
    href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_GetOnScreenVisibleArea_DrawnUi_Draw_DrawingContext_System_Numerics_Vector2_
  - name: (
  - uid: DrawnUi.Draw.DrawingContext
    name: DrawingContext
    href: DrawnUi.Draw.DrawingContext.html
  - name: ','
  - name: " "
  - uid: System.Numerics.Vector2
    name: Vector2
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.numerics.vector2
  - name: )
- uid: DrawnUi.Draw.IDrawnBase.Invalidate
  commentId: M:DrawnUi.Draw.IDrawnBase.Invalidate
  parent: DrawnUi.Draw.IDrawnBase
  href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_Invalidate
  name: Invalidate()
  nameWithType: IDrawnBase.Invalidate()
  fullName: DrawnUi.Draw.IDrawnBase.Invalidate()
  spec.csharp:
  - uid: DrawnUi.Draw.IDrawnBase.Invalidate
    name: Invalidate
    href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_Invalidate
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.IDrawnBase.Invalidate
    name: Invalidate
    href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_Invalidate
  - name: (
  - name: )
- uid: DrawnUi.Draw.IDrawnBase.InvalidateParents
  commentId: M:DrawnUi.Draw.IDrawnBase.InvalidateParents
  parent: DrawnUi.Draw.IDrawnBase
  href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_InvalidateParents
  name: InvalidateParents()
  nameWithType: IDrawnBase.InvalidateParents()
  fullName: DrawnUi.Draw.IDrawnBase.InvalidateParents()
  spec.csharp:
  - uid: DrawnUi.Draw.IDrawnBase.InvalidateParents
    name: InvalidateParents
    href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_InvalidateParents
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.IDrawnBase.InvalidateParents
    name: InvalidateParents
    href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_InvalidateParents
  - name: (
  - name: )
- uid: DrawnUi.Draw.IDrawnBase.ClipSmart(SkiaSharp.SKCanvas,SkiaSharp.SKPath,SkiaSharp.SKClipOperation)
  commentId: M:DrawnUi.Draw.IDrawnBase.ClipSmart(SkiaSharp.SKCanvas,SkiaSharp.SKPath,SkiaSharp.SKClipOperation)
  parent: DrawnUi.Draw.IDrawnBase
  isExternal: true
  href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_ClipSmart_SkiaSharp_SKCanvas_SkiaSharp_SKPath_SkiaSharp_SKClipOperation_
  name: ClipSmart(SKCanvas, SKPath, SKClipOperation)
  nameWithType: IDrawnBase.ClipSmart(SKCanvas, SKPath, SKClipOperation)
  fullName: DrawnUi.Draw.IDrawnBase.ClipSmart(SkiaSharp.SKCanvas, SkiaSharp.SKPath, SkiaSharp.SKClipOperation)
  spec.csharp:
  - uid: DrawnUi.Draw.IDrawnBase.ClipSmart(SkiaSharp.SKCanvas,SkiaSharp.SKPath,SkiaSharp.SKClipOperation)
    name: ClipSmart
    href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_ClipSmart_SkiaSharp_SKCanvas_SkiaSharp_SKPath_SkiaSharp_SKClipOperation_
  - name: (
  - uid: SkiaSharp.SKCanvas
    name: SKCanvas
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skcanvas
  - name: ','
  - name: " "
  - uid: SkiaSharp.SKPath
    name: SKPath
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skpath
  - name: ','
  - name: " "
  - uid: SkiaSharp.SKClipOperation
    name: SKClipOperation
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skclipoperation
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.IDrawnBase.ClipSmart(SkiaSharp.SKCanvas,SkiaSharp.SKPath,SkiaSharp.SKClipOperation)
    name: ClipSmart
    href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_ClipSmart_SkiaSharp_SKCanvas_SkiaSharp_SKPath_SkiaSharp_SKClipOperation_
  - name: (
  - uid: SkiaSharp.SKCanvas
    name: SKCanvas
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skcanvas
  - name: ','
  - name: " "
  - uid: SkiaSharp.SKPath
    name: SKPath
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skpath
  - name: ','
  - name: " "
  - uid: SkiaSharp.SKClipOperation
    name: SKClipOperation
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skclipoperation
  - name: )
- uid: DrawnUi.Draw.IDrawnBase.CreateClip(System.Object,System.Boolean,SkiaSharp.SKPath)
  commentId: M:DrawnUi.Draw.IDrawnBase.CreateClip(System.Object,System.Boolean,SkiaSharp.SKPath)
  parent: DrawnUi.Draw.IDrawnBase
  isExternal: true
  href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_CreateClip_System_Object_System_Boolean_SkiaSharp_SKPath_
  name: CreateClip(object, bool, SKPath)
  nameWithType: IDrawnBase.CreateClip(object, bool, SKPath)
  fullName: DrawnUi.Draw.IDrawnBase.CreateClip(object, bool, SkiaSharp.SKPath)
  nameWithType.vb: IDrawnBase.CreateClip(Object, Boolean, SKPath)
  fullName.vb: DrawnUi.Draw.IDrawnBase.CreateClip(Object, Boolean, SkiaSharp.SKPath)
  name.vb: CreateClip(Object, Boolean, SKPath)
  spec.csharp:
  - uid: DrawnUi.Draw.IDrawnBase.CreateClip(System.Object,System.Boolean,SkiaSharp.SKPath)
    name: CreateClip
    href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_CreateClip_System_Object_System_Boolean_SkiaSharp_SKPath_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: bool
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: ','
  - name: " "
  - uid: SkiaSharp.SKPath
    name: SKPath
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skpath
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.IDrawnBase.CreateClip(System.Object,System.Boolean,SkiaSharp.SKPath)
    name: CreateClip
    href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_CreateClip_System_Object_System_Boolean_SkiaSharp_SKPath_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: Boolean
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: ','
  - name: " "
  - uid: SkiaSharp.SKPath
    name: SKPath
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skpath
  - name: )
- uid: DrawnUi.Draw.IDrawnBase.RegisterAnimator(DrawnUi.Draw.ISkiaAnimator)
  commentId: M:DrawnUi.Draw.IDrawnBase.RegisterAnimator(DrawnUi.Draw.ISkiaAnimator)
  parent: DrawnUi.Draw.IDrawnBase
  href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_RegisterAnimator_DrawnUi_Draw_ISkiaAnimator_
  name: RegisterAnimator(ISkiaAnimator)
  nameWithType: IDrawnBase.RegisterAnimator(ISkiaAnimator)
  fullName: DrawnUi.Draw.IDrawnBase.RegisterAnimator(DrawnUi.Draw.ISkiaAnimator)
  spec.csharp:
  - uid: DrawnUi.Draw.IDrawnBase.RegisterAnimator(DrawnUi.Draw.ISkiaAnimator)
    name: RegisterAnimator
    href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_RegisterAnimator_DrawnUi_Draw_ISkiaAnimator_
  - name: (
  - uid: DrawnUi.Draw.ISkiaAnimator
    name: ISkiaAnimator
    href: DrawnUi.Draw.ISkiaAnimator.html
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.IDrawnBase.RegisterAnimator(DrawnUi.Draw.ISkiaAnimator)
    name: RegisterAnimator
    href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_RegisterAnimator_DrawnUi_Draw_ISkiaAnimator_
  - name: (
  - uid: DrawnUi.Draw.ISkiaAnimator
    name: ISkiaAnimator
    href: DrawnUi.Draw.ISkiaAnimator.html
  - name: )
- uid: DrawnUi.Draw.IDrawnBase.UnregisterAnimator(System.Guid)
  commentId: M:DrawnUi.Draw.IDrawnBase.UnregisterAnimator(System.Guid)
  parent: DrawnUi.Draw.IDrawnBase
  isExternal: true
  href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_UnregisterAnimator_System_Guid_
  name: UnregisterAnimator(Guid)
  nameWithType: IDrawnBase.UnregisterAnimator(Guid)
  fullName: DrawnUi.Draw.IDrawnBase.UnregisterAnimator(System.Guid)
  spec.csharp:
  - uid: DrawnUi.Draw.IDrawnBase.UnregisterAnimator(System.Guid)
    name: UnregisterAnimator
    href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_UnregisterAnimator_System_Guid_
  - name: (
  - uid: System.Guid
    name: Guid
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.guid
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.IDrawnBase.UnregisterAnimator(System.Guid)
    name: UnregisterAnimator
    href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_UnregisterAnimator_System_Guid_
  - name: (
  - uid: System.Guid
    name: Guid
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.guid
  - name: )
- uid: DrawnUi.Draw.IDrawnBase.UnregisterAllAnimatorsByType(System.Type)
  commentId: M:DrawnUi.Draw.IDrawnBase.UnregisterAllAnimatorsByType(System.Type)
  parent: DrawnUi.Draw.IDrawnBase
  isExternal: true
  href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_UnregisterAllAnimatorsByType_System_Type_
  name: UnregisterAllAnimatorsByType(Type)
  nameWithType: IDrawnBase.UnregisterAllAnimatorsByType(Type)
  fullName: DrawnUi.Draw.IDrawnBase.UnregisterAllAnimatorsByType(System.Type)
  spec.csharp:
  - uid: DrawnUi.Draw.IDrawnBase.UnregisterAllAnimatorsByType(System.Type)
    name: UnregisterAllAnimatorsByType
    href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_UnregisterAllAnimatorsByType_System_Type_
  - name: (
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.IDrawnBase.UnregisterAllAnimatorsByType(System.Type)
    name: UnregisterAllAnimatorsByType
    href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_UnregisterAllAnimatorsByType_System_Type_
  - name: (
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
- uid: DrawnUi.Draw.IDrawnBase.RegisterGestureListener(DrawnUi.Draw.ISkiaGestureListener)
  commentId: M:DrawnUi.Draw.IDrawnBase.RegisterGestureListener(DrawnUi.Draw.ISkiaGestureListener)
  parent: DrawnUi.Draw.IDrawnBase
  href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_RegisterGestureListener_DrawnUi_Draw_ISkiaGestureListener_
  name: RegisterGestureListener(ISkiaGestureListener)
  nameWithType: IDrawnBase.RegisterGestureListener(ISkiaGestureListener)
  fullName: DrawnUi.Draw.IDrawnBase.RegisterGestureListener(DrawnUi.Draw.ISkiaGestureListener)
  spec.csharp:
  - uid: DrawnUi.Draw.IDrawnBase.RegisterGestureListener(DrawnUi.Draw.ISkiaGestureListener)
    name: RegisterGestureListener
    href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_RegisterGestureListener_DrawnUi_Draw_ISkiaGestureListener_
  - name: (
  - uid: DrawnUi.Draw.ISkiaGestureListener
    name: ISkiaGestureListener
    href: DrawnUi.Draw.ISkiaGestureListener.html
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.IDrawnBase.RegisterGestureListener(DrawnUi.Draw.ISkiaGestureListener)
    name: RegisterGestureListener
    href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_RegisterGestureListener_DrawnUi_Draw_ISkiaGestureListener_
  - name: (
  - uid: DrawnUi.Draw.ISkiaGestureListener
    name: ISkiaGestureListener
    href: DrawnUi.Draw.ISkiaGestureListener.html
  - name: )
- uid: DrawnUi.Draw.IDrawnBase.UnregisterGestureListener(DrawnUi.Draw.ISkiaGestureListener)
  commentId: M:DrawnUi.Draw.IDrawnBase.UnregisterGestureListener(DrawnUi.Draw.ISkiaGestureListener)
  parent: DrawnUi.Draw.IDrawnBase
  href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_UnregisterGestureListener_DrawnUi_Draw_ISkiaGestureListener_
  name: UnregisterGestureListener(ISkiaGestureListener)
  nameWithType: IDrawnBase.UnregisterGestureListener(ISkiaGestureListener)
  fullName: DrawnUi.Draw.IDrawnBase.UnregisterGestureListener(DrawnUi.Draw.ISkiaGestureListener)
  spec.csharp:
  - uid: DrawnUi.Draw.IDrawnBase.UnregisterGestureListener(DrawnUi.Draw.ISkiaGestureListener)
    name: UnregisterGestureListener
    href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_UnregisterGestureListener_DrawnUi_Draw_ISkiaGestureListener_
  - name: (
  - uid: DrawnUi.Draw.ISkiaGestureListener
    name: ISkiaGestureListener
    href: DrawnUi.Draw.ISkiaGestureListener.html
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.IDrawnBase.UnregisterGestureListener(DrawnUi.Draw.ISkiaGestureListener)
    name: UnregisterGestureListener
    href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_UnregisterGestureListener_DrawnUi_Draw_ISkiaGestureListener_
  - name: (
  - uid: DrawnUi.Draw.ISkiaGestureListener
    name: ISkiaGestureListener
    href: DrawnUi.Draw.ISkiaGestureListener.html
  - name: )
- uid: DrawnUi.Draw.IDrawnBase.PostAnimators
  commentId: P:DrawnUi.Draw.IDrawnBase.PostAnimators
  parent: DrawnUi.Draw.IDrawnBase
  href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_PostAnimators
  name: PostAnimators
  nameWithType: IDrawnBase.PostAnimators
  fullName: DrawnUi.Draw.IDrawnBase.PostAnimators
- uid: DrawnUi.Draw.IDrawnBase.Views
  commentId: P:DrawnUi.Draw.IDrawnBase.Views
  parent: DrawnUi.Draw.IDrawnBase
  href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_Views
  name: Views
  nameWithType: IDrawnBase.Views
  fullName: DrawnUi.Draw.IDrawnBase.Views
- uid: DrawnUi.Draw.IDrawnBase.AddSubView(DrawnUi.Draw.SkiaControl)
  commentId: M:DrawnUi.Draw.IDrawnBase.AddSubView(DrawnUi.Draw.SkiaControl)
  parent: DrawnUi.Draw.IDrawnBase
  href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_AddSubView_DrawnUi_Draw_SkiaControl_
  name: AddSubView(SkiaControl)
  nameWithType: IDrawnBase.AddSubView(SkiaControl)
  fullName: DrawnUi.Draw.IDrawnBase.AddSubView(DrawnUi.Draw.SkiaControl)
  spec.csharp:
  - uid: DrawnUi.Draw.IDrawnBase.AddSubView(DrawnUi.Draw.SkiaControl)
    name: AddSubView
    href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_AddSubView_DrawnUi_Draw_SkiaControl_
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.IDrawnBase.AddSubView(DrawnUi.Draw.SkiaControl)
    name: AddSubView
    href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_AddSubView_DrawnUi_Draw_SkiaControl_
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
- uid: DrawnUi.Draw.IDrawnBase.RemoveSubView(DrawnUi.Draw.SkiaControl)
  commentId: M:DrawnUi.Draw.IDrawnBase.RemoveSubView(DrawnUi.Draw.SkiaControl)
  parent: DrawnUi.Draw.IDrawnBase
  href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_RemoveSubView_DrawnUi_Draw_SkiaControl_
  name: RemoveSubView(SkiaControl)
  nameWithType: IDrawnBase.RemoveSubView(SkiaControl)
  fullName: DrawnUi.Draw.IDrawnBase.RemoveSubView(DrawnUi.Draw.SkiaControl)
  spec.csharp:
  - uid: DrawnUi.Draw.IDrawnBase.RemoveSubView(DrawnUi.Draw.SkiaControl)
    name: RemoveSubView
    href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_RemoveSubView_DrawnUi_Draw_SkiaControl_
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.IDrawnBase.RemoveSubView(DrawnUi.Draw.SkiaControl)
    name: RemoveSubView
    href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_RemoveSubView_DrawnUi_Draw_SkiaControl_
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
- uid: DrawnUi.Draw.IDrawnBase.MeasuredSize
  commentId: P:DrawnUi.Draw.IDrawnBase.MeasuredSize
  parent: DrawnUi.Draw.IDrawnBase
  href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_MeasuredSize
  name: MeasuredSize
  nameWithType: IDrawnBase.MeasuredSize
  fullName: DrawnUi.Draw.IDrawnBase.MeasuredSize
- uid: DrawnUi.Draw.IDrawnBase.Destination
  commentId: P:DrawnUi.Draw.IDrawnBase.Destination
  parent: DrawnUi.Draw.IDrawnBase
  href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_Destination
  name: Destination
  nameWithType: IDrawnBase.Destination
  fullName: DrawnUi.Draw.IDrawnBase.Destination
- uid: DrawnUi.Draw.IDrawnBase.HeightRequest
  commentId: P:DrawnUi.Draw.IDrawnBase.HeightRequest
  parent: DrawnUi.Draw.IDrawnBase
  href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_HeightRequest
  name: HeightRequest
  nameWithType: IDrawnBase.HeightRequest
  fullName: DrawnUi.Draw.IDrawnBase.HeightRequest
- uid: DrawnUi.Draw.IDrawnBase.WidthRequest
  commentId: P:DrawnUi.Draw.IDrawnBase.WidthRequest
  parent: DrawnUi.Draw.IDrawnBase
  href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_WidthRequest
  name: WidthRequest
  nameWithType: IDrawnBase.WidthRequest
  fullName: DrawnUi.Draw.IDrawnBase.WidthRequest
- uid: DrawnUi.Draw.IDrawnBase.Height
  commentId: P:DrawnUi.Draw.IDrawnBase.Height
  parent: DrawnUi.Draw.IDrawnBase
  href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_Height
  name: Height
  nameWithType: IDrawnBase.Height
  fullName: DrawnUi.Draw.IDrawnBase.Height
- uid: DrawnUi.Draw.IDrawnBase.Width
  commentId: P:DrawnUi.Draw.IDrawnBase.Width
  parent: DrawnUi.Draw.IDrawnBase
  href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_Width
  name: Width
  nameWithType: IDrawnBase.Width
  fullName: DrawnUi.Draw.IDrawnBase.Width
- uid: DrawnUi.Draw.IDrawnBase.TranslationX
  commentId: P:DrawnUi.Draw.IDrawnBase.TranslationX
  parent: DrawnUi.Draw.IDrawnBase
  href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_TranslationX
  name: TranslationX
  nameWithType: IDrawnBase.TranslationX
  fullName: DrawnUi.Draw.IDrawnBase.TranslationX
- uid: DrawnUi.Draw.IDrawnBase.TranslationY
  commentId: P:DrawnUi.Draw.IDrawnBase.TranslationY
  parent: DrawnUi.Draw.IDrawnBase
  href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_TranslationY
  name: TranslationY
  nameWithType: IDrawnBase.TranslationY
  fullName: DrawnUi.Draw.IDrawnBase.TranslationY
- uid: DrawnUi.Draw.IDrawnBase.InputTransparent
  commentId: P:DrawnUi.Draw.IDrawnBase.InputTransparent
  parent: DrawnUi.Draw.IDrawnBase
  href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_InputTransparent
  name: InputTransparent
  nameWithType: IDrawnBase.InputTransparent
  fullName: DrawnUi.Draw.IDrawnBase.InputTransparent
- uid: DrawnUi.Draw.IDrawnBase.IsClippedToBounds
  commentId: P:DrawnUi.Draw.IDrawnBase.IsClippedToBounds
  parent: DrawnUi.Draw.IDrawnBase
  href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_IsClippedToBounds
  name: IsClippedToBounds
  nameWithType: IDrawnBase.IsClippedToBounds
  fullName: DrawnUi.Draw.IDrawnBase.IsClippedToBounds
- uid: DrawnUi.Draw.IDrawnBase.ClipEffects
  commentId: P:DrawnUi.Draw.IDrawnBase.ClipEffects
  parent: DrawnUi.Draw.IDrawnBase
  href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_ClipEffects
  name: ClipEffects
  nameWithType: IDrawnBase.ClipEffects
  fullName: DrawnUi.Draw.IDrawnBase.ClipEffects
- uid: DrawnUi.Draw.IDrawnBase.UpdateLocks
  commentId: P:DrawnUi.Draw.IDrawnBase.UpdateLocks
  parent: DrawnUi.Draw.IDrawnBase
  href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_UpdateLocks
  name: UpdateLocks
  nameWithType: IDrawnBase.UpdateLocks
  fullName: DrawnUi.Draw.IDrawnBase.UpdateLocks
- uid: DrawnUi.Draw.IDrawnBase.InvalidateByChild(DrawnUi.Draw.SkiaControl)
  commentId: M:DrawnUi.Draw.IDrawnBase.InvalidateByChild(DrawnUi.Draw.SkiaControl)
  parent: DrawnUi.Draw.IDrawnBase
  href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_InvalidateByChild_DrawnUi_Draw_SkiaControl_
  name: InvalidateByChild(SkiaControl)
  nameWithType: IDrawnBase.InvalidateByChild(SkiaControl)
  fullName: DrawnUi.Draw.IDrawnBase.InvalidateByChild(DrawnUi.Draw.SkiaControl)
  spec.csharp:
  - uid: DrawnUi.Draw.IDrawnBase.InvalidateByChild(DrawnUi.Draw.SkiaControl)
    name: InvalidateByChild
    href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_InvalidateByChild_DrawnUi_Draw_SkiaControl_
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.IDrawnBase.InvalidateByChild(DrawnUi.Draw.SkiaControl)
    name: InvalidateByChild
    href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_InvalidateByChild_DrawnUi_Draw_SkiaControl_
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
- uid: DrawnUi.Draw.IDrawnBase.UpdateByChild(DrawnUi.Draw.SkiaControl)
  commentId: M:DrawnUi.Draw.IDrawnBase.UpdateByChild(DrawnUi.Draw.SkiaControl)
  parent: DrawnUi.Draw.IDrawnBase
  href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_UpdateByChild_DrawnUi_Draw_SkiaControl_
  name: UpdateByChild(SkiaControl)
  nameWithType: IDrawnBase.UpdateByChild(SkiaControl)
  fullName: DrawnUi.Draw.IDrawnBase.UpdateByChild(DrawnUi.Draw.SkiaControl)
  spec.csharp:
  - uid: DrawnUi.Draw.IDrawnBase.UpdateByChild(DrawnUi.Draw.SkiaControl)
    name: UpdateByChild
    href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_UpdateByChild_DrawnUi_Draw_SkiaControl_
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.IDrawnBase.UpdateByChild(DrawnUi.Draw.SkiaControl)
    name: UpdateByChild
    href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_UpdateByChild_DrawnUi_Draw_SkiaControl_
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
- uid: DrawnUi.Draw.IDrawnBase.ShouldInvalidateByChildren
  commentId: P:DrawnUi.Draw.IDrawnBase.ShouldInvalidateByChildren
  parent: DrawnUi.Draw.IDrawnBase
  href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_ShouldInvalidateByChildren
  name: ShouldInvalidateByChildren
  nameWithType: IDrawnBase.ShouldInvalidateByChildren
  fullName: DrawnUi.Draw.IDrawnBase.ShouldInvalidateByChildren
- uid: DrawnUi.Draw.IDrawnBase.RenderingScale
  commentId: P:DrawnUi.Draw.IDrawnBase.RenderingScale
  parent: DrawnUi.Draw.IDrawnBase
  href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_RenderingScale
  name: RenderingScale
  nameWithType: IDrawnBase.RenderingScale
  fullName: DrawnUi.Draw.IDrawnBase.RenderingScale
- uid: DrawnUi.Draw.IDrawnBase.X
  commentId: P:DrawnUi.Draw.IDrawnBase.X
  parent: DrawnUi.Draw.IDrawnBase
  href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_X
  name: X
  nameWithType: IDrawnBase.X
  fullName: DrawnUi.Draw.IDrawnBase.X
- uid: DrawnUi.Draw.IDrawnBase.Y
  commentId: P:DrawnUi.Draw.IDrawnBase.Y
  parent: DrawnUi.Draw.IDrawnBase
  href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_Y
  name: Y
  nameWithType: IDrawnBase.Y
  fullName: DrawnUi.Draw.IDrawnBase.Y
- uid: DrawnUi.Draw.IDrawnBase.InvalidateViewport
  commentId: M:DrawnUi.Draw.IDrawnBase.InvalidateViewport
  parent: DrawnUi.Draw.IDrawnBase
  href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_InvalidateViewport
  name: InvalidateViewport()
  nameWithType: IDrawnBase.InvalidateViewport()
  fullName: DrawnUi.Draw.IDrawnBase.InvalidateViewport()
  spec.csharp:
  - uid: DrawnUi.Draw.IDrawnBase.InvalidateViewport
    name: InvalidateViewport
    href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_InvalidateViewport
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.IDrawnBase.InvalidateViewport
    name: InvalidateViewport
    href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_InvalidateViewport
  - name: (
  - name: )
- uid: DrawnUi.Draw.IDrawnBase.Repaint
  commentId: M:DrawnUi.Draw.IDrawnBase.Repaint
  parent: DrawnUi.Draw.IDrawnBase
  href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_Repaint
  name: Repaint()
  nameWithType: IDrawnBase.Repaint()
  fullName: DrawnUi.Draw.IDrawnBase.Repaint()
  spec.csharp:
  - uid: DrawnUi.Draw.IDrawnBase.Repaint
    name: Repaint
    href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_Repaint
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.IDrawnBase.Repaint
    name: Repaint
    href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_Repaint
  - name: (
  - name: )
- uid: DrawnUi.Draw.IDrawnBase.InvalidateViewsList
  commentId: M:DrawnUi.Draw.IDrawnBase.InvalidateViewsList
  parent: DrawnUi.Draw.IDrawnBase
  href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_InvalidateViewsList
  name: InvalidateViewsList()
  nameWithType: IDrawnBase.InvalidateViewsList()
  fullName: DrawnUi.Draw.IDrawnBase.InvalidateViewsList()
  spec.csharp:
  - uid: DrawnUi.Draw.IDrawnBase.InvalidateViewsList
    name: InvalidateViewsList
    href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_InvalidateViewsList
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.IDrawnBase.InvalidateViewsList
    name: InvalidateViewsList
    href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_InvalidateViewsList
  - name: (
  - name: )
- uid: DrawnUi.Draw.IDrawnBase.DisposeObject(System.IDisposable)
  commentId: M:DrawnUi.Draw.IDrawnBase.DisposeObject(System.IDisposable)
  parent: DrawnUi.Draw.IDrawnBase
  isExternal: true
  href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_DisposeObject_System_IDisposable_
  name: DisposeObject(IDisposable)
  nameWithType: IDrawnBase.DisposeObject(IDisposable)
  fullName: DrawnUi.Draw.IDrawnBase.DisposeObject(System.IDisposable)
  spec.csharp:
  - uid: DrawnUi.Draw.IDrawnBase.DisposeObject(System.IDisposable)
    name: DisposeObject
    href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_DisposeObject_System_IDisposable_
  - name: (
  - uid: System.IDisposable
    name: IDisposable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.idisposable
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.IDrawnBase.DisposeObject(System.IDisposable)
    name: DisposeObject
    href: DrawnUi.Draw.IDrawnBase.html#DrawnUi_Draw_IDrawnBase_DisposeObject_System_IDisposable_
  - name: (
  - uid: System.IDisposable
    name: IDisposable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.idisposable
  - name: )
- uid: System.IDisposable.Dispose
  commentId: M:System.IDisposable.Dispose
  parent: System.IDisposable
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.idisposable.dispose
  name: Dispose()
  nameWithType: IDisposable.Dispose()
  fullName: System.IDisposable.Dispose()
  spec.csharp:
  - uid: System.IDisposable.Dispose
    name: Dispose
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.idisposable.dispose
  - name: (
  - name: )
  spec.vb:
  - uid: System.IDisposable.Dispose
    name: Dispose
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.idisposable.dispose
  - name: (
  - name: )
- uid: DrawnUi.Draw.ICanBeUpdatedWithContext.BindingContext
  commentId: P:DrawnUi.Draw.ICanBeUpdatedWithContext.BindingContext
  parent: DrawnUi.Draw.ICanBeUpdatedWithContext
  href: DrawnUi.Draw.ICanBeUpdatedWithContext.html#DrawnUi_Draw_ICanBeUpdatedWithContext_BindingContext
  name: BindingContext
  nameWithType: ICanBeUpdatedWithContext.BindingContext
  fullName: DrawnUi.Draw.ICanBeUpdatedWithContext.BindingContext
- uid: DrawnUi.Draw.ICanBeUpdated.Update
  commentId: M:DrawnUi.Draw.ICanBeUpdated.Update
  parent: DrawnUi.Draw.ICanBeUpdated
  href: DrawnUi.Draw.ICanBeUpdated.html#DrawnUi_Draw_ICanBeUpdated_Update
  name: Update()
  nameWithType: ICanBeUpdated.Update()
  fullName: DrawnUi.Draw.ICanBeUpdated.Update()
  spec.csharp:
  - uid: DrawnUi.Draw.ICanBeUpdated.Update
    name: Update
    href: DrawnUi.Draw.ICanBeUpdated.html#DrawnUi_Draw_ICanBeUpdated_Update
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.ICanBeUpdated.Update
    name: Update
    href: DrawnUi.Draw.ICanBeUpdated.html#DrawnUi_Draw_ICanBeUpdated_Update
  - name: (
  - name: )
- uid: DrawnUi.Draw.IDrawnBase.DrawnUi.Draw.DrawnExtensions.GetVelocityRatioForChild(DrawnUi.Draw.ISkiaControl)
  commentId: M:DrawnUi.Draw.DrawnExtensions.GetVelocityRatioForChild(DrawnUi.Draw.IDrawnBase,DrawnUi.Draw.ISkiaControl)
  parent: DrawnUi.Draw.DrawnExtensions
  definition: DrawnUi.Draw.DrawnExtensions.GetVelocityRatioForChild(DrawnUi.Draw.IDrawnBase,DrawnUi.Draw.ISkiaControl)
  href: DrawnUi.Draw.DrawnExtensions.html#DrawnUi_Draw_DrawnExtensions_GetVelocityRatioForChild_DrawnUi_Draw_IDrawnBase_DrawnUi_Draw_ISkiaControl_
  name: GetVelocityRatioForChild(IDrawnBase, ISkiaControl)
  nameWithType: DrawnExtensions.GetVelocityRatioForChild(IDrawnBase, ISkiaControl)
  fullName: DrawnUi.Draw.DrawnExtensions.GetVelocityRatioForChild(DrawnUi.Draw.IDrawnBase, DrawnUi.Draw.ISkiaControl)
  spec.csharp:
  - uid: DrawnUi.Draw.DrawnExtensions.GetVelocityRatioForChild(DrawnUi.Draw.IDrawnBase,DrawnUi.Draw.ISkiaControl)
    name: GetVelocityRatioForChild
    href: DrawnUi.Draw.DrawnExtensions.html#DrawnUi_Draw_DrawnExtensions_GetVelocityRatioForChild_DrawnUi_Draw_IDrawnBase_DrawnUi_Draw_ISkiaControl_
  - name: (
  - uid: DrawnUi.Draw.IDrawnBase
    name: IDrawnBase
    href: DrawnUi.Draw.IDrawnBase.html
  - name: ','
  - name: " "
  - uid: DrawnUi.Draw.ISkiaControl
    name: ISkiaControl
    href: DrawnUi.Draw.ISkiaControl.html
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.DrawnExtensions.GetVelocityRatioForChild(DrawnUi.Draw.IDrawnBase,DrawnUi.Draw.ISkiaControl)
    name: GetVelocityRatioForChild
    href: DrawnUi.Draw.DrawnExtensions.html#DrawnUi_Draw_DrawnExtensions_GetVelocityRatioForChild_DrawnUi_Draw_IDrawnBase_DrawnUi_Draw_ISkiaControl_
  - name: (
  - uid: DrawnUi.Draw.IDrawnBase
    name: IDrawnBase
    href: DrawnUi.Draw.IDrawnBase.html
  - name: ','
  - name: " "
  - uid: DrawnUi.Draw.ISkiaControl
    name: ISkiaControl
    href: DrawnUi.Draw.ISkiaControl.html
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Draw.IDrawnBase
  commentId: T:DrawnUi.Draw.IDrawnBase
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.IDrawnBase.html
  name: IDrawnBase
  nameWithType: IDrawnBase
  fullName: DrawnUi.Draw.IDrawnBase
- uid: System.IDisposable
  commentId: T:System.IDisposable
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.idisposable
  name: IDisposable
  nameWithType: IDisposable
  fullName: System.IDisposable
- uid: DrawnUi.Draw.ICanBeUpdatedWithContext
  commentId: T:DrawnUi.Draw.ICanBeUpdatedWithContext
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ICanBeUpdatedWithContext.html
  name: ICanBeUpdatedWithContext
  nameWithType: ICanBeUpdatedWithContext
  fullName: DrawnUi.Draw.ICanBeUpdatedWithContext
- uid: DrawnUi.Draw.ICanBeUpdated
  commentId: T:DrawnUi.Draw.ICanBeUpdated
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ICanBeUpdated.html
  name: ICanBeUpdated
  nameWithType: ICanBeUpdated
  fullName: DrawnUi.Draw.ICanBeUpdated
- uid: DrawnUi.Draw.DrawnExtensions.GetVelocityRatioForChild(DrawnUi.Draw.IDrawnBase,DrawnUi.Draw.ISkiaControl)
  commentId: M:DrawnUi.Draw.DrawnExtensions.GetVelocityRatioForChild(DrawnUi.Draw.IDrawnBase,DrawnUi.Draw.ISkiaControl)
  href: DrawnUi.Draw.DrawnExtensions.html#DrawnUi_Draw_DrawnExtensions_GetVelocityRatioForChild_DrawnUi_Draw_IDrawnBase_DrawnUi_Draw_ISkiaControl_
  name: GetVelocityRatioForChild(IDrawnBase, ISkiaControl)
  nameWithType: DrawnExtensions.GetVelocityRatioForChild(IDrawnBase, ISkiaControl)
  fullName: DrawnUi.Draw.DrawnExtensions.GetVelocityRatioForChild(DrawnUi.Draw.IDrawnBase, DrawnUi.Draw.ISkiaControl)
  spec.csharp:
  - uid: DrawnUi.Draw.DrawnExtensions.GetVelocityRatioForChild(DrawnUi.Draw.IDrawnBase,DrawnUi.Draw.ISkiaControl)
    name: GetVelocityRatioForChild
    href: DrawnUi.Draw.DrawnExtensions.html#DrawnUi_Draw_DrawnExtensions_GetVelocityRatioForChild_DrawnUi_Draw_IDrawnBase_DrawnUi_Draw_ISkiaControl_
  - name: (
  - uid: DrawnUi.Draw.IDrawnBase
    name: IDrawnBase
    href: DrawnUi.Draw.IDrawnBase.html
  - name: ','
  - name: " "
  - uid: DrawnUi.Draw.ISkiaControl
    name: ISkiaControl
    href: DrawnUi.Draw.ISkiaControl.html
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.DrawnExtensions.GetVelocityRatioForChild(DrawnUi.Draw.IDrawnBase,DrawnUi.Draw.ISkiaControl)
    name: GetVelocityRatioForChild
    href: DrawnUi.Draw.DrawnExtensions.html#DrawnUi_Draw_DrawnExtensions_GetVelocityRatioForChild_DrawnUi_Draw_IDrawnBase_DrawnUi_Draw_ISkiaControl_
  - name: (
  - uid: DrawnUi.Draw.IDrawnBase
    name: IDrawnBase
    href: DrawnUi.Draw.IDrawnBase.html
  - name: ','
  - name: " "
  - uid: DrawnUi.Draw.ISkiaControl
    name: ISkiaControl
    href: DrawnUi.Draw.ISkiaControl.html
  - name: )
- uid: DrawnUi.Draw.DrawnExtensions
  commentId: T:DrawnUi.Draw.DrawnExtensions
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.DrawnExtensions.html
  name: DrawnExtensions
  nameWithType: DrawnExtensions
  fullName: DrawnUi.Draw.DrawnExtensions
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.IRefreshIndicator.SetDragRatio*
  commentId: Overload:DrawnUi.Draw.IRefreshIndicator.SetDragRatio
  href: DrawnUi.Draw.IRefreshIndicator.html#DrawnUi_Draw_IRefreshIndicator_SetDragRatio_System_Single_System_Single_System_Double_
  name: SetDragRatio
  nameWithType: IRefreshIndicator.SetDragRatio
  fullName: DrawnUi.Draw.IRefreshIndicator.SetDragRatio
- uid: System.Single
  commentId: T:System.Single
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.single
  name: float
  nameWithType: float
  fullName: float
  nameWithType.vb: Single
  fullName.vb: Single
  name.vb: Single
- uid: System.Double
  commentId: T:System.Double
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.double
  name: double
  nameWithType: double
  fullName: double
  nameWithType.vb: Double
  fullName.vb: Double
  name.vb: Double
