### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.SkiaShape.ShapePaintArguments
  commentId: T:DrawnUi.Draw.SkiaShape.ShapePaintArguments
  id: SkiaShape.ShapePaintArguments
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.SkiaShape.ShapePaintArguments.StrokeAwareChildrenSize
  - DrawnUi.Draw.SkiaShape.ShapePaintArguments.StrokeAwareClipSize
  - DrawnUi.Draw.SkiaShape.ShapePaintArguments.StrokeAwareSize
  langs:
  - csharp
  - vb
  name: SkiaShape.ShapePaintArguments
  nameWithType: SkiaShape.ShapePaintArguments
  fullName: DrawnUi.Draw.SkiaShape.ShapePaintArguments
  type: Struct
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/SkiaShape.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ShapePaintArguments
    path: ../src/Maui/DrawnUi/Draw/SkiaShape.cs
    startLine: 347
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public struct SkiaShape.ShapePaintArguments
    content.vb: Public Structure SkiaShape.ShapePaintArguments
  inheritedMembers:
  - System.ValueType.Equals(System.Object)
  - System.ValueType.GetHashCode
  - System.ValueType.ToString
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetType
  - System.Object.ReferenceEquals(System.Object,System.Object)
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.SkiaShape.ShapePaintArguments.StrokeAwareSize
  commentId: P:DrawnUi.Draw.SkiaShape.ShapePaintArguments.StrokeAwareSize
  id: StrokeAwareSize
  parent: DrawnUi.Draw.SkiaShape.ShapePaintArguments
  langs:
  - csharp
  - vb
  name: StrokeAwareSize
  nameWithType: SkiaShape.ShapePaintArguments.StrokeAwareSize
  fullName: DrawnUi.Draw.SkiaShape.ShapePaintArguments.StrokeAwareSize
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/SkiaShape.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: StrokeAwareSize
    path: ../src/Maui/DrawnUi/Draw/SkiaShape.cs
    startLine: 349
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public SKRect StrokeAwareSize { readonly get; set; }
    parameters: []
    return:
      type: SkiaSharp.SKRect
    content.vb: Public Property StrokeAwareSize As SKRect
  overload: DrawnUi.Draw.SkiaShape.ShapePaintArguments.StrokeAwareSize*
- uid: DrawnUi.Draw.SkiaShape.ShapePaintArguments.StrokeAwareChildrenSize
  commentId: P:DrawnUi.Draw.SkiaShape.ShapePaintArguments.StrokeAwareChildrenSize
  id: StrokeAwareChildrenSize
  parent: DrawnUi.Draw.SkiaShape.ShapePaintArguments
  langs:
  - csharp
  - vb
  name: StrokeAwareChildrenSize
  nameWithType: SkiaShape.ShapePaintArguments.StrokeAwareChildrenSize
  fullName: DrawnUi.Draw.SkiaShape.ShapePaintArguments.StrokeAwareChildrenSize
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/SkiaShape.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: StrokeAwareChildrenSize
    path: ../src/Maui/DrawnUi/Draw/SkiaShape.cs
    startLine: 350
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public SKRect StrokeAwareChildrenSize { readonly get; set; }
    parameters: []
    return:
      type: SkiaSharp.SKRect
    content.vb: Public Property StrokeAwareChildrenSize As SKRect
  overload: DrawnUi.Draw.SkiaShape.ShapePaintArguments.StrokeAwareChildrenSize*
- uid: DrawnUi.Draw.SkiaShape.ShapePaintArguments.StrokeAwareClipSize
  commentId: P:DrawnUi.Draw.SkiaShape.ShapePaintArguments.StrokeAwareClipSize
  id: StrokeAwareClipSize
  parent: DrawnUi.Draw.SkiaShape.ShapePaintArguments
  langs:
  - csharp
  - vb
  name: StrokeAwareClipSize
  nameWithType: SkiaShape.ShapePaintArguments.StrokeAwareClipSize
  fullName: DrawnUi.Draw.SkiaShape.ShapePaintArguments.StrokeAwareClipSize
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/SkiaShape.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: StrokeAwareClipSize
    path: ../src/Maui/DrawnUi/Draw/SkiaShape.cs
    startLine: 351
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public SKRect StrokeAwareClipSize { readonly get; set; }
    parameters: []
    return:
      type: SkiaSharp.SKRect
    content.vb: Public Property StrokeAwareClipSize As SKRect
  overload: DrawnUi.Draw.SkiaShape.ShapePaintArguments.StrokeAwareClipSize*
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.ValueType.Equals(System.Object)
  commentId: M:System.ValueType.Equals(System.Object)
  parent: System.ValueType
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype.equals
  name: Equals(object)
  nameWithType: ValueType.Equals(object)
  fullName: System.ValueType.Equals(object)
  nameWithType.vb: ValueType.Equals(Object)
  fullName.vb: System.ValueType.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.ValueType.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.equals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.ValueType.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.equals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.ValueType.GetHashCode
  commentId: M:System.ValueType.GetHashCode
  parent: System.ValueType
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype.gethashcode
  name: GetHashCode()
  nameWithType: ValueType.GetHashCode()
  fullName: System.ValueType.GetHashCode()
  spec.csharp:
  - uid: System.ValueType.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.ValueType.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.gethashcode
  - name: (
  - name: )
- uid: System.ValueType.ToString
  commentId: M:System.ValueType.ToString
  parent: System.ValueType
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype.tostring
  name: ToString()
  nameWithType: ValueType.ToString()
  fullName: System.ValueType.ToString()
  spec.csharp:
  - uid: System.ValueType.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.ValueType.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.tostring
  - name: (
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.ValueType
  commentId: T:System.ValueType
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype
  name: ValueType
  nameWithType: ValueType
  fullName: System.ValueType
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.SkiaShape.ShapePaintArguments.StrokeAwareSize*
  commentId: Overload:DrawnUi.Draw.SkiaShape.ShapePaintArguments.StrokeAwareSize
  href: DrawnUi.Draw.SkiaShape.ShapePaintArguments.html#DrawnUi_Draw_SkiaShape_ShapePaintArguments_StrokeAwareSize
  name: StrokeAwareSize
  nameWithType: SkiaShape.ShapePaintArguments.StrokeAwareSize
  fullName: DrawnUi.Draw.SkiaShape.ShapePaintArguments.StrokeAwareSize
- uid: SkiaSharp.SKRect
  commentId: T:SkiaSharp.SKRect
  parent: SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.skrect
  name: SKRect
  nameWithType: SKRect
  fullName: SkiaSharp.SKRect
- uid: SkiaSharp
  commentId: N:SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp
  name: SkiaSharp
  nameWithType: SkiaSharp
  fullName: SkiaSharp
- uid: DrawnUi.Draw.SkiaShape.ShapePaintArguments.StrokeAwareChildrenSize*
  commentId: Overload:DrawnUi.Draw.SkiaShape.ShapePaintArguments.StrokeAwareChildrenSize
  href: DrawnUi.Draw.SkiaShape.ShapePaintArguments.html#DrawnUi_Draw_SkiaShape_ShapePaintArguments_StrokeAwareChildrenSize
  name: StrokeAwareChildrenSize
  nameWithType: SkiaShape.ShapePaintArguments.StrokeAwareChildrenSize
  fullName: DrawnUi.Draw.SkiaShape.ShapePaintArguments.StrokeAwareChildrenSize
- uid: DrawnUi.Draw.SkiaShape.ShapePaintArguments.StrokeAwareClipSize*
  commentId: Overload:DrawnUi.Draw.SkiaShape.ShapePaintArguments.StrokeAwareClipSize
  href: DrawnUi.Draw.SkiaShape.ShapePaintArguments.html#DrawnUi_Draw_SkiaShape_ShapePaintArguments_StrokeAwareClipSize
  name: StrokeAwareClipSize
  nameWithType: SkiaShape.ShapePaintArguments.StrokeAwareClipSize
  fullName: DrawnUi.Draw.SkiaShape.ShapePaintArguments.StrokeAwareClipSize
