### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.IDrawnTextSpan
  commentId: T:DrawnUi.Draw.IDrawnTextSpan
  id: IDrawnTextSpan
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.IDrawnTextSpan.Measure(System.Single,System.Single,System.Single)
  - DrawnUi.Draw.IDrawnTextSpan.Render(DrawnUi.Draw.DrawingContext)
  - DrawnUi.Draw.IDrawnTextSpan.VerticalAlignement
  langs:
  - csharp
  - vb
  name: IDrawnTextSpan
  nameWithType: IDrawnTextSpan
  fullName: DrawnUi.Draw.IDrawnTextSpan
  type: Interface
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/IDrawnTextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IDrawnTextSpan
    path: ../src/Maui/DrawnUi/Draw/Text/IDrawnTextSpan.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public interface IDrawnTextSpan
    content.vb: Public Interface IDrawnTextSpan
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.IDrawnTextSpan.Render(DrawnUi.Draw.DrawingContext)
  commentId: M:DrawnUi.Draw.IDrawnTextSpan.Render(DrawnUi.Draw.DrawingContext)
  id: Render(DrawnUi.Draw.DrawingContext)
  parent: DrawnUi.Draw.IDrawnTextSpan
  langs:
  - csharp
  - vb
  name: Render(DrawingContext)
  nameWithType: IDrawnTextSpan.Render(DrawingContext)
  fullName: DrawnUi.Draw.IDrawnTextSpan.Render(DrawnUi.Draw.DrawingContext)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/IDrawnTextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Render
    path: ../src/Maui/DrawnUi/Draw/Text/IDrawnTextSpan.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: void Render(DrawingContext ctx)
    parameters:
    - id: ctx
      type: DrawnUi.Draw.DrawingContext
    content.vb: Sub Render(ctx As DrawingContext)
  overload: DrawnUi.Draw.IDrawnTextSpan.Render*
- uid: DrawnUi.Draw.IDrawnTextSpan.Measure(System.Single,System.Single,System.Single)
  commentId: M:DrawnUi.Draw.IDrawnTextSpan.Measure(System.Single,System.Single,System.Single)
  id: Measure(System.Single,System.Single,System.Single)
  parent: DrawnUi.Draw.IDrawnTextSpan
  langs:
  - csharp
  - vb
  name: Measure(float, float, float)
  nameWithType: IDrawnTextSpan.Measure(float, float, float)
  fullName: DrawnUi.Draw.IDrawnTextSpan.Measure(float, float, float)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/IDrawnTextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Measure
    path: ../src/Maui/DrawnUi/Draw/Text/IDrawnTextSpan.cs
    startLine: 6
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: ScaledSize Measure(float maxWidth, float maxHeight, float scale)
    parameters:
    - id: maxWidth
      type: System.Single
    - id: maxHeight
      type: System.Single
    - id: scale
      type: System.Single
    return:
      type: DrawnUi.Draw.ScaledSize
    content.vb: Function Measure(maxWidth As Single, maxHeight As Single, scale As Single) As ScaledSize
  overload: DrawnUi.Draw.IDrawnTextSpan.Measure*
  nameWithType.vb: IDrawnTextSpan.Measure(Single, Single, Single)
  fullName.vb: DrawnUi.Draw.IDrawnTextSpan.Measure(Single, Single, Single)
  name.vb: Measure(Single, Single, Single)
- uid: DrawnUi.Draw.IDrawnTextSpan.VerticalAlignement
  commentId: P:DrawnUi.Draw.IDrawnTextSpan.VerticalAlignement
  id: VerticalAlignement
  parent: DrawnUi.Draw.IDrawnTextSpan
  langs:
  - csharp
  - vb
  name: VerticalAlignement
  nameWithType: IDrawnTextSpan.VerticalAlignement
  fullName: DrawnUi.Draw.IDrawnTextSpan.VerticalAlignement
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/IDrawnTextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: VerticalAlignement
    path: ../src/Maui/DrawnUi/Draw/Text/IDrawnTextSpan.cs
    startLine: 8
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: DrawImageAlignment VerticalAlignement { get; }
    parameters: []
    return:
      type: DrawnUi.Draw.DrawImageAlignment
    content.vb: ReadOnly Property VerticalAlignement As DrawImageAlignment
  overload: DrawnUi.Draw.IDrawnTextSpan.VerticalAlignement*
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.IDrawnTextSpan.Render*
  commentId: Overload:DrawnUi.Draw.IDrawnTextSpan.Render
  href: DrawnUi.Draw.IDrawnTextSpan.html#DrawnUi_Draw_IDrawnTextSpan_Render_DrawnUi_Draw_DrawingContext_
  name: Render
  nameWithType: IDrawnTextSpan.Render
  fullName: DrawnUi.Draw.IDrawnTextSpan.Render
- uid: DrawnUi.Draw.DrawingContext
  commentId: T:DrawnUi.Draw.DrawingContext
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.DrawingContext.html
  name: DrawingContext
  nameWithType: DrawingContext
  fullName: DrawnUi.Draw.DrawingContext
- uid: DrawnUi.Draw.IDrawnTextSpan.Measure*
  commentId: Overload:DrawnUi.Draw.IDrawnTextSpan.Measure
  href: DrawnUi.Draw.IDrawnTextSpan.html#DrawnUi_Draw_IDrawnTextSpan_Measure_System_Single_System_Single_System_Single_
  name: Measure
  nameWithType: IDrawnTextSpan.Measure
  fullName: DrawnUi.Draw.IDrawnTextSpan.Measure
- uid: System.Single
  commentId: T:System.Single
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.single
  name: float
  nameWithType: float
  fullName: float
  nameWithType.vb: Single
  fullName.vb: Single
  name.vb: Single
- uid: DrawnUi.Draw.ScaledSize
  commentId: T:DrawnUi.Draw.ScaledSize
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ScaledSize.html
  name: ScaledSize
  nameWithType: ScaledSize
  fullName: DrawnUi.Draw.ScaledSize
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Draw.IDrawnTextSpan.VerticalAlignement*
  commentId: Overload:DrawnUi.Draw.IDrawnTextSpan.VerticalAlignement
  href: DrawnUi.Draw.IDrawnTextSpan.html#DrawnUi_Draw_IDrawnTextSpan_VerticalAlignement
  name: VerticalAlignement
  nameWithType: IDrawnTextSpan.VerticalAlignement
  fullName: DrawnUi.Draw.IDrawnTextSpan.VerticalAlignement
- uid: DrawnUi.Draw.DrawImageAlignment
  commentId: T:DrawnUi.Draw.DrawImageAlignment
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.DrawImageAlignment.html
  name: DrawImageAlignment
  nameWithType: DrawImageAlignment
  fullName: DrawnUi.Draw.DrawImageAlignment
