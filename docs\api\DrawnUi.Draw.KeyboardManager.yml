### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.KeyboardManager
  commentId: T:DrawnUi.Draw.KeyboardManager
  id: KeyboardManager
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.KeyboardManager.IsAltPressed
  - DrawnUi.Draw.KeyboardManager.IsControlPressed
  - DrawnUi.Draw.KeyboardManager.IsShiftPressed
  - DrawnUi.Draw.KeyboardManager.KeyDown
  - DrawnUi.Draw.KeyboardManager.KeyUp
  - DrawnUi.Draw.KeyboardManager.KeyboardPressed(DrawnUi.Draw.MauiKey)
  - DrawnUi.Draw.KeyboardManager.KeyboardReleased(DrawnUi.Draw.MauiKey)
  langs:
  - csharp
  - vb
  name: KeyboardManager
  nameWithType: KeyboardManager
  fullName: DrawnUi.Draw.KeyboardManager
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Keyboard/KeyboardManager.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: KeyboardManager
    path: ../src/Maui/DrawnUi/Features/Keyboard/KeyboardManager.cs
    startLine: 5
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public class KeyboardManager
    content.vb: Public Class KeyboardManager
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.KeyboardManager.KeyDown
  commentId: E:DrawnUi.Draw.KeyboardManager.KeyDown
  id: KeyDown
  parent: DrawnUi.Draw.KeyboardManager
  langs:
  - csharp
  - vb
  name: KeyDown
  nameWithType: KeyboardManager.KeyDown
  fullName: DrawnUi.Draw.KeyboardManager.KeyDown
  type: Event
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Keyboard/KeyboardManager.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: KeyDown
    path: ../src/Maui/DrawnUi/Features/Keyboard/KeyboardManager.cs
    startLine: 8
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static event EventHandler<MauiKey> KeyDown
    return:
      type: System.EventHandler{DrawnUi.Draw.MauiKey}
    content.vb: Public Shared Event KeyDown As EventHandler(Of MauiKey)
- uid: DrawnUi.Draw.KeyboardManager.KeyUp
  commentId: E:DrawnUi.Draw.KeyboardManager.KeyUp
  id: KeyUp
  parent: DrawnUi.Draw.KeyboardManager
  langs:
  - csharp
  - vb
  name: KeyUp
  nameWithType: KeyboardManager.KeyUp
  fullName: DrawnUi.Draw.KeyboardManager.KeyUp
  type: Event
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Keyboard/KeyboardManager.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: KeyUp
    path: ../src/Maui/DrawnUi/Features/Keyboard/KeyboardManager.cs
    startLine: 10
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static event EventHandler<MauiKey> KeyUp
    return:
      type: System.EventHandler{DrawnUi.Draw.MauiKey}
    content.vb: Public Shared Event KeyUp As EventHandler(Of MauiKey)
- uid: DrawnUi.Draw.KeyboardManager.KeyboardPressed(DrawnUi.Draw.MauiKey)
  commentId: M:DrawnUi.Draw.KeyboardManager.KeyboardPressed(DrawnUi.Draw.MauiKey)
  id: KeyboardPressed(DrawnUi.Draw.MauiKey)
  parent: DrawnUi.Draw.KeyboardManager
  langs:
  - csharp
  - vb
  name: KeyboardPressed(MauiKey)
  nameWithType: KeyboardManager.KeyboardPressed(MauiKey)
  fullName: DrawnUi.Draw.KeyboardManager.KeyboardPressed(DrawnUi.Draw.MauiKey)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Keyboard/KeyboardManager.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: KeyboardPressed
    path: ../src/Maui/DrawnUi/Features/Keyboard/KeyboardManager.cs
    startLine: 12
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static void KeyboardPressed(MauiKey key)
    parameters:
    - id: key
      type: DrawnUi.Draw.MauiKey
    content.vb: Public Shared Sub KeyboardPressed(key As MauiKey)
  overload: DrawnUi.Draw.KeyboardManager.KeyboardPressed*
- uid: DrawnUi.Draw.KeyboardManager.KeyboardReleased(DrawnUi.Draw.MauiKey)
  commentId: M:DrawnUi.Draw.KeyboardManager.KeyboardReleased(DrawnUi.Draw.MauiKey)
  id: KeyboardReleased(DrawnUi.Draw.MauiKey)
  parent: DrawnUi.Draw.KeyboardManager
  langs:
  - csharp
  - vb
  name: KeyboardReleased(MauiKey)
  nameWithType: KeyboardManager.KeyboardReleased(MauiKey)
  fullName: DrawnUi.Draw.KeyboardManager.KeyboardReleased(DrawnUi.Draw.MauiKey)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Keyboard/KeyboardManager.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: KeyboardReleased
    path: ../src/Maui/DrawnUi/Features/Keyboard/KeyboardManager.cs
    startLine: 21
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static void KeyboardReleased(MauiKey key)
    parameters:
    - id: key
      type: DrawnUi.Draw.MauiKey
    content.vb: Public Shared Sub KeyboardReleased(key As MauiKey)
  overload: DrawnUi.Draw.KeyboardManager.KeyboardReleased*
- uid: DrawnUi.Draw.KeyboardManager.IsShiftPressed
  commentId: P:DrawnUi.Draw.KeyboardManager.IsShiftPressed
  id: IsShiftPressed
  parent: DrawnUi.Draw.KeyboardManager
  langs:
  - csharp
  - vb
  name: IsShiftPressed
  nameWithType: KeyboardManager.IsShiftPressed
  fullName: DrawnUi.Draw.KeyboardManager.IsShiftPressed
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Keyboard/KeyboardManager.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IsShiftPressed
    path: ../src/Maui/DrawnUi/Features/Keyboard/KeyboardManager.cs
    startLine: 28
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static bool IsShiftPressed { get; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Shared ReadOnly Property IsShiftPressed As Boolean
  overload: DrawnUi.Draw.KeyboardManager.IsShiftPressed*
- uid: DrawnUi.Draw.KeyboardManager.IsAltPressed
  commentId: P:DrawnUi.Draw.KeyboardManager.IsAltPressed
  id: IsAltPressed
  parent: DrawnUi.Draw.KeyboardManager
  langs:
  - csharp
  - vb
  name: IsAltPressed
  nameWithType: KeyboardManager.IsAltPressed
  fullName: DrawnUi.Draw.KeyboardManager.IsAltPressed
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Keyboard/KeyboardManager.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IsAltPressed
    path: ../src/Maui/DrawnUi/Features/Keyboard/KeyboardManager.cs
    startLine: 36
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static bool IsAltPressed { get; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Shared ReadOnly Property IsAltPressed As Boolean
  overload: DrawnUi.Draw.KeyboardManager.IsAltPressed*
- uid: DrawnUi.Draw.KeyboardManager.IsControlPressed
  commentId: P:DrawnUi.Draw.KeyboardManager.IsControlPressed
  id: IsControlPressed
  parent: DrawnUi.Draw.KeyboardManager
  langs:
  - csharp
  - vb
  name: IsControlPressed
  nameWithType: KeyboardManager.IsControlPressed
  fullName: DrawnUi.Draw.KeyboardManager.IsControlPressed
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Keyboard/KeyboardManager.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IsControlPressed
    path: ../src/Maui/DrawnUi/Features/Keyboard/KeyboardManager.cs
    startLine: 44
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static bool IsControlPressed { get; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Shared ReadOnly Property IsControlPressed As Boolean
  overload: DrawnUi.Draw.KeyboardManager.IsControlPressed*
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: System.EventHandler{DrawnUi.Draw.MauiKey}
  commentId: T:System.EventHandler{DrawnUi.Draw.MauiKey}
  parent: System
  definition: System.EventHandler`1
  href: https://learn.microsoft.com/dotnet/api/system.eventhandler-1
  name: EventHandler<MauiKey>
  nameWithType: EventHandler<MauiKey>
  fullName: System.EventHandler<DrawnUi.Draw.MauiKey>
  nameWithType.vb: EventHandler(Of MauiKey)
  fullName.vb: System.EventHandler(Of DrawnUi.Draw.MauiKey)
  name.vb: EventHandler(Of MauiKey)
  spec.csharp:
  - uid: System.EventHandler`1
    name: EventHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.eventhandler-1
  - name: <
  - uid: DrawnUi.Draw.MauiKey
    name: MauiKey
    href: DrawnUi.Draw.MauiKey.html
  - name: '>'
  spec.vb:
  - uid: System.EventHandler`1
    name: EventHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.eventhandler-1
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Draw.MauiKey
    name: MauiKey
    href: DrawnUi.Draw.MauiKey.html
  - name: )
- uid: System.EventHandler`1
  commentId: T:System.EventHandler`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.eventhandler-1
  name: EventHandler<TEventArgs>
  nameWithType: EventHandler<TEventArgs>
  fullName: System.EventHandler<TEventArgs>
  nameWithType.vb: EventHandler(Of TEventArgs)
  fullName.vb: System.EventHandler(Of TEventArgs)
  name.vb: EventHandler(Of TEventArgs)
  spec.csharp:
  - uid: System.EventHandler`1
    name: EventHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.eventhandler-1
  - name: <
  - name: TEventArgs
  - name: '>'
  spec.vb:
  - uid: System.EventHandler`1
    name: EventHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.eventhandler-1
  - name: (
  - name: Of
  - name: " "
  - name: TEventArgs
  - name: )
- uid: DrawnUi.Draw.KeyboardManager.KeyboardPressed*
  commentId: Overload:DrawnUi.Draw.KeyboardManager.KeyboardPressed
  href: DrawnUi.Draw.KeyboardManager.html#DrawnUi_Draw_KeyboardManager_KeyboardPressed_DrawnUi_Draw_MauiKey_
  name: KeyboardPressed
  nameWithType: KeyboardManager.KeyboardPressed
  fullName: DrawnUi.Draw.KeyboardManager.KeyboardPressed
- uid: DrawnUi.Draw.MauiKey
  commentId: T:DrawnUi.Draw.MauiKey
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.MauiKey.html
  name: MauiKey
  nameWithType: MauiKey
  fullName: DrawnUi.Draw.MauiKey
- uid: DrawnUi.Draw.KeyboardManager.KeyboardReleased*
  commentId: Overload:DrawnUi.Draw.KeyboardManager.KeyboardReleased
  href: DrawnUi.Draw.KeyboardManager.html#DrawnUi_Draw_KeyboardManager_KeyboardReleased_DrawnUi_Draw_MauiKey_
  name: KeyboardReleased
  nameWithType: KeyboardManager.KeyboardReleased
  fullName: DrawnUi.Draw.KeyboardManager.KeyboardReleased
- uid: DrawnUi.Draw.KeyboardManager.IsShiftPressed*
  commentId: Overload:DrawnUi.Draw.KeyboardManager.IsShiftPressed
  href: DrawnUi.Draw.KeyboardManager.html#DrawnUi_Draw_KeyboardManager_IsShiftPressed
  name: IsShiftPressed
  nameWithType: KeyboardManager.IsShiftPressed
  fullName: DrawnUi.Draw.KeyboardManager.IsShiftPressed
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: DrawnUi.Draw.KeyboardManager.IsAltPressed*
  commentId: Overload:DrawnUi.Draw.KeyboardManager.IsAltPressed
  href: DrawnUi.Draw.KeyboardManager.html#DrawnUi_Draw_KeyboardManager_IsAltPressed
  name: IsAltPressed
  nameWithType: KeyboardManager.IsAltPressed
  fullName: DrawnUi.Draw.KeyboardManager.IsAltPressed
- uid: DrawnUi.Draw.KeyboardManager.IsControlPressed*
  commentId: Overload:DrawnUi.Draw.KeyboardManager.IsControlPressed
  href: DrawnUi.Draw.KeyboardManager.html#DrawnUi_Draw_KeyboardManager_IsControlPressed
  name: IsControlPressed
  nameWithType: KeyboardManager.IsControlPressed
  fullName: DrawnUi.Draw.KeyboardManager.IsControlPressed
