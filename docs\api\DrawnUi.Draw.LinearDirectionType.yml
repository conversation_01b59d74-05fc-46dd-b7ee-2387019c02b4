### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.LinearDirectionType
  commentId: T:DrawnUi.Draw.LinearDirectionType
  id: LinearDirectionType
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.LinearDirectionType.Backward
  - DrawnUi.Draw.LinearDirectionType.Forward
  - DrawnUi.Draw.LinearDirectionType.None
  langs:
  - csharp
  - vb
  name: LinearDirectionType
  nameWithType: LinearDirectionType
  fullName: DrawnUi.Draw.LinearDirectionType
  type: Enum
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/PointedDirectionType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: LinearDirectionType
    path: ../src/Shared/Draw/Internals/Enums/PointedDirectionType.cs
    startLine: 13
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public enum LinearDirectionType
    content.vb: Public Enum LinearDirectionType
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.LinearDirectionType.None
  commentId: F:DrawnUi.Draw.LinearDirectionType.None
  id: None
  parent: DrawnUi.Draw.LinearDirectionType
  langs:
  - csharp
  - vb
  name: None
  nameWithType: LinearDirectionType.None
  fullName: DrawnUi.Draw.LinearDirectionType.None
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/PointedDirectionType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: None
    path: ../src/Shared/Draw/Internals/Enums/PointedDirectionType.cs
    startLine: 15
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: None = 0
    return:
      type: DrawnUi.Draw.LinearDirectionType
- uid: DrawnUi.Draw.LinearDirectionType.Forward
  commentId: F:DrawnUi.Draw.LinearDirectionType.Forward
  id: Forward
  parent: DrawnUi.Draw.LinearDirectionType
  langs:
  - csharp
  - vb
  name: Forward
  nameWithType: LinearDirectionType.Forward
  fullName: DrawnUi.Draw.LinearDirectionType.Forward
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/PointedDirectionType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Forward
    path: ../src/Shared/Draw/Internals/Enums/PointedDirectionType.cs
    startLine: 20
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Or down
  example: []
  syntax:
    content: Forward = 1
    return:
      type: DrawnUi.Draw.LinearDirectionType
- uid: DrawnUi.Draw.LinearDirectionType.Backward
  commentId: F:DrawnUi.Draw.LinearDirectionType.Backward
  id: Backward
  parent: DrawnUi.Draw.LinearDirectionType
  langs:
  - csharp
  - vb
  name: Backward
  nameWithType: LinearDirectionType.Backward
  fullName: DrawnUi.Draw.LinearDirectionType.Backward
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/PointedDirectionType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Backward
    path: ../src/Shared/Draw/Internals/Enums/PointedDirectionType.cs
    startLine: 25
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Or up
  example: []
  syntax:
    content: Backward = 2
    return:
      type: DrawnUi.Draw.LinearDirectionType
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.LinearDirectionType
  commentId: T:DrawnUi.Draw.LinearDirectionType
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.LinearDirectionType.html
  name: LinearDirectionType
  nameWithType: LinearDirectionType
  fullName: DrawnUi.Draw.LinearDirectionType
