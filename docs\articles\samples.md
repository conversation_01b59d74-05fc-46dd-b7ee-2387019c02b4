# Samples

Explore real-world examples and code snippets to help you get started and master DrawnUi.Maui.

## Example Apps
- **Sandbox**: A playground app demonstrating most controls and features. See the `samples/Sandbox` folder in the repository.
- **TestCalendar**: Example of a custom calendar UI built with DrawnUi.Maui.

## Code Snippets
- [Game UI & Interactive Games](advanced/game-ui.md)
- [Gradients](advanced/gradients.md)
- [SkiaScroll & Virtualization](advanced/skiascroll.md)
- [Gestures & Touch Input](advanced/gestures.md)

## How to Run the Samples
1. Clone the repository.
2. Open the solution in Visual Studio or your preferred IDE.
3. Set the desired sample project (e.g., Sandbox) as the startup project.
4. Run and explore the code.

## Contributing Samples
Have a cool UI or feature? Submit a PR or open an issue to share your sample with the community!
