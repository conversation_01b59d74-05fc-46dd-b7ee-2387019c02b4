<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <title>Class LayoutStructure | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width">
      <meta name="title" content="Class LayoutStructure | DrawnUi Documentation ">
    
    
      <link rel="shortcut icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../styles/docfx.vendor.min.css">
      <link rel="stylesheet" href="../styles/docfx.css">
      <link rel="stylesheet" href="../styles/main.css">
      <meta property="docfx:navrel" content="../toc.html">
      <meta property="docfx:tocrel" content="toc.html">
    
    <meta property="docfx:rel" content="../">
    
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first=First data-prev=Previous data-next=Next data-last=Last></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="DrawnUi.Draw.LayoutStructure">



  <h1 id="DrawnUi_Draw_LayoutStructure" data-uid="DrawnUi.Draw.LayoutStructure" class="text-break">Class LayoutStructure</h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></div>
    <div class="level1"><a class="xref" href="DrawnUi.Draw.DynamicGrid-1.html">DynamicGrid</a>&lt;<a class="xref" href="DrawnUi.Draw.ControlInStack.html">ControlInStack</a>&gt;</div>
    <div class="level2"><span class="xref">LayoutStructure</span></div>
  </div>
  <div class="inheritedMembers">
    <h5>Inherited Members</h5>
    <div>
      <a class="xref" href="DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_grid">DynamicGrid&lt;ControlInStack&gt;.grid</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_MaxRows">DynamicGrid&lt;ControlInStack&gt;.MaxRows</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_MaxColumns">DynamicGrid&lt;ControlInStack&gt;.MaxColumns</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_Add__0_System_Int32_System_Int32_">DynamicGrid&lt;ControlInStack&gt;.Add(ControlInStack, int, int)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_Get_System_Int32_System_Int32_">DynamicGrid&lt;ControlInStack&gt;.Get(int, int)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_GetRow_System_Int32_">DynamicGrid&lt;ControlInStack&gt;.GetRow(int)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_GetColumn_System_Int32_">DynamicGrid&lt;ControlInStack&gt;.GetColumn(int)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_Clear">DynamicGrid&lt;ControlInStack&gt;.Clear()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_GetChildren">DynamicGrid&lt;ControlInStack&gt;.GetChildren()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_FindChildAtIndex_System_Int32_">DynamicGrid&lt;ControlInStack&gt;.FindChildAtIndex(int)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_GetChildrenAsSpans">DynamicGrid&lt;ControlInStack&gt;.GetChildrenAsSpans()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_GetCount">DynamicGrid&lt;ControlInStack&gt;.GetCount()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_GetColumnCountForRow_System_Int32_">DynamicGrid&lt;ControlInStack&gt;.GetColumnCountForRow(int)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)">object.Equals(object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)">object.Equals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gethashcode">object.GetHashCode()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gettype">object.GetType()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone">object.MemberwiseClone()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.referenceequals">object.ReferenceEquals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.tostring">object.ToString()</a>
    </div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="DrawnUi.html">DrawnUi</a>.<a class="xref" href="DrawnUi.Draw.html">Draw</a></h6>
  <h6><strong>Assembly</strong>: DrawnUi.Maui.dll</h6>
  <h5 id="DrawnUi_Draw_LayoutStructure_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class LayoutStructure : DynamicGrid&lt;ControlInStack&gt;</code></pre>
  </div>
  <h3 id="constructors">Constructors
</h3>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_LayoutStructure__ctor.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.LayoutStructure.%23ctor%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/LayoutStructure.cs/#L5">View Source</a>
  </span>
  <a id="DrawnUi_Draw_LayoutStructure__ctor_" data-uid="DrawnUi.Draw.LayoutStructure.#ctor*"></a>
  <h4 id="DrawnUi_Draw_LayoutStructure__ctor" data-uid="DrawnUi.Draw.LayoutStructure.#ctor">LayoutStructure()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public LayoutStructure()</code></pre>
  </div>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_LayoutStructure__ctor_System_Collections_Generic_List_System_Collections_Generic_List_DrawnUi_Draw_ControlInStack___.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.LayoutStructure.%23ctor(System.Collections.Generic.List%7BSystem.Collections.Generic.List%7BDrawnUi.Draw.ControlInStack%7D%7D)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/LayoutStructure.cs/#L30">View Source</a>
  </span>
  <a id="DrawnUi_Draw_LayoutStructure__ctor_" data-uid="DrawnUi.Draw.LayoutStructure.#ctor*"></a>
  <h4 id="DrawnUi_Draw_LayoutStructure__ctor_System_Collections_Generic_List_System_Collections_Generic_List_DrawnUi_Draw_ControlInStack___" data-uid="DrawnUi.Draw.LayoutStructure.#ctor(System.Collections.Generic.List{System.Collections.Generic.List{DrawnUi.Draw.ControlInStack}})">LayoutStructure(List&lt;List&lt;ControlInStack&gt;&gt;)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public LayoutStructure(List&lt;List&lt;ControlInStack&gt;&gt; grid)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1">List</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1">List</a>&lt;<a class="xref" href="DrawnUi.Draw.ControlInStack.html">ControlInStack</a>&gt;&gt;</td>
        <td><span class="parametername">grid</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
</h3>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_LayoutStructure_Append_System_Collections_Generic_List_System_Collections_Generic_List_DrawnUi_Draw_ControlInStack___.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.LayoutStructure.Append(System.Collections.Generic.List%7BSystem.Collections.Generic.List%7BDrawnUi.Draw.ControlInStack%7D%7D)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/LayoutStructure.cs/#L48">View Source</a>
  </span>
  <a id="DrawnUi_Draw_LayoutStructure_Append_" data-uid="DrawnUi.Draw.LayoutStructure.Append*"></a>
  <h4 id="DrawnUi_Draw_LayoutStructure_Append_System_Collections_Generic_List_System_Collections_Generic_List_DrawnUi_Draw_ControlInStack___" data-uid="DrawnUi.Draw.LayoutStructure.Append(System.Collections.Generic.List{System.Collections.Generic.List{DrawnUi.Draw.ControlInStack}})">Append(List&lt;List&lt;ControlInStack&gt;&gt;)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Append(List&lt;List&lt;ControlInStack&gt;&gt; grid)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1">List</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1">List</a>&lt;<a class="xref" href="DrawnUi.Draw.ControlInStack.html">ControlInStack</a>&gt;&gt;</td>
        <td><span class="parametername">grid</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_LayoutStructure_Clone.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.LayoutStructure.Clone%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/LayoutStructure.cs/#L14">View Source</a>
  </span>
  <a id="DrawnUi_Draw_LayoutStructure_Clone_" data-uid="DrawnUi.Draw.LayoutStructure.Clone*"></a>
  <h4 id="DrawnUi_Draw_LayoutStructure_Clone" data-uid="DrawnUi.Draw.LayoutStructure.Clone">Clone()</h4>
  <div class="markdown level1 summary"><p>Returns a new instance of LayoutStructure with the same items.
This performs a shallow copy of the existing structure.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public LayoutStructure Clone()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="DrawnUi.Draw.LayoutStructure.html">LayoutStructure</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_LayoutStructure_GetForIndex_System_Int32_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.LayoutStructure.GetForIndex(System.Int32)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/LayoutStructure.cs/#L25">View Source</a>
  </span>
  <a id="DrawnUi_Draw_LayoutStructure_GetForIndex_" data-uid="DrawnUi.Draw.LayoutStructure.GetForIndex*"></a>
  <h4 id="DrawnUi_Draw_LayoutStructure_GetForIndex_System_Int32_" data-uid="DrawnUi.Draw.LayoutStructure.GetForIndex(System.Int32)">GetForIndex(int)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public ControlInStack GetForIndex(int index)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></td>
        <td><span class="parametername">index</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="DrawnUi.Draw.ControlInStack.html">ControlInStack</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="extensionmethods">Extension Methods</h3>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_">InternalExtensions.FromPlatform(object)</a>
  </div>

</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_LayoutStructure.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.LayoutStructure%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A" class="contribution-link">Edit this page</a>
                  </li>
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Layout/LayoutStructure.cs/#L3" class="contribution-link">View Source</a>
                  </li>
                </ul>
              </div>
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In this article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      
      <span>Generated by <strong>DocFX</strong></span>
          </div>
        </div>
      </footer>
    </div>

    <script type="text/javascript" src="../styles/docfx.vendor.min.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
