### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.RecycleTemplateType
  commentId: T:DrawnUi.Draw.RecycleTemplateType
  id: RecycleTemplateType
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.RecycleTemplateType.FillViewport
  - DrawnUi.Draw.RecycleTemplateType.None
  - DrawnUi.Draw.RecycleTemplateType.Single
  langs:
  - csharp
  - vb
  name: RecycleTemplateType
  nameWithType: RecycleTemplateType
  fullName: DrawnUi.Draw.RecycleTemplateType
  type: Enum
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/RecycleTemplateType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: RecycleTemplateType
    path: ../src/Shared/Draw/Internals/Enums/RecycleTemplateType.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public enum RecycleTemplateType
    content.vb: Public Enum RecycleTemplateType
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.RecycleTemplateType.None
  commentId: F:DrawnUi.Draw.RecycleTemplateType.None
  id: None
  parent: DrawnUi.Draw.RecycleTemplateType
  langs:
  - csharp
  - vb
  name: None
  nameWithType: RecycleTemplateType.None
  fullName: DrawnUi.Draw.RecycleTemplateType.None
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/RecycleTemplateType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: None
    path: ../src/Shared/Draw/Internals/Enums/RecycleTemplateType.cs
    startLine: 8
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: >-
    One cell per item will be created, while a SkiaControl has little memory consumption for some controls

    like SkiaLayer it might take more, so you might consider recycling for large number o items
  example: []
  syntax:
    content: None = 0
    return:
      type: DrawnUi.Draw.RecycleTemplateType
- uid: DrawnUi.Draw.RecycleTemplateType.FillViewport
  commentId: F:DrawnUi.Draw.RecycleTemplateType.FillViewport
  id: FillViewport
  parent: DrawnUi.Draw.RecycleTemplateType
  langs:
  - csharp
  - vb
  name: FillViewport
  nameWithType: RecycleTemplateType.FillViewport
  fullName: DrawnUi.Draw.RecycleTemplateType.FillViewport
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/RecycleTemplateType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: FillViewport
    path: ../src/Shared/Draw/Internals/Enums/RecycleTemplateType.cs
    startLine: 13
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Create cells instances until viewport is filled, then recycle while scrolling
  example: []
  syntax:
    content: FillViewport = 1
    return:
      type: DrawnUi.Draw.RecycleTemplateType
- uid: DrawnUi.Draw.RecycleTemplateType.Single
  commentId: F:DrawnUi.Draw.RecycleTemplateType.Single
  id: Single
  parent: DrawnUi.Draw.RecycleTemplateType
  langs:
  - csharp
  - vb
  name: Single
  nameWithType: RecycleTemplateType.Single
  fullName: DrawnUi.Draw.RecycleTemplateType.Single
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/RecycleTemplateType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Single
    path: ../src/Shared/Draw/Internals/Enums/RecycleTemplateType.cs
    startLine: 19
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: >-
    Try using one cell per template at all times, binding context will change just before drawing.

    ToDo investigate case of async data changes like images loading from web.
  example: []
  syntax:
    content: Single = 2
    return:
      type: DrawnUi.Draw.RecycleTemplateType
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.RecycleTemplateType
  commentId: T:DrawnUi.Draw.RecycleTemplateType
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.RecycleTemplateType.html
  name: RecycleTemplateType
  nameWithType: RecycleTemplateType
  fullName: DrawnUi.Draw.RecycleTemplateType
