<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <title>Namespace DrawnUi.Views | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width">
      <meta name="title" content="Namespace DrawnUi.Views | DrawnUi Documentation ">
    
    
      <link rel="shortcut icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../styles/docfx.vendor.min.css">
      <link rel="stylesheet" href="../styles/docfx.css">
      <link rel="stylesheet" href="../styles/main.css">
      <meta property="docfx:navrel" content="../toc.html">
      <meta property="docfx:tocrel" content="toc.html">
    
    <meta property="docfx:rel" content="../">
    
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first=First data-prev=Previous data-next=Next data-last=Last></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="DrawnUi.Views">

  <h1 id="DrawnUi_Views" data-uid="DrawnUi.Views" class="text-break">Namespace DrawnUi.Views</h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="markdown level0 remarks"></div>
    <h3 id="classes">
Classes
</h3>
      <h4><a class="xref" href="DrawnUi.Views.BasePageReloadable.html">BasePageReloadable</a></h4>
      <section><p>Base class for a page with canvas, supports C# HotReload for building UI with code (not XAML).
Override <code>Build()</code>, see examples.</p>
</section>
      <h4><a class="xref" href="DrawnUi.Views.Canvas.html">Canvas</a></h4>
      <section><p>Optimized DrawnView having only one child inside Content property. Can autosize to to children size.
For all drawn app put this directly inside the ContentPage as root view.
If you put this inside some Maui control like Grid whatever expect more GC collections during animations making them somewhat less fluid.</p>
</section>
      <h4><a class="xref" href="DrawnUi.Views.DisposableManager.html">DisposableManager</a></h4>
      <section><p>Manages delayed disposal of IDisposable objects based on frame count</p>
</section>
      <h4><a class="xref" href="DrawnUi.Views.DrawnUiBasePage.html">DrawnUiBasePage</a></h4>
      <section><p>Actually used to: respond to keyboard resizing on mobile and keyboard key presses on Mac. Other than for that this
is not needed at all.</p>
</section>
      <h4><a class="xref" href="DrawnUi.Views.DrawnView.html">DrawnView</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Views.DrawnView.DiagnosticData.html">DrawnView.DiagnosticData</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Views.DrawnView.FocusedItemChangedArgs.html">DrawnView.FocusedItemChangedArgs</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Views.DrawnView.OffscreenCommand.html">DrawnView.OffscreenCommand</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Views.SkiaView.html">SkiaView</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Views.SkiaViewAccelerated.html">SkiaViewAccelerated</a></h4>
      <section></section>
    <h3 id="structs">
Structs
</h3>
      <h4><a class="xref" href="DrawnUi.Views.DrawnView.TimedDisposable.html">DrawnView.TimedDisposable</a></h4>
      <section></section>


</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In this article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      
      <span>Generated by <strong>DocFX</strong></span>
          </div>
        </div>
      </footer>
    </div>

    <script type="text/javascript" src="../styles/docfx.vendor.min.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
