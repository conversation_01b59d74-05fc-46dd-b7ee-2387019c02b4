### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.SkiaLabel.TextMetrics
  commentId: T:DrawnUi.Draw.SkiaLabel.TextMetrics
  id: SkiaLabel.TextMetrics
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.SkiaLabel.TextMetrics.LineHeightPixels
  - DrawnUi.Draw.SkiaLabel.TextMetrics.LineHeightWithSpacing
  - DrawnUi.Draw.SkiaLabel.TextMetrics.LineSpacing
  - DrawnUi.Draw.SkiaLabel.TextMetrics.ParagraphSpacing
  - DrawnUi.Draw.SkiaLabel.TextMetrics.SpaceBetweenLines
  - DrawnUi.Draw.SkiaLabel.TextMetrics.SpaceBetweenParagraphs
  langs:
  - csharp
  - vb
  name: SkiaLabel.TextMetrics
  nameWithType: SkiaLabel.TextMetrics
  fullName: DrawnUi.Draw.SkiaLabel.TextMetrics
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/SkiaLabel.Line.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: TextMetrics
    path: ../src/Maui/DrawnUi/Draw/Text/SkiaLabel.Line.cs
    startLine: 7
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public class SkiaLabel.TextMetrics
    content.vb: Public Class SkiaLabel.TextMetrics
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.SkiaLabel.TextMetrics.LineSpacing
  commentId: P:DrawnUi.Draw.SkiaLabel.TextMetrics.LineSpacing
  id: LineSpacing
  parent: DrawnUi.Draw.SkiaLabel.TextMetrics
  langs:
  - csharp
  - vb
  name: LineSpacing
  nameWithType: SkiaLabel.TextMetrics.LineSpacing
  fullName: DrawnUi.Draw.SkiaLabel.TextMetrics.LineSpacing
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/SkiaLabel.Line.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: LineSpacing
    path: ../src/Maui/DrawnUi/Draw/Text/SkiaLabel.Line.cs
    startLine: 11
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public double LineSpacing { get; set; }
    parameters: []
    return:
      type: System.Double
    content.vb: Public Property LineSpacing As Double
  overload: DrawnUi.Draw.SkiaLabel.TextMetrics.LineSpacing*
- uid: DrawnUi.Draw.SkiaLabel.TextMetrics.LineHeightPixels
  commentId: P:DrawnUi.Draw.SkiaLabel.TextMetrics.LineHeightPixels
  id: LineHeightPixels
  parent: DrawnUi.Draw.SkiaLabel.TextMetrics
  langs:
  - csharp
  - vb
  name: LineHeightPixels
  nameWithType: SkiaLabel.TextMetrics.LineHeightPixels
  fullName: DrawnUi.Draw.SkiaLabel.TextMetrics.LineHeightPixels
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/SkiaLabel.Line.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: LineHeightPixels
    path: ../src/Maui/DrawnUi/Draw/Text/SkiaLabel.Line.cs
    startLine: 13
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public float LineHeightPixels { get; set; }
    parameters: []
    return:
      type: System.Single
    content.vb: Public Property LineHeightPixels As Single
  overload: DrawnUi.Draw.SkiaLabel.TextMetrics.LineHeightPixels*
- uid: DrawnUi.Draw.SkiaLabel.TextMetrics.ParagraphSpacing
  commentId: P:DrawnUi.Draw.SkiaLabel.TextMetrics.ParagraphSpacing
  id: ParagraphSpacing
  parent: DrawnUi.Draw.SkiaLabel.TextMetrics
  langs:
  - csharp
  - vb
  name: ParagraphSpacing
  nameWithType: SkiaLabel.TextMetrics.ParagraphSpacing
  fullName: DrawnUi.Draw.SkiaLabel.TextMetrics.ParagraphSpacing
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/SkiaLabel.Line.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ParagraphSpacing
    path: ../src/Maui/DrawnUi/Draw/Text/SkiaLabel.Line.cs
    startLine: 15
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public double ParagraphSpacing { get; set; }
    parameters: []
    return:
      type: System.Double
    content.vb: Public Property ParagraphSpacing As Double
  overload: DrawnUi.Draw.SkiaLabel.TextMetrics.ParagraphSpacing*
- uid: DrawnUi.Draw.SkiaLabel.TextMetrics.SpaceBetweenParagraphs
  commentId: P:DrawnUi.Draw.SkiaLabel.TextMetrics.SpaceBetweenParagraphs
  id: SpaceBetweenParagraphs
  parent: DrawnUi.Draw.SkiaLabel.TextMetrics
  langs:
  - csharp
  - vb
  name: SpaceBetweenParagraphs
  nameWithType: SkiaLabel.TextMetrics.SpaceBetweenParagraphs
  fullName: DrawnUi.Draw.SkiaLabel.TextMetrics.SpaceBetweenParagraphs
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/SkiaLabel.Line.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: SpaceBetweenParagraphs
    path: ../src/Maui/DrawnUi/Draw/Text/SkiaLabel.Line.cs
    startLine: 17
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public double SpaceBetweenParagraphs { get; }
    parameters: []
    return:
      type: System.Double
    content.vb: Public ReadOnly Property SpaceBetweenParagraphs As Double
  overload: DrawnUi.Draw.SkiaLabel.TextMetrics.SpaceBetweenParagraphs*
- uid: DrawnUi.Draw.SkiaLabel.TextMetrics.LineHeightWithSpacing
  commentId: P:DrawnUi.Draw.SkiaLabel.TextMetrics.LineHeightWithSpacing
  id: LineHeightWithSpacing
  parent: DrawnUi.Draw.SkiaLabel.TextMetrics
  langs:
  - csharp
  - vb
  name: LineHeightWithSpacing
  nameWithType: SkiaLabel.TextMetrics.LineHeightWithSpacing
  fullName: DrawnUi.Draw.SkiaLabel.TextMetrics.LineHeightWithSpacing
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/SkiaLabel.Line.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: LineHeightWithSpacing
    path: ../src/Maui/DrawnUi/Draw/Text/SkiaLabel.Line.cs
    startLine: 24
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public double LineHeightWithSpacing { get; }
    parameters: []
    return:
      type: System.Double
    content.vb: Public ReadOnly Property LineHeightWithSpacing As Double
  overload: DrawnUi.Draw.SkiaLabel.TextMetrics.LineHeightWithSpacing*
- uid: DrawnUi.Draw.SkiaLabel.TextMetrics.SpaceBetweenLines
  commentId: P:DrawnUi.Draw.SkiaLabel.TextMetrics.SpaceBetweenLines
  id: SpaceBetweenLines
  parent: DrawnUi.Draw.SkiaLabel.TextMetrics
  langs:
  - csharp
  - vb
  name: SpaceBetweenLines
  nameWithType: SkiaLabel.TextMetrics.SpaceBetweenLines
  fullName: DrawnUi.Draw.SkiaLabel.TextMetrics.SpaceBetweenLines
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/SkiaLabel.Line.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: SpaceBetweenLines
    path: ../src/Maui/DrawnUi/Draw/Text/SkiaLabel.Line.cs
    startLine: 31
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public double SpaceBetweenLines { get; }
    parameters: []
    return:
      type: System.Double
    content.vb: Public ReadOnly Property SpaceBetweenLines As Double
  overload: DrawnUi.Draw.SkiaLabel.TextMetrics.SpaceBetweenLines*
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.SkiaLabel.TextMetrics.LineSpacing*
  commentId: Overload:DrawnUi.Draw.SkiaLabel.TextMetrics.LineSpacing
  href: DrawnUi.Draw.SkiaLabel.TextMetrics.html#DrawnUi_Draw_SkiaLabel_TextMetrics_LineSpacing
  name: LineSpacing
  nameWithType: SkiaLabel.TextMetrics.LineSpacing
  fullName: DrawnUi.Draw.SkiaLabel.TextMetrics.LineSpacing
- uid: System.Double
  commentId: T:System.Double
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.double
  name: double
  nameWithType: double
  fullName: double
  nameWithType.vb: Double
  fullName.vb: Double
  name.vb: Double
- uid: DrawnUi.Draw.SkiaLabel.TextMetrics.LineHeightPixels*
  commentId: Overload:DrawnUi.Draw.SkiaLabel.TextMetrics.LineHeightPixels
  href: DrawnUi.Draw.SkiaLabel.TextMetrics.html#DrawnUi_Draw_SkiaLabel_TextMetrics_LineHeightPixels
  name: LineHeightPixels
  nameWithType: SkiaLabel.TextMetrics.LineHeightPixels
  fullName: DrawnUi.Draw.SkiaLabel.TextMetrics.LineHeightPixels
- uid: System.Single
  commentId: T:System.Single
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.single
  name: float
  nameWithType: float
  fullName: float
  nameWithType.vb: Single
  fullName.vb: Single
  name.vb: Single
- uid: DrawnUi.Draw.SkiaLabel.TextMetrics.ParagraphSpacing*
  commentId: Overload:DrawnUi.Draw.SkiaLabel.TextMetrics.ParagraphSpacing
  href: DrawnUi.Draw.SkiaLabel.TextMetrics.html#DrawnUi_Draw_SkiaLabel_TextMetrics_ParagraphSpacing
  name: ParagraphSpacing
  nameWithType: SkiaLabel.TextMetrics.ParagraphSpacing
  fullName: DrawnUi.Draw.SkiaLabel.TextMetrics.ParagraphSpacing
- uid: DrawnUi.Draw.SkiaLabel.TextMetrics.SpaceBetweenParagraphs*
  commentId: Overload:DrawnUi.Draw.SkiaLabel.TextMetrics.SpaceBetweenParagraphs
  href: DrawnUi.Draw.SkiaLabel.TextMetrics.html#DrawnUi_Draw_SkiaLabel_TextMetrics_SpaceBetweenParagraphs
  name: SpaceBetweenParagraphs
  nameWithType: SkiaLabel.TextMetrics.SpaceBetweenParagraphs
  fullName: DrawnUi.Draw.SkiaLabel.TextMetrics.SpaceBetweenParagraphs
- uid: DrawnUi.Draw.SkiaLabel.TextMetrics.LineHeightWithSpacing*
  commentId: Overload:DrawnUi.Draw.SkiaLabel.TextMetrics.LineHeightWithSpacing
  href: DrawnUi.Draw.SkiaLabel.TextMetrics.html#DrawnUi_Draw_SkiaLabel_TextMetrics_LineHeightWithSpacing
  name: LineHeightWithSpacing
  nameWithType: SkiaLabel.TextMetrics.LineHeightWithSpacing
  fullName: DrawnUi.Draw.SkiaLabel.TextMetrics.LineHeightWithSpacing
- uid: DrawnUi.Draw.SkiaLabel.TextMetrics.SpaceBetweenLines*
  commentId: Overload:DrawnUi.Draw.SkiaLabel.TextMetrics.SpaceBetweenLines
  href: DrawnUi.Draw.SkiaLabel.TextMetrics.html#DrawnUi_Draw_SkiaLabel_TextMetrics_SpaceBetweenLines
  name: SpaceBetweenLines
  nameWithType: SkiaLabel.TextMetrics.SpaceBetweenLines
  fullName: DrawnUi.Draw.SkiaLabel.TextMetrics.SpaceBetweenLines
