### YamlMime:ManagedReference
items:
- uid: DrawnUi.Infrastructure.Spring
  commentId: T:DrawnUi.Infrastructure.Spring
  id: Spring
  parent: DrawnUi.Infrastructure
  children:
  - DrawnUi.Infrastructure.Spring.#ctor(System.Single,System.Single,System.Single)
  - DrawnUi.Infrastructure.Spring.Damped
  - DrawnUi.Infrastructure.Spring.DampingRatio
  - DrawnUi.Infrastructure.Spring.Default
  - DrawnUi.Infrastructure.Spring.Mass
  - DrawnUi.Infrastructure.Spring.Stiffness
  langs:
  - csharp
  - vb
  name: Spring
  nameWithType: Spring
  fullName: DrawnUi.Infrastructure.Spring
  type: Struct
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/Spring.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Spring
    path: ../src/Shared/Draw/Internals/Models/Spring.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public struct Spring
    content.vb: Public Structure Spring
  inheritedMembers:
  - System.ValueType.Equals(System.Object)
  - System.ValueType.GetHashCode
  - System.ValueType.ToString
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetType
  - System.Object.ReferenceEquals(System.Object,System.Object)
  extensionMethods:
  - DrawnUi.Infrastructure.Spring.DrawnUi.Draw.SpringExtensions.Beta
  - DrawnUi.Infrastructure.Spring.DrawnUi.Draw.SpringExtensions.DampedNaturalFrequency
  - DrawnUi.Infrastructure.Spring.DrawnUi.Draw.SpringExtensions.Damping
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Infrastructure.Spring.Mass
  commentId: P:DrawnUi.Infrastructure.Spring.Mass
  id: Mass
  parent: DrawnUi.Infrastructure.Spring
  langs:
  - csharp
  - vb
  name: Mass
  nameWithType: Spring.Mass
  fullName: DrawnUi.Infrastructure.Spring.Mass
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/Spring.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Mass
    path: ../src/Shared/Draw/Internals/Models/Spring.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public float Mass { readonly get; set; }
    parameters: []
    return:
      type: System.Single
    content.vb: Public Property Mass As Single
  overload: DrawnUi.Infrastructure.Spring.Mass*
- uid: DrawnUi.Infrastructure.Spring.Stiffness
  commentId: P:DrawnUi.Infrastructure.Spring.Stiffness
  id: Stiffness
  parent: DrawnUi.Infrastructure.Spring
  langs:
  - csharp
  - vb
  name: Stiffness
  nameWithType: Spring.Stiffness
  fullName: DrawnUi.Infrastructure.Spring.Stiffness
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/Spring.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Stiffness
    path: ../src/Shared/Draw/Internals/Models/Spring.cs
    startLine: 5
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public float Stiffness { readonly get; set; }
    parameters: []
    return:
      type: System.Single
    content.vb: Public Property Stiffness As Single
  overload: DrawnUi.Infrastructure.Spring.Stiffness*
- uid: DrawnUi.Infrastructure.Spring.DampingRatio
  commentId: P:DrawnUi.Infrastructure.Spring.DampingRatio
  id: DampingRatio
  parent: DrawnUi.Infrastructure.Spring
  langs:
  - csharp
  - vb
  name: DampingRatio
  nameWithType: Spring.DampingRatio
  fullName: DrawnUi.Infrastructure.Spring.DampingRatio
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/Spring.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: DampingRatio
    path: ../src/Shared/Draw/Internals/Models/Spring.cs
    startLine: 6
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public float DampingRatio { readonly get; set; }
    parameters: []
    return:
      type: System.Single
    content.vb: Public Property DampingRatio As Single
  overload: DrawnUi.Infrastructure.Spring.DampingRatio*
- uid: DrawnUi.Infrastructure.Spring.#ctor(System.Single,System.Single,System.Single)
  commentId: M:DrawnUi.Infrastructure.Spring.#ctor(System.Single,System.Single,System.Single)
  id: '#ctor(System.Single,System.Single,System.Single)'
  parent: DrawnUi.Infrastructure.Spring
  langs:
  - csharp
  - vb
  name: Spring(float, float, float)
  nameWithType: Spring.Spring(float, float, float)
  fullName: DrawnUi.Infrastructure.Spring.Spring(float, float, float)
  type: Constructor
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/Spring.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Shared/Draw/Internals/Models/Spring.cs
    startLine: 8
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public Spring(float mass, float stiffness, float dampingRatio)
    parameters:
    - id: mass
      type: System.Single
    - id: stiffness
      type: System.Single
    - id: dampingRatio
      type: System.Single
    content.vb: Public Sub New(mass As Single, stiffness As Single, dampingRatio As Single)
  overload: DrawnUi.Infrastructure.Spring.#ctor*
  nameWithType.vb: Spring.New(Single, Single, Single)
  fullName.vb: DrawnUi.Infrastructure.Spring.New(Single, Single, Single)
  name.vb: New(Single, Single, Single)
- uid: DrawnUi.Infrastructure.Spring.Damped
  commentId: P:DrawnUi.Infrastructure.Spring.Damped
  id: Damped
  parent: DrawnUi.Infrastructure.Spring
  langs:
  - csharp
  - vb
  name: Damped
  nameWithType: Spring.Damped
  fullName: DrawnUi.Infrastructure.Spring.Damped
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/Spring.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Damped
    path: ../src/Shared/Draw/Internals/Models/Spring.cs
    startLine: 15
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public static Spring Damped { get; }
    parameters: []
    return:
      type: DrawnUi.Infrastructure.Spring
    content.vb: Public Shared ReadOnly Property Damped As Spring
  overload: DrawnUi.Infrastructure.Spring.Damped*
- uid: DrawnUi.Infrastructure.Spring.Default
  commentId: P:DrawnUi.Infrastructure.Spring.Default
  id: Default
  parent: DrawnUi.Infrastructure.Spring
  langs:
  - csharp
  - vb
  name: Default
  nameWithType: Spring.Default
  fullName: DrawnUi.Infrastructure.Spring.Default
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/Spring.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Default
    path: ../src/Shared/Draw/Internals/Models/Spring.cs
    startLine: 17
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure
  syntax:
    content: public static Spring Default { get; }
    parameters: []
    return:
      type: DrawnUi.Infrastructure.Spring
    content.vb: Public Shared ReadOnly Property [Default] As Spring
  overload: DrawnUi.Infrastructure.Spring.Default*
references:
- uid: DrawnUi.Infrastructure
  commentId: N:DrawnUi.Infrastructure
  href: DrawnUi.html
  name: DrawnUi.Infrastructure
  nameWithType: DrawnUi.Infrastructure
  fullName: DrawnUi.Infrastructure
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Infrastructure
    name: Infrastructure
    href: DrawnUi.Infrastructure.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Infrastructure
    name: Infrastructure
    href: DrawnUi.Infrastructure.html
- uid: System.ValueType.Equals(System.Object)
  commentId: M:System.ValueType.Equals(System.Object)
  parent: System.ValueType
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype.equals
  name: Equals(object)
  nameWithType: ValueType.Equals(object)
  fullName: System.ValueType.Equals(object)
  nameWithType.vb: ValueType.Equals(Object)
  fullName.vb: System.ValueType.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.ValueType.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.equals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.ValueType.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.equals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.ValueType.GetHashCode
  commentId: M:System.ValueType.GetHashCode
  parent: System.ValueType
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype.gethashcode
  name: GetHashCode()
  nameWithType: ValueType.GetHashCode()
  fullName: System.ValueType.GetHashCode()
  spec.csharp:
  - uid: System.ValueType.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.ValueType.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.gethashcode
  - name: (
  - name: )
- uid: System.ValueType.ToString
  commentId: M:System.ValueType.ToString
  parent: System.ValueType
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype.tostring
  name: ToString()
  nameWithType: ValueType.ToString()
  fullName: System.ValueType.ToString()
  spec.csharp:
  - uid: System.ValueType.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.ValueType.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.tostring
  - name: (
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Infrastructure.Spring.DrawnUi.Draw.SpringExtensions.Beta
  commentId: M:DrawnUi.Draw.SpringExtensions.Beta(DrawnUi.Infrastructure.Spring)
  parent: DrawnUi.Draw.SpringExtensions
  definition: DrawnUi.Draw.SpringExtensions.Beta(DrawnUi.Infrastructure.Spring)
  href: DrawnUi.Draw.SpringExtensions.html#DrawnUi_Draw_SpringExtensions_Beta_DrawnUi_Infrastructure_Spring_
  name: Beta(Spring)
  nameWithType: SpringExtensions.Beta(Spring)
  fullName: DrawnUi.Draw.SpringExtensions.Beta(DrawnUi.Infrastructure.Spring)
  spec.csharp:
  - uid: DrawnUi.Draw.SpringExtensions.Beta(DrawnUi.Infrastructure.Spring)
    name: Beta
    href: DrawnUi.Draw.SpringExtensions.html#DrawnUi_Draw_SpringExtensions_Beta_DrawnUi_Infrastructure_Spring_
  - name: (
  - uid: DrawnUi.Infrastructure.Spring
    name: Spring
    href: DrawnUi.Infrastructure.Spring.html
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SpringExtensions.Beta(DrawnUi.Infrastructure.Spring)
    name: Beta
    href: DrawnUi.Draw.SpringExtensions.html#DrawnUi_Draw_SpringExtensions_Beta_DrawnUi_Infrastructure_Spring_
  - name: (
  - uid: DrawnUi.Infrastructure.Spring
    name: Spring
    href: DrawnUi.Infrastructure.Spring.html
  - name: )
- uid: DrawnUi.Infrastructure.Spring.DrawnUi.Draw.SpringExtensions.DampedNaturalFrequency
  commentId: M:DrawnUi.Draw.SpringExtensions.DampedNaturalFrequency(DrawnUi.Infrastructure.Spring)
  parent: DrawnUi.Draw.SpringExtensions
  definition: DrawnUi.Draw.SpringExtensions.DampedNaturalFrequency(DrawnUi.Infrastructure.Spring)
  href: DrawnUi.Draw.SpringExtensions.html#DrawnUi_Draw_SpringExtensions_DampedNaturalFrequency_DrawnUi_Infrastructure_Spring_
  name: DampedNaturalFrequency(Spring)
  nameWithType: SpringExtensions.DampedNaturalFrequency(Spring)
  fullName: DrawnUi.Draw.SpringExtensions.DampedNaturalFrequency(DrawnUi.Infrastructure.Spring)
  spec.csharp:
  - uid: DrawnUi.Draw.SpringExtensions.DampedNaturalFrequency(DrawnUi.Infrastructure.Spring)
    name: DampedNaturalFrequency
    href: DrawnUi.Draw.SpringExtensions.html#DrawnUi_Draw_SpringExtensions_DampedNaturalFrequency_DrawnUi_Infrastructure_Spring_
  - name: (
  - uid: DrawnUi.Infrastructure.Spring
    name: Spring
    href: DrawnUi.Infrastructure.Spring.html
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SpringExtensions.DampedNaturalFrequency(DrawnUi.Infrastructure.Spring)
    name: DampedNaturalFrequency
    href: DrawnUi.Draw.SpringExtensions.html#DrawnUi_Draw_SpringExtensions_DampedNaturalFrequency_DrawnUi_Infrastructure_Spring_
  - name: (
  - uid: DrawnUi.Infrastructure.Spring
    name: Spring
    href: DrawnUi.Infrastructure.Spring.html
  - name: )
- uid: DrawnUi.Infrastructure.Spring.DrawnUi.Draw.SpringExtensions.Damping
  commentId: M:DrawnUi.Draw.SpringExtensions.Damping(DrawnUi.Infrastructure.Spring)
  parent: DrawnUi.Draw.SpringExtensions
  definition: DrawnUi.Draw.SpringExtensions.Damping(DrawnUi.Infrastructure.Spring)
  href: DrawnUi.Draw.SpringExtensions.html#DrawnUi_Draw_SpringExtensions_Damping_DrawnUi_Infrastructure_Spring_
  name: Damping(Spring)
  nameWithType: SpringExtensions.Damping(Spring)
  fullName: DrawnUi.Draw.SpringExtensions.Damping(DrawnUi.Infrastructure.Spring)
  spec.csharp:
  - uid: DrawnUi.Draw.SpringExtensions.Damping(DrawnUi.Infrastructure.Spring)
    name: Damping
    href: DrawnUi.Draw.SpringExtensions.html#DrawnUi_Draw_SpringExtensions_Damping_DrawnUi_Infrastructure_Spring_
  - name: (
  - uid: DrawnUi.Infrastructure.Spring
    name: Spring
    href: DrawnUi.Infrastructure.Spring.html
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SpringExtensions.Damping(DrawnUi.Infrastructure.Spring)
    name: Damping
    href: DrawnUi.Draw.SpringExtensions.html#DrawnUi_Draw_SpringExtensions_Damping_DrawnUi_Infrastructure_Spring_
  - name: (
  - uid: DrawnUi.Infrastructure.Spring
    name: Spring
    href: DrawnUi.Infrastructure.Spring.html
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.ValueType
  commentId: T:System.ValueType
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype
  name: ValueType
  nameWithType: ValueType
  fullName: System.ValueType
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: DrawnUi.Draw.SpringExtensions.Beta(DrawnUi.Infrastructure.Spring)
  commentId: M:DrawnUi.Draw.SpringExtensions.Beta(DrawnUi.Infrastructure.Spring)
  href: DrawnUi.Draw.SpringExtensions.html#DrawnUi_Draw_SpringExtensions_Beta_DrawnUi_Infrastructure_Spring_
  name: Beta(Spring)
  nameWithType: SpringExtensions.Beta(Spring)
  fullName: DrawnUi.Draw.SpringExtensions.Beta(DrawnUi.Infrastructure.Spring)
  spec.csharp:
  - uid: DrawnUi.Draw.SpringExtensions.Beta(DrawnUi.Infrastructure.Spring)
    name: Beta
    href: DrawnUi.Draw.SpringExtensions.html#DrawnUi_Draw_SpringExtensions_Beta_DrawnUi_Infrastructure_Spring_
  - name: (
  - uid: DrawnUi.Infrastructure.Spring
    name: Spring
    href: DrawnUi.Infrastructure.Spring.html
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SpringExtensions.Beta(DrawnUi.Infrastructure.Spring)
    name: Beta
    href: DrawnUi.Draw.SpringExtensions.html#DrawnUi_Draw_SpringExtensions_Beta_DrawnUi_Infrastructure_Spring_
  - name: (
  - uid: DrawnUi.Infrastructure.Spring
    name: Spring
    href: DrawnUi.Infrastructure.Spring.html
  - name: )
- uid: DrawnUi.Draw.SpringExtensions
  commentId: T:DrawnUi.Draw.SpringExtensions
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SpringExtensions.html
  name: SpringExtensions
  nameWithType: SpringExtensions
  fullName: DrawnUi.Draw.SpringExtensions
- uid: DrawnUi.Draw.SpringExtensions.DampedNaturalFrequency(DrawnUi.Infrastructure.Spring)
  commentId: M:DrawnUi.Draw.SpringExtensions.DampedNaturalFrequency(DrawnUi.Infrastructure.Spring)
  href: DrawnUi.Draw.SpringExtensions.html#DrawnUi_Draw_SpringExtensions_DampedNaturalFrequency_DrawnUi_Infrastructure_Spring_
  name: DampedNaturalFrequency(Spring)
  nameWithType: SpringExtensions.DampedNaturalFrequency(Spring)
  fullName: DrawnUi.Draw.SpringExtensions.DampedNaturalFrequency(DrawnUi.Infrastructure.Spring)
  spec.csharp:
  - uid: DrawnUi.Draw.SpringExtensions.DampedNaturalFrequency(DrawnUi.Infrastructure.Spring)
    name: DampedNaturalFrequency
    href: DrawnUi.Draw.SpringExtensions.html#DrawnUi_Draw_SpringExtensions_DampedNaturalFrequency_DrawnUi_Infrastructure_Spring_
  - name: (
  - uid: DrawnUi.Infrastructure.Spring
    name: Spring
    href: DrawnUi.Infrastructure.Spring.html
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SpringExtensions.DampedNaturalFrequency(DrawnUi.Infrastructure.Spring)
    name: DampedNaturalFrequency
    href: DrawnUi.Draw.SpringExtensions.html#DrawnUi_Draw_SpringExtensions_DampedNaturalFrequency_DrawnUi_Infrastructure_Spring_
  - name: (
  - uid: DrawnUi.Infrastructure.Spring
    name: Spring
    href: DrawnUi.Infrastructure.Spring.html
  - name: )
- uid: DrawnUi.Draw.SpringExtensions.Damping(DrawnUi.Infrastructure.Spring)
  commentId: M:DrawnUi.Draw.SpringExtensions.Damping(DrawnUi.Infrastructure.Spring)
  href: DrawnUi.Draw.SpringExtensions.html#DrawnUi_Draw_SpringExtensions_Damping_DrawnUi_Infrastructure_Spring_
  name: Damping(Spring)
  nameWithType: SpringExtensions.Damping(Spring)
  fullName: DrawnUi.Draw.SpringExtensions.Damping(DrawnUi.Infrastructure.Spring)
  spec.csharp:
  - uid: DrawnUi.Draw.SpringExtensions.Damping(DrawnUi.Infrastructure.Spring)
    name: Damping
    href: DrawnUi.Draw.SpringExtensions.html#DrawnUi_Draw_SpringExtensions_Damping_DrawnUi_Infrastructure_Spring_
  - name: (
  - uid: DrawnUi.Infrastructure.Spring
    name: Spring
    href: DrawnUi.Infrastructure.Spring.html
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SpringExtensions.Damping(DrawnUi.Infrastructure.Spring)
    name: Damping
    href: DrawnUi.Draw.SpringExtensions.html#DrawnUi_Draw_SpringExtensions_Damping_DrawnUi_Infrastructure_Spring_
  - name: (
  - uid: DrawnUi.Infrastructure.Spring
    name: Spring
    href: DrawnUi.Infrastructure.Spring.html
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Infrastructure.Spring.Mass*
  commentId: Overload:DrawnUi.Infrastructure.Spring.Mass
  href: DrawnUi.Infrastructure.Spring.html#DrawnUi_Infrastructure_Spring_Mass
  name: Mass
  nameWithType: Spring.Mass
  fullName: DrawnUi.Infrastructure.Spring.Mass
- uid: System.Single
  commentId: T:System.Single
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.single
  name: float
  nameWithType: float
  fullName: float
  nameWithType.vb: Single
  fullName.vb: Single
  name.vb: Single
- uid: DrawnUi.Infrastructure.Spring.Stiffness*
  commentId: Overload:DrawnUi.Infrastructure.Spring.Stiffness
  href: DrawnUi.Infrastructure.Spring.html#DrawnUi_Infrastructure_Spring_Stiffness
  name: Stiffness
  nameWithType: Spring.Stiffness
  fullName: DrawnUi.Infrastructure.Spring.Stiffness
- uid: DrawnUi.Infrastructure.Spring.DampingRatio*
  commentId: Overload:DrawnUi.Infrastructure.Spring.DampingRatio
  href: DrawnUi.Infrastructure.Spring.html#DrawnUi_Infrastructure_Spring_DampingRatio
  name: DampingRatio
  nameWithType: Spring.DampingRatio
  fullName: DrawnUi.Infrastructure.Spring.DampingRatio
- uid: DrawnUi.Infrastructure.Spring.#ctor*
  commentId: Overload:DrawnUi.Infrastructure.Spring.#ctor
  href: DrawnUi.Infrastructure.Spring.html#DrawnUi_Infrastructure_Spring__ctor_System_Single_System_Single_System_Single_
  name: Spring
  nameWithType: Spring.Spring
  fullName: DrawnUi.Infrastructure.Spring.Spring
  nameWithType.vb: Spring.New
  fullName.vb: DrawnUi.Infrastructure.Spring.New
  name.vb: New
- uid: DrawnUi.Infrastructure.Spring.Damped*
  commentId: Overload:DrawnUi.Infrastructure.Spring.Damped
  href: DrawnUi.Infrastructure.Spring.html#DrawnUi_Infrastructure_Spring_Damped
  name: Damped
  nameWithType: Spring.Damped
  fullName: DrawnUi.Infrastructure.Spring.Damped
- uid: DrawnUi.Infrastructure.Spring
  commentId: T:DrawnUi.Infrastructure.Spring
  parent: DrawnUi.Infrastructure
  href: DrawnUi.Infrastructure.Spring.html
  name: Spring
  nameWithType: Spring
  fullName: DrawnUi.Infrastructure.Spring
- uid: DrawnUi.Infrastructure.Spring.Default*
  commentId: Overload:DrawnUi.Infrastructure.Spring.Default
  href: DrawnUi.Infrastructure.Spring.html#DrawnUi_Infrastructure_Spring_Default
  name: Default
  nameWithType: Spring.Default
  fullName: DrawnUi.Infrastructure.Spring.Default
