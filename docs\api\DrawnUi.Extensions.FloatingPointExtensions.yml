### YamlMime:ManagedReference
items:
- uid: DrawnUi.Extensions.FloatingPointExtensions
  commentId: T:DrawnUi.Extensions.FloatingPointExtensions
  id: FloatingPointExtensions
  parent: DrawnUi.Extensions
  children:
  - DrawnUi.Extensions.FloatingPointExtensions.IsEqual(System.Double,System.Double,System.Double)
  - DrawnUi.Extensions.FloatingPointExtensions.IsGreater(System.Double,System.Double,System.Double)
  - DrawnUi.Extensions.FloatingPointExtensions.IsLess(System.Double,System.Double,System.Double)
  - DrawnUi.Extensions.FloatingPointExtensions.Project(System.Double,System.Double)
  langs:
  - csharp
  - vb
  name: FloatingPointExtensions
  nameWithType: FloatingPointExtensions
  fullName: DrawnUi.Extensions.FloatingPointExtensions
  type: Class
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/PointExtensions.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: FloatingPointExtensions
    path: ../src/Shared/Draw/Internals/Extensions/PointExtensions.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Extensions
  syntax:
    content: public static class FloatingPointExtensions
    content.vb: Public Module FloatingPointExtensions
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
- uid: DrawnUi.Extensions.FloatingPointExtensions.Project(System.Double,System.Double)
  commentId: M:DrawnUi.Extensions.FloatingPointExtensions.Project(System.Double,System.Double)
  id: Project(System.Double,System.Double)
  isExtensionMethod: true
  parent: DrawnUi.Extensions.FloatingPointExtensions
  langs:
  - csharp
  - vb
  name: Project(double, double)
  nameWithType: FloatingPointExtensions.Project(double, double)
  fullName: DrawnUi.Extensions.FloatingPointExtensions.Project(double, double)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/PointExtensions.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Project
    path: ../src/Shared/Draw/Internals/Extensions/PointExtensions.cs
    startLine: 6
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Extensions
  syntax:
    content: public static double Project(this double initialVelocity, double decelerationRate)
    parameters:
    - id: initialVelocity
      type: System.Double
    - id: decelerationRate
      type: System.Double
    return:
      type: System.Double
    content.vb: Public Shared Function Project(initialVelocity As Double, decelerationRate As Double) As Double
  overload: DrawnUi.Extensions.FloatingPointExtensions.Project*
  nameWithType.vb: FloatingPointExtensions.Project(Double, Double)
  fullName.vb: DrawnUi.Extensions.FloatingPointExtensions.Project(Double, Double)
  name.vb: Project(Double, Double)
- uid: DrawnUi.Extensions.FloatingPointExtensions.IsLess(System.Double,System.Double,System.Double)
  commentId: M:DrawnUi.Extensions.FloatingPointExtensions.IsLess(System.Double,System.Double,System.Double)
  id: IsLess(System.Double,System.Double,System.Double)
  isExtensionMethod: true
  parent: DrawnUi.Extensions.FloatingPointExtensions
  langs:
  - csharp
  - vb
  name: IsLess(double, double, double)
  nameWithType: FloatingPointExtensions.IsLess(double, double, double)
  fullName: DrawnUi.Extensions.FloatingPointExtensions.IsLess(double, double, double)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/PointExtensions.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IsLess
    path: ../src/Shared/Draw/Internals/Extensions/PointExtensions.cs
    startLine: 17
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Extensions
  syntax:
    content: public static bool IsLess(this double number, double other, double eps)
    parameters:
    - id: number
      type: System.Double
    - id: other
      type: System.Double
    - id: eps
      type: System.Double
    return:
      type: System.Boolean
    content.vb: Public Shared Function IsLess(number As Double, other As Double, eps As Double) As Boolean
  overload: DrawnUi.Extensions.FloatingPointExtensions.IsLess*
  nameWithType.vb: FloatingPointExtensions.IsLess(Double, Double, Double)
  fullName.vb: DrawnUi.Extensions.FloatingPointExtensions.IsLess(Double, Double, Double)
  name.vb: IsLess(Double, Double, Double)
- uid: DrawnUi.Extensions.FloatingPointExtensions.IsGreater(System.Double,System.Double,System.Double)
  commentId: M:DrawnUi.Extensions.FloatingPointExtensions.IsGreater(System.Double,System.Double,System.Double)
  id: IsGreater(System.Double,System.Double,System.Double)
  isExtensionMethod: true
  parent: DrawnUi.Extensions.FloatingPointExtensions
  langs:
  - csharp
  - vb
  name: IsGreater(double, double, double)
  nameWithType: FloatingPointExtensions.IsGreater(double, double, double)
  fullName: DrawnUi.Extensions.FloatingPointExtensions.IsGreater(double, double, double)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/PointExtensions.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IsGreater
    path: ../src/Shared/Draw/Internals/Extensions/PointExtensions.cs
    startLine: 22
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Extensions
  syntax:
    content: public static bool IsGreater(this double number, double other, double eps)
    parameters:
    - id: number
      type: System.Double
    - id: other
      type: System.Double
    - id: eps
      type: System.Double
    return:
      type: System.Boolean
    content.vb: Public Shared Function IsGreater(number As Double, other As Double, eps As Double) As Boolean
  overload: DrawnUi.Extensions.FloatingPointExtensions.IsGreater*
  nameWithType.vb: FloatingPointExtensions.IsGreater(Double, Double, Double)
  fullName.vb: DrawnUi.Extensions.FloatingPointExtensions.IsGreater(Double, Double, Double)
  name.vb: IsGreater(Double, Double, Double)
- uid: DrawnUi.Extensions.FloatingPointExtensions.IsEqual(System.Double,System.Double,System.Double)
  commentId: M:DrawnUi.Extensions.FloatingPointExtensions.IsEqual(System.Double,System.Double,System.Double)
  id: IsEqual(System.Double,System.Double,System.Double)
  isExtensionMethod: true
  parent: DrawnUi.Extensions.FloatingPointExtensions
  langs:
  - csharp
  - vb
  name: IsEqual(double, double, double)
  nameWithType: FloatingPointExtensions.IsEqual(double, double, double)
  fullName: DrawnUi.Extensions.FloatingPointExtensions.IsEqual(double, double, double)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/PointExtensions.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IsEqual
    path: ../src/Shared/Draw/Internals/Extensions/PointExtensions.cs
    startLine: 27
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Extensions
  syntax:
    content: public static bool IsEqual(this double number, double other, double eps)
    parameters:
    - id: number
      type: System.Double
    - id: other
      type: System.Double
    - id: eps
      type: System.Double
    return:
      type: System.Boolean
    content.vb: Public Shared Function IsEqual(number As Double, other As Double, eps As Double) As Boolean
  overload: DrawnUi.Extensions.FloatingPointExtensions.IsEqual*
  nameWithType.vb: FloatingPointExtensions.IsEqual(Double, Double, Double)
  fullName.vb: DrawnUi.Extensions.FloatingPointExtensions.IsEqual(Double, Double, Double)
  name.vb: IsEqual(Double, Double, Double)
references:
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.FloatingPointExtensions.Project*
  commentId: Overload:DrawnUi.Extensions.FloatingPointExtensions.Project
  href: DrawnUi.Extensions.FloatingPointExtensions.html#DrawnUi_Extensions_FloatingPointExtensions_Project_System_Double_System_Double_
  name: Project
  nameWithType: FloatingPointExtensions.Project
  fullName: DrawnUi.Extensions.FloatingPointExtensions.Project
- uid: System.Double
  commentId: T:System.Double
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.double
  name: double
  nameWithType: double
  fullName: double
  nameWithType.vb: Double
  fullName.vb: Double
  name.vb: Double
- uid: DrawnUi.Extensions.FloatingPointExtensions.IsLess*
  commentId: Overload:DrawnUi.Extensions.FloatingPointExtensions.IsLess
  href: DrawnUi.Extensions.FloatingPointExtensions.html#DrawnUi_Extensions_FloatingPointExtensions_IsLess_System_Double_System_Double_System_Double_
  name: IsLess
  nameWithType: FloatingPointExtensions.IsLess
  fullName: DrawnUi.Extensions.FloatingPointExtensions.IsLess
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: DrawnUi.Extensions.FloatingPointExtensions.IsGreater*
  commentId: Overload:DrawnUi.Extensions.FloatingPointExtensions.IsGreater
  href: DrawnUi.Extensions.FloatingPointExtensions.html#DrawnUi_Extensions_FloatingPointExtensions_IsGreater_System_Double_System_Double_System_Double_
  name: IsGreater
  nameWithType: FloatingPointExtensions.IsGreater
  fullName: DrawnUi.Extensions.FloatingPointExtensions.IsGreater
- uid: DrawnUi.Extensions.FloatingPointExtensions.IsEqual*
  commentId: Overload:DrawnUi.Extensions.FloatingPointExtensions.IsEqual
  href: DrawnUi.Extensions.FloatingPointExtensions.html#DrawnUi_Extensions_FloatingPointExtensions_IsEqual_System_Double_System_Double_System_Double_
  name: IsEqual
  nameWithType: FloatingPointExtensions.IsEqual
  fullName: DrawnUi.Extensions.FloatingPointExtensions.IsEqual
