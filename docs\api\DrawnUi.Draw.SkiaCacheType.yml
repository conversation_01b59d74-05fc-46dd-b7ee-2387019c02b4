### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.SkiaCacheType
  commentId: T:DrawnUi.Draw.SkiaCacheType
  id: SkiaCacheType
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.SkiaCacheType.GPU
  - DrawnUi.Draw.SkiaCacheType.Image
  - DrawnUi.Draw.SkiaCacheType.ImageComposite
  - DrawnUi.Draw.SkiaCacheType.ImageDoubleBuffered
  - DrawnUi.Draw.SkiaCacheType.None
  - DrawnUi.Draw.SkiaCacheType.Operations
  - DrawnUi.Draw.SkiaCacheType.OperationsFull
  langs:
  - csharp
  - vb
  name: SkiaCacheType
  nameWithType: SkiaCacheType
  fullName: DrawnUi.Draw.SkiaCacheType
  type: Enum
  source:
    remote:
      path: src/Shared/Draw/Cache/SkiaCacheType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: SkiaCacheType
    path: ../src/Shared/Draw/Cache/SkiaCacheType.cs
    startLine: 3
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public enum SkiaCacheType
    content.vb: Public Enum SkiaCacheType
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.SkiaCacheType.None
  commentId: F:DrawnUi.Draw.SkiaCacheType.None
  id: None
  parent: DrawnUi.Draw.SkiaCacheType
  langs:
  - csharp
  - vb
  name: None
  nameWithType: SkiaCacheType.None
  fullName: DrawnUi.Draw.SkiaCacheType.None
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Cache/SkiaCacheType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: None
    path: ../src/Shared/Draw/Cache/SkiaCacheType.cs
    startLine: 8
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: True and old school
  example: []
  syntax:
    content: None = 0
    return:
      type: DrawnUi.Draw.SkiaCacheType
- uid: DrawnUi.Draw.SkiaCacheType.Operations
  commentId: F:DrawnUi.Draw.SkiaCacheType.Operations
  id: Operations
  parent: DrawnUi.Draw.SkiaCacheType
  langs:
  - csharp
  - vb
  name: Operations
  nameWithType: SkiaCacheType.Operations
  fullName: DrawnUi.Draw.SkiaCacheType.Operations
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Cache/SkiaCacheType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Operations
    path: ../src/Shared/Draw/Cache/SkiaCacheType.cs
    startLine: 14
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: >-
    Create and reuse SKPicture. Try this first for labels, svg etc. 

    Do not use this when dropping shadows or with other effects, better use Bitmap.
  example: []
  syntax:
    content: Operations = 1
    return:
      type: DrawnUi.Draw.SkiaCacheType
- uid: DrawnUi.Draw.SkiaCacheType.OperationsFull
  commentId: F:DrawnUi.Draw.SkiaCacheType.OperationsFull
  id: OperationsFull
  parent: DrawnUi.Draw.SkiaCacheType
  langs:
  - csharp
  - vb
  name: OperationsFull
  nameWithType: SkiaCacheType.OperationsFull
  fullName: DrawnUi.Draw.SkiaCacheType.OperationsFull
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Cache/SkiaCacheType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: OperationsFull
    path: ../src/Shared/Draw/Cache/SkiaCacheType.cs
    startLine: 21
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: >-
    EXPERIMENTAL! May be bugged. Create and reuse SKPicture all over the canvas ignoring clipping.

    Try this first for labels, svg etc. 

    Do not use this when dropping shadows or with other effects, better use Bitmap.
  example: []
  syntax:
    content: OperationsFull = 2
    return:
      type: DrawnUi.Draw.SkiaCacheType
- uid: DrawnUi.Draw.SkiaCacheType.Image
  commentId: F:DrawnUi.Draw.SkiaCacheType.Image
  id: Image
  parent: DrawnUi.Draw.SkiaCacheType
  langs:
  - csharp
  - vb
  name: Image
  nameWithType: SkiaCacheType.Image
  fullName: DrawnUi.Draw.SkiaCacheType.Image
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Cache/SkiaCacheType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Image
    path: ../src/Shared/Draw/Cache/SkiaCacheType.cs
    startLine: 27
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: >-
    Will use simple SKBitmap cache type, will not use hardware acceleration.

    Slower but will work for sizes bigger than graphics memory if needed.
  example: []
  syntax:
    content: Image = 3
    return:
      type: DrawnUi.Draw.SkiaCacheType
- uid: DrawnUi.Draw.SkiaCacheType.ImageDoubleBuffered
  commentId: F:DrawnUi.Draw.SkiaCacheType.ImageDoubleBuffered
  id: ImageDoubleBuffered
  parent: DrawnUi.Draw.SkiaCacheType
  langs:
  - csharp
  - vb
  name: ImageDoubleBuffered
  nameWithType: SkiaCacheType.ImageDoubleBuffered
  fullName: DrawnUi.Draw.SkiaCacheType.ImageDoubleBuffered
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Cache/SkiaCacheType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ImageDoubleBuffered
    path: ../src/Shared/Draw/Cache/SkiaCacheType.cs
    startLine: 32
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Using `Image` cache type with double buffering. Will display a previous cache while rendering the new one in background, thus not slowing scrolling etc.
  example: []
  syntax:
    content: ImageDoubleBuffered = 4
    return:
      type: DrawnUi.Draw.SkiaCacheType
- uid: DrawnUi.Draw.SkiaCacheType.ImageComposite
  commentId: F:DrawnUi.Draw.SkiaCacheType.ImageComposite
  id: ImageComposite
  parent: DrawnUi.Draw.SkiaCacheType
  langs:
  - csharp
  - vb
  name: ImageComposite
  nameWithType: SkiaCacheType.ImageComposite
  fullName: DrawnUi.Draw.SkiaCacheType.ImageComposite
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Cache/SkiaCacheType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ImageComposite
    path: ../src/Shared/Draw/Cache/SkiaCacheType.cs
    startLine: 38
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Would receive the invalidated area rectangle, then redraw the previous cache but clipped to exclude the dirty area, then would re-create the dirty area and draw it clipped inside the dirty rectangle. This is useful for layouts with many children, like scroll content etc, but useless for non-containers.
  example: []
  syntax:
    content: ImageComposite = 5
    return:
      type: DrawnUi.Draw.SkiaCacheType
- uid: DrawnUi.Draw.SkiaCacheType.GPU
  commentId: F:DrawnUi.Draw.SkiaCacheType.GPU
  id: GPU
  parent: DrawnUi.Draw.SkiaCacheType
  langs:
  - csharp
  - vb
  name: GPU
  nameWithType: SkiaCacheType.GPU
  fullName: DrawnUi.Draw.SkiaCacheType.GPU
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Cache/SkiaCacheType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: GPU
    path: ../src/Shared/Draw/Cache/SkiaCacheType.cs
    startLine: 45
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: >-
    The cached surface will use the same graphic context as your hardware-accelerated canvas.

    This kind of cache will not apply Opacity as not all platforms support transparency for hardware accelerated layer.

    Will fallback to simple Image cache type if hardware acceleration is not available.
  example: []
  syntax:
    content: GPU = 6
    return:
      type: DrawnUi.Draw.SkiaCacheType
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.SkiaCacheType
  commentId: T:DrawnUi.Draw.SkiaCacheType
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaCacheType.html
  name: SkiaCacheType
  nameWithType: SkiaCacheType
  fullName: DrawnUi.Draw.SkiaCacheType
