### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.ScaledRect
  commentId: T:DrawnUi.Draw.ScaledRect
  id: ScaledRect
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.ScaledRect.#ctor
  - DrawnUi.Draw.ScaledRect.Clone(SkiaSharp.SKRect)
  - DrawnUi.Draw.ScaledRect.Compare(DrawnUi.Draw.DrawingRect)
  - DrawnUi.Draw.ScaledRect.Compare(DrawnUi.Draw.ScaledRect)
  - DrawnUi.Draw.ScaledRect.FromPixels(SkiaSharp.SKRect,System.Single)
  - DrawnUi.Draw.ScaledRect.FromUnits(SkiaSharp.SKRect,System.Single)
  - DrawnUi.Draw.ScaledRect.Pixels
  - DrawnUi.Draw.ScaledRect.Scale
  - DrawnUi.Draw.ScaledRect.ToString
  - DrawnUi.Draw.ScaledRect.Units
  langs:
  - csharp
  - vb
  name: ScaledRect
  nameWithType: ScaledRect
  fullName: DrawnUi.Draw.ScaledRect
  type: Struct
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ScaledRect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ScaledRect
    path: ../src/Shared/Draw/Internals/Models/ScaledRect.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public struct ScaledRect
    content.vb: Public Structure ScaledRect
  inheritedMembers:
  - System.ValueType.Equals(System.Object)
  - System.ValueType.GetHashCode
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetType
  - System.Object.ReferenceEquals(System.Object,System.Object)
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.ScaledRect.ToString
  commentId: M:DrawnUi.Draw.ScaledRect.ToString
  id: ToString
  parent: DrawnUi.Draw.ScaledRect
  langs:
  - csharp
  - vb
  name: ToString()
  nameWithType: ScaledRect.ToString()
  fullName: DrawnUi.Draw.ScaledRect.ToString()
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ScaledRect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ToString
    path: ../src/Shared/Draw/Internals/Models/ScaledRect.cs
    startLine: 5
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Returns the fully qualified type name of this instance.
  example: []
  syntax:
    content: public override string ToString()
    return:
      type: System.String
      description: The fully qualified type name.
    content.vb: Public Overrides Function ToString() As String
  overridden: System.ValueType.ToString
  overload: DrawnUi.Draw.ScaledRect.ToString*
- uid: DrawnUi.Draw.ScaledRect.#ctor
  commentId: M:DrawnUi.Draw.ScaledRect.#ctor
  id: '#ctor'
  parent: DrawnUi.Draw.ScaledRect
  langs:
  - csharp
  - vb
  name: ScaledRect()
  nameWithType: ScaledRect.ScaledRect()
  fullName: DrawnUi.Draw.ScaledRect.ScaledRect()
  type: Constructor
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ScaledRect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Shared/Draw/Internals/Models/ScaledRect.cs
    startLine: 10
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public ScaledRect()
    content.vb: Public Sub New()
  overload: DrawnUi.Draw.ScaledRect.#ctor*
  nameWithType.vb: ScaledRect.New()
  fullName.vb: DrawnUi.Draw.ScaledRect.New()
  name.vb: New()
- uid: DrawnUi.Draw.ScaledRect.Scale
  commentId: P:DrawnUi.Draw.ScaledRect.Scale
  id: Scale
  parent: DrawnUi.Draw.ScaledRect
  langs:
  - csharp
  - vb
  name: Scale
  nameWithType: ScaledRect.Scale
  fullName: DrawnUi.Draw.ScaledRect.Scale
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ScaledRect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Scale
    path: ../src/Shared/Draw/Internals/Models/ScaledRect.cs
    startLine: 16
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public float Scale { readonly get; set; }
    parameters: []
    return:
      type: System.Single
    content.vb: Public Property Scale As Single
  overload: DrawnUi.Draw.ScaledRect.Scale*
- uid: DrawnUi.Draw.ScaledRect.Units
  commentId: P:DrawnUi.Draw.ScaledRect.Units
  id: Units
  parent: DrawnUi.Draw.ScaledRect
  langs:
  - csharp
  - vb
  name: Units
  nameWithType: ScaledRect.Units
  fullName: DrawnUi.Draw.ScaledRect.Units
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ScaledRect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Units
    path: ../src/Shared/Draw/Internals/Models/ScaledRect.cs
    startLine: 17
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public SKRect Units { readonly get; set; }
    parameters: []
    return:
      type: SkiaSharp.SKRect
    content.vb: Public Property Units As SKRect
  overload: DrawnUi.Draw.ScaledRect.Units*
- uid: DrawnUi.Draw.ScaledRect.Pixels
  commentId: P:DrawnUi.Draw.ScaledRect.Pixels
  id: Pixels
  parent: DrawnUi.Draw.ScaledRect
  langs:
  - csharp
  - vb
  name: Pixels
  nameWithType: ScaledRect.Pixels
  fullName: DrawnUi.Draw.ScaledRect.Pixels
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ScaledRect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Pixels
    path: ../src/Shared/Draw/Internals/Models/ScaledRect.cs
    startLine: 18
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public SKRect Pixels { readonly get; set; }
    parameters: []
    return:
      type: SkiaSharp.SKRect
    content.vb: Public Property Pixels As SKRect
  overload: DrawnUi.Draw.ScaledRect.Pixels*
- uid: DrawnUi.Draw.ScaledRect.Compare(DrawnUi.Draw.DrawingRect)
  commentId: M:DrawnUi.Draw.ScaledRect.Compare(DrawnUi.Draw.DrawingRect)
  id: Compare(DrawnUi.Draw.DrawingRect)
  parent: DrawnUi.Draw.ScaledRect
  langs:
  - csharp
  - vb
  name: Compare(DrawingRect)
  nameWithType: ScaledRect.Compare(DrawingRect)
  fullName: DrawnUi.Draw.ScaledRect.Compare(DrawnUi.Draw.DrawingRect)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ScaledRect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Compare
    path: ../src/Shared/Draw/Internals/Models/ScaledRect.cs
    startLine: 20
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public bool Compare(DrawingRect b)
    parameters:
    - id: b
      type: DrawnUi.Draw.DrawingRect
    return:
      type: System.Boolean
    content.vb: Public Function Compare(b As DrawingRect) As Boolean
  overload: DrawnUi.Draw.ScaledRect.Compare*
- uid: DrawnUi.Draw.ScaledRect.Compare(DrawnUi.Draw.ScaledRect)
  commentId: M:DrawnUi.Draw.ScaledRect.Compare(DrawnUi.Draw.ScaledRect)
  id: Compare(DrawnUi.Draw.ScaledRect)
  parent: DrawnUi.Draw.ScaledRect
  langs:
  - csharp
  - vb
  name: Compare(ScaledRect)
  nameWithType: ScaledRect.Compare(ScaledRect)
  fullName: DrawnUi.Draw.ScaledRect.Compare(DrawnUi.Draw.ScaledRect)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ScaledRect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Compare
    path: ../src/Shared/Draw/Internals/Models/ScaledRect.cs
    startLine: 25
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public bool Compare(ScaledRect b)
    parameters:
    - id: b
      type: DrawnUi.Draw.ScaledRect
    return:
      type: System.Boolean
    content.vb: Public Function Compare(b As ScaledRect) As Boolean
  overload: DrawnUi.Draw.ScaledRect.Compare*
- uid: DrawnUi.Draw.ScaledRect.FromUnits(SkiaSharp.SKRect,System.Single)
  commentId: M:DrawnUi.Draw.ScaledRect.FromUnits(SkiaSharp.SKRect,System.Single)
  id: FromUnits(SkiaSharp.SKRect,System.Single)
  parent: DrawnUi.Draw.ScaledRect
  langs:
  - csharp
  - vb
  name: FromUnits(SKRect, float)
  nameWithType: ScaledRect.FromUnits(SKRect, float)
  fullName: DrawnUi.Draw.ScaledRect.FromUnits(SkiaSharp.SKRect, float)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ScaledRect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: FromUnits
    path: ../src/Shared/Draw/Internals/Models/ScaledRect.cs
    startLine: 30
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static ScaledRect FromUnits(SKRect rect, float scale)
    parameters:
    - id: rect
      type: SkiaSharp.SKRect
    - id: scale
      type: System.Single
    return:
      type: DrawnUi.Draw.ScaledRect
    content.vb: Public Shared Function FromUnits(rect As SKRect, scale As Single) As ScaledRect
  overload: DrawnUi.Draw.ScaledRect.FromUnits*
  nameWithType.vb: ScaledRect.FromUnits(SKRect, Single)
  fullName.vb: DrawnUi.Draw.ScaledRect.FromUnits(SkiaSharp.SKRect, Single)
  name.vb: FromUnits(SKRect, Single)
- uid: DrawnUi.Draw.ScaledRect.FromPixels(SkiaSharp.SKRect,System.Single)
  commentId: M:DrawnUi.Draw.ScaledRect.FromPixels(SkiaSharp.SKRect,System.Single)
  id: FromPixels(SkiaSharp.SKRect,System.Single)
  parent: DrawnUi.Draw.ScaledRect
  langs:
  - csharp
  - vb
  name: FromPixels(SKRect, float)
  nameWithType: ScaledRect.FromPixels(SKRect, float)
  fullName: DrawnUi.Draw.ScaledRect.FromPixels(SkiaSharp.SKRect, float)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ScaledRect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: FromPixels
    path: ../src/Shared/Draw/Internals/Models/ScaledRect.cs
    startLine: 41
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static ScaledRect FromPixels(SKRect rect, float scale)
    parameters:
    - id: rect
      type: SkiaSharp.SKRect
    - id: scale
      type: System.Single
    return:
      type: DrawnUi.Draw.ScaledRect
    content.vb: Public Shared Function FromPixels(rect As SKRect, scale As Single) As ScaledRect
  overload: DrawnUi.Draw.ScaledRect.FromPixels*
  nameWithType.vb: ScaledRect.FromPixels(SKRect, Single)
  fullName.vb: DrawnUi.Draw.ScaledRect.FromPixels(SkiaSharp.SKRect, Single)
  name.vb: FromPixels(SKRect, Single)
- uid: DrawnUi.Draw.ScaledRect.Clone(SkiaSharp.SKRect)
  commentId: M:DrawnUi.Draw.ScaledRect.Clone(SkiaSharp.SKRect)
  id: Clone(SkiaSharp.SKRect)
  parent: DrawnUi.Draw.ScaledRect
  langs:
  - csharp
  - vb
  name: Clone(SKRect)
  nameWithType: ScaledRect.Clone(SKRect)
  fullName: DrawnUi.Draw.ScaledRect.Clone(SkiaSharp.SKRect)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ScaledRect.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Clone
    path: ../src/Shared/Draw/Internals/Models/ScaledRect.cs
    startLine: 52
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static SKRect Clone(SKRect rect)
    parameters:
    - id: rect
      type: SkiaSharp.SKRect
    return:
      type: SkiaSharp.SKRect
    content.vb: Public Shared Function Clone(rect As SKRect) As SKRect
  overload: DrawnUi.Draw.ScaledRect.Clone*
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.ValueType.Equals(System.Object)
  commentId: M:System.ValueType.Equals(System.Object)
  parent: System.ValueType
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype.equals
  name: Equals(object)
  nameWithType: ValueType.Equals(object)
  fullName: System.ValueType.Equals(object)
  nameWithType.vb: ValueType.Equals(Object)
  fullName.vb: System.ValueType.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.ValueType.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.equals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.ValueType.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.equals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.ValueType.GetHashCode
  commentId: M:System.ValueType.GetHashCode
  parent: System.ValueType
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype.gethashcode
  name: GetHashCode()
  nameWithType: ValueType.GetHashCode()
  fullName: System.ValueType.GetHashCode()
  spec.csharp:
  - uid: System.ValueType.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.ValueType.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.gethashcode
  - name: (
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.ValueType
  commentId: T:System.ValueType
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype
  name: ValueType
  nameWithType: ValueType
  fullName: System.ValueType
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: System.ValueType.ToString
  commentId: M:System.ValueType.ToString
  parent: System.ValueType
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype.tostring
  name: ToString()
  nameWithType: ValueType.ToString()
  fullName: System.ValueType.ToString()
  spec.csharp:
  - uid: System.ValueType.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.ValueType.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.tostring
  - name: (
  - name: )
- uid: DrawnUi.Draw.ScaledRect.ToString*
  commentId: Overload:DrawnUi.Draw.ScaledRect.ToString
  href: DrawnUi.Draw.ScaledRect.html#DrawnUi_Draw_ScaledRect_ToString
  name: ToString
  nameWithType: ScaledRect.ToString
  fullName: DrawnUi.Draw.ScaledRect.ToString
- uid: System.String
  commentId: T:System.String
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.string
  name: string
  nameWithType: string
  fullName: string
  nameWithType.vb: String
  fullName.vb: String
  name.vb: String
- uid: DrawnUi.Draw.ScaledRect.#ctor*
  commentId: Overload:DrawnUi.Draw.ScaledRect.#ctor
  href: DrawnUi.Draw.ScaledRect.html#DrawnUi_Draw_ScaledRect__ctor
  name: ScaledRect
  nameWithType: ScaledRect.ScaledRect
  fullName: DrawnUi.Draw.ScaledRect.ScaledRect
  nameWithType.vb: ScaledRect.New
  fullName.vb: DrawnUi.Draw.ScaledRect.New
  name.vb: New
- uid: DrawnUi.Draw.ScaledRect.Scale*
  commentId: Overload:DrawnUi.Draw.ScaledRect.Scale
  href: DrawnUi.Draw.ScaledRect.html#DrawnUi_Draw_ScaledRect_Scale
  name: Scale
  nameWithType: ScaledRect.Scale
  fullName: DrawnUi.Draw.ScaledRect.Scale
- uid: System.Single
  commentId: T:System.Single
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.single
  name: float
  nameWithType: float
  fullName: float
  nameWithType.vb: Single
  fullName.vb: Single
  name.vb: Single
- uid: DrawnUi.Draw.ScaledRect.Units*
  commentId: Overload:DrawnUi.Draw.ScaledRect.Units
  href: DrawnUi.Draw.ScaledRect.html#DrawnUi_Draw_ScaledRect_Units
  name: Units
  nameWithType: ScaledRect.Units
  fullName: DrawnUi.Draw.ScaledRect.Units
- uid: SkiaSharp.SKRect
  commentId: T:SkiaSharp.SKRect
  parent: SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.skrect
  name: SKRect
  nameWithType: SKRect
  fullName: SkiaSharp.SKRect
- uid: SkiaSharp
  commentId: N:SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp
  name: SkiaSharp
  nameWithType: SkiaSharp
  fullName: SkiaSharp
- uid: DrawnUi.Draw.ScaledRect.Pixels*
  commentId: Overload:DrawnUi.Draw.ScaledRect.Pixels
  href: DrawnUi.Draw.ScaledRect.html#DrawnUi_Draw_ScaledRect_Pixels
  name: Pixels
  nameWithType: ScaledRect.Pixels
  fullName: DrawnUi.Draw.ScaledRect.Pixels
- uid: DrawnUi.Draw.ScaledRect.Compare*
  commentId: Overload:DrawnUi.Draw.ScaledRect.Compare
  href: DrawnUi.Draw.ScaledRect.html#DrawnUi_Draw_ScaledRect_Compare_DrawnUi_Draw_DrawingRect_
  name: Compare
  nameWithType: ScaledRect.Compare
  fullName: DrawnUi.Draw.ScaledRect.Compare
- uid: DrawnUi.Draw.DrawingRect
  commentId: T:DrawnUi.Draw.DrawingRect
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.DrawingRect.html
  name: DrawingRect
  nameWithType: DrawingRect
  fullName: DrawnUi.Draw.DrawingRect
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: DrawnUi.Draw.ScaledRect
  commentId: T:DrawnUi.Draw.ScaledRect
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ScaledRect.html
  name: ScaledRect
  nameWithType: ScaledRect
  fullName: DrawnUi.Draw.ScaledRect
- uid: DrawnUi.Draw.ScaledRect.FromUnits*
  commentId: Overload:DrawnUi.Draw.ScaledRect.FromUnits
  href: DrawnUi.Draw.ScaledRect.html#DrawnUi_Draw_ScaledRect_FromUnits_SkiaSharp_SKRect_System_Single_
  name: FromUnits
  nameWithType: ScaledRect.FromUnits
  fullName: DrawnUi.Draw.ScaledRect.FromUnits
- uid: DrawnUi.Draw.ScaledRect.FromPixels*
  commentId: Overload:DrawnUi.Draw.ScaledRect.FromPixels
  href: DrawnUi.Draw.ScaledRect.html#DrawnUi_Draw_ScaledRect_FromPixels_SkiaSharp_SKRect_System_Single_
  name: FromPixels
  nameWithType: ScaledRect.FromPixels
  fullName: DrawnUi.Draw.ScaledRect.FromPixels
- uid: DrawnUi.Draw.ScaledRect.Clone*
  commentId: Overload:DrawnUi.Draw.ScaledRect.Clone
  href: DrawnUi.Draw.ScaledRect.html#DrawnUi_Draw_ScaledRect_Clone_SkiaSharp_SKRect_
  name: Clone
  nameWithType: ScaledRect.Clone
  fullName: DrawnUi.Draw.ScaledRect.Clone
