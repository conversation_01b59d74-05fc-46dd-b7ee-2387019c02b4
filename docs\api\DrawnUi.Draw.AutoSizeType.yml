### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.AutoSizeType
  commentId: T:DrawnUi.Draw.AutoSizeType
  id: AutoSizeType
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.AutoSizeType.FillHorizontal
  - DrawnUi.Draw.AutoSizeType.FillVertical
  - DrawnUi.Draw.AutoSizeType.FitFillHorizontal
  - DrawnUi.Draw.AutoSizeType.FitFillVertical
  - DrawnUi.Draw.AutoSizeType.FitHorizontal
  - DrawnUi.Draw.AutoSizeType.FitVertical
  - DrawnUi.Draw.AutoSizeType.None
  langs:
  - csharp
  - vb
  name: AutoSizeType
  nameWithType: AutoSizeType
  fullName: DrawnUi.Draw.AutoSizeType
  type: Enum
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/AutoSizeType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: AutoSizeType
    path: ../src/Shared/Draw/Internals/Enums/AutoSizeType.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public enum AutoSizeType
    content.vb: Public Enum AutoSizeType
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.AutoSizeType.None
  commentId: F:DrawnUi.Draw.AutoSizeType.None
  id: None
  parent: DrawnUi.Draw.AutoSizeType
  langs:
  - csharp
  - vb
  name: None
  nameWithType: AutoSizeType.None
  fullName: DrawnUi.Draw.AutoSizeType.None
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/AutoSizeType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: None
    path: ../src/Shared/Draw/Internals/Enums/AutoSizeType.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: None = 0
    return:
      type: DrawnUi.Draw.AutoSizeType
- uid: DrawnUi.Draw.AutoSizeType.FitFillHorizontal
  commentId: F:DrawnUi.Draw.AutoSizeType.FitFillHorizontal
  id: FitFillHorizontal
  parent: DrawnUi.Draw.AutoSizeType
  langs:
  - csharp
  - vb
  name: FitFillHorizontal
  nameWithType: AutoSizeType.FitFillHorizontal
  fullName: DrawnUi.Draw.AutoSizeType.FitFillHorizontal
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/AutoSizeType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: FitFillHorizontal
    path: ../src/Shared/Draw/Internals/Enums/AutoSizeType.cs
    startLine: 9
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: This might be faster than FitHorizontal or FillHorizontal for dynamically changing text
  example: []
  syntax:
    content: FitFillHorizontal = 1
    return:
      type: DrawnUi.Draw.AutoSizeType
- uid: DrawnUi.Draw.AutoSizeType.FitFillVertical
  commentId: F:DrawnUi.Draw.AutoSizeType.FitFillVertical
  id: FitFillVertical
  parent: DrawnUi.Draw.AutoSizeType
  langs:
  - csharp
  - vb
  name: FitFillVertical
  nameWithType: AutoSizeType.FitFillVertical
  fullName: DrawnUi.Draw.AutoSizeType.FitFillVertical
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/AutoSizeType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: FitFillVertical
    path: ../src/Shared/Draw/Internals/Enums/AutoSizeType.cs
    startLine: 14
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: This might be faster than FitVertical or FillVertical for dynamically changing text
  example: []
  syntax:
    content: FitFillVertical = 2
    return:
      type: DrawnUi.Draw.AutoSizeType
- uid: DrawnUi.Draw.AutoSizeType.FitHorizontal
  commentId: F:DrawnUi.Draw.AutoSizeType.FitHorizontal
  id: FitHorizontal
  parent: DrawnUi.Draw.AutoSizeType
  langs:
  - csharp
  - vb
  name: FitHorizontal
  nameWithType: AutoSizeType.FitHorizontal
  fullName: DrawnUi.Draw.AutoSizeType.FitHorizontal
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/AutoSizeType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: FitHorizontal
    path: ../src/Shared/Draw/Internals/Enums/AutoSizeType.cs
    startLine: 19
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: todo FIX NOT WORKING!!! If you have dynamically changing text think about using FitFillHorizontal instead
  example: []
  syntax:
    content: FitHorizontal = 3
    return:
      type: DrawnUi.Draw.AutoSizeType
- uid: DrawnUi.Draw.AutoSizeType.FillHorizontal
  commentId: F:DrawnUi.Draw.AutoSizeType.FillHorizontal
  id: FillHorizontal
  parent: DrawnUi.Draw.AutoSizeType
  langs:
  - csharp
  - vb
  name: FillHorizontal
  nameWithType: AutoSizeType.FillHorizontal
  fullName: DrawnUi.Draw.AutoSizeType.FillHorizontal
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/AutoSizeType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: FillHorizontal
    path: ../src/Shared/Draw/Internals/Enums/AutoSizeType.cs
    startLine: 24
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: If you have dynamically changing text think about using FitFillHorizontal instead
  example: []
  syntax:
    content: FillHorizontal = 4
    return:
      type: DrawnUi.Draw.AutoSizeType
- uid: DrawnUi.Draw.AutoSizeType.FitVertical
  commentId: F:DrawnUi.Draw.AutoSizeType.FitVertical
  id: FitVertical
  parent: DrawnUi.Draw.AutoSizeType
  langs:
  - csharp
  - vb
  name: FitVertical
  nameWithType: AutoSizeType.FitVertical
  fullName: DrawnUi.Draw.AutoSizeType.FitVertical
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/AutoSizeType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: FitVertical
    path: ../src/Shared/Draw/Internals/Enums/AutoSizeType.cs
    startLine: 29
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: If you have dynamically changing text think about using FitFillVertical instead
  example: []
  syntax:
    content: FitVertical = 5
    return:
      type: DrawnUi.Draw.AutoSizeType
- uid: DrawnUi.Draw.AutoSizeType.FillVertical
  commentId: F:DrawnUi.Draw.AutoSizeType.FillVertical
  id: FillVertical
  parent: DrawnUi.Draw.AutoSizeType
  langs:
  - csharp
  - vb
  name: FillVertical
  nameWithType: AutoSizeType.FillVertical
  fullName: DrawnUi.Draw.AutoSizeType.FillVertical
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/AutoSizeType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: FillVertical
    path: ../src/Shared/Draw/Internals/Enums/AutoSizeType.cs
    startLine: 35
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: If you have dynamically changing text think about using FitFillVertical instead
  example: []
  syntax:
    content: FillVertical = 6
    return:
      type: DrawnUi.Draw.AutoSizeType
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.AutoSizeType
  commentId: T:DrawnUi.Draw.AutoSizeType
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.AutoSizeType.html
  name: AutoSizeType
  nameWithType: AutoSizeType
  fullName: DrawnUi.Draw.AutoSizeType
