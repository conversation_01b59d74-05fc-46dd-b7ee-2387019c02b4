### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.SpringExtensions
  commentId: T:DrawnUi.Draw.SpringExtensions
  id: SpringExtensions
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.SpringExtensions.Beta(DrawnUi.Infrastructure.Spring)
  - DrawnUi.Draw.SpringExtensions.DampedNaturalFrequency(DrawnUi.Infrastructure.Spring)
  - DrawnUi.Draw.SpringExtensions.Damping(DrawnUi.Infrastructure.Spring)
  langs:
  - csharp
  - vb
  name: SpringExtensions
  nameWithType: SpringExtensions
  fullName: DrawnUi.Draw.SpringExtensions
  type: Class
  source:
    remote:
      path: src/Shared/Features/Animations/Extensions/SpringExtensions.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: SpringExtensions
    path: ../src/Shared/Features/Animations/Extensions/SpringExtensions.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static class SpringExtensions
    content.vb: Public Module SpringExtensions
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
- uid: DrawnUi.Draw.SpringExtensions.Damping(DrawnUi.Infrastructure.Spring)
  commentId: M:DrawnUi.Draw.SpringExtensions.Damping(DrawnUi.Infrastructure.Spring)
  id: Damping(DrawnUi.Infrastructure.Spring)
  isExtensionMethod: true
  parent: DrawnUi.Draw.SpringExtensions
  langs:
  - csharp
  - vb
  name: Damping(Spring)
  nameWithType: SpringExtensions.Damping(Spring)
  fullName: DrawnUi.Draw.SpringExtensions.Damping(DrawnUi.Infrastructure.Spring)
  type: Method
  source:
    remote:
      path: src/Shared/Features/Animations/Extensions/SpringExtensions.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Damping
    path: ../src/Shared/Features/Animations/Extensions/SpringExtensions.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static float Damping(this Spring spring)
    parameters:
    - id: spring
      type: DrawnUi.Infrastructure.Spring
    return:
      type: System.Single
    content.vb: Public Shared Function Damping(spring As Spring) As Single
  overload: DrawnUi.Draw.SpringExtensions.Damping*
- uid: DrawnUi.Draw.SpringExtensions.Beta(DrawnUi.Infrastructure.Spring)
  commentId: M:DrawnUi.Draw.SpringExtensions.Beta(DrawnUi.Infrastructure.Spring)
  id: Beta(DrawnUi.Infrastructure.Spring)
  isExtensionMethod: true
  parent: DrawnUi.Draw.SpringExtensions
  langs:
  - csharp
  - vb
  name: Beta(Spring)
  nameWithType: SpringExtensions.Beta(Spring)
  fullName: DrawnUi.Draw.SpringExtensions.Beta(DrawnUi.Infrastructure.Spring)
  type: Method
  source:
    remote:
      path: src/Shared/Features/Animations/Extensions/SpringExtensions.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Beta
    path: ../src/Shared/Features/Animations/Extensions/SpringExtensions.cs
    startLine: 9
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static float Beta(this Spring spring)
    parameters:
    - id: spring
      type: DrawnUi.Infrastructure.Spring
    return:
      type: System.Single
    content.vb: Public Shared Function Beta(spring As Spring) As Single
  overload: DrawnUi.Draw.SpringExtensions.Beta*
- uid: DrawnUi.Draw.SpringExtensions.DampedNaturalFrequency(DrawnUi.Infrastructure.Spring)
  commentId: M:DrawnUi.Draw.SpringExtensions.DampedNaturalFrequency(DrawnUi.Infrastructure.Spring)
  id: DampedNaturalFrequency(DrawnUi.Infrastructure.Spring)
  isExtensionMethod: true
  parent: DrawnUi.Draw.SpringExtensions
  langs:
  - csharp
  - vb
  name: DampedNaturalFrequency(Spring)
  nameWithType: SpringExtensions.DampedNaturalFrequency(Spring)
  fullName: DrawnUi.Draw.SpringExtensions.DampedNaturalFrequency(DrawnUi.Infrastructure.Spring)
  type: Method
  source:
    remote:
      path: src/Shared/Features/Animations/Extensions/SpringExtensions.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: DampedNaturalFrequency
    path: ../src/Shared/Features/Animations/Extensions/SpringExtensions.cs
    startLine: 14
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static float DampedNaturalFrequency(this Spring spring)
    parameters:
    - id: spring
      type: DrawnUi.Infrastructure.Spring
    return:
      type: System.Single
    content.vb: Public Shared Function DampedNaturalFrequency(spring As Spring) As Single
  overload: DrawnUi.Draw.SpringExtensions.DampedNaturalFrequency*
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Draw.SpringExtensions.Damping*
  commentId: Overload:DrawnUi.Draw.SpringExtensions.Damping
  href: DrawnUi.Draw.SpringExtensions.html#DrawnUi_Draw_SpringExtensions_Damping_DrawnUi_Infrastructure_Spring_
  name: Damping
  nameWithType: SpringExtensions.Damping
  fullName: DrawnUi.Draw.SpringExtensions.Damping
- uid: DrawnUi.Infrastructure.Spring
  commentId: T:DrawnUi.Infrastructure.Spring
  parent: DrawnUi.Infrastructure
  href: DrawnUi.Infrastructure.Spring.html
  name: Spring
  nameWithType: Spring
  fullName: DrawnUi.Infrastructure.Spring
- uid: System.Single
  commentId: T:System.Single
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.single
  name: float
  nameWithType: float
  fullName: float
  nameWithType.vb: Single
  fullName.vb: Single
  name.vb: Single
- uid: DrawnUi.Infrastructure
  commentId: N:DrawnUi.Infrastructure
  href: DrawnUi.html
  name: DrawnUi.Infrastructure
  nameWithType: DrawnUi.Infrastructure
  fullName: DrawnUi.Infrastructure
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Infrastructure
    name: Infrastructure
    href: DrawnUi.Infrastructure.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Infrastructure
    name: Infrastructure
    href: DrawnUi.Infrastructure.html
- uid: DrawnUi.Draw.SpringExtensions.Beta*
  commentId: Overload:DrawnUi.Draw.SpringExtensions.Beta
  href: DrawnUi.Draw.SpringExtensions.html#DrawnUi_Draw_SpringExtensions_Beta_DrawnUi_Infrastructure_Spring_
  name: Beta
  nameWithType: SpringExtensions.Beta
  fullName: DrawnUi.Draw.SpringExtensions.Beta
- uid: DrawnUi.Draw.SpringExtensions.DampedNaturalFrequency*
  commentId: Overload:DrawnUi.Draw.SpringExtensions.DampedNaturalFrequency
  href: DrawnUi.Draw.SpringExtensions.html#DrawnUi_Draw_SpringExtensions_DampedNaturalFrequency_DrawnUi_Infrastructure_Spring_
  name: DampedNaturalFrequency
  nameWithType: SpringExtensions.DampedNaturalFrequency
  fullName: DrawnUi.Draw.SpringExtensions.DampedNaturalFrequency
