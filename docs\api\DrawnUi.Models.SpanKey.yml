### YamlMime:ManagedReference
items:
- uid: DrawnUi.Models.SpanKey
  commentId: T:DrawnUi.Models.SpanKey
  id: SpanKey
  parent: DrawnUi.Models
  children:
  - DrawnUi.Models.SpanKey.#ctor(System.Int32,System.Int32,System.Boolean)
  - DrawnUi.Models.SpanKey.IsColumn
  - DrawnUi.Models.SpanKey.Length
  - DrawnUi.Models.SpanKey.Start
  langs:
  - csharp
  - vb
  name: SpanKey
  nameWithType: SpanKey
  fullName: DrawnUi.Models.SpanKey
  type: Class
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/SpanKey.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: SpanKey
    path: ../src/Shared/Draw/Internals/Models/SpanKey.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Models
  syntax:
    content: 'public record SpanKey : IEquatable<SpanKey>'
    content.vb: Public Class SpanKey Implements IEquatable(Of SpanKey)
  inheritance:
  - System.Object
  implements:
  - System.IEquatable{DrawnUi.Models.SpanKey}
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Models.SpanKey.#ctor(System.Int32,System.Int32,System.Boolean)
  commentId: M:DrawnUi.Models.SpanKey.#ctor(System.Int32,System.Int32,System.Boolean)
  id: '#ctor(System.Int32,System.Int32,System.Boolean)'
  parent: DrawnUi.Models.SpanKey
  langs:
  - csharp
  - vb
  name: SpanKey(int, int, bool)
  nameWithType: SpanKey.SpanKey(int, int, bool)
  fullName: DrawnUi.Models.SpanKey.SpanKey(int, int, bool)
  type: Constructor
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/SpanKey.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Shared/Draw/Internals/Models/SpanKey.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Models
  syntax:
    content: public SpanKey(int Start, int Length, bool IsColumn)
    parameters:
    - id: Start
      type: System.Int32
    - id: Length
      type: System.Int32
    - id: IsColumn
      type: System.Boolean
    content.vb: Public Sub New(Start As Integer, Length As Integer, IsColumn As Boolean)
  overload: DrawnUi.Models.SpanKey.#ctor*
  nameWithType.vb: SpanKey.New(Integer, Integer, Boolean)
  fullName.vb: DrawnUi.Models.SpanKey.New(Integer, Integer, Boolean)
  name.vb: New(Integer, Integer, Boolean)
- uid: DrawnUi.Models.SpanKey.Start
  commentId: P:DrawnUi.Models.SpanKey.Start
  id: Start
  parent: DrawnUi.Models.SpanKey
  langs:
  - csharp
  - vb
  name: Start
  nameWithType: SpanKey.Start
  fullName: DrawnUi.Models.SpanKey.Start
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/SpanKey.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Start
    path: ../src/Shared/Draw/Internals/Models/SpanKey.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Models
  syntax:
    content: public int Start { get; init; }
    parameters: []
    return:
      type: System.Int32
    content.vb: Public Property Start As Integer
  overload: DrawnUi.Models.SpanKey.Start*
- uid: DrawnUi.Models.SpanKey.Length
  commentId: P:DrawnUi.Models.SpanKey.Length
  id: Length
  parent: DrawnUi.Models.SpanKey
  langs:
  - csharp
  - vb
  name: Length
  nameWithType: SpanKey.Length
  fullName: DrawnUi.Models.SpanKey.Length
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/SpanKey.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Length
    path: ../src/Shared/Draw/Internals/Models/SpanKey.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Models
  syntax:
    content: public int Length { get; init; }
    parameters: []
    return:
      type: System.Int32
    content.vb: Public Property Length As Integer
  overload: DrawnUi.Models.SpanKey.Length*
- uid: DrawnUi.Models.SpanKey.IsColumn
  commentId: P:DrawnUi.Models.SpanKey.IsColumn
  id: IsColumn
  parent: DrawnUi.Models.SpanKey
  langs:
  - csharp
  - vb
  name: IsColumn
  nameWithType: SpanKey.IsColumn
  fullName: DrawnUi.Models.SpanKey.IsColumn
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/SpanKey.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IsColumn
    path: ../src/Shared/Draw/Internals/Models/SpanKey.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Models
  syntax:
    content: public bool IsColumn { get; init; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property IsColumn As Boolean
  overload: DrawnUi.Models.SpanKey.IsColumn*
references:
- uid: DrawnUi.Models
  commentId: N:DrawnUi.Models
  href: DrawnUi.html
  name: DrawnUi.Models
  nameWithType: DrawnUi.Models
  fullName: DrawnUi.Models
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Models
    name: Models
    href: DrawnUi.Models.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Models
    name: Models
    href: DrawnUi.Models.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.IEquatable{DrawnUi.Models.SpanKey}
  commentId: T:System.IEquatable{DrawnUi.Models.SpanKey}
  parent: System
  definition: System.IEquatable`1
  href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  name: IEquatable<SpanKey>
  nameWithType: IEquatable<SpanKey>
  fullName: System.IEquatable<DrawnUi.Models.SpanKey>
  nameWithType.vb: IEquatable(Of SpanKey)
  fullName.vb: System.IEquatable(Of DrawnUi.Models.SpanKey)
  name.vb: IEquatable(Of SpanKey)
  spec.csharp:
  - uid: System.IEquatable`1
    name: IEquatable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  - name: <
  - uid: DrawnUi.Models.SpanKey
    name: SpanKey
    href: DrawnUi.Models.SpanKey.html
  - name: '>'
  spec.vb:
  - uid: System.IEquatable`1
    name: IEquatable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Models.SpanKey
    name: SpanKey
    href: DrawnUi.Models.SpanKey.html
  - name: )
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: System.IEquatable`1
  commentId: T:System.IEquatable`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  name: IEquatable<T>
  nameWithType: IEquatable<T>
  fullName: System.IEquatable<T>
  nameWithType.vb: IEquatable(Of T)
  fullName.vb: System.IEquatable(Of T)
  name.vb: IEquatable(Of T)
  spec.csharp:
  - uid: System.IEquatable`1
    name: IEquatable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.IEquatable`1
    name: IEquatable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Models.SpanKey.#ctor*
  commentId: Overload:DrawnUi.Models.SpanKey.#ctor
  href: DrawnUi.Models.SpanKey.html#DrawnUi_Models_SpanKey__ctor_System_Int32_System_Int32_System_Boolean_
  name: SpanKey
  nameWithType: SpanKey.SpanKey
  fullName: DrawnUi.Models.SpanKey.SpanKey
  nameWithType.vb: SpanKey.New
  fullName.vb: DrawnUi.Models.SpanKey.New
  name.vb: New
- uid: System.Int32
  commentId: T:System.Int32
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.int32
  name: int
  nameWithType: int
  fullName: int
  nameWithType.vb: Integer
  fullName.vb: Integer
  name.vb: Integer
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: DrawnUi.Models.SpanKey.Start*
  commentId: Overload:DrawnUi.Models.SpanKey.Start
  href: DrawnUi.Models.SpanKey.html#DrawnUi_Models_SpanKey_Start
  name: Start
  nameWithType: SpanKey.Start
  fullName: DrawnUi.Models.SpanKey.Start
- uid: DrawnUi.Models.SpanKey.Length*
  commentId: Overload:DrawnUi.Models.SpanKey.Length
  href: DrawnUi.Models.SpanKey.html#DrawnUi_Models_SpanKey_Length
  name: Length
  nameWithType: SpanKey.Length
  fullName: DrawnUi.Models.SpanKey.Length
- uid: DrawnUi.Models.SpanKey.IsColumn*
  commentId: Overload:DrawnUi.Models.SpanKey.IsColumn
  href: DrawnUi.Models.SpanKey.html#DrawnUi_Models_SpanKey_IsColumn
  name: IsColumn
  nameWithType: SpanKey.IsColumn
  fullName: DrawnUi.Models.SpanKey.IsColumn
