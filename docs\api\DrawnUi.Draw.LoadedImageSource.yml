### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.LoadedImageSource
  commentId: T:DrawnUi.Draw.LoadedImageSource
  id: LoadedImageSource
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.LoadedImageSource.#ctor
  - DrawnUi.Draw.LoadedImageSource.#ctor(SkiaSharp.SKBitmap)
  - DrawnUi.Draw.LoadedImageSource.#ctor(SkiaSharp.SKImage)
  - DrawnUi.Draw.LoadedImageSource.#ctor(System.Byte[])
  - DrawnUi.Draw.LoadedImageSource.Bitmap
  - DrawnUi.Draw.LoadedImageSource.Clone
  - DrawnUi.Draw.LoadedImageSource.Dispose
  - DrawnUi.Draw.LoadedImageSource.GetBitmap
  - DrawnUi.Draw.LoadedImageSource.Height
  - DrawnUi.Draw.LoadedImageSource.Id
  - DrawnUi.Draw.LoadedImageSource.Image
  - DrawnUi.Draw.LoadedImageSource.IsDisposed
  - DrawnUi.Draw.LoadedImageSource.ProtectBitmapFromDispose
  - DrawnUi.Draw.LoadedImageSource.ProtectFromDispose
  - DrawnUi.Draw.LoadedImageSource.Width
  langs:
  - csharp
  - vb
  name: LoadedImageSource
  nameWithType: LoadedImageSource
  fullName: DrawnUi.Draw.LoadedImageSource
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Images/LoadedImageSource.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: LoadedImageSource
    path: ../src/Maui/DrawnUi/Draw/Images/LoadedImageSource.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: 'public class LoadedImageSource : IDisposable'
    content.vb: Public Class LoadedImageSource Implements IDisposable
  inheritance:
  - System.Object
  implements:
  - System.IDisposable
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.LoadedImageSource.Clone
  commentId: M:DrawnUi.Draw.LoadedImageSource.Clone
  id: Clone
  parent: DrawnUi.Draw.LoadedImageSource
  langs:
  - csharp
  - vb
  name: Clone()
  nameWithType: LoadedImageSource.Clone()
  fullName: DrawnUi.Draw.LoadedImageSource.Clone()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Images/LoadedImageSource.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Clone
    path: ../src/Maui/DrawnUi/Draw/Images/LoadedImageSource.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public LoadedImageSource Clone()
    return:
      type: DrawnUi.Draw.LoadedImageSource
    content.vb: Public Function Clone() As LoadedImageSource
  overload: DrawnUi.Draw.LoadedImageSource.Clone*
- uid: DrawnUi.Draw.LoadedImageSource.Id
  commentId: P:DrawnUi.Draw.LoadedImageSource.Id
  id: Id
  parent: DrawnUi.Draw.LoadedImageSource
  langs:
  - csharp
  - vb
  name: Id
  nameWithType: LoadedImageSource.Id
  fullName: DrawnUi.Draw.LoadedImageSource.Id
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Images/LoadedImageSource.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Id
    path: ../src/Maui/DrawnUi/Draw/Images/LoadedImageSource.cs
    startLine: 41
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public Guid Id { get; }
    parameters: []
    return:
      type: System.Guid
    content.vb: Public ReadOnly Property Id As Guid
  overload: DrawnUi.Draw.LoadedImageSource.Id*
- uid: DrawnUi.Draw.LoadedImageSource.ProtectFromDispose
  commentId: P:DrawnUi.Draw.LoadedImageSource.ProtectFromDispose
  id: ProtectFromDispose
  parent: DrawnUi.Draw.LoadedImageSource
  langs:
  - csharp
  - vb
  name: ProtectFromDispose
  nameWithType: LoadedImageSource.ProtectFromDispose
  fullName: DrawnUi.Draw.LoadedImageSource.ProtectFromDispose
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Images/LoadedImageSource.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ProtectFromDispose
    path: ../src/Maui/DrawnUi/Draw/Images/LoadedImageSource.cs
    startLine: 46
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: As this can be disposed automatically by the consuming control like SkiaImage etc we can manually prohibit this for cases this instance is used elsewhere.
  example: []
  syntax:
    content: public bool ProtectFromDispose { get; set; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property ProtectFromDispose As Boolean
  overload: DrawnUi.Draw.LoadedImageSource.ProtectFromDispose*
- uid: DrawnUi.Draw.LoadedImageSource.ProtectBitmapFromDispose
  commentId: P:DrawnUi.Draw.LoadedImageSource.ProtectBitmapFromDispose
  id: ProtectBitmapFromDispose
  parent: DrawnUi.Draw.LoadedImageSource
  langs:
  - csharp
  - vb
  name: ProtectBitmapFromDispose
  nameWithType: LoadedImageSource.ProtectBitmapFromDispose
  fullName: DrawnUi.Draw.LoadedImageSource.ProtectBitmapFromDispose
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Images/LoadedImageSource.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ProtectBitmapFromDispose
    path: ../src/Maui/DrawnUi/Draw/Images/LoadedImageSource.cs
    startLine: 51
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Should be set to true for loaded with SkiaImageManager.ReuseBitmaps
  example: []
  syntax:
    content: public bool ProtectBitmapFromDispose { get; set; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property ProtectBitmapFromDispose As Boolean
  overload: DrawnUi.Draw.LoadedImageSource.ProtectBitmapFromDispose*
- uid: DrawnUi.Draw.LoadedImageSource.Dispose
  commentId: M:DrawnUi.Draw.LoadedImageSource.Dispose
  id: Dispose
  parent: DrawnUi.Draw.LoadedImageSource
  langs:
  - csharp
  - vb
  name: Dispose()
  nameWithType: LoadedImageSource.Dispose()
  fullName: DrawnUi.Draw.LoadedImageSource.Dispose()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Images/LoadedImageSource.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Dispose
    path: ../src/Maui/DrawnUi/Draw/Images/LoadedImageSource.cs
    startLine: 53
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
  example: []
  syntax:
    content: public void Dispose()
    content.vb: Public Sub Dispose()
  overload: DrawnUi.Draw.LoadedImageSource.Dispose*
  implements:
  - System.IDisposable.Dispose
- uid: DrawnUi.Draw.LoadedImageSource.#ctor(SkiaSharp.SKBitmap)
  commentId: M:DrawnUi.Draw.LoadedImageSource.#ctor(SkiaSharp.SKBitmap)
  id: '#ctor(SkiaSharp.SKBitmap)'
  parent: DrawnUi.Draw.LoadedImageSource
  langs:
  - csharp
  - vb
  name: LoadedImageSource(SKBitmap)
  nameWithType: LoadedImageSource.LoadedImageSource(SKBitmap)
  fullName: DrawnUi.Draw.LoadedImageSource.LoadedImageSource(SkiaSharp.SKBitmap)
  type: Constructor
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Images/LoadedImageSource.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Maui/DrawnUi/Draw/Images/LoadedImageSource.cs
    startLine: 70
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public LoadedImageSource(SKBitmap bitmap)
    parameters:
    - id: bitmap
      type: SkiaSharp.SKBitmap
    content.vb: Public Sub New(bitmap As SKBitmap)
  overload: DrawnUi.Draw.LoadedImageSource.#ctor*
  nameWithType.vb: LoadedImageSource.New(SKBitmap)
  fullName.vb: DrawnUi.Draw.LoadedImageSource.New(SkiaSharp.SKBitmap)
  name.vb: New(SKBitmap)
- uid: DrawnUi.Draw.LoadedImageSource.#ctor(SkiaSharp.SKImage)
  commentId: M:DrawnUi.Draw.LoadedImageSource.#ctor(SkiaSharp.SKImage)
  id: '#ctor(SkiaSharp.SKImage)'
  parent: DrawnUi.Draw.LoadedImageSource
  langs:
  - csharp
  - vb
  name: LoadedImageSource(SKImage)
  nameWithType: LoadedImageSource.LoadedImageSource(SKImage)
  fullName: DrawnUi.Draw.LoadedImageSource.LoadedImageSource(SkiaSharp.SKImage)
  type: Constructor
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Images/LoadedImageSource.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Maui/DrawnUi/Draw/Images/LoadedImageSource.cs
    startLine: 75
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public LoadedImageSource(SKImage image)
    parameters:
    - id: image
      type: SkiaSharp.SKImage
    content.vb: Public Sub New(image As SKImage)
  overload: DrawnUi.Draw.LoadedImageSource.#ctor*
  nameWithType.vb: LoadedImageSource.New(SKImage)
  fullName.vb: DrawnUi.Draw.LoadedImageSource.New(SkiaSharp.SKImage)
  name.vb: New(SKImage)
- uid: DrawnUi.Draw.LoadedImageSource.#ctor(System.Byte[])
  commentId: M:DrawnUi.Draw.LoadedImageSource.#ctor(System.Byte[])
  id: '#ctor(System.Byte[])'
  parent: DrawnUi.Draw.LoadedImageSource
  langs:
  - csharp
  - vb
  name: LoadedImageSource(byte[])
  nameWithType: LoadedImageSource.LoadedImageSource(byte[])
  fullName: DrawnUi.Draw.LoadedImageSource.LoadedImageSource(byte[])
  type: Constructor
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Images/LoadedImageSource.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Maui/DrawnUi/Draw/Images/LoadedImageSource.cs
    startLine: 80
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public LoadedImageSource(byte[] bytes)
    parameters:
    - id: bytes
      type: System.Byte[]
    content.vb: Public Sub New(bytes As Byte())
  overload: DrawnUi.Draw.LoadedImageSource.#ctor*
  nameWithType.vb: LoadedImageSource.New(Byte())
  fullName.vb: DrawnUi.Draw.LoadedImageSource.New(Byte())
  name.vb: New(Byte())
- uid: DrawnUi.Draw.LoadedImageSource.#ctor
  commentId: M:DrawnUi.Draw.LoadedImageSource.#ctor
  id: '#ctor'
  parent: DrawnUi.Draw.LoadedImageSource
  langs:
  - csharp
  - vb
  name: LoadedImageSource()
  nameWithType: LoadedImageSource.LoadedImageSource()
  fullName: DrawnUi.Draw.LoadedImageSource.LoadedImageSource()
  type: Constructor
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Images/LoadedImageSource.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Maui/DrawnUi/Draw/Images/LoadedImageSource.cs
    startLine: 85
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public LoadedImageSource()
    content.vb: Public Sub New()
  overload: DrawnUi.Draw.LoadedImageSource.#ctor*
  nameWithType.vb: LoadedImageSource.New()
  fullName.vb: DrawnUi.Draw.LoadedImageSource.New()
  name.vb: New()
- uid: DrawnUi.Draw.LoadedImageSource.Width
  commentId: P:DrawnUi.Draw.LoadedImageSource.Width
  id: Width
  parent: DrawnUi.Draw.LoadedImageSource
  langs:
  - csharp
  - vb
  name: Width
  nameWithType: LoadedImageSource.Width
  fullName: DrawnUi.Draw.LoadedImageSource.Width
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Images/LoadedImageSource.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Width
    path: ../src/Maui/DrawnUi/Draw/Images/LoadedImageSource.cs
    startLine: 95
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public int Width { get; }
    parameters: []
    return:
      type: System.Int32
    content.vb: Public ReadOnly Property Width As Integer
  overload: DrawnUi.Draw.LoadedImageSource.Width*
- uid: DrawnUi.Draw.LoadedImageSource.Height
  commentId: P:DrawnUi.Draw.LoadedImageSource.Height
  id: Height
  parent: DrawnUi.Draw.LoadedImageSource
  langs:
  - csharp
  - vb
  name: Height
  nameWithType: LoadedImageSource.Height
  fullName: DrawnUi.Draw.LoadedImageSource.Height
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Images/LoadedImageSource.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Height
    path: ../src/Maui/DrawnUi/Draw/Images/LoadedImageSource.cs
    startLine: 102
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public int Height { get; }
    parameters: []
    return:
      type: System.Int32
    content.vb: Public ReadOnly Property Height As Integer
  overload: DrawnUi.Draw.LoadedImageSource.Height*
- uid: DrawnUi.Draw.LoadedImageSource.IsDisposed
  commentId: P:DrawnUi.Draw.LoadedImageSource.IsDisposed
  id: IsDisposed
  parent: DrawnUi.Draw.LoadedImageSource
  langs:
  - csharp
  - vb
  name: IsDisposed
  nameWithType: LoadedImageSource.IsDisposed
  fullName: DrawnUi.Draw.LoadedImageSource.IsDisposed
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Images/LoadedImageSource.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IsDisposed
    path: ../src/Maui/DrawnUi/Draw/Images/LoadedImageSource.cs
    startLine: 110
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public bool IsDisposed { get; protected set; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property IsDisposed As Boolean
  overload: DrawnUi.Draw.LoadedImageSource.IsDisposed*
- uid: DrawnUi.Draw.LoadedImageSource.Bitmap
  commentId: P:DrawnUi.Draw.LoadedImageSource.Bitmap
  id: Bitmap
  parent: DrawnUi.Draw.LoadedImageSource
  langs:
  - csharp
  - vb
  name: Bitmap
  nameWithType: LoadedImageSource.Bitmap
  fullName: DrawnUi.Draw.LoadedImageSource.Bitmap
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Images/LoadedImageSource.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Bitmap
    path: ../src/Maui/DrawnUi/Draw/Images/LoadedImageSource.cs
    startLine: 112
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public SKBitmap Bitmap { get; set; }
    parameters: []
    return:
      type: SkiaSharp.SKBitmap
    content.vb: Public Property Bitmap As SKBitmap
  overload: DrawnUi.Draw.LoadedImageSource.Bitmap*
- uid: DrawnUi.Draw.LoadedImageSource.Image
  commentId: P:DrawnUi.Draw.LoadedImageSource.Image
  id: Image
  parent: DrawnUi.Draw.LoadedImageSource
  langs:
  - csharp
  - vb
  name: Image
  nameWithType: LoadedImageSource.Image
  fullName: DrawnUi.Draw.LoadedImageSource.Image
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Images/LoadedImageSource.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Image
    path: ../src/Maui/DrawnUi/Draw/Images/LoadedImageSource.cs
    startLine: 142
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public SKImage Image { get; set; }
    parameters: []
    return:
      type: SkiaSharp.SKImage
    content.vb: Public Property Image As SKImage
  overload: DrawnUi.Draw.LoadedImageSource.Image*
- uid: DrawnUi.Draw.LoadedImageSource.GetBitmap
  commentId: M:DrawnUi.Draw.LoadedImageSource.GetBitmap
  id: GetBitmap
  parent: DrawnUi.Draw.LoadedImageSource
  langs:
  - csharp
  - vb
  name: GetBitmap()
  nameWithType: LoadedImageSource.GetBitmap()
  fullName: DrawnUi.Draw.LoadedImageSource.GetBitmap()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Images/LoadedImageSource.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: GetBitmap
    path: ../src/Maui/DrawnUi/Draw/Images/LoadedImageSource.cs
    startLine: 170
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public SKBitmap GetBitmap()
    return:
      type: SkiaSharp.SKBitmap
    content.vb: Public Function GetBitmap() As SKBitmap
  overload: DrawnUi.Draw.LoadedImageSource.GetBitmap*
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.IDisposable
  commentId: T:System.IDisposable
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.idisposable
  name: IDisposable
  nameWithType: IDisposable
  fullName: System.IDisposable
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.LoadedImageSource.Clone*
  commentId: Overload:DrawnUi.Draw.LoadedImageSource.Clone
  href: DrawnUi.Draw.LoadedImageSource.html#DrawnUi_Draw_LoadedImageSource_Clone
  name: Clone
  nameWithType: LoadedImageSource.Clone
  fullName: DrawnUi.Draw.LoadedImageSource.Clone
- uid: DrawnUi.Draw.LoadedImageSource
  commentId: T:DrawnUi.Draw.LoadedImageSource
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.LoadedImageSource.html
  name: LoadedImageSource
  nameWithType: LoadedImageSource
  fullName: DrawnUi.Draw.LoadedImageSource
- uid: DrawnUi.Draw.LoadedImageSource.Id*
  commentId: Overload:DrawnUi.Draw.LoadedImageSource.Id
  href: DrawnUi.Draw.LoadedImageSource.html#DrawnUi_Draw_LoadedImageSource_Id
  name: Id
  nameWithType: LoadedImageSource.Id
  fullName: DrawnUi.Draw.LoadedImageSource.Id
- uid: System.Guid
  commentId: T:System.Guid
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.guid
  name: Guid
  nameWithType: Guid
  fullName: System.Guid
- uid: DrawnUi.Draw.LoadedImageSource.ProtectFromDispose*
  commentId: Overload:DrawnUi.Draw.LoadedImageSource.ProtectFromDispose
  href: DrawnUi.Draw.LoadedImageSource.html#DrawnUi_Draw_LoadedImageSource_ProtectFromDispose
  name: ProtectFromDispose
  nameWithType: LoadedImageSource.ProtectFromDispose
  fullName: DrawnUi.Draw.LoadedImageSource.ProtectFromDispose
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: DrawnUi.Draw.LoadedImageSource.ProtectBitmapFromDispose*
  commentId: Overload:DrawnUi.Draw.LoadedImageSource.ProtectBitmapFromDispose
  href: DrawnUi.Draw.LoadedImageSource.html#DrawnUi_Draw_LoadedImageSource_ProtectBitmapFromDispose
  name: ProtectBitmapFromDispose
  nameWithType: LoadedImageSource.ProtectBitmapFromDispose
  fullName: DrawnUi.Draw.LoadedImageSource.ProtectBitmapFromDispose
- uid: DrawnUi.Draw.LoadedImageSource.Dispose*
  commentId: Overload:DrawnUi.Draw.LoadedImageSource.Dispose
  href: DrawnUi.Draw.LoadedImageSource.html#DrawnUi_Draw_LoadedImageSource_Dispose
  name: Dispose
  nameWithType: LoadedImageSource.Dispose
  fullName: DrawnUi.Draw.LoadedImageSource.Dispose
- uid: System.IDisposable.Dispose
  commentId: M:System.IDisposable.Dispose
  parent: System.IDisposable
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.idisposable.dispose
  name: Dispose()
  nameWithType: IDisposable.Dispose()
  fullName: System.IDisposable.Dispose()
  spec.csharp:
  - uid: System.IDisposable.Dispose
    name: Dispose
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.idisposable.dispose
  - name: (
  - name: )
  spec.vb:
  - uid: System.IDisposable.Dispose
    name: Dispose
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.idisposable.dispose
  - name: (
  - name: )
- uid: DrawnUi.Draw.LoadedImageSource.#ctor*
  commentId: Overload:DrawnUi.Draw.LoadedImageSource.#ctor
  href: DrawnUi.Draw.LoadedImageSource.html#DrawnUi_Draw_LoadedImageSource__ctor_SkiaSharp_SKBitmap_
  name: LoadedImageSource
  nameWithType: LoadedImageSource.LoadedImageSource
  fullName: DrawnUi.Draw.LoadedImageSource.LoadedImageSource
  nameWithType.vb: LoadedImageSource.New
  fullName.vb: DrawnUi.Draw.LoadedImageSource.New
  name.vb: New
- uid: SkiaSharp.SKBitmap
  commentId: T:SkiaSharp.SKBitmap
  parent: SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.skbitmap
  name: SKBitmap
  nameWithType: SKBitmap
  fullName: SkiaSharp.SKBitmap
- uid: SkiaSharp
  commentId: N:SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp
  name: SkiaSharp
  nameWithType: SkiaSharp
  fullName: SkiaSharp
- uid: SkiaSharp.SKImage
  commentId: T:SkiaSharp.SKImage
  parent: SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.skimage
  name: SKImage
  nameWithType: SKImage
  fullName: SkiaSharp.SKImage
- uid: System.Byte[]
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.byte
  name: byte[]
  nameWithType: byte[]
  fullName: byte[]
  nameWithType.vb: Byte()
  fullName.vb: Byte()
  name.vb: Byte()
  spec.csharp:
  - uid: System.Byte
    name: byte
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.byte
  - name: '['
  - name: ']'
  spec.vb:
  - uid: System.Byte
    name: Byte
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.byte
  - name: (
  - name: )
- uid: DrawnUi.Draw.LoadedImageSource.Width*
  commentId: Overload:DrawnUi.Draw.LoadedImageSource.Width
  href: DrawnUi.Draw.LoadedImageSource.html#DrawnUi_Draw_LoadedImageSource_Width
  name: Width
  nameWithType: LoadedImageSource.Width
  fullName: DrawnUi.Draw.LoadedImageSource.Width
- uid: System.Int32
  commentId: T:System.Int32
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.int32
  name: int
  nameWithType: int
  fullName: int
  nameWithType.vb: Integer
  fullName.vb: Integer
  name.vb: Integer
- uid: DrawnUi.Draw.LoadedImageSource.Height*
  commentId: Overload:DrawnUi.Draw.LoadedImageSource.Height
  href: DrawnUi.Draw.LoadedImageSource.html#DrawnUi_Draw_LoadedImageSource_Height
  name: Height
  nameWithType: LoadedImageSource.Height
  fullName: DrawnUi.Draw.LoadedImageSource.Height
- uid: DrawnUi.Draw.LoadedImageSource.IsDisposed*
  commentId: Overload:DrawnUi.Draw.LoadedImageSource.IsDisposed
  href: DrawnUi.Draw.LoadedImageSource.html#DrawnUi_Draw_LoadedImageSource_IsDisposed
  name: IsDisposed
  nameWithType: LoadedImageSource.IsDisposed
  fullName: DrawnUi.Draw.LoadedImageSource.IsDisposed
- uid: DrawnUi.Draw.LoadedImageSource.Bitmap*
  commentId: Overload:DrawnUi.Draw.LoadedImageSource.Bitmap
  href: DrawnUi.Draw.LoadedImageSource.html#DrawnUi_Draw_LoadedImageSource_Bitmap
  name: Bitmap
  nameWithType: LoadedImageSource.Bitmap
  fullName: DrawnUi.Draw.LoadedImageSource.Bitmap
- uid: DrawnUi.Draw.LoadedImageSource.Image*
  commentId: Overload:DrawnUi.Draw.LoadedImageSource.Image
  href: DrawnUi.Draw.LoadedImageSource.html#DrawnUi_Draw_LoadedImageSource_Image
  name: Image
  nameWithType: LoadedImageSource.Image
  fullName: DrawnUi.Draw.LoadedImageSource.Image
- uid: DrawnUi.Draw.LoadedImageSource.GetBitmap*
  commentId: Overload:DrawnUi.Draw.LoadedImageSource.GetBitmap
  href: DrawnUi.Draw.LoadedImageSource.html#DrawnUi_Draw_LoadedImageSource_GetBitmap
  name: GetBitmap
  nameWithType: LoadedImageSource.GetBitmap
  fullName: DrawnUi.Draw.LoadedImageSource.GetBitmap
