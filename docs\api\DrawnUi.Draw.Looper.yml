### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.Looper
  commentId: T:DrawnUi.Draw.Looper
  id: Looper
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.Looper.#ctor
  - DrawnUi.Draw.Looper.#ctor(System.Action)
  - DrawnUi.Draw.Looper.Cancel
  - DrawnUi.Draw.Looper.Dispose
  - DrawnUi.Draw.Looper.IsRunning
  - DrawnUi.Draw.Looper.OnFrame
  - DrawnUi.Draw.Looper.SetTargetFps(System.Int32)
  - DrawnUi.Draw.Looper.Start(System.Int32,System.Boolean)
  - DrawnUi.Draw.Looper.StartLegacyLooperAsync(System.Threading.CancellationToken)
  - DrawnUi.Draw.Looper.StartLooperAsync(System.Threading.CancellationToken)
  - DrawnUi.Draw.Looper.StartOnMainThread(System.Int32,System.Boolean)
  - DrawnUi.Draw.Looper.Stop
  langs:
  - csharp
  - vb
  name: Looper
  nameWithType: Looper
  fullName: DrawnUi.Draw.Looper
  type: Class
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/Looper.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Looper
    path: ../src/Shared/Draw/Internals/Helpers/Looper.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: 'public class Looper : IDisposable'
    content.vb: Public Class Looper Implements IDisposable
  inheritance:
  - System.Object
  implements:
  - System.IDisposable
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.Looper.#ctor
  commentId: M:DrawnUi.Draw.Looper.#ctor
  id: '#ctor'
  parent: DrawnUi.Draw.Looper
  langs:
  - csharp
  - vb
  name: Looper()
  nameWithType: Looper.Looper()
  fullName: DrawnUi.Draw.Looper.Looper()
  type: Constructor
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/Looper.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Shared/Draw/Internals/Helpers/Looper.cs
    startLine: 6
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public Looper()
    content.vb: Public Sub New()
  overload: DrawnUi.Draw.Looper.#ctor*
  nameWithType.vb: Looper.New()
  fullName.vb: DrawnUi.Draw.Looper.New()
  name.vb: New()
- uid: DrawnUi.Draw.Looper.#ctor(System.Action)
  commentId: M:DrawnUi.Draw.Looper.#ctor(System.Action)
  id: '#ctor(System.Action)'
  parent: DrawnUi.Draw.Looper
  langs:
  - csharp
  - vb
  name: Looper(Action)
  nameWithType: Looper.Looper(Action)
  fullName: DrawnUi.Draw.Looper.Looper(System.Action)
  type: Constructor
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/Looper.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Shared/Draw/Internals/Helpers/Looper.cs
    startLine: 11
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public Looper(Action onFrame)
    parameters:
    - id: onFrame
      type: System.Action
    content.vb: Public Sub New(onFrame As Action)
  overload: DrawnUi.Draw.Looper.#ctor*
  nameWithType.vb: Looper.New(Action)
  fullName.vb: DrawnUi.Draw.Looper.New(System.Action)
  name.vb: New(Action)
- uid: DrawnUi.Draw.Looper.Cancel
  commentId: P:DrawnUi.Draw.Looper.Cancel
  id: Cancel
  parent: DrawnUi.Draw.Looper
  langs:
  - csharp
  - vb
  name: Cancel
  nameWithType: Looper.Cancel
  fullName: DrawnUi.Draw.Looper.Cancel
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/Looper.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Cancel
    path: ../src/Shared/Draw/Internals/Helpers/Looper.cs
    startLine: 16
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public CancellationTokenSource Cancel { get; protected set; }
    parameters: []
    return:
      type: System.Threading.CancellationTokenSource
    content.vb: Public Property Cancel As CancellationTokenSource
  overload: DrawnUi.Draw.Looper.Cancel*
- uid: DrawnUi.Draw.Looper.Start(System.Int32,System.Boolean)
  commentId: M:DrawnUi.Draw.Looper.Start(System.Int32,System.Boolean)
  id: Start(System.Int32,System.Boolean)
  parent: DrawnUi.Draw.Looper
  langs:
  - csharp
  - vb
  name: Start(int, bool)
  nameWithType: Looper.Start(int, bool)
  fullName: DrawnUi.Draw.Looper.Start(int, bool)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/Looper.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Start
    path: ../src/Shared/Draw/Internals/Helpers/Looper.cs
    startLine: 18
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public void Start(int targetFps, bool useLegacy = false)
    parameters:
    - id: targetFps
      type: System.Int32
    - id: useLegacy
      type: System.Boolean
    content.vb: Public Sub Start(targetFps As Integer, useLegacy As Boolean = False)
  overload: DrawnUi.Draw.Looper.Start*
  nameWithType.vb: Looper.Start(Integer, Boolean)
  fullName.vb: DrawnUi.Draw.Looper.Start(Integer, Boolean)
  name.vb: Start(Integer, Boolean)
- uid: DrawnUi.Draw.Looper.StartOnMainThread(System.Int32,System.Boolean)
  commentId: M:DrawnUi.Draw.Looper.StartOnMainThread(System.Int32,System.Boolean)
  id: StartOnMainThread(System.Int32,System.Boolean)
  parent: DrawnUi.Draw.Looper
  langs:
  - csharp
  - vb
  name: StartOnMainThread(int, bool)
  nameWithType: Looper.StartOnMainThread(int, bool)
  fullName: DrawnUi.Draw.Looper.StartOnMainThread(int, bool)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/Looper.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: StartOnMainThread
    path: ../src/Shared/Draw/Internals/Helpers/Looper.cs
    startLine: 42
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public void StartOnMainThread(int targetFps, bool useLegacy = false)
    parameters:
    - id: targetFps
      type: System.Int32
    - id: useLegacy
      type: System.Boolean
    content.vb: Public Sub StartOnMainThread(targetFps As Integer, useLegacy As Boolean = False)
  overload: DrawnUi.Draw.Looper.StartOnMainThread*
  nameWithType.vb: Looper.StartOnMainThread(Integer, Boolean)
  fullName.vb: DrawnUi.Draw.Looper.StartOnMainThread(Integer, Boolean)
  name.vb: StartOnMainThread(Integer, Boolean)
- uid: DrawnUi.Draw.Looper.SetTargetFps(System.Int32)
  commentId: M:DrawnUi.Draw.Looper.SetTargetFps(System.Int32)
  id: SetTargetFps(System.Int32)
  parent: DrawnUi.Draw.Looper
  langs:
  - csharp
  - vb
  name: SetTargetFps(int)
  nameWithType: Looper.SetTargetFps(int)
  fullName: DrawnUi.Draw.Looper.SetTargetFps(int)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/Looper.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: SetTargetFps
    path: ../src/Shared/Draw/Internals/Helpers/Looper.cs
    startLine: 82
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public void SetTargetFps(int targetFps)
    parameters:
    - id: targetFps
      type: System.Int32
    content.vb: Public Sub SetTargetFps(targetFps As Integer)
  overload: DrawnUi.Draw.Looper.SetTargetFps*
  nameWithType.vb: Looper.SetTargetFps(Integer)
  fullName.vb: DrawnUi.Draw.Looper.SetTargetFps(Integer)
  name.vb: SetTargetFps(Integer)
- uid: DrawnUi.Draw.Looper.Stop
  commentId: M:DrawnUi.Draw.Looper.Stop
  id: Stop
  parent: DrawnUi.Draw.Looper
  langs:
  - csharp
  - vb
  name: Stop()
  nameWithType: Looper.Stop()
  fullName: DrawnUi.Draw.Looper.Stop()
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/Looper.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Stop
    path: ../src/Shared/Draw/Internals/Helpers/Looper.cs
    startLine: 87
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public void Stop()
    content.vb: Public Sub [Stop]()
  overload: DrawnUi.Draw.Looper.Stop*
- uid: DrawnUi.Draw.Looper.Dispose
  commentId: M:DrawnUi.Draw.Looper.Dispose
  id: Dispose
  parent: DrawnUi.Draw.Looper
  langs:
  - csharp
  - vb
  name: Dispose()
  nameWithType: Looper.Dispose()
  fullName: DrawnUi.Draw.Looper.Dispose()
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/Looper.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Dispose
    path: ../src/Shared/Draw/Internals/Helpers/Looper.cs
    startLine: 92
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
  example: []
  syntax:
    content: public void Dispose()
    content.vb: Public Sub Dispose()
  overload: DrawnUi.Draw.Looper.Dispose*
  implements:
  - System.IDisposable.Dispose
- uid: DrawnUi.Draw.Looper.IsRunning
  commentId: P:DrawnUi.Draw.Looper.IsRunning
  id: IsRunning
  parent: DrawnUi.Draw.Looper
  langs:
  - csharp
  - vb
  name: IsRunning
  nameWithType: Looper.IsRunning
  fullName: DrawnUi.Draw.Looper.IsRunning
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/Looper.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IsRunning
    path: ../src/Shared/Draw/Internals/Helpers/Looper.cs
    startLine: 101
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public bool IsRunning { get; protected set; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property IsRunning As Boolean
  overload: DrawnUi.Draw.Looper.IsRunning*
- uid: DrawnUi.Draw.Looper.StartLegacyLooperAsync(System.Threading.CancellationToken)
  commentId: M:DrawnUi.Draw.Looper.StartLegacyLooperAsync(System.Threading.CancellationToken)
  id: StartLegacyLooperAsync(System.Threading.CancellationToken)
  parent: DrawnUi.Draw.Looper
  langs:
  - csharp
  - vb
  name: StartLegacyLooperAsync(CancellationToken)
  nameWithType: Looper.StartLegacyLooperAsync(CancellationToken)
  fullName: DrawnUi.Draw.Looper.StartLegacyLooperAsync(System.Threading.CancellationToken)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/Looper.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: StartLegacyLooperAsync
    path: ../src/Shared/Draw/Internals/Helpers/Looper.cs
    startLine: 103
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: protected Task StartLegacyLooperAsync(CancellationToken cancellationToken)
    parameters:
    - id: cancellationToken
      type: System.Threading.CancellationToken
    return:
      type: System.Threading.Tasks.Task
    content.vb: Protected Function StartLegacyLooperAsync(cancellationToken As CancellationToken) As Task
  overload: DrawnUi.Draw.Looper.StartLegacyLooperAsync*
- uid: DrawnUi.Draw.Looper.StartLooperAsync(System.Threading.CancellationToken)
  commentId: M:DrawnUi.Draw.Looper.StartLooperAsync(System.Threading.CancellationToken)
  id: StartLooperAsync(System.Threading.CancellationToken)
  parent: DrawnUi.Draw.Looper
  langs:
  - csharp
  - vb
  name: StartLooperAsync(CancellationToken)
  nameWithType: Looper.StartLooperAsync(CancellationToken)
  fullName: DrawnUi.Draw.Looper.StartLooperAsync(System.Threading.CancellationToken)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/Looper.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: StartLooperAsync
    path: ../src/Shared/Draw/Internals/Helpers/Looper.cs
    startLine: 134
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: protected Task StartLooperAsync(CancellationToken cancellationToken)
    parameters:
    - id: cancellationToken
      type: System.Threading.CancellationToken
    return:
      type: System.Threading.Tasks.Task
    content.vb: Protected Function StartLooperAsync(cancellationToken As CancellationToken) As Task
  overload: DrawnUi.Draw.Looper.StartLooperAsync*
- uid: DrawnUi.Draw.Looper.OnFrame
  commentId: P:DrawnUi.Draw.Looper.OnFrame
  id: OnFrame
  parent: DrawnUi.Draw.Looper
  langs:
  - csharp
  - vb
  name: OnFrame
  nameWithType: Looper.OnFrame
  fullName: DrawnUi.Draw.Looper.OnFrame
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/Looper.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: OnFrame
    path: ../src/Shared/Draw/Internals/Helpers/Looper.cs
    startLine: 173
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public Action OnFrame { get; set; }
    parameters: []
    return:
      type: System.Action
    content.vb: Public Property OnFrame As Action
  overload: DrawnUi.Draw.Looper.OnFrame*
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.IDisposable
  commentId: T:System.IDisposable
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.idisposable
  name: IDisposable
  nameWithType: IDisposable
  fullName: System.IDisposable
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.Looper.#ctor*
  commentId: Overload:DrawnUi.Draw.Looper.#ctor
  href: DrawnUi.Draw.Looper.html#DrawnUi_Draw_Looper__ctor
  name: Looper
  nameWithType: Looper.Looper
  fullName: DrawnUi.Draw.Looper.Looper
  nameWithType.vb: Looper.New
  fullName.vb: DrawnUi.Draw.Looper.New
  name.vb: New
- uid: System.Action
  commentId: T:System.Action
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.action
  name: Action
  nameWithType: Action
  fullName: System.Action
- uid: DrawnUi.Draw.Looper.Cancel*
  commentId: Overload:DrawnUi.Draw.Looper.Cancel
  href: DrawnUi.Draw.Looper.html#DrawnUi_Draw_Looper_Cancel
  name: Cancel
  nameWithType: Looper.Cancel
  fullName: DrawnUi.Draw.Looper.Cancel
- uid: System.Threading.CancellationTokenSource
  commentId: T:System.Threading.CancellationTokenSource
  parent: System.Threading
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.threading.cancellationtokensource
  name: CancellationTokenSource
  nameWithType: CancellationTokenSource
  fullName: System.Threading.CancellationTokenSource
- uid: System.Threading
  commentId: N:System.Threading
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Threading
  nameWithType: System.Threading
  fullName: System.Threading
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Threading
    name: Threading
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Threading
    name: Threading
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading
- uid: DrawnUi.Draw.Looper.Start*
  commentId: Overload:DrawnUi.Draw.Looper.Start
  href: DrawnUi.Draw.Looper.html#DrawnUi_Draw_Looper_Start_System_Int32_System_Boolean_
  name: Start
  nameWithType: Looper.Start
  fullName: DrawnUi.Draw.Looper.Start
- uid: System.Int32
  commentId: T:System.Int32
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.int32
  name: int
  nameWithType: int
  fullName: int
  nameWithType.vb: Integer
  fullName.vb: Integer
  name.vb: Integer
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: DrawnUi.Draw.Looper.StartOnMainThread*
  commentId: Overload:DrawnUi.Draw.Looper.StartOnMainThread
  href: DrawnUi.Draw.Looper.html#DrawnUi_Draw_Looper_StartOnMainThread_System_Int32_System_Boolean_
  name: StartOnMainThread
  nameWithType: Looper.StartOnMainThread
  fullName: DrawnUi.Draw.Looper.StartOnMainThread
- uid: DrawnUi.Draw.Looper.SetTargetFps*
  commentId: Overload:DrawnUi.Draw.Looper.SetTargetFps
  href: DrawnUi.Draw.Looper.html#DrawnUi_Draw_Looper_SetTargetFps_System_Int32_
  name: SetTargetFps
  nameWithType: Looper.SetTargetFps
  fullName: DrawnUi.Draw.Looper.SetTargetFps
- uid: DrawnUi.Draw.Looper.Stop*
  commentId: Overload:DrawnUi.Draw.Looper.Stop
  href: DrawnUi.Draw.Looper.html#DrawnUi_Draw_Looper_Stop
  name: Stop
  nameWithType: Looper.Stop
  fullName: DrawnUi.Draw.Looper.Stop
- uid: DrawnUi.Draw.Looper.Dispose*
  commentId: Overload:DrawnUi.Draw.Looper.Dispose
  href: DrawnUi.Draw.Looper.html#DrawnUi_Draw_Looper_Dispose
  name: Dispose
  nameWithType: Looper.Dispose
  fullName: DrawnUi.Draw.Looper.Dispose
- uid: System.IDisposable.Dispose
  commentId: M:System.IDisposable.Dispose
  parent: System.IDisposable
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.idisposable.dispose
  name: Dispose()
  nameWithType: IDisposable.Dispose()
  fullName: System.IDisposable.Dispose()
  spec.csharp:
  - uid: System.IDisposable.Dispose
    name: Dispose
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.idisposable.dispose
  - name: (
  - name: )
  spec.vb:
  - uid: System.IDisposable.Dispose
    name: Dispose
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.idisposable.dispose
  - name: (
  - name: )
- uid: DrawnUi.Draw.Looper.IsRunning*
  commentId: Overload:DrawnUi.Draw.Looper.IsRunning
  href: DrawnUi.Draw.Looper.html#DrawnUi_Draw_Looper_IsRunning
  name: IsRunning
  nameWithType: Looper.IsRunning
  fullName: DrawnUi.Draw.Looper.IsRunning
- uid: DrawnUi.Draw.Looper.StartLegacyLooperAsync*
  commentId: Overload:DrawnUi.Draw.Looper.StartLegacyLooperAsync
  href: DrawnUi.Draw.Looper.html#DrawnUi_Draw_Looper_StartLegacyLooperAsync_System_Threading_CancellationToken_
  name: StartLegacyLooperAsync
  nameWithType: Looper.StartLegacyLooperAsync
  fullName: DrawnUi.Draw.Looper.StartLegacyLooperAsync
- uid: System.Threading.CancellationToken
  commentId: T:System.Threading.CancellationToken
  parent: System.Threading
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.threading.cancellationtoken
  name: CancellationToken
  nameWithType: CancellationToken
  fullName: System.Threading.CancellationToken
- uid: System.Threading.Tasks.Task
  commentId: T:System.Threading.Tasks.Task
  parent: System.Threading.Tasks
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.threading.tasks.task
  name: Task
  nameWithType: Task
  fullName: System.Threading.Tasks.Task
- uid: System.Threading.Tasks
  commentId: N:System.Threading.Tasks
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Threading.Tasks
  nameWithType: System.Threading.Tasks
  fullName: System.Threading.Tasks
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Threading
    name: Threading
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading
  - name: .
  - uid: System.Threading.Tasks
    name: Tasks
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading.tasks
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Threading
    name: Threading
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading
  - name: .
  - uid: System.Threading.Tasks
    name: Tasks
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading.tasks
- uid: DrawnUi.Draw.Looper.StartLooperAsync*
  commentId: Overload:DrawnUi.Draw.Looper.StartLooperAsync
  href: DrawnUi.Draw.Looper.html#DrawnUi_Draw_Looper_StartLooperAsync_System_Threading_CancellationToken_
  name: StartLooperAsync
  nameWithType: Looper.StartLooperAsync
  fullName: DrawnUi.Draw.Looper.StartLooperAsync
- uid: DrawnUi.Draw.Looper.OnFrame*
  commentId: Overload:DrawnUi.Draw.Looper.OnFrame
  href: DrawnUi.Draw.Looper.html#DrawnUi_Draw_Looper_OnFrame
  name: OnFrame
  nameWithType: Looper.OnFrame
  fullName: DrawnUi.Draw.Looper.OnFrame
