<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <title>Enum SkiaCacheType | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width">
      <meta name="title" content="Enum SkiaCacheType | DrawnUi Documentation ">
    
    
      <link rel="shortcut icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../styles/docfx.vendor.min.css">
      <link rel="stylesheet" href="../styles/docfx.css">
      <link rel="stylesheet" href="../styles/main.css">
      <meta property="docfx:navrel" content="../toc.html">
      <meta property="docfx:tocrel" content="toc.html">
    
    <meta property="docfx:rel" content="../">
    
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first=First data-prev=Previous data-next=Next data-last=Last></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="DrawnUi.Draw.SkiaCacheType">




  <h1 id="DrawnUi_Draw_SkiaCacheType" data-uid="DrawnUi.Draw.SkiaCacheType" class="text-break">Enum SkiaCacheType</h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <h6><strong>Namespace</strong>: <a class="xref" href="DrawnUi.html">DrawnUi</a>.<a class="xref" href="DrawnUi.Draw.html">Draw</a></h6>
  <h6><strong>Assembly</strong>: DrawnUi.Maui.dll</h6>
  <h5 id="DrawnUi_Draw_SkiaCacheType_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public enum SkiaCacheType</code></pre>
  </div>
  <h3 id="fields">Fields
</h3>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    <thead>
    </thead></thead><tbody>
      <tr>
        <td id="DrawnUi_Draw_SkiaCacheType_GPU">GPU</td>
        <td><p>The cached surface will use the same graphic context as your hardware-accelerated canvas.
This kind of cache will not apply Opacity as not all platforms support transparency for hardware accelerated layer.
Will fallback to simple Image cache type if hardware acceleration is not available.</p>
</td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_SkiaCacheType_Image">Image</td>
        <td><p>Will use simple SKBitmap cache type, will not use hardware acceleration.
Slower but will work for sizes bigger than graphics memory if needed.</p>
</td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_SkiaCacheType_ImageComposite">ImageComposite</td>
        <td><p>Would receive the invalidated area rectangle, then redraw the previous cache but clipped to exclude the dirty area, then would re-create the dirty area and draw it clipped inside the dirty rectangle. This is useful for layouts with many children, like scroll content etc, but useless for non-containers.</p>
</td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_SkiaCacheType_ImageDoubleBuffered">ImageDoubleBuffered</td>
        <td><p>Using <code>Image</code> cache type with double buffering. Will display a previous cache while rendering the new one in background, thus not slowing scrolling etc.</p>
</td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_SkiaCacheType_None">None</td>
        <td><p>True and old school</p>
</td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_SkiaCacheType_Operations">Operations</td>
        <td><p>Create and reuse SKPicture. Try this first for labels, svg etc.
Do not use this when dropping shadows or with other effects, better use Bitmap.</p>
</td>
      </tr>
      <tr>
        <td id="DrawnUi_Draw_SkiaCacheType_OperationsFull">OperationsFull</td>
        <td><p>EXPERIMENTAL! May be bugged. Create and reuse SKPicture all over the canvas ignoring clipping.
Try this first for labels, svg etc.
Do not use this when dropping shadows or with other effects, better use Bitmap.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h3 id="extensionmethods">Extension Methods</h3>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_">InternalExtensions.FromPlatform(object)</a>
  </div>
</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_SkiaCacheType.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.SkiaCacheType%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A" class="contribution-link">Edit this page</a>
                  </li>
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Cache/SkiaCacheType.cs/#L4" class="contribution-link">View Source</a>
                  </li>
                </ul>
              </div>
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In this article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      
      <span>Generated by <strong>DocFX</strong></span>
          </div>
        </div>
      </footer>
    </div>

    <script type="text/javascript" src="../styles/docfx.vendor.min.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
