### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.LockTouch
  commentId: T:DrawnUi.Draw.LockTouch
  id: LockTouch
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.LockTouch.Disabled
  - DrawnUi.Draw.LockTouch.Enabled
  - DrawnUi.Draw.LockTouch.PassNone
  - DrawnUi.Draw.LockTouch.PassTap
  - DrawnUi.Draw.LockTouch.PassTapAndLongPress
  langs:
  - csharp
  - vb
  name: LockTouch
  nameWithType: LockTouch
  fullName: DrawnUi.Draw.LockTouch
  type: Enum
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/LockTouch.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: LockTouch
    path: ../src/Shared/Draw/Internals/Enums/LockTouch.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public enum LockTouch
    content.vb: Public Enum LockTouch
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.LockTouch.Disabled
  commentId: F:DrawnUi.Draw.LockTouch.Disabled
  id: Disabled
  parent: DrawnUi.Draw.LockTouch
  langs:
  - csharp
  - vb
  name: Disabled
  nameWithType: LockTouch.Disabled
  fullName: DrawnUi.Draw.LockTouch.Disabled
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/LockTouch.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Disabled
    path: ../src/Shared/Draw/Internals/Enums/LockTouch.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: Disabled = 0
    return:
      type: DrawnUi.Draw.LockTouch
- uid: DrawnUi.Draw.LockTouch.Enabled
  commentId: F:DrawnUi.Draw.LockTouch.Enabled
  id: Enabled
  parent: DrawnUi.Draw.LockTouch
  langs:
  - csharp
  - vb
  name: Enabled
  nameWithType: LockTouch.Enabled
  fullName: DrawnUi.Draw.LockTouch.Enabled
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/LockTouch.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Enabled
    path: ../src/Shared/Draw/Internals/Enums/LockTouch.cs
    startLine: 9
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Pass nothing below and mark all gestures as consumed by this control
  example: []
  syntax:
    content: Enabled = 1
    return:
      type: DrawnUi.Draw.LockTouch
- uid: DrawnUi.Draw.LockTouch.PassNone
  commentId: F:DrawnUi.Draw.LockTouch.PassNone
  id: PassNone
  parent: DrawnUi.Draw.LockTouch
  langs:
  - csharp
  - vb
  name: PassNone
  nameWithType: LockTouch.PassNone
  fullName: DrawnUi.Draw.LockTouch.PassNone
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/LockTouch.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: PassNone
    path: ../src/Shared/Draw/Internals/Enums/LockTouch.cs
    startLine: 14
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Pass nothing below
  example: []
  syntax:
    content: PassNone = 2
    return:
      type: DrawnUi.Draw.LockTouch
- uid: DrawnUi.Draw.LockTouch.PassTap
  commentId: F:DrawnUi.Draw.LockTouch.PassTap
  id: PassTap
  parent: DrawnUi.Draw.LockTouch
  langs:
  - csharp
  - vb
  name: PassTap
  nameWithType: LockTouch.PassTap
  fullName: DrawnUi.Draw.LockTouch.PassTap
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/LockTouch.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: PassTap
    path: ../src/Shared/Draw/Internals/Enums/LockTouch.cs
    startLine: 19
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Pass only Tapped below
  example: []
  syntax:
    content: PassTap = 3
    return:
      type: DrawnUi.Draw.LockTouch
- uid: DrawnUi.Draw.LockTouch.PassTapAndLongPress
  commentId: F:DrawnUi.Draw.LockTouch.PassTapAndLongPress
  id: PassTapAndLongPress
  parent: DrawnUi.Draw.LockTouch
  langs:
  - csharp
  - vb
  name: PassTapAndLongPress
  nameWithType: LockTouch.PassTapAndLongPress
  fullName: DrawnUi.Draw.LockTouch.PassTapAndLongPress
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/LockTouch.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: PassTapAndLongPress
    path: ../src/Shared/Draw/Internals/Enums/LockTouch.cs
    startLine: 24
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Pass only Tapped and LongPressing below
  example: []
  syntax:
    content: PassTapAndLongPress = 4
    return:
      type: DrawnUi.Draw.LockTouch
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.LockTouch
  commentId: T:DrawnUi.Draw.LockTouch
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.LockTouch.html
  name: LockTouch
  nameWithType: LockTouch
  fullName: DrawnUi.Draw.LockTouch
