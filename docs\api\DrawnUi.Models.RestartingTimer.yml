### YamlMime:ManagedReference
items:
- uid: DrawnUi.Models.RestartingTimer
  commentId: T:DrawnUi.Models.RestartingTimer
  id: RestartingTimer
  parent: DrawnUi.Models
  children:
  - DrawnUi.Models.RestartingTimer.#ctor(System.TimeSpan,System.Action)
  - DrawnUi.Models.RestartingTimer.#ctor(System.UInt32,System.Action)
  - DrawnUi.Models.RestartingTimer.Dispose
  - DrawnUi.Models.RestartingTimer.IsRunning
  - DrawnUi.Models.RestartingTimer.Kick
  - DrawnUi.Models.RestartingTimer.Restart
  - DrawnUi.Models.RestartingTimer.Start
  - DrawnUi.Models.RestartingTimer.Stop
  - DrawnUi.Models.RestartingTimer.disposed
  langs:
  - csharp
  - vb
  name: RestartingTimer
  nameWithType: RestartingTimer
  fullName: DrawnUi.Models.RestartingTimer
  type: Class
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/RestartingTimer.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: RestartingTimer
    path: ../src/Shared/Draw/Internals/Helpers/RestartingTimer.cs
    startLine: 79
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Models
  syntax:
    content: 'public class RestartingTimer : IDisposable'
    content.vb: Public Class RestartingTimer Implements IDisposable
  inheritance:
  - System.Object
  implements:
  - System.IDisposable
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Models.RestartingTimer.IsRunning
  commentId: P:DrawnUi.Models.RestartingTimer.IsRunning
  id: IsRunning
  parent: DrawnUi.Models.RestartingTimer
  langs:
  - csharp
  - vb
  name: IsRunning
  nameWithType: RestartingTimer.IsRunning
  fullName: DrawnUi.Models.RestartingTimer.IsRunning
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/RestartingTimer.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IsRunning
    path: ../src/Shared/Draw/Internals/Helpers/RestartingTimer.cs
    startLine: 84
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Models
  summary: Is actually running
  example: []
  syntax:
    content: public bool IsRunning { get; protected set; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property IsRunning As Boolean
  overload: DrawnUi.Models.RestartingTimer.IsRunning*
- uid: DrawnUi.Models.RestartingTimer.Kick
  commentId: M:DrawnUi.Models.RestartingTimer.Kick
  id: Kick
  parent: DrawnUi.Models.RestartingTimer
  langs:
  - csharp
  - vb
  name: Kick()
  nameWithType: RestartingTimer.Kick()
  fullName: DrawnUi.Models.RestartingTimer.Kick()
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/RestartingTimer.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Kick
    path: ../src/Shared/Draw/Internals/Helpers/RestartingTimer.cs
    startLine: 91
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Models
  summary: >-
    Starts the timer if not running or restarts it if already running,

    but only if the timer is active
  example: []
  syntax:
    content: public void Kick()
    content.vb: Public Sub Kick()
  overload: DrawnUi.Models.RestartingTimer.Kick*
- uid: DrawnUi.Models.RestartingTimer.#ctor(System.UInt32,System.Action)
  commentId: M:DrawnUi.Models.RestartingTimer.#ctor(System.UInt32,System.Action)
  id: '#ctor(System.UInt32,System.Action)'
  parent: DrawnUi.Models.RestartingTimer
  langs:
  - csharp
  - vb
  name: RestartingTimer(uint, Action)
  nameWithType: RestartingTimer.RestartingTimer(uint, Action)
  fullName: DrawnUi.Models.RestartingTimer.RestartingTimer(uint, System.Action)
  type: Constructor
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/RestartingTimer.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Shared/Draw/Internals/Helpers/RestartingTimer.cs
    startLine: 113
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Models
  summary: Creates a new timer with the specified millisecond delay and callback
  example: []
  syntax:
    content: public RestartingTimer(uint ms, Action callback)
    parameters:
    - id: ms
      type: System.UInt32
      description: Milliseconds to delay before invoking callback
    - id: callback
      type: System.Action
      description: Action to execute when timer completes
    content.vb: Public Sub New(ms As UInteger, callback As Action)
  overload: DrawnUi.Models.RestartingTimer.#ctor*
  nameWithType.vb: RestartingTimer.New(UInteger, Action)
  fullName.vb: DrawnUi.Models.RestartingTimer.New(UInteger, System.Action)
  name.vb: New(UInteger, Action)
- uid: DrawnUi.Models.RestartingTimer.#ctor(System.TimeSpan,System.Action)
  commentId: M:DrawnUi.Models.RestartingTimer.#ctor(System.TimeSpan,System.Action)
  id: '#ctor(System.TimeSpan,System.Action)'
  parent: DrawnUi.Models.RestartingTimer
  langs:
  - csharp
  - vb
  name: RestartingTimer(TimeSpan, Action)
  nameWithType: RestartingTimer.RestartingTimer(TimeSpan, Action)
  fullName: DrawnUi.Models.RestartingTimer.RestartingTimer(System.TimeSpan, System.Action)
  type: Constructor
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/RestartingTimer.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Shared/Draw/Internals/Helpers/RestartingTimer.cs
    startLine: 125
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Models
  summary: Creates a new timer with the specified timespan delay and callback
  example: []
  syntax:
    content: public RestartingTimer(TimeSpan timespan, Action callback)
    parameters:
    - id: timespan
      type: System.TimeSpan
      description: Time to delay before invoking callback
    - id: callback
      type: System.Action
      description: Action to execute when timer completes
    content.vb: Public Sub New(timespan As TimeSpan, callback As Action)
  overload: DrawnUi.Models.RestartingTimer.#ctor*
  nameWithType.vb: RestartingTimer.New(TimeSpan, Action)
  fullName.vb: DrawnUi.Models.RestartingTimer.New(System.TimeSpan, System.Action)
  name.vb: New(TimeSpan, Action)
- uid: DrawnUi.Models.RestartingTimer.Restart
  commentId: M:DrawnUi.Models.RestartingTimer.Restart
  id: Restart
  parent: DrawnUi.Models.RestartingTimer
  langs:
  - csharp
  - vb
  name: Restart()
  nameWithType: RestartingTimer.Restart()
  fullName: DrawnUi.Models.RestartingTimer.Restart()
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/RestartingTimer.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Restart
    path: ../src/Shared/Draw/Internals/Helpers/RestartingTimer.cs
    startLine: 135
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Models
  summary: Restarts the timer by stopping it and starting it again
  example: []
  syntax:
    content: public void Restart()
    content.vb: Public Sub Restart()
  overload: DrawnUi.Models.RestartingTimer.Restart*
- uid: DrawnUi.Models.RestartingTimer.Start
  commentId: M:DrawnUi.Models.RestartingTimer.Start
  id: Start
  parent: DrawnUi.Models.RestartingTimer
  langs:
  - csharp
  - vb
  name: Start()
  nameWithType: RestartingTimer.Start()
  fullName: DrawnUi.Models.RestartingTimer.Start()
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/RestartingTimer.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Start
    path: ../src/Shared/Draw/Internals/Helpers/RestartingTimer.cs
    startLine: 144
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Models
  summary: Starts the timer
  example: []
  syntax:
    content: protected void Start()
    content.vb: Protected Sub Start()
  overload: DrawnUi.Models.RestartingTimer.Start*
- uid: DrawnUi.Models.RestartingTimer.Stop
  commentId: M:DrawnUi.Models.RestartingTimer.Stop
  id: Stop
  parent: DrawnUi.Models.RestartingTimer
  langs:
  - csharp
  - vb
  name: Stop()
  nameWithType: RestartingTimer.Stop()
  fullName: DrawnUi.Models.RestartingTimer.Stop()
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/RestartingTimer.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Stop
    path: ../src/Shared/Draw/Internals/Helpers/RestartingTimer.cs
    startLine: 159
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Models
  summary: Stops the timer
  example: []
  syntax:
    content: public void Stop()
    content.vb: Public Sub [Stop]()
  overload: DrawnUi.Models.RestartingTimer.Stop*
- uid: DrawnUi.Models.RestartingTimer.disposed
  commentId: F:DrawnUi.Models.RestartingTimer.disposed
  id: disposed
  parent: DrawnUi.Models.RestartingTimer
  langs:
  - csharp
  - vb
  name: disposed
  nameWithType: RestartingTimer.disposed
  fullName: DrawnUi.Models.RestartingTimer.disposed
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/RestartingTimer.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: disposed
    path: ../src/Shared/Draw/Internals/Helpers/RestartingTimer.cs
    startLine: 170
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Models
  syntax:
    content: protected bool disposed
    return:
      type: System.Boolean
    content.vb: Protected disposed As Boolean
- uid: DrawnUi.Models.RestartingTimer.Dispose
  commentId: M:DrawnUi.Models.RestartingTimer.Dispose
  id: Dispose
  parent: DrawnUi.Models.RestartingTimer
  langs:
  - csharp
  - vb
  name: Dispose()
  nameWithType: RestartingTimer.Dispose()
  fullName: DrawnUi.Models.RestartingTimer.Dispose()
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/RestartingTimer.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Dispose
    path: ../src/Shared/Draw/Internals/Helpers/RestartingTimer.cs
    startLine: 175
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Models
  summary: Disposes the timer and releases resources
  example: []
  syntax:
    content: public void Dispose()
    content.vb: Public Sub Dispose()
  overload: DrawnUi.Models.RestartingTimer.Dispose*
  implements:
  - System.IDisposable.Dispose
references:
- uid: DrawnUi.Models
  commentId: N:DrawnUi.Models
  href: DrawnUi.html
  name: DrawnUi.Models
  nameWithType: DrawnUi.Models
  fullName: DrawnUi.Models
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Models
    name: Models
    href: DrawnUi.Models.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Models
    name: Models
    href: DrawnUi.Models.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.IDisposable
  commentId: T:System.IDisposable
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.idisposable
  name: IDisposable
  nameWithType: IDisposable
  fullName: System.IDisposable
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Models.RestartingTimer.IsRunning*
  commentId: Overload:DrawnUi.Models.RestartingTimer.IsRunning
  href: DrawnUi.Models.RestartingTimer.html#DrawnUi_Models_RestartingTimer_IsRunning
  name: IsRunning
  nameWithType: RestartingTimer.IsRunning
  fullName: DrawnUi.Models.RestartingTimer.IsRunning
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: DrawnUi.Models.RestartingTimer.Kick*
  commentId: Overload:DrawnUi.Models.RestartingTimer.Kick
  href: DrawnUi.Models.RestartingTimer.html#DrawnUi_Models_RestartingTimer_Kick
  name: Kick
  nameWithType: RestartingTimer.Kick
  fullName: DrawnUi.Models.RestartingTimer.Kick
- uid: DrawnUi.Models.RestartingTimer.#ctor*
  commentId: Overload:DrawnUi.Models.RestartingTimer.#ctor
  href: DrawnUi.Models.RestartingTimer.html#DrawnUi_Models_RestartingTimer__ctor_System_UInt32_System_Action_
  name: RestartingTimer
  nameWithType: RestartingTimer.RestartingTimer
  fullName: DrawnUi.Models.RestartingTimer.RestartingTimer
  nameWithType.vb: RestartingTimer.New
  fullName.vb: DrawnUi.Models.RestartingTimer.New
  name.vb: New
- uid: System.UInt32
  commentId: T:System.UInt32
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.uint32
  name: uint
  nameWithType: uint
  fullName: uint
  nameWithType.vb: UInteger
  fullName.vb: UInteger
  name.vb: UInteger
- uid: System.Action
  commentId: T:System.Action
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.action
  name: Action
  nameWithType: Action
  fullName: System.Action
- uid: System.TimeSpan
  commentId: T:System.TimeSpan
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.timespan
  name: TimeSpan
  nameWithType: TimeSpan
  fullName: System.TimeSpan
- uid: DrawnUi.Models.RestartingTimer.Restart*
  commentId: Overload:DrawnUi.Models.RestartingTimer.Restart
  href: DrawnUi.Models.RestartingTimer.html#DrawnUi_Models_RestartingTimer_Restart
  name: Restart
  nameWithType: RestartingTimer.Restart
  fullName: DrawnUi.Models.RestartingTimer.Restart
- uid: DrawnUi.Models.RestartingTimer.Start*
  commentId: Overload:DrawnUi.Models.RestartingTimer.Start
  href: DrawnUi.Models.RestartingTimer.html#DrawnUi_Models_RestartingTimer_Start
  name: Start
  nameWithType: RestartingTimer.Start
  fullName: DrawnUi.Models.RestartingTimer.Start
- uid: DrawnUi.Models.RestartingTimer.Stop*
  commentId: Overload:DrawnUi.Models.RestartingTimer.Stop
  href: DrawnUi.Models.RestartingTimer.html#DrawnUi_Models_RestartingTimer_Stop
  name: Stop
  nameWithType: RestartingTimer.Stop
  fullName: DrawnUi.Models.RestartingTimer.Stop
- uid: DrawnUi.Models.RestartingTimer.Dispose*
  commentId: Overload:DrawnUi.Models.RestartingTimer.Dispose
  href: DrawnUi.Models.RestartingTimer.html#DrawnUi_Models_RestartingTimer_Dispose
  name: Dispose
  nameWithType: RestartingTimer.Dispose
  fullName: DrawnUi.Models.RestartingTimer.Dispose
- uid: System.IDisposable.Dispose
  commentId: M:System.IDisposable.Dispose
  parent: System.IDisposable
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.idisposable.dispose
  name: Dispose()
  nameWithType: IDisposable.Dispose()
  fullName: System.IDisposable.Dispose()
  spec.csharp:
  - uid: System.IDisposable.Dispose
    name: Dispose
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.idisposable.dispose
  - name: (
  - name: )
  spec.vb:
  - uid: System.IDisposable.Dispose
    name: Dispose
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.idisposable.dispose
  - name: (
  - name: )
