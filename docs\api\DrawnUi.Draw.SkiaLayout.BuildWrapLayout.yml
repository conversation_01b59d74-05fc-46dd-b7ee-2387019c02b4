### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.SkiaLayout.BuildWrapLayout
  commentId: T:DrawnUi.Draw.SkiaLayout.BuildWrapLayout
  id: SkiaLayout.BuildWrapLayout
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.SkiaLayout.BuildWrapLayout.#ctor(DrawnUi.Draw.SkiaLayout)
  - DrawnUi.Draw.SkiaLayout.BuildWrapLayout.Build(SkiaSharp.SKRect,System.Single)
  langs:
  - csharp
  - vb
  name: SkiaLayout.BuildWrapLayout
  nameWithType: SkiaLayout.BuildWrapLayout
  fullName: DrawnUi.Draw.SkiaLayout.BuildWrapLayout
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.BuildWrapLayout.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: BuildWrapLayout
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.BuildWrapLayout.cs
    startLine: 10
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Implementation for LayoutType.Wrap
  example: []
  syntax:
    content: 'public class SkiaLayout.BuildWrapLayout : StackLayoutStructure'
    content.vb: Public Class SkiaLayout.BuildWrapLayout Inherits StackLayoutStructure
  inheritance:
  - System.Object
  - DrawnUi.Draw.StackLayoutStructure
  inheritedMembers:
  - DrawnUi.Draw.StackLayoutStructure._layout
  - DrawnUi.Draw.StackLayoutStructure.ChildrenCount
  - DrawnUi.Draw.StackLayoutStructure.EnumerateViewsForMeasurement
  - DrawnUi.Draw.StackLayoutStructure.MeasureCell(SkiaSharp.SKRect,DrawnUi.Draw.ControlInStack,DrawnUi.Draw.SkiaControl,System.Single)
  - DrawnUi.Draw.StackLayoutStructure.GetSpacingForIndex(System.Int32,System.Single)
  - DrawnUi.Draw.StackLayoutStructure.CreateWrapper(System.Int32,DrawnUi.Draw.SkiaControl)
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.SkiaLayout.BuildWrapLayout.#ctor(DrawnUi.Draw.SkiaLayout)
  commentId: M:DrawnUi.Draw.SkiaLayout.BuildWrapLayout.#ctor(DrawnUi.Draw.SkiaLayout)
  id: '#ctor(DrawnUi.Draw.SkiaLayout)'
  parent: DrawnUi.Draw.SkiaLayout.BuildWrapLayout
  langs:
  - csharp
  - vb
  name: BuildWrapLayout(SkiaLayout)
  nameWithType: SkiaLayout.BuildWrapLayout.BuildWrapLayout(SkiaLayout)
  fullName: DrawnUi.Draw.SkiaLayout.BuildWrapLayout.BuildWrapLayout(DrawnUi.Draw.SkiaLayout)
  type: Constructor
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.BuildWrapLayout.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.BuildWrapLayout.cs
    startLine: 12
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public BuildWrapLayout(SkiaLayout layout)
    parameters:
    - id: layout
      type: DrawnUi.Draw.SkiaLayout
    content.vb: Public Sub New(layout As SkiaLayout)
  overload: DrawnUi.Draw.SkiaLayout.BuildWrapLayout.#ctor*
  nameWithType.vb: SkiaLayout.BuildWrapLayout.New(SkiaLayout)
  fullName.vb: DrawnUi.Draw.SkiaLayout.BuildWrapLayout.New(DrawnUi.Draw.SkiaLayout)
  name.vb: New(SkiaLayout)
- uid: DrawnUi.Draw.SkiaLayout.BuildWrapLayout.Build(SkiaSharp.SKRect,System.Single)
  commentId: M:DrawnUi.Draw.SkiaLayout.BuildWrapLayout.Build(SkiaSharp.SKRect,System.Single)
  id: Build(SkiaSharp.SKRect,System.Single)
  parent: DrawnUi.Draw.SkiaLayout.BuildWrapLayout
  langs:
  - csharp
  - vb
  name: Build(SKRect, float)
  nameWithType: SkiaLayout.BuildWrapLayout.Build(SKRect, float)
  fullName: DrawnUi.Draw.SkiaLayout.BuildWrapLayout.Build(SkiaSharp.SKRect, float)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.BuildWrapLayout.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Build
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.BuildWrapLayout.cs
    startLine: 16
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Will measure children and build appropriate stack structure for the layout
  example: []
  syntax:
    content: public override ScaledSize Build(SKRect rectForChildrenPixels, float scale)
    parameters:
    - id: rectForChildrenPixels
      type: SkiaSharp.SKRect
    - id: scale
      type: System.Single
    return:
      type: DrawnUi.Draw.ScaledSize
    content.vb: Public Overrides Function Build(rectForChildrenPixels As SKRect, scale As Single) As ScaledSize
  overridden: DrawnUi.Draw.StackLayoutStructure.Build(SkiaSharp.SKRect,System.Single)
  overload: DrawnUi.Draw.SkiaLayout.BuildWrapLayout.Build*
  nameWithType.vb: SkiaLayout.BuildWrapLayout.Build(SKRect, Single)
  fullName.vb: DrawnUi.Draw.SkiaLayout.BuildWrapLayout.Build(SkiaSharp.SKRect, Single)
  name.vb: Build(SKRect, Single)
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: DrawnUi.Draw.StackLayoutStructure
  commentId: T:DrawnUi.Draw.StackLayoutStructure
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.StackLayoutStructure.html
  name: StackLayoutStructure
  nameWithType: StackLayoutStructure
  fullName: DrawnUi.Draw.StackLayoutStructure
- uid: DrawnUi.Draw.StackLayoutStructure._layout
  commentId: F:DrawnUi.Draw.StackLayoutStructure._layout
  parent: DrawnUi.Draw.StackLayoutStructure
  href: DrawnUi.Draw.StackLayoutStructure.html#DrawnUi_Draw_StackLayoutStructure__layout
  name: _layout
  nameWithType: StackLayoutStructure._layout
  fullName: DrawnUi.Draw.StackLayoutStructure._layout
- uid: DrawnUi.Draw.StackLayoutStructure.ChildrenCount
  commentId: F:DrawnUi.Draw.StackLayoutStructure.ChildrenCount
  parent: DrawnUi.Draw.StackLayoutStructure
  href: DrawnUi.Draw.StackLayoutStructure.html#DrawnUi_Draw_StackLayoutStructure_ChildrenCount
  name: ChildrenCount
  nameWithType: StackLayoutStructure.ChildrenCount
  fullName: DrawnUi.Draw.StackLayoutStructure.ChildrenCount
- uid: DrawnUi.Draw.StackLayoutStructure.EnumerateViewsForMeasurement
  commentId: M:DrawnUi.Draw.StackLayoutStructure.EnumerateViewsForMeasurement
  parent: DrawnUi.Draw.StackLayoutStructure
  href: DrawnUi.Draw.StackLayoutStructure.html#DrawnUi_Draw_StackLayoutStructure_EnumerateViewsForMeasurement
  name: EnumerateViewsForMeasurement()
  nameWithType: StackLayoutStructure.EnumerateViewsForMeasurement()
  fullName: DrawnUi.Draw.StackLayoutStructure.EnumerateViewsForMeasurement()
  spec.csharp:
  - uid: DrawnUi.Draw.StackLayoutStructure.EnumerateViewsForMeasurement
    name: EnumerateViewsForMeasurement
    href: DrawnUi.Draw.StackLayoutStructure.html#DrawnUi_Draw_StackLayoutStructure_EnumerateViewsForMeasurement
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.StackLayoutStructure.EnumerateViewsForMeasurement
    name: EnumerateViewsForMeasurement
    href: DrawnUi.Draw.StackLayoutStructure.html#DrawnUi_Draw_StackLayoutStructure_EnumerateViewsForMeasurement
  - name: (
  - name: )
- uid: DrawnUi.Draw.StackLayoutStructure.MeasureCell(SkiaSharp.SKRect,DrawnUi.Draw.ControlInStack,DrawnUi.Draw.SkiaControl,System.Single)
  commentId: M:DrawnUi.Draw.StackLayoutStructure.MeasureCell(SkiaSharp.SKRect,DrawnUi.Draw.ControlInStack,DrawnUi.Draw.SkiaControl,System.Single)
  parent: DrawnUi.Draw.StackLayoutStructure
  isExternal: true
  href: DrawnUi.Draw.StackLayoutStructure.html#DrawnUi_Draw_StackLayoutStructure_MeasureCell_SkiaSharp_SKRect_DrawnUi_Draw_ControlInStack_DrawnUi_Draw_SkiaControl_System_Single_
  name: MeasureCell(SKRect, ControlInStack, SkiaControl, float)
  nameWithType: StackLayoutStructure.MeasureCell(SKRect, ControlInStack, SkiaControl, float)
  fullName: DrawnUi.Draw.StackLayoutStructure.MeasureCell(SkiaSharp.SKRect, DrawnUi.Draw.ControlInStack, DrawnUi.Draw.SkiaControl, float)
  nameWithType.vb: StackLayoutStructure.MeasureCell(SKRect, ControlInStack, SkiaControl, Single)
  fullName.vb: DrawnUi.Draw.StackLayoutStructure.MeasureCell(SkiaSharp.SKRect, DrawnUi.Draw.ControlInStack, DrawnUi.Draw.SkiaControl, Single)
  name.vb: MeasureCell(SKRect, ControlInStack, SkiaControl, Single)
  spec.csharp:
  - uid: DrawnUi.Draw.StackLayoutStructure.MeasureCell(SkiaSharp.SKRect,DrawnUi.Draw.ControlInStack,DrawnUi.Draw.SkiaControl,System.Single)
    name: MeasureCell
    href: DrawnUi.Draw.StackLayoutStructure.html#DrawnUi_Draw_StackLayoutStructure_MeasureCell_SkiaSharp_SKRect_DrawnUi_Draw_ControlInStack_DrawnUi_Draw_SkiaControl_System_Single_
  - name: (
  - uid: SkiaSharp.SKRect
    name: SKRect
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skrect
  - name: ','
  - name: " "
  - uid: DrawnUi.Draw.ControlInStack
    name: ControlInStack
    href: DrawnUi.Draw.ControlInStack.html
  - name: ','
  - name: " "
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: ','
  - name: " "
  - uid: System.Single
    name: float
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.single
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.StackLayoutStructure.MeasureCell(SkiaSharp.SKRect,DrawnUi.Draw.ControlInStack,DrawnUi.Draw.SkiaControl,System.Single)
    name: MeasureCell
    href: DrawnUi.Draw.StackLayoutStructure.html#DrawnUi_Draw_StackLayoutStructure_MeasureCell_SkiaSharp_SKRect_DrawnUi_Draw_ControlInStack_DrawnUi_Draw_SkiaControl_System_Single_
  - name: (
  - uid: SkiaSharp.SKRect
    name: SKRect
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skrect
  - name: ','
  - name: " "
  - uid: DrawnUi.Draw.ControlInStack
    name: ControlInStack
    href: DrawnUi.Draw.ControlInStack.html
  - name: ','
  - name: " "
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: ','
  - name: " "
  - uid: System.Single
    name: Single
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.single
  - name: )
- uid: DrawnUi.Draw.StackLayoutStructure.GetSpacingForIndex(System.Int32,System.Single)
  commentId: M:DrawnUi.Draw.StackLayoutStructure.GetSpacingForIndex(System.Int32,System.Single)
  parent: DrawnUi.Draw.StackLayoutStructure
  isExternal: true
  href: DrawnUi.Draw.StackLayoutStructure.html#DrawnUi_Draw_StackLayoutStructure_GetSpacingForIndex_System_Int32_System_Single_
  name: GetSpacingForIndex(int, float)
  nameWithType: StackLayoutStructure.GetSpacingForIndex(int, float)
  fullName: DrawnUi.Draw.StackLayoutStructure.GetSpacingForIndex(int, float)
  nameWithType.vb: StackLayoutStructure.GetSpacingForIndex(Integer, Single)
  fullName.vb: DrawnUi.Draw.StackLayoutStructure.GetSpacingForIndex(Integer, Single)
  name.vb: GetSpacingForIndex(Integer, Single)
  spec.csharp:
  - uid: DrawnUi.Draw.StackLayoutStructure.GetSpacingForIndex(System.Int32,System.Single)
    name: GetSpacingForIndex
    href: DrawnUi.Draw.StackLayoutStructure.html#DrawnUi_Draw_StackLayoutStructure_GetSpacingForIndex_System_Int32_System_Single_
  - name: (
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: System.Single
    name: float
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.single
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.StackLayoutStructure.GetSpacingForIndex(System.Int32,System.Single)
    name: GetSpacingForIndex
    href: DrawnUi.Draw.StackLayoutStructure.html#DrawnUi_Draw_StackLayoutStructure_GetSpacingForIndex_System_Int32_System_Single_
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: System.Single
    name: Single
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.single
  - name: )
- uid: DrawnUi.Draw.StackLayoutStructure.CreateWrapper(System.Int32,DrawnUi.Draw.SkiaControl)
  commentId: M:DrawnUi.Draw.StackLayoutStructure.CreateWrapper(System.Int32,DrawnUi.Draw.SkiaControl)
  parent: DrawnUi.Draw.StackLayoutStructure
  isExternal: true
  href: DrawnUi.Draw.StackLayoutStructure.html#DrawnUi_Draw_StackLayoutStructure_CreateWrapper_System_Int32_DrawnUi_Draw_SkiaControl_
  name: CreateWrapper(int, SkiaControl)
  nameWithType: StackLayoutStructure.CreateWrapper(int, SkiaControl)
  fullName: DrawnUi.Draw.StackLayoutStructure.CreateWrapper(int, DrawnUi.Draw.SkiaControl)
  nameWithType.vb: StackLayoutStructure.CreateWrapper(Integer, SkiaControl)
  fullName.vb: DrawnUi.Draw.StackLayoutStructure.CreateWrapper(Integer, DrawnUi.Draw.SkiaControl)
  name.vb: CreateWrapper(Integer, SkiaControl)
  spec.csharp:
  - uid: DrawnUi.Draw.StackLayoutStructure.CreateWrapper(System.Int32,DrawnUi.Draw.SkiaControl)
    name: CreateWrapper
    href: DrawnUi.Draw.StackLayoutStructure.html#DrawnUi_Draw_StackLayoutStructure_CreateWrapper_System_Int32_DrawnUi_Draw_SkiaControl_
  - name: (
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.StackLayoutStructure.CreateWrapper(System.Int32,DrawnUi.Draw.SkiaControl)
    name: CreateWrapper
    href: DrawnUi.Draw.StackLayoutStructure.html#DrawnUi_Draw_StackLayoutStructure_CreateWrapper_System_Int32_DrawnUi_Draw_SkiaControl_
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.SkiaLayout.BuildWrapLayout.#ctor*
  commentId: Overload:DrawnUi.Draw.SkiaLayout.BuildWrapLayout.#ctor
  href: DrawnUi.Draw.SkiaLayout.BuildWrapLayout.html#DrawnUi_Draw_SkiaLayout_BuildWrapLayout__ctor_DrawnUi_Draw_SkiaLayout_
  name: BuildWrapLayout
  nameWithType: SkiaLayout.BuildWrapLayout.BuildWrapLayout
  fullName: DrawnUi.Draw.SkiaLayout.BuildWrapLayout.BuildWrapLayout
  nameWithType.vb: SkiaLayout.BuildWrapLayout.New
  fullName.vb: DrawnUi.Draw.SkiaLayout.BuildWrapLayout.New
  name.vb: New
- uid: DrawnUi.Draw.SkiaLayout
  commentId: T:DrawnUi.Draw.SkiaLayout
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaLayout.html
  name: SkiaLayout
  nameWithType: SkiaLayout
  fullName: DrawnUi.Draw.SkiaLayout
- uid: DrawnUi.Draw.StackLayoutStructure.Build(SkiaSharp.SKRect,System.Single)
  commentId: M:DrawnUi.Draw.StackLayoutStructure.Build(SkiaSharp.SKRect,System.Single)
  parent: DrawnUi.Draw.StackLayoutStructure
  isExternal: true
  href: DrawnUi.Draw.StackLayoutStructure.html#DrawnUi_Draw_StackLayoutStructure_Build_SkiaSharp_SKRect_System_Single_
  name: Build(SKRect, float)
  nameWithType: StackLayoutStructure.Build(SKRect, float)
  fullName: DrawnUi.Draw.StackLayoutStructure.Build(SkiaSharp.SKRect, float)
  nameWithType.vb: StackLayoutStructure.Build(SKRect, Single)
  fullName.vb: DrawnUi.Draw.StackLayoutStructure.Build(SkiaSharp.SKRect, Single)
  name.vb: Build(SKRect, Single)
  spec.csharp:
  - uid: DrawnUi.Draw.StackLayoutStructure.Build(SkiaSharp.SKRect,System.Single)
    name: Build
    href: DrawnUi.Draw.StackLayoutStructure.html#DrawnUi_Draw_StackLayoutStructure_Build_SkiaSharp_SKRect_System_Single_
  - name: (
  - uid: SkiaSharp.SKRect
    name: SKRect
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skrect
  - name: ','
  - name: " "
  - uid: System.Single
    name: float
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.single
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.StackLayoutStructure.Build(SkiaSharp.SKRect,System.Single)
    name: Build
    href: DrawnUi.Draw.StackLayoutStructure.html#DrawnUi_Draw_StackLayoutStructure_Build_SkiaSharp_SKRect_System_Single_
  - name: (
  - uid: SkiaSharp.SKRect
    name: SKRect
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skrect
  - name: ','
  - name: " "
  - uid: System.Single
    name: Single
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.single
  - name: )
- uid: DrawnUi.Draw.SkiaLayout.BuildWrapLayout.Build*
  commentId: Overload:DrawnUi.Draw.SkiaLayout.BuildWrapLayout.Build
  href: DrawnUi.Draw.SkiaLayout.BuildWrapLayout.html#DrawnUi_Draw_SkiaLayout_BuildWrapLayout_Build_SkiaSharp_SKRect_System_Single_
  name: Build
  nameWithType: SkiaLayout.BuildWrapLayout.Build
  fullName: DrawnUi.Draw.SkiaLayout.BuildWrapLayout.Build
- uid: SkiaSharp.SKRect
  commentId: T:SkiaSharp.SKRect
  parent: SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.skrect
  name: SKRect
  nameWithType: SKRect
  fullName: SkiaSharp.SKRect
- uid: System.Single
  commentId: T:System.Single
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.single
  name: float
  nameWithType: float
  fullName: float
  nameWithType.vb: Single
  fullName.vb: Single
  name.vb: Single
- uid: DrawnUi.Draw.ScaledSize
  commentId: T:DrawnUi.Draw.ScaledSize
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ScaledSize.html
  name: ScaledSize
  nameWithType: ScaledSize
  fullName: DrawnUi.Draw.ScaledSize
- uid: SkiaSharp
  commentId: N:SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp
  name: SkiaSharp
  nameWithType: SkiaSharp
  fullName: SkiaSharp
