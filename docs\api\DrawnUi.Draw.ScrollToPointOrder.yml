### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.ScrollToPointOrder
  commentId: T:DrawnUi.Draw.ScrollToPointOrder
  id: ScrollToPointOrder
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.ScrollToPointOrder.Animated
  - DrawnUi.Draw.ScrollToPointOrder.IsValid
  - DrawnUi.Draw.ScrollToPointOrder.Location
  - DrawnUi.Draw.ScrollToPointOrder.MaxTimeSecs
  - DrawnUi.Draw.ScrollToPointOrder.NotValid
  - DrawnUi.Draw.ScrollToPointOrder.ToCoords(System.Single,System.Single,System.Boolean)
  - DrawnUi.Draw.ScrollToPointOrder.ToCoords(System.Single,System.Single,System.Single)
  - DrawnUi.Draw.ScrollToPointOrder.ToPoint(SkiaSharp.SKPoint,System.Boolean)
  langs:
  - csharp
  - vb
  name: ScrollToPointOrder
  nameWithType: ScrollToPointOrder
  fullName: DrawnUi.Draw.ScrollToPointOrder
  type: Struct
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Scroll/ScrollToIndexOrder.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ScrollToPointOrder
    path: ../src/Maui/DrawnUi/Draw/Scroll/ScrollToIndexOrder.cs
    startLine: 63
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public struct ScrollToPointOrder
    content.vb: Public Structure ScrollToPointOrder
  inheritedMembers:
  - System.ValueType.Equals(System.Object)
  - System.ValueType.GetHashCode
  - System.ValueType.ToString
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetType
  - System.Object.ReferenceEquals(System.Object,System.Object)
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.ScrollToPointOrder.IsValid
  commentId: P:DrawnUi.Draw.ScrollToPointOrder.IsValid
  id: IsValid
  parent: DrawnUi.Draw.ScrollToPointOrder
  langs:
  - csharp
  - vb
  name: IsValid
  nameWithType: ScrollToPointOrder.IsValid
  fullName: DrawnUi.Draw.ScrollToPointOrder.IsValid
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Scroll/ScrollToIndexOrder.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IsValid
    path: ../src/Maui/DrawnUi/Draw/Scroll/ScrollToIndexOrder.cs
    startLine: 65
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public bool IsValid { get; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public ReadOnly Property IsValid As Boolean
  overload: DrawnUi.Draw.ScrollToPointOrder.IsValid*
- uid: DrawnUi.Draw.ScrollToPointOrder.NotValid
  commentId: P:DrawnUi.Draw.ScrollToPointOrder.NotValid
  id: NotValid
  parent: DrawnUi.Draw.ScrollToPointOrder
  langs:
  - csharp
  - vb
  name: NotValid
  nameWithType: ScrollToPointOrder.NotValid
  fullName: DrawnUi.Draw.ScrollToPointOrder.NotValid
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Scroll/ScrollToIndexOrder.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: NotValid
    path: ../src/Maui/DrawnUi/Draw/Scroll/ScrollToIndexOrder.cs
    startLine: 73
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static ScrollToPointOrder NotValid { get; }
    parameters: []
    return:
      type: DrawnUi.Draw.ScrollToPointOrder
    content.vb: Public Shared ReadOnly Property NotValid As ScrollToPointOrder
  overload: DrawnUi.Draw.ScrollToPointOrder.NotValid*
- uid: DrawnUi.Draw.ScrollToPointOrder.ToPoint(SkiaSharp.SKPoint,System.Boolean)
  commentId: M:DrawnUi.Draw.ScrollToPointOrder.ToPoint(SkiaSharp.SKPoint,System.Boolean)
  id: ToPoint(SkiaSharp.SKPoint,System.Boolean)
  parent: DrawnUi.Draw.ScrollToPointOrder
  langs:
  - csharp
  - vb
  name: ToPoint(SKPoint, bool)
  nameWithType: ScrollToPointOrder.ToPoint(SKPoint, bool)
  fullName: DrawnUi.Draw.ScrollToPointOrder.ToPoint(SkiaSharp.SKPoint, bool)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Scroll/ScrollToIndexOrder.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ToPoint
    path: ../src/Maui/DrawnUi/Draw/Scroll/ScrollToIndexOrder.cs
    startLine: 79
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static ScrollToPointOrder ToPoint(SKPoint point, bool animated)
    parameters:
    - id: point
      type: SkiaSharp.SKPoint
    - id: animated
      type: System.Boolean
    return:
      type: DrawnUi.Draw.ScrollToPointOrder
    content.vb: Public Shared Function ToPoint(point As SKPoint, animated As Boolean) As ScrollToPointOrder
  overload: DrawnUi.Draw.ScrollToPointOrder.ToPoint*
  nameWithType.vb: ScrollToPointOrder.ToPoint(SKPoint, Boolean)
  fullName.vb: DrawnUi.Draw.ScrollToPointOrder.ToPoint(SkiaSharp.SKPoint, Boolean)
  name.vb: ToPoint(SKPoint, Boolean)
- uid: DrawnUi.Draw.ScrollToPointOrder.ToCoords(System.Single,System.Single,System.Boolean)
  commentId: M:DrawnUi.Draw.ScrollToPointOrder.ToCoords(System.Single,System.Single,System.Boolean)
  id: ToCoords(System.Single,System.Single,System.Boolean)
  parent: DrawnUi.Draw.ScrollToPointOrder
  langs:
  - csharp
  - vb
  name: ToCoords(float, float, bool)
  nameWithType: ScrollToPointOrder.ToCoords(float, float, bool)
  fullName: DrawnUi.Draw.ScrollToPointOrder.ToCoords(float, float, bool)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Scroll/ScrollToIndexOrder.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ToCoords
    path: ../src/Maui/DrawnUi/Draw/Scroll/ScrollToIndexOrder.cs
    startLine: 88
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static ScrollToPointOrder ToCoords(float x, float y, bool animated)
    parameters:
    - id: x
      type: System.Single
    - id: y
      type: System.Single
    - id: animated
      type: System.Boolean
    return:
      type: DrawnUi.Draw.ScrollToPointOrder
    content.vb: Public Shared Function ToCoords(x As Single, y As Single, animated As Boolean) As ScrollToPointOrder
  overload: DrawnUi.Draw.ScrollToPointOrder.ToCoords*
  nameWithType.vb: ScrollToPointOrder.ToCoords(Single, Single, Boolean)
  fullName.vb: DrawnUi.Draw.ScrollToPointOrder.ToCoords(Single, Single, Boolean)
  name.vb: ToCoords(Single, Single, Boolean)
- uid: DrawnUi.Draw.ScrollToPointOrder.ToCoords(System.Single,System.Single,System.Single)
  commentId: M:DrawnUi.Draw.ScrollToPointOrder.ToCoords(System.Single,System.Single,System.Single)
  id: ToCoords(System.Single,System.Single,System.Single)
  parent: DrawnUi.Draw.ScrollToPointOrder
  langs:
  - csharp
  - vb
  name: ToCoords(float, float, float)
  nameWithType: ScrollToPointOrder.ToCoords(float, float, float)
  fullName: DrawnUi.Draw.ScrollToPointOrder.ToCoords(float, float, float)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Scroll/ScrollToIndexOrder.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ToCoords
    path: ../src/Maui/DrawnUi/Draw/Scroll/ScrollToIndexOrder.cs
    startLine: 97
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static ScrollToPointOrder ToCoords(float x, float y, float maxTimeSecs)
    parameters:
    - id: x
      type: System.Single
    - id: y
      type: System.Single
    - id: maxTimeSecs
      type: System.Single
    return:
      type: DrawnUi.Draw.ScrollToPointOrder
    content.vb: Public Shared Function ToCoords(x As Single, y As Single, maxTimeSecs As Single) As ScrollToPointOrder
  overload: DrawnUi.Draw.ScrollToPointOrder.ToCoords*
  nameWithType.vb: ScrollToPointOrder.ToCoords(Single, Single, Single)
  fullName.vb: DrawnUi.Draw.ScrollToPointOrder.ToCoords(Single, Single, Single)
  name.vb: ToCoords(Single, Single, Single)
- uid: DrawnUi.Draw.ScrollToPointOrder.Animated
  commentId: P:DrawnUi.Draw.ScrollToPointOrder.Animated
  id: Animated
  parent: DrawnUi.Draw.ScrollToPointOrder
  langs:
  - csharp
  - vb
  name: Animated
  nameWithType: ScrollToPointOrder.Animated
  fullName: DrawnUi.Draw.ScrollToPointOrder.Animated
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Scroll/ScrollToIndexOrder.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Animated
    path: ../src/Maui/DrawnUi/Draw/Scroll/ScrollToIndexOrder.cs
    startLine: 106
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public bool Animated { readonly get; set; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property Animated As Boolean
  overload: DrawnUi.Draw.ScrollToPointOrder.Animated*
- uid: DrawnUi.Draw.ScrollToPointOrder.Location
  commentId: P:DrawnUi.Draw.ScrollToPointOrder.Location
  id: Location
  parent: DrawnUi.Draw.ScrollToPointOrder
  langs:
  - csharp
  - vb
  name: Location
  nameWithType: ScrollToPointOrder.Location
  fullName: DrawnUi.Draw.ScrollToPointOrder.Location
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Scroll/ScrollToIndexOrder.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Location
    path: ../src/Maui/DrawnUi/Draw/Scroll/ScrollToIndexOrder.cs
    startLine: 107
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public SKPoint Location { readonly get; set; }
    parameters: []
    return:
      type: SkiaSharp.SKPoint
    content.vb: Public Property Location As SKPoint
  overload: DrawnUi.Draw.ScrollToPointOrder.Location*
- uid: DrawnUi.Draw.ScrollToPointOrder.MaxTimeSecs
  commentId: P:DrawnUi.Draw.ScrollToPointOrder.MaxTimeSecs
  id: MaxTimeSecs
  parent: DrawnUi.Draw.ScrollToPointOrder
  langs:
  - csharp
  - vb
  name: MaxTimeSecs
  nameWithType: ScrollToPointOrder.MaxTimeSecs
  fullName: DrawnUi.Draw.ScrollToPointOrder.MaxTimeSecs
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Scroll/ScrollToIndexOrder.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: MaxTimeSecs
    path: ../src/Maui/DrawnUi/Draw/Scroll/ScrollToIndexOrder.cs
    startLine: 108
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public float MaxTimeSecs { readonly get; set; }
    parameters: []
    return:
      type: System.Single
    content.vb: Public Property MaxTimeSecs As Single
  overload: DrawnUi.Draw.ScrollToPointOrder.MaxTimeSecs*
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.ValueType.Equals(System.Object)
  commentId: M:System.ValueType.Equals(System.Object)
  parent: System.ValueType
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype.equals
  name: Equals(object)
  nameWithType: ValueType.Equals(object)
  fullName: System.ValueType.Equals(object)
  nameWithType.vb: ValueType.Equals(Object)
  fullName.vb: System.ValueType.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.ValueType.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.equals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.ValueType.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.equals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.ValueType.GetHashCode
  commentId: M:System.ValueType.GetHashCode
  parent: System.ValueType
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype.gethashcode
  name: GetHashCode()
  nameWithType: ValueType.GetHashCode()
  fullName: System.ValueType.GetHashCode()
  spec.csharp:
  - uid: System.ValueType.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.ValueType.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.gethashcode
  - name: (
  - name: )
- uid: System.ValueType.ToString
  commentId: M:System.ValueType.ToString
  parent: System.ValueType
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype.tostring
  name: ToString()
  nameWithType: ValueType.ToString()
  fullName: System.ValueType.ToString()
  spec.csharp:
  - uid: System.ValueType.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.ValueType.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.tostring
  - name: (
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.ValueType
  commentId: T:System.ValueType
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype
  name: ValueType
  nameWithType: ValueType
  fullName: System.ValueType
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.ScrollToPointOrder.IsValid*
  commentId: Overload:DrawnUi.Draw.ScrollToPointOrder.IsValid
  href: DrawnUi.Draw.ScrollToPointOrder.html#DrawnUi_Draw_ScrollToPointOrder_IsValid
  name: IsValid
  nameWithType: ScrollToPointOrder.IsValid
  fullName: DrawnUi.Draw.ScrollToPointOrder.IsValid
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: DrawnUi.Draw.ScrollToPointOrder.NotValid*
  commentId: Overload:DrawnUi.Draw.ScrollToPointOrder.NotValid
  href: DrawnUi.Draw.ScrollToPointOrder.html#DrawnUi_Draw_ScrollToPointOrder_NotValid
  name: NotValid
  nameWithType: ScrollToPointOrder.NotValid
  fullName: DrawnUi.Draw.ScrollToPointOrder.NotValid
- uid: DrawnUi.Draw.ScrollToPointOrder
  commentId: T:DrawnUi.Draw.ScrollToPointOrder
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ScrollToPointOrder.html
  name: ScrollToPointOrder
  nameWithType: ScrollToPointOrder
  fullName: DrawnUi.Draw.ScrollToPointOrder
- uid: DrawnUi.Draw.ScrollToPointOrder.ToPoint*
  commentId: Overload:DrawnUi.Draw.ScrollToPointOrder.ToPoint
  href: DrawnUi.Draw.ScrollToPointOrder.html#DrawnUi_Draw_ScrollToPointOrder_ToPoint_SkiaSharp_SKPoint_System_Boolean_
  name: ToPoint
  nameWithType: ScrollToPointOrder.ToPoint
  fullName: DrawnUi.Draw.ScrollToPointOrder.ToPoint
- uid: SkiaSharp.SKPoint
  commentId: T:SkiaSharp.SKPoint
  parent: SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.skpoint
  name: SKPoint
  nameWithType: SKPoint
  fullName: SkiaSharp.SKPoint
- uid: SkiaSharp
  commentId: N:SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp
  name: SkiaSharp
  nameWithType: SkiaSharp
  fullName: SkiaSharp
- uid: DrawnUi.Draw.ScrollToPointOrder.ToCoords*
  commentId: Overload:DrawnUi.Draw.ScrollToPointOrder.ToCoords
  href: DrawnUi.Draw.ScrollToPointOrder.html#DrawnUi_Draw_ScrollToPointOrder_ToCoords_System_Single_System_Single_System_Boolean_
  name: ToCoords
  nameWithType: ScrollToPointOrder.ToCoords
  fullName: DrawnUi.Draw.ScrollToPointOrder.ToCoords
- uid: System.Single
  commentId: T:System.Single
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.single
  name: float
  nameWithType: float
  fullName: float
  nameWithType.vb: Single
  fullName.vb: Single
  name.vb: Single
- uid: DrawnUi.Draw.ScrollToPointOrder.Animated*
  commentId: Overload:DrawnUi.Draw.ScrollToPointOrder.Animated
  href: DrawnUi.Draw.ScrollToPointOrder.html#DrawnUi_Draw_ScrollToPointOrder_Animated
  name: Animated
  nameWithType: ScrollToPointOrder.Animated
  fullName: DrawnUi.Draw.ScrollToPointOrder.Animated
- uid: DrawnUi.Draw.ScrollToPointOrder.Location*
  commentId: Overload:DrawnUi.Draw.ScrollToPointOrder.Location
  href: DrawnUi.Draw.ScrollToPointOrder.html#DrawnUi_Draw_ScrollToPointOrder_Location
  name: Location
  nameWithType: ScrollToPointOrder.Location
  fullName: DrawnUi.Draw.ScrollToPointOrder.Location
- uid: DrawnUi.Draw.ScrollToPointOrder.MaxTimeSecs*
  commentId: Overload:DrawnUi.Draw.ScrollToPointOrder.MaxTimeSecs
  href: DrawnUi.Draw.ScrollToPointOrder.html#DrawnUi_Draw_ScrollToPointOrder_MaxTimeSecs
  name: MaxTimeSecs
  nameWithType: ScrollToPointOrder.MaxTimeSecs
  fullName: DrawnUi.Draw.ScrollToPointOrder.MaxTimeSecs
