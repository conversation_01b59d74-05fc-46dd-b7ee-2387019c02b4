### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.FrameTimeInterpolator
  commentId: T:DrawnUi.Draw.FrameTimeInterpolator
  id: FrameTimeInterpolator
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.FrameTimeInterpolator.FpsBuffer
  - DrawnUi.Draw.FrameTimeInterpolator.GetDeltaTime(System.Single)
  - DrawnUi.Draw.FrameTimeInterpolator.Instance
  - DrawnUi.Draw.FrameTimeInterpolator.IsSkippingFrames
  - DrawnUi.Draw.FrameTimeInterpolator.LastFrameStep
  - DrawnUi.Draw.FrameTimeInterpolator.Reset
  - DrawnUi.Draw.FrameTimeInterpolator.ShouldReduceQuality
  - DrawnUi.Draw.FrameTimeInterpolator.TargetFps
  langs:
  - csharp
  - vb
  name: FrameTimeInterpolator
  nameWithType: FrameTimeInterpolator
  fullName: DrawnUi.Draw.FrameTimeInterpolator
  type: Class
  source:
    remote:
      path: src/Shared/Features/Animations/FrameTimeInterpolator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: FrameTimeInterpolator
    path: ../src/Shared/Features/Animations/FrameTimeInterpolator.cs
    startLine: 5
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Interpolated time between frames, works in seconds. See examples..
  example: []
  syntax:
    content: public class FrameTimeInterpolator
    content.vb: Public Class FrameTimeInterpolator
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.FrameTimeInterpolator.TargetFps
  commentId: P:DrawnUi.Draw.FrameTimeInterpolator.TargetFps
  id: TargetFps
  parent: DrawnUi.Draw.FrameTimeInterpolator
  langs:
  - csharp
  - vb
  name: TargetFps
  nameWithType: FrameTimeInterpolator.TargetFps
  fullName: DrawnUi.Draw.FrameTimeInterpolator.TargetFps
  type: Property
  source:
    remote:
      path: src/Shared/Features/Animations/FrameTimeInterpolator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: TargetFps
    path: ../src/Shared/Features/Animations/FrameTimeInterpolator.cs
    startLine: 34
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Gets or sets the target FPS for the application
  example: []
  syntax:
    content: public float TargetFps { get; set; }
    parameters: []
    return:
      type: System.Single
    content.vb: Public Property TargetFps As Single
  overload: DrawnUi.Draw.FrameTimeInterpolator.TargetFps*
- uid: DrawnUi.Draw.FrameTimeInterpolator.FpsBuffer
  commentId: P:DrawnUi.Draw.FrameTimeInterpolator.FpsBuffer
  id: FpsBuffer
  parent: DrawnUi.Draw.FrameTimeInterpolator
  langs:
  - csharp
  - vb
  name: FpsBuffer
  nameWithType: FrameTimeInterpolator.FpsBuffer
  fullName: DrawnUi.Draw.FrameTimeInterpolator.FpsBuffer
  type: Property
  source:
    remote:
      path: src/Shared/Features/Animations/FrameTimeInterpolator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: FpsBuffer
    path: ../src/Shared/Features/Animations/FrameTimeInterpolator.cs
    startLine: 55
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Gets or sets the FPS buffer below target where frame skipping begins
  example: []
  syntax:
    content: public float FpsBuffer { get; set; }
    parameters: []
    return:
      type: System.Single
    content.vb: Public Property FpsBuffer As Single
  overload: DrawnUi.Draw.FrameTimeInterpolator.FpsBuffer*
- uid: DrawnUi.Draw.FrameTimeInterpolator.LastFrameStep
  commentId: P:DrawnUi.Draw.FrameTimeInterpolator.LastFrameStep
  id: LastFrameStep
  parent: DrawnUi.Draw.FrameTimeInterpolator
  langs:
  - csharp
  - vb
  name: LastFrameStep
  nameWithType: FrameTimeInterpolator.LastFrameStep
  fullName: DrawnUi.Draw.FrameTimeInterpolator.LastFrameStep
  type: Property
  source:
    remote:
      path: src/Shared/Features/Animations/FrameTimeInterpolator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: LastFrameStep
    path: ../src/Shared/Features/Animations/FrameTimeInterpolator.cs
    startLine: 72
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public float LastFrameStep { get; protected set; }
    parameters: []
    return:
      type: System.Single
    content.vb: Public Property LastFrameStep As Single
  overload: DrawnUi.Draw.FrameTimeInterpolator.LastFrameStep*
- uid: DrawnUi.Draw.FrameTimeInterpolator.IsSkippingFrames
  commentId: P:DrawnUi.Draw.FrameTimeInterpolator.IsSkippingFrames
  id: IsSkippingFrames
  parent: DrawnUi.Draw.FrameTimeInterpolator
  langs:
  - csharp
  - vb
  name: IsSkippingFrames
  nameWithType: FrameTimeInterpolator.IsSkippingFrames
  fullName: DrawnUi.Draw.FrameTimeInterpolator.IsSkippingFrames
  type: Property
  source:
    remote:
      path: src/Shared/Features/Animations/FrameTimeInterpolator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IsSkippingFrames
    path: ../src/Shared/Features/Animations/FrameTimeInterpolator.cs
    startLine: 73
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public bool IsSkippingFrames { get; protected set; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property IsSkippingFrames As Boolean
  overload: DrawnUi.Draw.FrameTimeInterpolator.IsSkippingFrames*
- uid: DrawnUi.Draw.FrameTimeInterpolator.ShouldReduceQuality
  commentId: P:DrawnUi.Draw.FrameTimeInterpolator.ShouldReduceQuality
  id: ShouldReduceQuality
  parent: DrawnUi.Draw.FrameTimeInterpolator
  langs:
  - csharp
  - vb
  name: ShouldReduceQuality
  nameWithType: FrameTimeInterpolator.ShouldReduceQuality
  fullName: DrawnUi.Draw.FrameTimeInterpolator.ShouldReduceQuality
  type: Property
  source:
    remote:
      path: src/Shared/Features/Animations/FrameTimeInterpolator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ShouldReduceQuality
    path: ../src/Shared/Features/Animations/FrameTimeInterpolator.cs
    startLine: 74
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public bool ShouldReduceQuality { get; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property ShouldReduceQuality As Boolean
  overload: DrawnUi.Draw.FrameTimeInterpolator.ShouldReduceQuality*
- uid: DrawnUi.Draw.FrameTimeInterpolator.GetDeltaTime(System.Single)
  commentId: M:DrawnUi.Draw.FrameTimeInterpolator.GetDeltaTime(System.Single)
  id: GetDeltaTime(System.Single)
  parent: DrawnUi.Draw.FrameTimeInterpolator
  langs:
  - csharp
  - vb
  name: GetDeltaTime(float)
  nameWithType: FrameTimeInterpolator.GetDeltaTime(float)
  fullName: DrawnUi.Draw.FrameTimeInterpolator.GetDeltaTime(float)
  type: Method
  source:
    remote:
      path: src/Shared/Features/Animations/FrameTimeInterpolator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: GetDeltaTime
    path: ../src/Shared/Features/Animations/FrameTimeInterpolator.cs
    startLine: 82
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: >-
    Calculates the delta time for the current frame based on performance monitoring

    and the target FPS
  example: []
  syntax:
    content: public float GetDeltaTime(float currentFrameTime)
    parameters:
    - id: currentFrameTime
      type: System.Single
      description: The current frame time from the game loop
    return:
      type: System.Single
      description: The delta time to use for game updates
    content.vb: Public Function GetDeltaTime(currentFrameTime As Single) As Single
  overload: DrawnUi.Draw.FrameTimeInterpolator.GetDeltaTime*
  nameWithType.vb: FrameTimeInterpolator.GetDeltaTime(Single)
  fullName.vb: DrawnUi.Draw.FrameTimeInterpolator.GetDeltaTime(Single)
  name.vb: GetDeltaTime(Single)
- uid: DrawnUi.Draw.FrameTimeInterpolator.Reset
  commentId: M:DrawnUi.Draw.FrameTimeInterpolator.Reset
  id: Reset
  parent: DrawnUi.Draw.FrameTimeInterpolator
  langs:
  - csharp
  - vb
  name: Reset()
  nameWithType: FrameTimeInterpolator.Reset()
  fullName: DrawnUi.Draw.FrameTimeInterpolator.Reset()
  type: Method
  source:
    remote:
      path: src/Shared/Features/Animations/FrameTimeInterpolator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Reset
    path: ../src/Shared/Features/Animations/FrameTimeInterpolator.cs
    startLine: 132
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Resets the time interpolation state
  example: []
  syntax:
    content: public void Reset()
    content.vb: Public Sub Reset()
  overload: DrawnUi.Draw.FrameTimeInterpolator.Reset*
- uid: DrawnUi.Draw.FrameTimeInterpolator.Instance
  commentId: P:DrawnUi.Draw.FrameTimeInterpolator.Instance
  id: Instance
  parent: DrawnUi.Draw.FrameTimeInterpolator
  langs:
  - csharp
  - vb
  name: Instance
  nameWithType: FrameTimeInterpolator.Instance
  fullName: DrawnUi.Draw.FrameTimeInterpolator.Instance
  type: Property
  source:
    remote:
      path: src/Shared/Features/Animations/FrameTimeInterpolator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Instance
    path: ../src/Shared/Features/Animations/FrameTimeInterpolator.cs
    startLine: 233
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static FrameTimeInterpolator Instance { get; }
    parameters: []
    return:
      type: DrawnUi.Draw.FrameTimeInterpolator
    content.vb: Public Shared ReadOnly Property Instance As FrameTimeInterpolator
  overload: DrawnUi.Draw.FrameTimeInterpolator.Instance*
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.FrameTimeInterpolator.TargetFps*
  commentId: Overload:DrawnUi.Draw.FrameTimeInterpolator.TargetFps
  href: DrawnUi.Draw.FrameTimeInterpolator.html#DrawnUi_Draw_FrameTimeInterpolator_TargetFps
  name: TargetFps
  nameWithType: FrameTimeInterpolator.TargetFps
  fullName: DrawnUi.Draw.FrameTimeInterpolator.TargetFps
- uid: System.Single
  commentId: T:System.Single
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.single
  name: float
  nameWithType: float
  fullName: float
  nameWithType.vb: Single
  fullName.vb: Single
  name.vb: Single
- uid: DrawnUi.Draw.FrameTimeInterpolator.FpsBuffer*
  commentId: Overload:DrawnUi.Draw.FrameTimeInterpolator.FpsBuffer
  href: DrawnUi.Draw.FrameTimeInterpolator.html#DrawnUi_Draw_FrameTimeInterpolator_FpsBuffer
  name: FpsBuffer
  nameWithType: FrameTimeInterpolator.FpsBuffer
  fullName: DrawnUi.Draw.FrameTimeInterpolator.FpsBuffer
- uid: DrawnUi.Draw.FrameTimeInterpolator.LastFrameStep*
  commentId: Overload:DrawnUi.Draw.FrameTimeInterpolator.LastFrameStep
  href: DrawnUi.Draw.FrameTimeInterpolator.html#DrawnUi_Draw_FrameTimeInterpolator_LastFrameStep
  name: LastFrameStep
  nameWithType: FrameTimeInterpolator.LastFrameStep
  fullName: DrawnUi.Draw.FrameTimeInterpolator.LastFrameStep
- uid: DrawnUi.Draw.FrameTimeInterpolator.IsSkippingFrames*
  commentId: Overload:DrawnUi.Draw.FrameTimeInterpolator.IsSkippingFrames
  href: DrawnUi.Draw.FrameTimeInterpolator.html#DrawnUi_Draw_FrameTimeInterpolator_IsSkippingFrames
  name: IsSkippingFrames
  nameWithType: FrameTimeInterpolator.IsSkippingFrames
  fullName: DrawnUi.Draw.FrameTimeInterpolator.IsSkippingFrames
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: DrawnUi.Draw.FrameTimeInterpolator.ShouldReduceQuality*
  commentId: Overload:DrawnUi.Draw.FrameTimeInterpolator.ShouldReduceQuality
  href: DrawnUi.Draw.FrameTimeInterpolator.html#DrawnUi_Draw_FrameTimeInterpolator_ShouldReduceQuality
  name: ShouldReduceQuality
  nameWithType: FrameTimeInterpolator.ShouldReduceQuality
  fullName: DrawnUi.Draw.FrameTimeInterpolator.ShouldReduceQuality
- uid: DrawnUi.Draw.FrameTimeInterpolator.GetDeltaTime*
  commentId: Overload:DrawnUi.Draw.FrameTimeInterpolator.GetDeltaTime
  href: DrawnUi.Draw.FrameTimeInterpolator.html#DrawnUi_Draw_FrameTimeInterpolator_GetDeltaTime_System_Single_
  name: GetDeltaTime
  nameWithType: FrameTimeInterpolator.GetDeltaTime
  fullName: DrawnUi.Draw.FrameTimeInterpolator.GetDeltaTime
- uid: DrawnUi.Draw.FrameTimeInterpolator.Reset*
  commentId: Overload:DrawnUi.Draw.FrameTimeInterpolator.Reset
  href: DrawnUi.Draw.FrameTimeInterpolator.html#DrawnUi_Draw_FrameTimeInterpolator_Reset
  name: Reset
  nameWithType: FrameTimeInterpolator.Reset
  fullName: DrawnUi.Draw.FrameTimeInterpolator.Reset
- uid: DrawnUi.Draw.FrameTimeInterpolator.Instance*
  commentId: Overload:DrawnUi.Draw.FrameTimeInterpolator.Instance
  href: DrawnUi.Draw.FrameTimeInterpolator.html#DrawnUi_Draw_FrameTimeInterpolator_Instance
  name: Instance
  nameWithType: FrameTimeInterpolator.Instance
  fullName: DrawnUi.Draw.FrameTimeInterpolator.Instance
- uid: DrawnUi.Draw.FrameTimeInterpolator
  commentId: T:DrawnUi.Draw.FrameTimeInterpolator
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.FrameTimeInterpolator.html
  name: FrameTimeInterpolator
  nameWithType: FrameTimeInterpolator
  fullName: DrawnUi.Draw.FrameTimeInterpolator
