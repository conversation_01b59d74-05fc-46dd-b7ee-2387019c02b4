### YamlMime:ManagedReference
items:
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  id: DrawnUi.Extensions
  children:
  - DrawnUi.Extensions.FloatingPointExtensions
  - DrawnUi.Extensions.InternalExtensions
  - DrawnUi.Extensions.PointExtensions
  langs:
  - csharp
  - vb
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  type: Namespace
  assemblies:
  - DrawnUi.Maui
references:
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions.FloatingPointExtensions
  commentId: T:DrawnUi.Extensions.FloatingPointExtensions
  href: DrawnUi.Extensions.FloatingPointExtensions.html
  name: FloatingPointExtensions
  nameWithType: FloatingPointExtensions
  fullName: DrawnUi.Extensions.FloatingPointExtensions
- uid: DrawnUi.Extensions.PointExtensions
  commentId: T:DrawnUi.Extensions.PointExtensions
  href: DrawnUi.Extensions.PointExtensions.html
  name: PointExtensions
  nameWithType: PointExtensions
  fullName: DrawnUi.Extensions.PointExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
