### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.MeasuredListCells
  commentId: T:DrawnUi.Draw.MeasuredListCells
  id: MeasuredListCells
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.MeasuredListCells.#ctor(System.Collections.Generic.IList{DrawnUi.Draw.MeasuredListCell})
  langs:
  - csharp
  - vb
  name: MeasuredListCells
  nameWithType: MeasuredListCells
  fullName: DrawnUi.Draw.MeasuredListCells
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: MeasuredListCells
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
    startLine: 810
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: 'public class MeasuredListCells : ReadOnlyCollection<MeasuredListCell>, IList<MeasuredListCell>, ICollection<MeasuredListCell>, IReadOnlyList<MeasuredListCell>, IReadOnlyCollection<MeasuredListCell>, IEnumerable<MeasuredListCell>, IList, ICollection, IEnumerable'
    content.vb: Public Class MeasuredListCells Inherits ReadOnlyCollection(Of MeasuredListCell) Implements IList(Of MeasuredListCell), ICollection(Of MeasuredListCell), IReadOnlyList(Of MeasuredListCell), IReadOnlyCollection(Of MeasuredListCell), IEnumerable(Of MeasuredListCell), IList, ICollection, IEnumerable
  inheritance:
  - System.Object
  - System.Collections.ObjectModel.ReadOnlyCollection{DrawnUi.Draw.MeasuredListCell}
  implements:
  - System.Collections.Generic.IList{DrawnUi.Draw.MeasuredListCell}
  - System.Collections.Generic.ICollection{DrawnUi.Draw.MeasuredListCell}
  - System.Collections.Generic.IReadOnlyList{DrawnUi.Draw.MeasuredListCell}
  - System.Collections.Generic.IReadOnlyCollection{DrawnUi.Draw.MeasuredListCell}
  - System.Collections.Generic.IEnumerable{DrawnUi.Draw.MeasuredListCell}
  - System.Collections.IList
  - System.Collections.ICollection
  - System.Collections.IEnumerable
  inheritedMembers:
  - System.Collections.ObjectModel.ReadOnlyCollection{DrawnUi.Draw.MeasuredListCell}.Contains(DrawnUi.Draw.MeasuredListCell)
  - System.Collections.ObjectModel.ReadOnlyCollection{DrawnUi.Draw.MeasuredListCell}.CopyTo(DrawnUi.Draw.MeasuredListCell[],System.Int32)
  - System.Collections.ObjectModel.ReadOnlyCollection{DrawnUi.Draw.MeasuredListCell}.GetEnumerator
  - System.Collections.ObjectModel.ReadOnlyCollection{DrawnUi.Draw.MeasuredListCell}.IndexOf(DrawnUi.Draw.MeasuredListCell)
  - System.Collections.ObjectModel.ReadOnlyCollection{DrawnUi.Draw.MeasuredListCell}.Count
  - System.Collections.ObjectModel.ReadOnlyCollection{DrawnUi.Draw.MeasuredListCell}.Empty
  - System.Collections.ObjectModel.ReadOnlyCollection{DrawnUi.Draw.MeasuredListCell}.Item(System.Int32)
  - System.Collections.ObjectModel.ReadOnlyCollection{DrawnUi.Draw.MeasuredListCell}.Items
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.MeasuredListCells.#ctor(System.Collections.Generic.IList{DrawnUi.Draw.MeasuredListCell})
  commentId: M:DrawnUi.Draw.MeasuredListCells.#ctor(System.Collections.Generic.IList{DrawnUi.Draw.MeasuredListCell})
  id: '#ctor(System.Collections.Generic.IList{DrawnUi.Draw.MeasuredListCell})'
  parent: DrawnUi.Draw.MeasuredListCells
  langs:
  - csharp
  - vb
  name: MeasuredListCells(IList<MeasuredListCell>)
  nameWithType: MeasuredListCells.MeasuredListCells(IList<MeasuredListCell>)
  fullName: DrawnUi.Draw.MeasuredListCells.MeasuredListCells(System.Collections.Generic.IList<DrawnUi.Draw.MeasuredListCell>)
  type: Constructor
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
    startLine: 812
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public MeasuredListCells(IList<MeasuredListCell> list)
    parameters:
    - id: list
      type: System.Collections.Generic.IList{DrawnUi.Draw.MeasuredListCell}
    content.vb: Public Sub New(list As IList(Of MeasuredListCell))
  overload: DrawnUi.Draw.MeasuredListCells.#ctor*
  nameWithType.vb: MeasuredListCells.New(IList(Of MeasuredListCell))
  fullName.vb: DrawnUi.Draw.MeasuredListCells.New(System.Collections.Generic.IList(Of DrawnUi.Draw.MeasuredListCell))
  name.vb: New(IList(Of MeasuredListCell))
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Collections.ObjectModel.ReadOnlyCollection{DrawnUi.Draw.MeasuredListCell}
  commentId: T:System.Collections.ObjectModel.ReadOnlyCollection{DrawnUi.Draw.MeasuredListCell}
  parent: System.Collections.ObjectModel
  definition: System.Collections.ObjectModel.ReadOnlyCollection`1
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.readonlycollection-1
  name: ReadOnlyCollection<MeasuredListCell>
  nameWithType: ReadOnlyCollection<MeasuredListCell>
  fullName: System.Collections.ObjectModel.ReadOnlyCollection<DrawnUi.Draw.MeasuredListCell>
  nameWithType.vb: ReadOnlyCollection(Of MeasuredListCell)
  fullName.vb: System.Collections.ObjectModel.ReadOnlyCollection(Of DrawnUi.Draw.MeasuredListCell)
  name.vb: ReadOnlyCollection(Of MeasuredListCell)
  spec.csharp:
  - uid: System.Collections.ObjectModel.ReadOnlyCollection`1
    name: ReadOnlyCollection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.readonlycollection-1
  - name: <
  - uid: DrawnUi.Draw.MeasuredListCell
    name: MeasuredListCell
    href: DrawnUi.Draw.MeasuredListCell.html
  - name: '>'
  spec.vb:
  - uid: System.Collections.ObjectModel.ReadOnlyCollection`1
    name: ReadOnlyCollection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.readonlycollection-1
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Draw.MeasuredListCell
    name: MeasuredListCell
    href: DrawnUi.Draw.MeasuredListCell.html
  - name: )
- uid: System.Collections.Generic.IList{DrawnUi.Draw.MeasuredListCell}
  commentId: T:System.Collections.Generic.IList{DrawnUi.Draw.MeasuredListCell}
  parent: System.Collections.Generic
  definition: System.Collections.Generic.IList`1
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ilist-1
  name: IList<MeasuredListCell>
  nameWithType: IList<MeasuredListCell>
  fullName: System.Collections.Generic.IList<DrawnUi.Draw.MeasuredListCell>
  nameWithType.vb: IList(Of MeasuredListCell)
  fullName.vb: System.Collections.Generic.IList(Of DrawnUi.Draw.MeasuredListCell)
  name.vb: IList(Of MeasuredListCell)
  spec.csharp:
  - uid: System.Collections.Generic.IList`1
    name: IList
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ilist-1
  - name: <
  - uid: DrawnUi.Draw.MeasuredListCell
    name: MeasuredListCell
    href: DrawnUi.Draw.MeasuredListCell.html
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.IList`1
    name: IList
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ilist-1
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Draw.MeasuredListCell
    name: MeasuredListCell
    href: DrawnUi.Draw.MeasuredListCell.html
  - name: )
- uid: System.Collections.Generic.ICollection{DrawnUi.Draw.MeasuredListCell}
  commentId: T:System.Collections.Generic.ICollection{DrawnUi.Draw.MeasuredListCell}
  parent: System.Collections.Generic
  definition: System.Collections.Generic.ICollection`1
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.icollection-1
  name: ICollection<MeasuredListCell>
  nameWithType: ICollection<MeasuredListCell>
  fullName: System.Collections.Generic.ICollection<DrawnUi.Draw.MeasuredListCell>
  nameWithType.vb: ICollection(Of MeasuredListCell)
  fullName.vb: System.Collections.Generic.ICollection(Of DrawnUi.Draw.MeasuredListCell)
  name.vb: ICollection(Of MeasuredListCell)
  spec.csharp:
  - uid: System.Collections.Generic.ICollection`1
    name: ICollection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.icollection-1
  - name: <
  - uid: DrawnUi.Draw.MeasuredListCell
    name: MeasuredListCell
    href: DrawnUi.Draw.MeasuredListCell.html
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.ICollection`1
    name: ICollection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.icollection-1
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Draw.MeasuredListCell
    name: MeasuredListCell
    href: DrawnUi.Draw.MeasuredListCell.html
  - name: )
- uid: System.Collections.Generic.IReadOnlyList{DrawnUi.Draw.MeasuredListCell}
  commentId: T:System.Collections.Generic.IReadOnlyList{DrawnUi.Draw.MeasuredListCell}
  parent: System.Collections.Generic
  definition: System.Collections.Generic.IReadOnlyList`1
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlylist-1
  name: IReadOnlyList<MeasuredListCell>
  nameWithType: IReadOnlyList<MeasuredListCell>
  fullName: System.Collections.Generic.IReadOnlyList<DrawnUi.Draw.MeasuredListCell>
  nameWithType.vb: IReadOnlyList(Of MeasuredListCell)
  fullName.vb: System.Collections.Generic.IReadOnlyList(Of DrawnUi.Draw.MeasuredListCell)
  name.vb: IReadOnlyList(Of MeasuredListCell)
  spec.csharp:
  - uid: System.Collections.Generic.IReadOnlyList`1
    name: IReadOnlyList
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlylist-1
  - name: <
  - uid: DrawnUi.Draw.MeasuredListCell
    name: MeasuredListCell
    href: DrawnUi.Draw.MeasuredListCell.html
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.IReadOnlyList`1
    name: IReadOnlyList
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlylist-1
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Draw.MeasuredListCell
    name: MeasuredListCell
    href: DrawnUi.Draw.MeasuredListCell.html
  - name: )
- uid: System.Collections.Generic.IReadOnlyCollection{DrawnUi.Draw.MeasuredListCell}
  commentId: T:System.Collections.Generic.IReadOnlyCollection{DrawnUi.Draw.MeasuredListCell}
  parent: System.Collections.Generic
  definition: System.Collections.Generic.IReadOnlyCollection`1
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlycollection-1
  name: IReadOnlyCollection<MeasuredListCell>
  nameWithType: IReadOnlyCollection<MeasuredListCell>
  fullName: System.Collections.Generic.IReadOnlyCollection<DrawnUi.Draw.MeasuredListCell>
  nameWithType.vb: IReadOnlyCollection(Of MeasuredListCell)
  fullName.vb: System.Collections.Generic.IReadOnlyCollection(Of DrawnUi.Draw.MeasuredListCell)
  name.vb: IReadOnlyCollection(Of MeasuredListCell)
  spec.csharp:
  - uid: System.Collections.Generic.IReadOnlyCollection`1
    name: IReadOnlyCollection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlycollection-1
  - name: <
  - uid: DrawnUi.Draw.MeasuredListCell
    name: MeasuredListCell
    href: DrawnUi.Draw.MeasuredListCell.html
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.IReadOnlyCollection`1
    name: IReadOnlyCollection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlycollection-1
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Draw.MeasuredListCell
    name: MeasuredListCell
    href: DrawnUi.Draw.MeasuredListCell.html
  - name: )
- uid: System.Collections.Generic.IEnumerable{DrawnUi.Draw.MeasuredListCell}
  commentId: T:System.Collections.Generic.IEnumerable{DrawnUi.Draw.MeasuredListCell}
  parent: System.Collections.Generic
  definition: System.Collections.Generic.IEnumerable`1
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1
  name: IEnumerable<MeasuredListCell>
  nameWithType: IEnumerable<MeasuredListCell>
  fullName: System.Collections.Generic.IEnumerable<DrawnUi.Draw.MeasuredListCell>
  nameWithType.vb: IEnumerable(Of MeasuredListCell)
  fullName.vb: System.Collections.Generic.IEnumerable(Of DrawnUi.Draw.MeasuredListCell)
  name.vb: IEnumerable(Of MeasuredListCell)
  spec.csharp:
  - uid: System.Collections.Generic.IEnumerable`1
    name: IEnumerable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1
  - name: <
  - uid: DrawnUi.Draw.MeasuredListCell
    name: MeasuredListCell
    href: DrawnUi.Draw.MeasuredListCell.html
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.IEnumerable`1
    name: IEnumerable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Draw.MeasuredListCell
    name: MeasuredListCell
    href: DrawnUi.Draw.MeasuredListCell.html
  - name: )
- uid: System.Collections.IList
  commentId: T:System.Collections.IList
  parent: System.Collections
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.ilist
  name: IList
  nameWithType: IList
  fullName: System.Collections.IList
- uid: System.Collections.ICollection
  commentId: T:System.Collections.ICollection
  parent: System.Collections
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.icollection
  name: ICollection
  nameWithType: ICollection
  fullName: System.Collections.ICollection
- uid: System.Collections.IEnumerable
  commentId: T:System.Collections.IEnumerable
  parent: System.Collections
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.ienumerable
  name: IEnumerable
  nameWithType: IEnumerable
  fullName: System.Collections.IEnumerable
- uid: System.Collections.ObjectModel.ReadOnlyCollection{DrawnUi.Draw.MeasuredListCell}.Contains(DrawnUi.Draw.MeasuredListCell)
  commentId: M:System.Collections.ObjectModel.ReadOnlyCollection{DrawnUi.Draw.MeasuredListCell}.Contains(DrawnUi.Draw.MeasuredListCell)
  parent: System.Collections.ObjectModel.ReadOnlyCollection{DrawnUi.Draw.MeasuredListCell}
  definition: System.Collections.ObjectModel.ReadOnlyCollection`1.Contains(`0)
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.readonlycollection-1.contains
  name: Contains(MeasuredListCell)
  nameWithType: ReadOnlyCollection<MeasuredListCell>.Contains(MeasuredListCell)
  fullName: System.Collections.ObjectModel.ReadOnlyCollection<DrawnUi.Draw.MeasuredListCell>.Contains(DrawnUi.Draw.MeasuredListCell)
  nameWithType.vb: ReadOnlyCollection(Of MeasuredListCell).Contains(MeasuredListCell)
  fullName.vb: System.Collections.ObjectModel.ReadOnlyCollection(Of DrawnUi.Draw.MeasuredListCell).Contains(DrawnUi.Draw.MeasuredListCell)
  spec.csharp:
  - uid: System.Collections.ObjectModel.ReadOnlyCollection{DrawnUi.Draw.MeasuredListCell}.Contains(DrawnUi.Draw.MeasuredListCell)
    name: Contains
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.readonlycollection-1.contains
  - name: (
  - uid: DrawnUi.Draw.MeasuredListCell
    name: MeasuredListCell
    href: DrawnUi.Draw.MeasuredListCell.html
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.ReadOnlyCollection{DrawnUi.Draw.MeasuredListCell}.Contains(DrawnUi.Draw.MeasuredListCell)
    name: Contains
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.readonlycollection-1.contains
  - name: (
  - uid: DrawnUi.Draw.MeasuredListCell
    name: MeasuredListCell
    href: DrawnUi.Draw.MeasuredListCell.html
  - name: )
- uid: System.Collections.ObjectModel.ReadOnlyCollection{DrawnUi.Draw.MeasuredListCell}.CopyTo(DrawnUi.Draw.MeasuredListCell[],System.Int32)
  commentId: M:System.Collections.ObjectModel.ReadOnlyCollection{DrawnUi.Draw.MeasuredListCell}.CopyTo(DrawnUi.Draw.MeasuredListCell[],System.Int32)
  parent: System.Collections.ObjectModel.ReadOnlyCollection{DrawnUi.Draw.MeasuredListCell}
  definition: System.Collections.ObjectModel.ReadOnlyCollection`1.CopyTo(`0[],System.Int32)
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.readonlycollection-1.copyto
  name: CopyTo(MeasuredListCell[], int)
  nameWithType: ReadOnlyCollection<MeasuredListCell>.CopyTo(MeasuredListCell[], int)
  fullName: System.Collections.ObjectModel.ReadOnlyCollection<DrawnUi.Draw.MeasuredListCell>.CopyTo(DrawnUi.Draw.MeasuredListCell[], int)
  nameWithType.vb: ReadOnlyCollection(Of MeasuredListCell).CopyTo(MeasuredListCell(), Integer)
  fullName.vb: System.Collections.ObjectModel.ReadOnlyCollection(Of DrawnUi.Draw.MeasuredListCell).CopyTo(DrawnUi.Draw.MeasuredListCell(), Integer)
  name.vb: CopyTo(MeasuredListCell(), Integer)
  spec.csharp:
  - uid: System.Collections.ObjectModel.ReadOnlyCollection{DrawnUi.Draw.MeasuredListCell}.CopyTo(DrawnUi.Draw.MeasuredListCell[],System.Int32)
    name: CopyTo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.readonlycollection-1.copyto
  - name: (
  - uid: DrawnUi.Draw.MeasuredListCell
    name: MeasuredListCell
    href: DrawnUi.Draw.MeasuredListCell.html
  - name: '['
  - name: ']'
  - name: ','
  - name: " "
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.ReadOnlyCollection{DrawnUi.Draw.MeasuredListCell}.CopyTo(DrawnUi.Draw.MeasuredListCell[],System.Int32)
    name: CopyTo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.readonlycollection-1.copyto
  - name: (
  - uid: DrawnUi.Draw.MeasuredListCell
    name: MeasuredListCell
    href: DrawnUi.Draw.MeasuredListCell.html
  - name: (
  - name: )
  - name: ','
  - name: " "
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: System.Collections.ObjectModel.ReadOnlyCollection{DrawnUi.Draw.MeasuredListCell}.GetEnumerator
  commentId: M:System.Collections.ObjectModel.ReadOnlyCollection{DrawnUi.Draw.MeasuredListCell}.GetEnumerator
  parent: System.Collections.ObjectModel.ReadOnlyCollection{DrawnUi.Draw.MeasuredListCell}
  definition: System.Collections.ObjectModel.ReadOnlyCollection`1.GetEnumerator
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.readonlycollection-1.getenumerator
  name: GetEnumerator()
  nameWithType: ReadOnlyCollection<MeasuredListCell>.GetEnumerator()
  fullName: System.Collections.ObjectModel.ReadOnlyCollection<DrawnUi.Draw.MeasuredListCell>.GetEnumerator()
  nameWithType.vb: ReadOnlyCollection(Of MeasuredListCell).GetEnumerator()
  fullName.vb: System.Collections.ObjectModel.ReadOnlyCollection(Of DrawnUi.Draw.MeasuredListCell).GetEnumerator()
  spec.csharp:
  - uid: System.Collections.ObjectModel.ReadOnlyCollection{DrawnUi.Draw.MeasuredListCell}.GetEnumerator
    name: GetEnumerator
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.readonlycollection-1.getenumerator
  - name: (
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.ReadOnlyCollection{DrawnUi.Draw.MeasuredListCell}.GetEnumerator
    name: GetEnumerator
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.readonlycollection-1.getenumerator
  - name: (
  - name: )
- uid: System.Collections.ObjectModel.ReadOnlyCollection{DrawnUi.Draw.MeasuredListCell}.IndexOf(DrawnUi.Draw.MeasuredListCell)
  commentId: M:System.Collections.ObjectModel.ReadOnlyCollection{DrawnUi.Draw.MeasuredListCell}.IndexOf(DrawnUi.Draw.MeasuredListCell)
  parent: System.Collections.ObjectModel.ReadOnlyCollection{DrawnUi.Draw.MeasuredListCell}
  definition: System.Collections.ObjectModel.ReadOnlyCollection`1.IndexOf(`0)
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.readonlycollection-1.indexof
  name: IndexOf(MeasuredListCell)
  nameWithType: ReadOnlyCollection<MeasuredListCell>.IndexOf(MeasuredListCell)
  fullName: System.Collections.ObjectModel.ReadOnlyCollection<DrawnUi.Draw.MeasuredListCell>.IndexOf(DrawnUi.Draw.MeasuredListCell)
  nameWithType.vb: ReadOnlyCollection(Of MeasuredListCell).IndexOf(MeasuredListCell)
  fullName.vb: System.Collections.ObjectModel.ReadOnlyCollection(Of DrawnUi.Draw.MeasuredListCell).IndexOf(DrawnUi.Draw.MeasuredListCell)
  spec.csharp:
  - uid: System.Collections.ObjectModel.ReadOnlyCollection{DrawnUi.Draw.MeasuredListCell}.IndexOf(DrawnUi.Draw.MeasuredListCell)
    name: IndexOf
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.readonlycollection-1.indexof
  - name: (
  - uid: DrawnUi.Draw.MeasuredListCell
    name: MeasuredListCell
    href: DrawnUi.Draw.MeasuredListCell.html
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.ReadOnlyCollection{DrawnUi.Draw.MeasuredListCell}.IndexOf(DrawnUi.Draw.MeasuredListCell)
    name: IndexOf
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.readonlycollection-1.indexof
  - name: (
  - uid: DrawnUi.Draw.MeasuredListCell
    name: MeasuredListCell
    href: DrawnUi.Draw.MeasuredListCell.html
  - name: )
- uid: System.Collections.ObjectModel.ReadOnlyCollection{DrawnUi.Draw.MeasuredListCell}.Count
  commentId: P:System.Collections.ObjectModel.ReadOnlyCollection{DrawnUi.Draw.MeasuredListCell}.Count
  parent: System.Collections.ObjectModel.ReadOnlyCollection{DrawnUi.Draw.MeasuredListCell}
  definition: System.Collections.ObjectModel.ReadOnlyCollection`1.Count
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.readonlycollection-1.count
  name: Count
  nameWithType: ReadOnlyCollection<MeasuredListCell>.Count
  fullName: System.Collections.ObjectModel.ReadOnlyCollection<DrawnUi.Draw.MeasuredListCell>.Count
  nameWithType.vb: ReadOnlyCollection(Of MeasuredListCell).Count
  fullName.vb: System.Collections.ObjectModel.ReadOnlyCollection(Of DrawnUi.Draw.MeasuredListCell).Count
- uid: System.Collections.ObjectModel.ReadOnlyCollection{DrawnUi.Draw.MeasuredListCell}.Empty
  commentId: P:System.Collections.ObjectModel.ReadOnlyCollection{DrawnUi.Draw.MeasuredListCell}.Empty
  parent: System.Collections.ObjectModel.ReadOnlyCollection{DrawnUi.Draw.MeasuredListCell}
  definition: System.Collections.ObjectModel.ReadOnlyCollection`1.Empty
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.readonlycollection-1.empty
  name: Empty
  nameWithType: ReadOnlyCollection<MeasuredListCell>.Empty
  fullName: System.Collections.ObjectModel.ReadOnlyCollection<DrawnUi.Draw.MeasuredListCell>.Empty
  nameWithType.vb: ReadOnlyCollection(Of MeasuredListCell).Empty
  fullName.vb: System.Collections.ObjectModel.ReadOnlyCollection(Of DrawnUi.Draw.MeasuredListCell).Empty
- uid: System.Collections.ObjectModel.ReadOnlyCollection{DrawnUi.Draw.MeasuredListCell}.Item(System.Int32)
  commentId: P:System.Collections.ObjectModel.ReadOnlyCollection{DrawnUi.Draw.MeasuredListCell}.Item(System.Int32)
  parent: System.Collections.ObjectModel.ReadOnlyCollection{DrawnUi.Draw.MeasuredListCell}
  definition: System.Collections.ObjectModel.ReadOnlyCollection`1.Item(System.Int32)
  href: https://learn.microsoft.com/dotnet/api/system.int32
  name: this[int]
  nameWithType: ReadOnlyCollection<MeasuredListCell>.this[int]
  fullName: System.Collections.ObjectModel.ReadOnlyCollection<DrawnUi.Draw.MeasuredListCell>.this[int]
  nameWithType.vb: ReadOnlyCollection(Of MeasuredListCell).this[](Integer)
  fullName.vb: System.Collections.ObjectModel.ReadOnlyCollection(Of DrawnUi.Draw.MeasuredListCell).this[](Integer)
  name.vb: this[](Integer)
  spec.csharp:
  - name: this
  - name: '['
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ']'
  spec.vb:
  - uid: System.Collections.ObjectModel.ReadOnlyCollection{DrawnUi.Draw.MeasuredListCell}.Item(System.Int32)
    name: this[]
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.readonlycollection-1.item
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: System.Collections.ObjectModel.ReadOnlyCollection{DrawnUi.Draw.MeasuredListCell}.Items
  commentId: P:System.Collections.ObjectModel.ReadOnlyCollection{DrawnUi.Draw.MeasuredListCell}.Items
  parent: System.Collections.ObjectModel.ReadOnlyCollection{DrawnUi.Draw.MeasuredListCell}
  definition: System.Collections.ObjectModel.ReadOnlyCollection`1.Items
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.readonlycollection-1.items
  name: Items
  nameWithType: ReadOnlyCollection<MeasuredListCell>.Items
  fullName: System.Collections.ObjectModel.ReadOnlyCollection<DrawnUi.Draw.MeasuredListCell>.Items
  nameWithType.vb: ReadOnlyCollection(Of MeasuredListCell).Items
  fullName.vb: System.Collections.ObjectModel.ReadOnlyCollection(Of DrawnUi.Draw.MeasuredListCell).Items
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: System.Collections.ObjectModel.ReadOnlyCollection`1
  commentId: T:System.Collections.ObjectModel.ReadOnlyCollection`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.readonlycollection-1
  name: ReadOnlyCollection<T>
  nameWithType: ReadOnlyCollection<T>
  fullName: System.Collections.ObjectModel.ReadOnlyCollection<T>
  nameWithType.vb: ReadOnlyCollection(Of T)
  fullName.vb: System.Collections.ObjectModel.ReadOnlyCollection(Of T)
  name.vb: ReadOnlyCollection(Of T)
  spec.csharp:
  - uid: System.Collections.ObjectModel.ReadOnlyCollection`1
    name: ReadOnlyCollection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.readonlycollection-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Collections.ObjectModel.ReadOnlyCollection`1
    name: ReadOnlyCollection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.readonlycollection-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: System.Collections.ObjectModel
  commentId: N:System.Collections.ObjectModel
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Collections.ObjectModel
  nameWithType: System.Collections.ObjectModel
  fullName: System.Collections.ObjectModel
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.ObjectModel
    name: ObjectModel
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.ObjectModel
    name: ObjectModel
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel
- uid: System.Collections.Generic.IList`1
  commentId: T:System.Collections.Generic.IList`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ilist-1
  name: IList<T>
  nameWithType: IList<T>
  fullName: System.Collections.Generic.IList<T>
  nameWithType.vb: IList(Of T)
  fullName.vb: System.Collections.Generic.IList(Of T)
  name.vb: IList(Of T)
  spec.csharp:
  - uid: System.Collections.Generic.IList`1
    name: IList
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ilist-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.IList`1
    name: IList
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ilist-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: System.Collections.Generic
  commentId: N:System.Collections.Generic
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Collections.Generic
  nameWithType: System.Collections.Generic
  fullName: System.Collections.Generic
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Generic
    name: Generic
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Generic
    name: Generic
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic
- uid: System.Collections.Generic.ICollection`1
  commentId: T:System.Collections.Generic.ICollection`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.icollection-1
  name: ICollection<T>
  nameWithType: ICollection<T>
  fullName: System.Collections.Generic.ICollection<T>
  nameWithType.vb: ICollection(Of T)
  fullName.vb: System.Collections.Generic.ICollection(Of T)
  name.vb: ICollection(Of T)
  spec.csharp:
  - uid: System.Collections.Generic.ICollection`1
    name: ICollection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.icollection-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.ICollection`1
    name: ICollection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.icollection-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: System.Collections.Generic.IReadOnlyList`1
  commentId: T:System.Collections.Generic.IReadOnlyList`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlylist-1
  name: IReadOnlyList<T>
  nameWithType: IReadOnlyList<T>
  fullName: System.Collections.Generic.IReadOnlyList<T>
  nameWithType.vb: IReadOnlyList(Of T)
  fullName.vb: System.Collections.Generic.IReadOnlyList(Of T)
  name.vb: IReadOnlyList(Of T)
  spec.csharp:
  - uid: System.Collections.Generic.IReadOnlyList`1
    name: IReadOnlyList
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlylist-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.IReadOnlyList`1
    name: IReadOnlyList
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlylist-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: System.Collections.Generic.IReadOnlyCollection`1
  commentId: T:System.Collections.Generic.IReadOnlyCollection`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlycollection-1
  name: IReadOnlyCollection<T>
  nameWithType: IReadOnlyCollection<T>
  fullName: System.Collections.Generic.IReadOnlyCollection<T>
  nameWithType.vb: IReadOnlyCollection(Of T)
  fullName.vb: System.Collections.Generic.IReadOnlyCollection(Of T)
  name.vb: IReadOnlyCollection(Of T)
  spec.csharp:
  - uid: System.Collections.Generic.IReadOnlyCollection`1
    name: IReadOnlyCollection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlycollection-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.IReadOnlyCollection`1
    name: IReadOnlyCollection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlycollection-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: System.Collections.Generic.IEnumerable`1
  commentId: T:System.Collections.Generic.IEnumerable`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1
  name: IEnumerable<T>
  nameWithType: IEnumerable<T>
  fullName: System.Collections.Generic.IEnumerable<T>
  nameWithType.vb: IEnumerable(Of T)
  fullName.vb: System.Collections.Generic.IEnumerable(Of T)
  name.vb: IEnumerable(Of T)
  spec.csharp:
  - uid: System.Collections.Generic.IEnumerable`1
    name: IEnumerable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.IEnumerable`1
    name: IEnumerable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: System.Collections
  commentId: N:System.Collections
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Collections
  nameWithType: System.Collections
  fullName: System.Collections
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
- uid: System.Collections.ObjectModel.ReadOnlyCollection`1.Contains(`0)
  commentId: M:System.Collections.ObjectModel.ReadOnlyCollection`1.Contains(`0)
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.readonlycollection-1.contains
  name: Contains(T)
  nameWithType: ReadOnlyCollection<T>.Contains(T)
  fullName: System.Collections.ObjectModel.ReadOnlyCollection<T>.Contains(T)
  nameWithType.vb: ReadOnlyCollection(Of T).Contains(T)
  fullName.vb: System.Collections.ObjectModel.ReadOnlyCollection(Of T).Contains(T)
  spec.csharp:
  - uid: System.Collections.ObjectModel.ReadOnlyCollection`1.Contains(`0)
    name: Contains
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.readonlycollection-1.contains
  - name: (
  - name: T
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.ReadOnlyCollection`1.Contains(`0)
    name: Contains
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.readonlycollection-1.contains
  - name: (
  - name: T
  - name: )
- uid: System.Collections.ObjectModel.ReadOnlyCollection`1.CopyTo(`0[],System.Int32)
  commentId: M:System.Collections.ObjectModel.ReadOnlyCollection`1.CopyTo(`0[],System.Int32)
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.readonlycollection-1.copyto
  name: CopyTo(T[], int)
  nameWithType: ReadOnlyCollection<T>.CopyTo(T[], int)
  fullName: System.Collections.ObjectModel.ReadOnlyCollection<T>.CopyTo(T[], int)
  nameWithType.vb: ReadOnlyCollection(Of T).CopyTo(T(), Integer)
  fullName.vb: System.Collections.ObjectModel.ReadOnlyCollection(Of T).CopyTo(T(), Integer)
  name.vb: CopyTo(T(), Integer)
  spec.csharp:
  - uid: System.Collections.ObjectModel.ReadOnlyCollection`1.CopyTo(`0[],System.Int32)
    name: CopyTo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.readonlycollection-1.copyto
  - name: (
  - name: T
  - name: '['
  - name: ']'
  - name: ','
  - name: " "
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.ReadOnlyCollection`1.CopyTo(`0[],System.Int32)
    name: CopyTo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.readonlycollection-1.copyto
  - name: (
  - name: T
  - name: (
  - name: )
  - name: ','
  - name: " "
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: System.Collections.ObjectModel.ReadOnlyCollection`1.GetEnumerator
  commentId: M:System.Collections.ObjectModel.ReadOnlyCollection`1.GetEnumerator
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.readonlycollection-1.getenumerator
  name: GetEnumerator()
  nameWithType: ReadOnlyCollection<T>.GetEnumerator()
  fullName: System.Collections.ObjectModel.ReadOnlyCollection<T>.GetEnumerator()
  nameWithType.vb: ReadOnlyCollection(Of T).GetEnumerator()
  fullName.vb: System.Collections.ObjectModel.ReadOnlyCollection(Of T).GetEnumerator()
  spec.csharp:
  - uid: System.Collections.ObjectModel.ReadOnlyCollection`1.GetEnumerator
    name: GetEnumerator
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.readonlycollection-1.getenumerator
  - name: (
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.ReadOnlyCollection`1.GetEnumerator
    name: GetEnumerator
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.readonlycollection-1.getenumerator
  - name: (
  - name: )
- uid: System.Collections.ObjectModel.ReadOnlyCollection`1.IndexOf(`0)
  commentId: M:System.Collections.ObjectModel.ReadOnlyCollection`1.IndexOf(`0)
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.readonlycollection-1.indexof
  name: IndexOf(T)
  nameWithType: ReadOnlyCollection<T>.IndexOf(T)
  fullName: System.Collections.ObjectModel.ReadOnlyCollection<T>.IndexOf(T)
  nameWithType.vb: ReadOnlyCollection(Of T).IndexOf(T)
  fullName.vb: System.Collections.ObjectModel.ReadOnlyCollection(Of T).IndexOf(T)
  spec.csharp:
  - uid: System.Collections.ObjectModel.ReadOnlyCollection`1.IndexOf(`0)
    name: IndexOf
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.readonlycollection-1.indexof
  - name: (
  - name: T
  - name: )
  spec.vb:
  - uid: System.Collections.ObjectModel.ReadOnlyCollection`1.IndexOf(`0)
    name: IndexOf
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.readonlycollection-1.indexof
  - name: (
  - name: T
  - name: )
- uid: System.Collections.ObjectModel.ReadOnlyCollection`1.Count
  commentId: P:System.Collections.ObjectModel.ReadOnlyCollection`1.Count
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.readonlycollection-1.count
  name: Count
  nameWithType: ReadOnlyCollection<T>.Count
  fullName: System.Collections.ObjectModel.ReadOnlyCollection<T>.Count
  nameWithType.vb: ReadOnlyCollection(Of T).Count
  fullName.vb: System.Collections.ObjectModel.ReadOnlyCollection(Of T).Count
- uid: System.Collections.ObjectModel.ReadOnlyCollection`1.Empty
  commentId: P:System.Collections.ObjectModel.ReadOnlyCollection`1.Empty
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.readonlycollection-1.empty
  name: Empty
  nameWithType: ReadOnlyCollection<T>.Empty
  fullName: System.Collections.ObjectModel.ReadOnlyCollection<T>.Empty
  nameWithType.vb: ReadOnlyCollection(Of T).Empty
  fullName.vb: System.Collections.ObjectModel.ReadOnlyCollection(Of T).Empty
- uid: System.Collections.ObjectModel.ReadOnlyCollection`1.Item(System.Int32)
  commentId: P:System.Collections.ObjectModel.ReadOnlyCollection`1.Item(System.Int32)
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.int32
  name: this[int]
  nameWithType: ReadOnlyCollection<T>.this[int]
  fullName: System.Collections.ObjectModel.ReadOnlyCollection<T>.this[int]
  nameWithType.vb: ReadOnlyCollection(Of T).this[](Integer)
  fullName.vb: System.Collections.ObjectModel.ReadOnlyCollection(Of T).this[](Integer)
  name.vb: this[](Integer)
  spec.csharp:
  - name: this
  - name: '['
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ']'
  spec.vb:
  - uid: System.Collections.ObjectModel.ReadOnlyCollection`1.Item(System.Int32)
    name: this[]
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.readonlycollection-1.item
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: System.Collections.ObjectModel.ReadOnlyCollection`1.Items
  commentId: P:System.Collections.ObjectModel.ReadOnlyCollection`1.Items
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.readonlycollection-1.items
  name: Items
  nameWithType: ReadOnlyCollection<T>.Items
  fullName: System.Collections.ObjectModel.ReadOnlyCollection<T>.Items
  nameWithType.vb: ReadOnlyCollection(Of T).Items
  fullName.vb: System.Collections.ObjectModel.ReadOnlyCollection(Of T).Items
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.MeasuredListCells.#ctor*
  commentId: Overload:DrawnUi.Draw.MeasuredListCells.#ctor
  href: DrawnUi.Draw.MeasuredListCells.html#DrawnUi_Draw_MeasuredListCells__ctor_System_Collections_Generic_IList_DrawnUi_Draw_MeasuredListCell__
  name: MeasuredListCells
  nameWithType: MeasuredListCells.MeasuredListCells
  fullName: DrawnUi.Draw.MeasuredListCells.MeasuredListCells
  nameWithType.vb: MeasuredListCells.New
  fullName.vb: DrawnUi.Draw.MeasuredListCells.New
  name.vb: New
