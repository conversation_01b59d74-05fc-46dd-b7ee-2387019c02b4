### YamlMime:ManagedReference
items:
- uid: DrawnUi.Controls.NavigationSource
  commentId: T:DrawnUi.Controls.NavigationSource
  id: NavigationSource
  parent: DrawnUi.Controls
  children:
  - DrawnUi.Controls.NavigationSource.Pop
  - DrawnUi.Controls.NavigationSource.Push
  - DrawnUi.Controls.NavigationSource.Unknown
  langs:
  - csharp
  - vb
  name: NavigationSource
  nameWithType: NavigationSource
  fullName: DrawnUi.Controls.NavigationSource
  type: Enum
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/Navigation/NavigationSource.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: NavigationSource
    path: ../src/Maui/DrawnUi/Controls/Navigation/NavigationSource.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: public enum NavigationSource
    content.vb: Public Enum NavigationSource
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Controls.NavigationSource.Unknown
  commentId: F:DrawnUi.Controls.NavigationSource.Unknown
  id: Unknown
  parent: DrawnUi.Controls.NavigationSource
  langs:
  - csharp
  - vb
  name: Unknown
  nameWithType: NavigationSource.Unknown
  fullName: DrawnUi.Controls.NavigationSource.Unknown
  type: Field
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/Navigation/NavigationSource.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Unknown
    path: ../src/Maui/DrawnUi/Controls/Navigation/NavigationSource.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: Unknown = 0
    return:
      type: DrawnUi.Controls.NavigationSource
- uid: DrawnUi.Controls.NavigationSource.Push
  commentId: F:DrawnUi.Controls.NavigationSource.Push
  id: Push
  parent: DrawnUi.Controls.NavigationSource
  langs:
  - csharp
  - vb
  name: Push
  nameWithType: NavigationSource.Push
  fullName: DrawnUi.Controls.NavigationSource.Push
  type: Field
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/Navigation/NavigationSource.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Push
    path: ../src/Maui/DrawnUi/Controls/Navigation/NavigationSource.cs
    startLine: 5
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: Push = 1
    return:
      type: DrawnUi.Controls.NavigationSource
- uid: DrawnUi.Controls.NavigationSource.Pop
  commentId: F:DrawnUi.Controls.NavigationSource.Pop
  id: Pop
  parent: DrawnUi.Controls.NavigationSource
  langs:
  - csharp
  - vb
  name: Pop
  nameWithType: NavigationSource.Pop
  fullName: DrawnUi.Controls.NavigationSource.Pop
  type: Field
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/Navigation/NavigationSource.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Pop
    path: ../src/Maui/DrawnUi/Controls/Navigation/NavigationSource.cs
    startLine: 6
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: Pop = 2
    return:
      type: DrawnUi.Controls.NavigationSource
references:
- uid: DrawnUi.Controls
  commentId: N:DrawnUi.Controls
  href: DrawnUi.html
  name: DrawnUi.Controls
  nameWithType: DrawnUi.Controls
  fullName: DrawnUi.Controls
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Controls
    name: Controls
    href: DrawnUi.Controls.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Controls
    name: Controls
    href: DrawnUi.Controls.html
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Controls.NavigationSource
  commentId: T:DrawnUi.Controls.NavigationSource
  parent: DrawnUi.Controls
  href: DrawnUi.Controls.NavigationSource.html
  name: NavigationSource
  nameWithType: NavigationSource
  fullName: DrawnUi.Controls.NavigationSource
