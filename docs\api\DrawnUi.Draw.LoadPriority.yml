### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.LoadPriority
  commentId: T:DrawnUi.Draw.LoadPriority
  id: LoadPriority
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.LoadPriority.High
  - DrawnUi.Draw.LoadPriority.Low
  - DrawnUi.Draw.LoadPriority.Normal
  langs:
  - csharp
  - vb
  name: LoadPriority
  nameWithType: LoadPriority
  fullName: DrawnUi.Draw.LoadPriority
  type: Enum
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Images/LoadPriority.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: LoadPriority
    path: ../src/Maui/DrawnUi/Features/Images/LoadPriority.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public enum LoadPriority
    content.vb: Public Enum LoadPriority
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.LoadPriority.Low
  commentId: F:DrawnUi.Draw.LoadPriority.Low
  id: Low
  parent: DrawnUi.Draw.LoadPriority
  langs:
  - csharp
  - vb
  name: Low
  nameWithType: LoadPriority.Low
  fullName: DrawnUi.Draw.LoadPriority.Low
  type: Field
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Images/LoadPriority.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Low
    path: ../src/Maui/DrawnUi/Features/Images/LoadPriority.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: Low = 0
    return:
      type: DrawnUi.Draw.LoadPriority
- uid: DrawnUi.Draw.LoadPriority.Normal
  commentId: F:DrawnUi.Draw.LoadPriority.Normal
  id: Normal
  parent: DrawnUi.Draw.LoadPriority
  langs:
  - csharp
  - vb
  name: Normal
  nameWithType: LoadPriority.Normal
  fullName: DrawnUi.Draw.LoadPriority.Normal
  type: Field
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Images/LoadPriority.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Normal
    path: ../src/Maui/DrawnUi/Features/Images/LoadPriority.cs
    startLine: 5
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: Normal = 1
    return:
      type: DrawnUi.Draw.LoadPriority
- uid: DrawnUi.Draw.LoadPriority.High
  commentId: F:DrawnUi.Draw.LoadPriority.High
  id: High
  parent: DrawnUi.Draw.LoadPriority
  langs:
  - csharp
  - vb
  name: High
  nameWithType: LoadPriority.High
  fullName: DrawnUi.Draw.LoadPriority.High
  type: Field
  source:
    remote:
      path: src/Maui/DrawnUi/Features/Images/LoadPriority.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: High
    path: ../src/Maui/DrawnUi/Features/Images/LoadPriority.cs
    startLine: 6
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: High = 2
    return:
      type: DrawnUi.Draw.LoadPriority
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.LoadPriority
  commentId: T:DrawnUi.Draw.LoadPriority
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.LoadPriority.html
  name: LoadPriority
  nameWithType: LoadPriority
  fullName: DrawnUi.Draw.LoadPriority
