### YamlMime:ManagedReference
items:
- uid: DrawnUi.Controls.RadioButtonGroupManager
  commentId: T:DrawnUi.Controls.RadioButtonGroupManager
  id: RadioButtonGroupManager
  parent: DrawnUi.Controls
  children:
  - DrawnUi.Controls.RadioButtonGroupManager.#ctor
  - DrawnUi.Controls.RadioButtonGroupManager.AddToGroup(DrawnUi.Controls.ISkiaRadioButton,DrawnUi.Draw.SkiaControl)
  - DrawnUi.Controls.RadioButtonGroupManager.AddToGroup(DrawnUi.Controls.ISkiaRadioButton,System.String)
  - DrawnUi.Controls.RadioButtonGroupManager.GroupsByName
  - DrawnUi.Controls.RadioButtonGroupManager.GroupsByParent
  - DrawnUi.Controls.RadioButtonGroupManager.Instance
  - DrawnUi.Controls.RadioButtonGroupManager.RemoveFromGroup(DrawnUi.Draw.SkiaControl,DrawnUi.Controls.ISkiaRadioButton)
  - DrawnUi.Controls.RadioButtonGroupManager.RemoveFromGroup(System.String,DrawnUi.Controls.ISkiaRadioButton)
  - DrawnUi.Controls.RadioButtonGroupManager.RemoveFromGroups(DrawnUi.Controls.ISkiaRadioButton)
  - DrawnUi.Controls.RadioButtonGroupManager.ReportValueChange(DrawnUi.Controls.ISkiaRadioButton,System.Boolean)
  langs:
  - csharp
  - vb
  name: RadioButtonGroupManager
  nameWithType: RadioButtonGroupManager
  fullName: DrawnUi.Controls.RadioButtonGroupManager
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/RadioButtons/RadioButtonsManager.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: RadioButtonGroupManager
    path: ../src/Maui/DrawnUi/Controls/RadioButtons/RadioButtonsManager.cs
    startLine: 7
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: public class RadioButtonGroupManager
    content.vb: Public Class RadioButtonGroupManager
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Controls.RadioButtonGroupManager.Instance
  commentId: P:DrawnUi.Controls.RadioButtonGroupManager.Instance
  id: Instance
  parent: DrawnUi.Controls.RadioButtonGroupManager
  langs:
  - csharp
  - vb
  name: Instance
  nameWithType: RadioButtonGroupManager.Instance
  fullName: DrawnUi.Controls.RadioButtonGroupManager.Instance
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/RadioButtons/RadioButtonsManager.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Instance
    path: ../src/Maui/DrawnUi/Controls/RadioButtons/RadioButtonsManager.cs
    startLine: 11
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: public static RadioButtonGroupManager Instance { get; }
    parameters: []
    return:
      type: DrawnUi.Controls.RadioButtonGroupManager
    content.vb: Public Shared ReadOnly Property Instance As RadioButtonGroupManager
  overload: DrawnUi.Controls.RadioButtonGroupManager.Instance*
- uid: DrawnUi.Controls.RadioButtonGroupManager.GroupsByName
  commentId: P:DrawnUi.Controls.RadioButtonGroupManager.GroupsByName
  id: GroupsByName
  parent: DrawnUi.Controls.RadioButtonGroupManager
  langs:
  - csharp
  - vb
  name: GroupsByName
  nameWithType: RadioButtonGroupManager.GroupsByName
  fullName: DrawnUi.Controls.RadioButtonGroupManager.GroupsByName
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/RadioButtons/RadioButtonsManager.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: GroupsByName
    path: ../src/Maui/DrawnUi/Controls/RadioButtons/RadioButtonsManager.cs
    startLine: 22
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: protected Dictionary<string, List<ISkiaRadioButton>> GroupsByName { get; }
    parameters: []
    return:
      type: System.Collections.Generic.Dictionary{System.String,System.Collections.Generic.List{DrawnUi.Controls.ISkiaRadioButton}}
    content.vb: Protected Property GroupsByName As Dictionary(Of String, List(Of ISkiaRadioButton))
  overload: DrawnUi.Controls.RadioButtonGroupManager.GroupsByName*
- uid: DrawnUi.Controls.RadioButtonGroupManager.GroupsByParent
  commentId: P:DrawnUi.Controls.RadioButtonGroupManager.GroupsByParent
  id: GroupsByParent
  parent: DrawnUi.Controls.RadioButtonGroupManager
  langs:
  - csharp
  - vb
  name: GroupsByParent
  nameWithType: RadioButtonGroupManager.GroupsByParent
  fullName: DrawnUi.Controls.RadioButtonGroupManager.GroupsByParent
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/RadioButtons/RadioButtonsManager.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: GroupsByParent
    path: ../src/Maui/DrawnUi/Controls/RadioButtons/RadioButtonsManager.cs
    startLine: 23
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: protected Dictionary<SkiaControl, List<ISkiaRadioButton>> GroupsByParent { get; }
    parameters: []
    return:
      type: System.Collections.Generic.Dictionary{DrawnUi.Draw.SkiaControl,System.Collections.Generic.List{DrawnUi.Controls.ISkiaRadioButton}}
    content.vb: Protected Property GroupsByParent As Dictionary(Of SkiaControl, List(Of ISkiaRadioButton))
  overload: DrawnUi.Controls.RadioButtonGroupManager.GroupsByParent*
- uid: DrawnUi.Controls.RadioButtonGroupManager.#ctor
  commentId: M:DrawnUi.Controls.RadioButtonGroupManager.#ctor
  id: '#ctor'
  parent: DrawnUi.Controls.RadioButtonGroupManager
  langs:
  - csharp
  - vb
  name: RadioButtonGroupManager()
  nameWithType: RadioButtonGroupManager.RadioButtonGroupManager()
  fullName: DrawnUi.Controls.RadioButtonGroupManager.RadioButtonGroupManager()
  type: Constructor
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/RadioButtons/RadioButtonsManager.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Maui/DrawnUi/Controls/RadioButtons/RadioButtonsManager.cs
    startLine: 25
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: public RadioButtonGroupManager()
    content.vb: Public Sub New()
  overload: DrawnUi.Controls.RadioButtonGroupManager.#ctor*
  nameWithType.vb: RadioButtonGroupManager.New()
  fullName.vb: DrawnUi.Controls.RadioButtonGroupManager.New()
  name.vb: New()
- uid: DrawnUi.Controls.RadioButtonGroupManager.AddToGroup(DrawnUi.Controls.ISkiaRadioButton,System.String)
  commentId: M:DrawnUi.Controls.RadioButtonGroupManager.AddToGroup(DrawnUi.Controls.ISkiaRadioButton,System.String)
  id: AddToGroup(DrawnUi.Controls.ISkiaRadioButton,System.String)
  parent: DrawnUi.Controls.RadioButtonGroupManager
  langs:
  - csharp
  - vb
  name: AddToGroup(ISkiaRadioButton, string)
  nameWithType: RadioButtonGroupManager.AddToGroup(ISkiaRadioButton, string)
  fullName: DrawnUi.Controls.RadioButtonGroupManager.AddToGroup(DrawnUi.Controls.ISkiaRadioButton, string)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/RadioButtons/RadioButtonsManager.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: AddToGroup
    path: ../src/Maui/DrawnUi/Controls/RadioButtons/RadioButtonsManager.cs
    startLine: 32
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: public void AddToGroup(ISkiaRadioButton control, string groupName)
    parameters:
    - id: control
      type: DrawnUi.Controls.ISkiaRadioButton
    - id: groupName
      type: System.String
    content.vb: Public Sub AddToGroup(control As ISkiaRadioButton, groupName As String)
  overload: DrawnUi.Controls.RadioButtonGroupManager.AddToGroup*
  nameWithType.vb: RadioButtonGroupManager.AddToGroup(ISkiaRadioButton, String)
  fullName.vb: DrawnUi.Controls.RadioButtonGroupManager.AddToGroup(DrawnUi.Controls.ISkiaRadioButton, String)
  name.vb: AddToGroup(ISkiaRadioButton, String)
- uid: DrawnUi.Controls.RadioButtonGroupManager.AddToGroup(DrawnUi.Controls.ISkiaRadioButton,DrawnUi.Draw.SkiaControl)
  commentId: M:DrawnUi.Controls.RadioButtonGroupManager.AddToGroup(DrawnUi.Controls.ISkiaRadioButton,DrawnUi.Draw.SkiaControl)
  id: AddToGroup(DrawnUi.Controls.ISkiaRadioButton,DrawnUi.Draw.SkiaControl)
  parent: DrawnUi.Controls.RadioButtonGroupManager
  langs:
  - csharp
  - vb
  name: AddToGroup(ISkiaRadioButton, SkiaControl)
  nameWithType: RadioButtonGroupManager.AddToGroup(ISkiaRadioButton, SkiaControl)
  fullName: DrawnUi.Controls.RadioButtonGroupManager.AddToGroup(DrawnUi.Controls.ISkiaRadioButton, DrawnUi.Draw.SkiaControl)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/RadioButtons/RadioButtonsManager.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: AddToGroup
    path: ../src/Maui/DrawnUi/Controls/RadioButtons/RadioButtonsManager.cs
    startLine: 43
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: public void AddToGroup(ISkiaRadioButton control, SkiaControl parent)
    parameters:
    - id: control
      type: DrawnUi.Controls.ISkiaRadioButton
    - id: parent
      type: DrawnUi.Draw.SkiaControl
    content.vb: Public Sub AddToGroup(control As ISkiaRadioButton, parent As SkiaControl)
  overload: DrawnUi.Controls.RadioButtonGroupManager.AddToGroup*
- uid: DrawnUi.Controls.RadioButtonGroupManager.RemoveFromGroup(System.String,DrawnUi.Controls.ISkiaRadioButton)
  commentId: M:DrawnUi.Controls.RadioButtonGroupManager.RemoveFromGroup(System.String,DrawnUi.Controls.ISkiaRadioButton)
  id: RemoveFromGroup(System.String,DrawnUi.Controls.ISkiaRadioButton)
  parent: DrawnUi.Controls.RadioButtonGroupManager
  langs:
  - csharp
  - vb
  name: RemoveFromGroup(string, ISkiaRadioButton)
  nameWithType: RadioButtonGroupManager.RemoveFromGroup(string, ISkiaRadioButton)
  fullName: DrawnUi.Controls.RadioButtonGroupManager.RemoveFromGroup(string, DrawnUi.Controls.ISkiaRadioButton)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/RadioButtons/RadioButtonsManager.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: RemoveFromGroup
    path: ../src/Maui/DrawnUi/Controls/RadioButtons/RadioButtonsManager.cs
    startLine: 54
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: public void RemoveFromGroup(string groupName, ISkiaRadioButton control)
    parameters:
    - id: groupName
      type: System.String
    - id: control
      type: DrawnUi.Controls.ISkiaRadioButton
    content.vb: Public Sub RemoveFromGroup(groupName As String, control As ISkiaRadioButton)
  overload: DrawnUi.Controls.RadioButtonGroupManager.RemoveFromGroup*
  nameWithType.vb: RadioButtonGroupManager.RemoveFromGroup(String, ISkiaRadioButton)
  fullName.vb: DrawnUi.Controls.RadioButtonGroupManager.RemoveFromGroup(String, DrawnUi.Controls.ISkiaRadioButton)
  name.vb: RemoveFromGroup(String, ISkiaRadioButton)
- uid: DrawnUi.Controls.RadioButtonGroupManager.RemoveFromGroup(DrawnUi.Draw.SkiaControl,DrawnUi.Controls.ISkiaRadioButton)
  commentId: M:DrawnUi.Controls.RadioButtonGroupManager.RemoveFromGroup(DrawnUi.Draw.SkiaControl,DrawnUi.Controls.ISkiaRadioButton)
  id: RemoveFromGroup(DrawnUi.Draw.SkiaControl,DrawnUi.Controls.ISkiaRadioButton)
  parent: DrawnUi.Controls.RadioButtonGroupManager
  langs:
  - csharp
  - vb
  name: RemoveFromGroup(SkiaControl, ISkiaRadioButton)
  nameWithType: RadioButtonGroupManager.RemoveFromGroup(SkiaControl, ISkiaRadioButton)
  fullName: DrawnUi.Controls.RadioButtonGroupManager.RemoveFromGroup(DrawnUi.Draw.SkiaControl, DrawnUi.Controls.ISkiaRadioButton)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/RadioButtons/RadioButtonsManager.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: RemoveFromGroup
    path: ../src/Maui/DrawnUi/Controls/RadioButtons/RadioButtonsManager.cs
    startLine: 65
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: public void RemoveFromGroup(SkiaControl parent, ISkiaRadioButton control)
    parameters:
    - id: parent
      type: DrawnUi.Draw.SkiaControl
    - id: control
      type: DrawnUi.Controls.ISkiaRadioButton
    content.vb: Public Sub RemoveFromGroup(parent As SkiaControl, control As ISkiaRadioButton)
  overload: DrawnUi.Controls.RadioButtonGroupManager.RemoveFromGroup*
- uid: DrawnUi.Controls.RadioButtonGroupManager.RemoveFromGroups(DrawnUi.Controls.ISkiaRadioButton)
  commentId: M:DrawnUi.Controls.RadioButtonGroupManager.RemoveFromGroups(DrawnUi.Controls.ISkiaRadioButton)
  id: RemoveFromGroups(DrawnUi.Controls.ISkiaRadioButton)
  parent: DrawnUi.Controls.RadioButtonGroupManager
  langs:
  - csharp
  - vb
  name: RemoveFromGroups(ISkiaRadioButton)
  nameWithType: RadioButtonGroupManager.RemoveFromGroups(ISkiaRadioButton)
  fullName: DrawnUi.Controls.RadioButtonGroupManager.RemoveFromGroups(DrawnUi.Controls.ISkiaRadioButton)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/RadioButtons/RadioButtonsManager.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: RemoveFromGroups
    path: ../src/Maui/DrawnUi/Controls/RadioButtons/RadioButtonsManager.cs
    startLine: 75
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: public void RemoveFromGroups(ISkiaRadioButton control)
    parameters:
    - id: control
      type: DrawnUi.Controls.ISkiaRadioButton
    content.vb: Public Sub RemoveFromGroups(control As ISkiaRadioButton)
  overload: DrawnUi.Controls.RadioButtonGroupManager.RemoveFromGroups*
- uid: DrawnUi.Controls.RadioButtonGroupManager.ReportValueChange(DrawnUi.Controls.ISkiaRadioButton,System.Boolean)
  commentId: M:DrawnUi.Controls.RadioButtonGroupManager.ReportValueChange(DrawnUi.Controls.ISkiaRadioButton,System.Boolean)
  id: ReportValueChange(DrawnUi.Controls.ISkiaRadioButton,System.Boolean)
  parent: DrawnUi.Controls.RadioButtonGroupManager
  langs:
  - csharp
  - vb
  name: ReportValueChange(ISkiaRadioButton, bool)
  nameWithType: RadioButtonGroupManager.ReportValueChange(ISkiaRadioButton, bool)
  fullName: DrawnUi.Controls.RadioButtonGroupManager.ReportValueChange(DrawnUi.Controls.ISkiaRadioButton, bool)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/RadioButtons/RadioButtonsManager.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ReportValueChange
    path: ../src/Maui/DrawnUi/Controls/RadioButtons/RadioButtonsManager.cs
    startLine: 122
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: public void ReportValueChange(ISkiaRadioButton control, bool newValue)
    parameters:
    - id: control
      type: DrawnUi.Controls.ISkiaRadioButton
    - id: newValue
      type: System.Boolean
    content.vb: Public Sub ReportValueChange(control As ISkiaRadioButton, newValue As Boolean)
  overload: DrawnUi.Controls.RadioButtonGroupManager.ReportValueChange*
  nameWithType.vb: RadioButtonGroupManager.ReportValueChange(ISkiaRadioButton, Boolean)
  fullName.vb: DrawnUi.Controls.RadioButtonGroupManager.ReportValueChange(DrawnUi.Controls.ISkiaRadioButton, Boolean)
  name.vb: ReportValueChange(ISkiaRadioButton, Boolean)
references:
- uid: DrawnUi.Controls
  commentId: N:DrawnUi.Controls
  href: DrawnUi.html
  name: DrawnUi.Controls
  nameWithType: DrawnUi.Controls
  fullName: DrawnUi.Controls
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Controls
    name: Controls
    href: DrawnUi.Controls.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Controls
    name: Controls
    href: DrawnUi.Controls.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Controls.RadioButtonGroupManager.Instance*
  commentId: Overload:DrawnUi.Controls.RadioButtonGroupManager.Instance
  href: DrawnUi.Controls.RadioButtonGroupManager.html#DrawnUi_Controls_RadioButtonGroupManager_Instance
  name: Instance
  nameWithType: RadioButtonGroupManager.Instance
  fullName: DrawnUi.Controls.RadioButtonGroupManager.Instance
- uid: DrawnUi.Controls.RadioButtonGroupManager
  commentId: T:DrawnUi.Controls.RadioButtonGroupManager
  parent: DrawnUi.Controls
  href: DrawnUi.Controls.RadioButtonGroupManager.html
  name: RadioButtonGroupManager
  nameWithType: RadioButtonGroupManager
  fullName: DrawnUi.Controls.RadioButtonGroupManager
- uid: DrawnUi.Controls.RadioButtonGroupManager.GroupsByName*
  commentId: Overload:DrawnUi.Controls.RadioButtonGroupManager.GroupsByName
  href: DrawnUi.Controls.RadioButtonGroupManager.html#DrawnUi_Controls_RadioButtonGroupManager_GroupsByName
  name: GroupsByName
  nameWithType: RadioButtonGroupManager.GroupsByName
  fullName: DrawnUi.Controls.RadioButtonGroupManager.GroupsByName
- uid: System.Collections.Generic.Dictionary{System.String,System.Collections.Generic.List{DrawnUi.Controls.ISkiaRadioButton}}
  commentId: T:System.Collections.Generic.Dictionary{System.String,System.Collections.Generic.List{DrawnUi.Controls.ISkiaRadioButton}}
  parent: System.Collections.Generic
  definition: System.Collections.Generic.Dictionary`2
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2
  name: Dictionary<string, List<ISkiaRadioButton>>
  nameWithType: Dictionary<string, List<ISkiaRadioButton>>
  fullName: System.Collections.Generic.Dictionary<string, System.Collections.Generic.List<DrawnUi.Controls.ISkiaRadioButton>>
  nameWithType.vb: Dictionary(Of String, List(Of ISkiaRadioButton))
  fullName.vb: System.Collections.Generic.Dictionary(Of String, System.Collections.Generic.List(Of DrawnUi.Controls.ISkiaRadioButton))
  name.vb: Dictionary(Of String, List(Of ISkiaRadioButton))
  spec.csharp:
  - uid: System.Collections.Generic.Dictionary`2
    name: Dictionary
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2
  - name: <
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: <
  - uid: DrawnUi.Controls.ISkiaRadioButton
    name: ISkiaRadioButton
    href: DrawnUi.Controls.ISkiaRadioButton.html
  - name: '>'
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.Dictionary`2
    name: Dictionary
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2
  - name: (
  - name: Of
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Controls.ISkiaRadioButton
    name: ISkiaRadioButton
    href: DrawnUi.Controls.ISkiaRadioButton.html
  - name: )
  - name: )
- uid: System.Collections.Generic.Dictionary`2
  commentId: T:System.Collections.Generic.Dictionary`2
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2
  name: Dictionary<TKey, TValue>
  nameWithType: Dictionary<TKey, TValue>
  fullName: System.Collections.Generic.Dictionary<TKey, TValue>
  nameWithType.vb: Dictionary(Of TKey, TValue)
  fullName.vb: System.Collections.Generic.Dictionary(Of TKey, TValue)
  name.vb: Dictionary(Of TKey, TValue)
  spec.csharp:
  - uid: System.Collections.Generic.Dictionary`2
    name: Dictionary
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2
  - name: <
  - name: TKey
  - name: ','
  - name: " "
  - name: TValue
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.Dictionary`2
    name: Dictionary
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2
  - name: (
  - name: Of
  - name: " "
  - name: TKey
  - name: ','
  - name: " "
  - name: TValue
  - name: )
- uid: System.Collections.Generic
  commentId: N:System.Collections.Generic
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Collections.Generic
  nameWithType: System.Collections.Generic
  fullName: System.Collections.Generic
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Generic
    name: Generic
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Generic
    name: Generic
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic
- uid: DrawnUi.Controls.RadioButtonGroupManager.GroupsByParent*
  commentId: Overload:DrawnUi.Controls.RadioButtonGroupManager.GroupsByParent
  href: DrawnUi.Controls.RadioButtonGroupManager.html#DrawnUi_Controls_RadioButtonGroupManager_GroupsByParent
  name: GroupsByParent
  nameWithType: RadioButtonGroupManager.GroupsByParent
  fullName: DrawnUi.Controls.RadioButtonGroupManager.GroupsByParent
- uid: System.Collections.Generic.Dictionary{DrawnUi.Draw.SkiaControl,System.Collections.Generic.List{DrawnUi.Controls.ISkiaRadioButton}}
  commentId: T:System.Collections.Generic.Dictionary{DrawnUi.Draw.SkiaControl,System.Collections.Generic.List{DrawnUi.Controls.ISkiaRadioButton}}
  parent: System.Collections.Generic
  definition: System.Collections.Generic.Dictionary`2
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2
  name: Dictionary<SkiaControl, List<ISkiaRadioButton>>
  nameWithType: Dictionary<SkiaControl, List<ISkiaRadioButton>>
  fullName: System.Collections.Generic.Dictionary<DrawnUi.Draw.SkiaControl, System.Collections.Generic.List<DrawnUi.Controls.ISkiaRadioButton>>
  nameWithType.vb: Dictionary(Of SkiaControl, List(Of ISkiaRadioButton))
  fullName.vb: System.Collections.Generic.Dictionary(Of DrawnUi.Draw.SkiaControl, System.Collections.Generic.List(Of DrawnUi.Controls.ISkiaRadioButton))
  name.vb: Dictionary(Of SkiaControl, List(Of ISkiaRadioButton))
  spec.csharp:
  - uid: System.Collections.Generic.Dictionary`2
    name: Dictionary
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2
  - name: <
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: ','
  - name: " "
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: <
  - uid: DrawnUi.Controls.ISkiaRadioButton
    name: ISkiaRadioButton
    href: DrawnUi.Controls.ISkiaRadioButton.html
  - name: '>'
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.Dictionary`2
    name: Dictionary
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: ','
  - name: " "
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Controls.ISkiaRadioButton
    name: ISkiaRadioButton
    href: DrawnUi.Controls.ISkiaRadioButton.html
  - name: )
  - name: )
- uid: DrawnUi.Controls.RadioButtonGroupManager.#ctor*
  commentId: Overload:DrawnUi.Controls.RadioButtonGroupManager.#ctor
  href: DrawnUi.Controls.RadioButtonGroupManager.html#DrawnUi_Controls_RadioButtonGroupManager__ctor
  name: RadioButtonGroupManager
  nameWithType: RadioButtonGroupManager.RadioButtonGroupManager
  fullName: DrawnUi.Controls.RadioButtonGroupManager.RadioButtonGroupManager
  nameWithType.vb: RadioButtonGroupManager.New
  fullName.vb: DrawnUi.Controls.RadioButtonGroupManager.New
  name.vb: New
- uid: DrawnUi.Controls.RadioButtonGroupManager.AddToGroup*
  commentId: Overload:DrawnUi.Controls.RadioButtonGroupManager.AddToGroup
  href: DrawnUi.Controls.RadioButtonGroupManager.html#DrawnUi_Controls_RadioButtonGroupManager_AddToGroup_DrawnUi_Controls_ISkiaRadioButton_System_String_
  name: AddToGroup
  nameWithType: RadioButtonGroupManager.AddToGroup
  fullName: DrawnUi.Controls.RadioButtonGroupManager.AddToGroup
- uid: DrawnUi.Controls.ISkiaRadioButton
  commentId: T:DrawnUi.Controls.ISkiaRadioButton
  parent: DrawnUi.Controls
  href: DrawnUi.Controls.ISkiaRadioButton.html
  name: ISkiaRadioButton
  nameWithType: ISkiaRadioButton
  fullName: DrawnUi.Controls.ISkiaRadioButton
- uid: System.String
  commentId: T:System.String
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.string
  name: string
  nameWithType: string
  fullName: string
  nameWithType.vb: String
  fullName.vb: String
  name.vb: String
- uid: DrawnUi.Draw.SkiaControl
  commentId: T:DrawnUi.Draw.SkiaControl
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaControl.html
  name: SkiaControl
  nameWithType: SkiaControl
  fullName: DrawnUi.Draw.SkiaControl
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: DrawnUi.Controls.RadioButtonGroupManager.RemoveFromGroup*
  commentId: Overload:DrawnUi.Controls.RadioButtonGroupManager.RemoveFromGroup
  href: DrawnUi.Controls.RadioButtonGroupManager.html#DrawnUi_Controls_RadioButtonGroupManager_RemoveFromGroup_System_String_DrawnUi_Controls_ISkiaRadioButton_
  name: RemoveFromGroup
  nameWithType: RadioButtonGroupManager.RemoveFromGroup
  fullName: DrawnUi.Controls.RadioButtonGroupManager.RemoveFromGroup
- uid: DrawnUi.Controls.RadioButtonGroupManager.RemoveFromGroups*
  commentId: Overload:DrawnUi.Controls.RadioButtonGroupManager.RemoveFromGroups
  href: DrawnUi.Controls.RadioButtonGroupManager.html#DrawnUi_Controls_RadioButtonGroupManager_RemoveFromGroups_DrawnUi_Controls_ISkiaRadioButton_
  name: RemoveFromGroups
  nameWithType: RadioButtonGroupManager.RemoveFromGroups
  fullName: DrawnUi.Controls.RadioButtonGroupManager.RemoveFromGroups
- uid: DrawnUi.Controls.RadioButtonGroupManager.ReportValueChange*
  commentId: Overload:DrawnUi.Controls.RadioButtonGroupManager.ReportValueChange
  href: DrawnUi.Controls.RadioButtonGroupManager.html#DrawnUi_Controls_RadioButtonGroupManager_ReportValueChange_DrawnUi_Controls_ISkiaRadioButton_System_Boolean_
  name: ReportValueChange
  nameWithType: RadioButtonGroupManager.ReportValueChange
  fullName: DrawnUi.Controls.RadioButtonGroupManager.ReportValueChange
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
