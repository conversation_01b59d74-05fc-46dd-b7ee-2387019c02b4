### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.StaticResourcesExtensions
  commentId: T:DrawnUi.Draw.StaticResourcesExtensions
  id: StaticResourcesExtensions
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.StaticResourcesExtensions.FindParentByType``1(DrawnUi.Draw.SkiaControl)
  - DrawnUi.Draw.StaticResourcesExtensions.FindParent``1(Microsoft.Maui.Controls.Element)
  - DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents(Microsoft.Maui.Controls.VisualElement)
  - DrawnUi.Draw.StaticResourcesExtensions.GetSetterValue``1(Microsoft.Maui.Controls.Style,Microsoft.Maui.Controls.BindableProperty)
  - DrawnUi.Draw.StaticResourcesExtensions.Get``1(Microsoft.Maui.Controls.ResourceDictionary,System.String,Microsoft.Maui.Controls.VisualElement)
  langs:
  - csharp
  - vb
  name: StaticResourcesExtensions
  nameWithType: StaticResourcesExtensions
  fullName: DrawnUi.Draw.StaticResourcesExtensions
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Extensions/StaticResourcesExtensions.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: StaticResourcesExtensions
    path: ../src/Maui/DrawnUi/Internals/Extensions/StaticResourcesExtensions.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static class StaticResourcesExtensions
    content.vb: Public Module StaticResourcesExtensions
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
- uid: DrawnUi.Draw.StaticResourcesExtensions.GetSetterValue``1(Microsoft.Maui.Controls.Style,Microsoft.Maui.Controls.BindableProperty)
  commentId: M:DrawnUi.Draw.StaticResourcesExtensions.GetSetterValue``1(Microsoft.Maui.Controls.Style,Microsoft.Maui.Controls.BindableProperty)
  id: GetSetterValue``1(Microsoft.Maui.Controls.Style,Microsoft.Maui.Controls.BindableProperty)
  isExtensionMethod: true
  parent: DrawnUi.Draw.StaticResourcesExtensions
  langs:
  - csharp
  - vb
  name: GetSetterValue<T>(Style, BindableProperty)
  nameWithType: StaticResourcesExtensions.GetSetterValue<T>(Style, BindableProperty)
  fullName: DrawnUi.Draw.StaticResourcesExtensions.GetSetterValue<T>(Microsoft.Maui.Controls.Style, Microsoft.Maui.Controls.BindableProperty)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Extensions/StaticResourcesExtensions.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: GetSetterValue
    path: ../src/Maui/DrawnUi/Internals/Extensions/StaticResourcesExtensions.cs
    startLine: 5
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static T GetSetterValue<T>(this Style style, BindableProperty property)
    parameters:
    - id: style
      type: Microsoft.Maui.Controls.Style
    - id: property
      type: Microsoft.Maui.Controls.BindableProperty
    typeParameters:
    - id: T
    return:
      type: '{T}'
    content.vb: Public Shared Function GetSetterValue(Of T)(style As Style, [property] As BindableProperty) As T
  overload: DrawnUi.Draw.StaticResourcesExtensions.GetSetterValue*
  nameWithType.vb: StaticResourcesExtensions.GetSetterValue(Of T)(Style, BindableProperty)
  fullName.vb: DrawnUi.Draw.StaticResourcesExtensions.GetSetterValue(Of T)(Microsoft.Maui.Controls.Style, Microsoft.Maui.Controls.BindableProperty)
  name.vb: GetSetterValue(Of T)(Style, BindableProperty)
- uid: DrawnUi.Draw.StaticResourcesExtensions.Get``1(Microsoft.Maui.Controls.ResourceDictionary,System.String,Microsoft.Maui.Controls.VisualElement)
  commentId: M:DrawnUi.Draw.StaticResourcesExtensions.Get``1(Microsoft.Maui.Controls.ResourceDictionary,System.String,Microsoft.Maui.Controls.VisualElement)
  id: Get``1(Microsoft.Maui.Controls.ResourceDictionary,System.String,Microsoft.Maui.Controls.VisualElement)
  isExtensionMethod: true
  parent: DrawnUi.Draw.StaticResourcesExtensions
  langs:
  - csharp
  - vb
  name: Get<T>(ResourceDictionary, string, VisualElement)
  nameWithType: StaticResourcesExtensions.Get<T>(ResourceDictionary, string, VisualElement)
  fullName: DrawnUi.Draw.StaticResourcesExtensions.Get<T>(Microsoft.Maui.Controls.ResourceDictionary, string, Microsoft.Maui.Controls.VisualElement)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Extensions/StaticResourcesExtensions.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Get
    path: ../src/Maui/DrawnUi/Internals/Extensions/StaticResourcesExtensions.cs
    startLine: 14
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static T Get<T>(this ResourceDictionary resources, string name, VisualElement view = null)
    parameters:
    - id: resources
      type: Microsoft.Maui.Controls.ResourceDictionary
    - id: name
      type: System.String
    - id: view
      type: Microsoft.Maui.Controls.VisualElement
    typeParameters:
    - id: T
    return:
      type: '{T}'
    content.vb: Public Shared Function [Get](Of T)(resources As ResourceDictionary, name As String, view As VisualElement = Nothing) As T
  overload: DrawnUi.Draw.StaticResourcesExtensions.Get*
  nameWithType.vb: StaticResourcesExtensions.Get(Of T)(ResourceDictionary, String, VisualElement)
  fullName.vb: DrawnUi.Draw.StaticResourcesExtensions.Get(Of T)(Microsoft.Maui.Controls.ResourceDictionary, String, Microsoft.Maui.Controls.VisualElement)
  name.vb: Get(Of T)(ResourceDictionary, String, VisualElement)
- uid: DrawnUi.Draw.StaticResourcesExtensions.FindParent``1(Microsoft.Maui.Controls.Element)
  commentId: M:DrawnUi.Draw.StaticResourcesExtensions.FindParent``1(Microsoft.Maui.Controls.Element)
  id: FindParent``1(Microsoft.Maui.Controls.Element)
  isExtensionMethod: true
  parent: DrawnUi.Draw.StaticResourcesExtensions
  langs:
  - csharp
  - vb
  name: FindParent<T>(Element)
  nameWithType: StaticResourcesExtensions.FindParent<T>(Element)
  fullName: DrawnUi.Draw.StaticResourcesExtensions.FindParent<T>(Microsoft.Maui.Controls.Element)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Extensions/StaticResourcesExtensions.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: FindParent
    path: ../src/Maui/DrawnUi/Internals/Extensions/StaticResourcesExtensions.cs
    startLine: 47
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: 'public static T FindParent<T>(this Element view) where T : Element'
    parameters:
    - id: view
      type: Microsoft.Maui.Controls.Element
    typeParameters:
    - id: T
    return:
      type: '{T}'
    content.vb: Public Shared Function FindParent(Of T As Element)(view As Element) As T
  overload: DrawnUi.Draw.StaticResourcesExtensions.FindParent*
  nameWithType.vb: StaticResourcesExtensions.FindParent(Of T)(Element)
  fullName.vb: DrawnUi.Draw.StaticResourcesExtensions.FindParent(Of T)(Microsoft.Maui.Controls.Element)
  name.vb: FindParent(Of T)(Element)
- uid: DrawnUi.Draw.StaticResourcesExtensions.FindParentByType``1(DrawnUi.Draw.SkiaControl)
  commentId: M:DrawnUi.Draw.StaticResourcesExtensions.FindParentByType``1(DrawnUi.Draw.SkiaControl)
  id: FindParentByType``1(DrawnUi.Draw.SkiaControl)
  isExtensionMethod: true
  parent: DrawnUi.Draw.StaticResourcesExtensions
  langs:
  - csharp
  - vb
  name: FindParentByType<T>(SkiaControl)
  nameWithType: StaticResourcesExtensions.FindParentByType<T>(SkiaControl)
  fullName: DrawnUi.Draw.StaticResourcesExtensions.FindParentByType<T>(DrawnUi.Draw.SkiaControl)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Extensions/StaticResourcesExtensions.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: FindParentByType
    path: ../src/Maui/DrawnUi/Internals/Extensions/StaticResourcesExtensions.cs
    startLine: 61
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: 'public static T FindParentByType<T>(this SkiaControl view) where T : Element'
    parameters:
    - id: view
      type: DrawnUi.Draw.SkiaControl
    typeParameters:
    - id: T
    return:
      type: '{T}'
    content.vb: Public Shared Function FindParentByType(Of T As Element)(view As SkiaControl) As T
  overload: DrawnUi.Draw.StaticResourcesExtensions.FindParentByType*
  nameWithType.vb: StaticResourcesExtensions.FindParentByType(Of T)(SkiaControl)
  fullName.vb: DrawnUi.Draw.StaticResourcesExtensions.FindParentByType(Of T)(DrawnUi.Draw.SkiaControl)
  name.vb: FindParentByType(Of T)(SkiaControl)
- uid: DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents(Microsoft.Maui.Controls.VisualElement)
  commentId: M:DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents(Microsoft.Maui.Controls.VisualElement)
  id: GetAllWithMyselfParents(Microsoft.Maui.Controls.VisualElement)
  isExtensionMethod: true
  parent: DrawnUi.Draw.StaticResourcesExtensions
  langs:
  - csharp
  - vb
  name: GetAllWithMyselfParents(VisualElement)
  nameWithType: StaticResourcesExtensions.GetAllWithMyselfParents(VisualElement)
  fullName: DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents(Microsoft.Maui.Controls.VisualElement)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Extensions/StaticResourcesExtensions.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: GetAllWithMyselfParents
    path: ../src/Maui/DrawnUi/Internals/Extensions/StaticResourcesExtensions.cs
    startLine: 98
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static List<VisualElement> GetAllWithMyselfParents(this VisualElement view)
    parameters:
    - id: view
      type: Microsoft.Maui.Controls.VisualElement
    return:
      type: System.Collections.Generic.List{Microsoft.Maui.Controls.VisualElement}
    content.vb: Public Shared Function GetAllWithMyselfParents(view As VisualElement) As List(Of VisualElement)
  overload: DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents*
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Draw.StaticResourcesExtensions.GetSetterValue*
  commentId: Overload:DrawnUi.Draw.StaticResourcesExtensions.GetSetterValue
  href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_GetSetterValue__1_Microsoft_Maui_Controls_Style_Microsoft_Maui_Controls_BindableProperty_
  name: GetSetterValue
  nameWithType: StaticResourcesExtensions.GetSetterValue
  fullName: DrawnUi.Draw.StaticResourcesExtensions.GetSetterValue
- uid: Microsoft.Maui.Controls.Style
  commentId: T:Microsoft.Maui.Controls.Style
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.style
  name: Style
  nameWithType: Style
  fullName: Microsoft.Maui.Controls.Style
- uid: Microsoft.Maui.Controls.BindableProperty
  commentId: T:Microsoft.Maui.Controls.BindableProperty
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  name: BindableProperty
  nameWithType: BindableProperty
  fullName: Microsoft.Maui.Controls.BindableProperty
- uid: '{T}'
  commentId: '!:T'
  definition: T
  name: T
  nameWithType: T
  fullName: T
- uid: Microsoft.Maui.Controls
  commentId: N:Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft
  name: Microsoft.Maui.Controls
  nameWithType: Microsoft.Maui.Controls
  fullName: Microsoft.Maui.Controls
  spec.csharp:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Controls
    name: Controls
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls
  spec.vb:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Controls
    name: Controls
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls
- uid: T
  name: T
  nameWithType: T
  fullName: T
- uid: DrawnUi.Draw.StaticResourcesExtensions.Get*
  commentId: Overload:DrawnUi.Draw.StaticResourcesExtensions.Get
  href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_Get__1_Microsoft_Maui_Controls_ResourceDictionary_System_String_Microsoft_Maui_Controls_VisualElement_
  name: Get
  nameWithType: StaticResourcesExtensions.Get
  fullName: DrawnUi.Draw.StaticResourcesExtensions.Get
- uid: Microsoft.Maui.Controls.ResourceDictionary
  commentId: T:Microsoft.Maui.Controls.ResourceDictionary
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.resourcedictionary
  name: ResourceDictionary
  nameWithType: ResourceDictionary
  fullName: Microsoft.Maui.Controls.ResourceDictionary
- uid: System.String
  commentId: T:System.String
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.string
  name: string
  nameWithType: string
  fullName: string
  nameWithType.vb: String
  fullName.vb: String
  name.vb: String
- uid: Microsoft.Maui.Controls.VisualElement
  commentId: T:Microsoft.Maui.Controls.VisualElement
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement
  name: VisualElement
  nameWithType: VisualElement
  fullName: Microsoft.Maui.Controls.VisualElement
- uid: DrawnUi.Draw.StaticResourcesExtensions.FindParent*
  commentId: Overload:DrawnUi.Draw.StaticResourcesExtensions.FindParent
  href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_FindParent__1_Microsoft_Maui_Controls_Element_
  name: FindParent
  nameWithType: StaticResourcesExtensions.FindParent
  fullName: DrawnUi.Draw.StaticResourcesExtensions.FindParent
- uid: Microsoft.Maui.Controls.Element
  commentId: T:Microsoft.Maui.Controls.Element
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  name: Element
  nameWithType: Element
  fullName: Microsoft.Maui.Controls.Element
- uid: DrawnUi.Draw.StaticResourcesExtensions.FindParentByType*
  commentId: Overload:DrawnUi.Draw.StaticResourcesExtensions.FindParentByType
  href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_FindParentByType__1_DrawnUi_Draw_SkiaControl_
  name: FindParentByType
  nameWithType: StaticResourcesExtensions.FindParentByType
  fullName: DrawnUi.Draw.StaticResourcesExtensions.FindParentByType
- uid: DrawnUi.Draw.SkiaControl
  commentId: T:DrawnUi.Draw.SkiaControl
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaControl.html
  name: SkiaControl
  nameWithType: SkiaControl
  fullName: DrawnUi.Draw.SkiaControl
- uid: DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents*
  commentId: Overload:DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents
  href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_GetAllWithMyselfParents_Microsoft_Maui_Controls_VisualElement_
  name: GetAllWithMyselfParents
  nameWithType: StaticResourcesExtensions.GetAllWithMyselfParents
  fullName: DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents
- uid: System.Collections.Generic.List{Microsoft.Maui.Controls.VisualElement}
  commentId: T:System.Collections.Generic.List{Microsoft.Maui.Controls.VisualElement}
  parent: System.Collections.Generic
  definition: System.Collections.Generic.List`1
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  name: List<VisualElement>
  nameWithType: List<VisualElement>
  fullName: System.Collections.Generic.List<Microsoft.Maui.Controls.VisualElement>
  nameWithType.vb: List(Of VisualElement)
  fullName.vb: System.Collections.Generic.List(Of Microsoft.Maui.Controls.VisualElement)
  name.vb: List(Of VisualElement)
  spec.csharp:
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: <
  - uid: Microsoft.Maui.Controls.VisualElement
    name: VisualElement
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: (
  - name: Of
  - name: " "
  - uid: Microsoft.Maui.Controls.VisualElement
    name: VisualElement
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement
  - name: )
- uid: System.Collections.Generic.List`1
  commentId: T:System.Collections.Generic.List`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  name: List<T>
  nameWithType: List<T>
  fullName: System.Collections.Generic.List<T>
  nameWithType.vb: List(Of T)
  fullName.vb: System.Collections.Generic.List(Of T)
  name.vb: List(Of T)
  spec.csharp:
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: System.Collections.Generic
  commentId: N:System.Collections.Generic
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Collections.Generic
  nameWithType: System.Collections.Generic
  fullName: System.Collections.Generic
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Generic
    name: Generic
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Generic
    name: Generic
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic
