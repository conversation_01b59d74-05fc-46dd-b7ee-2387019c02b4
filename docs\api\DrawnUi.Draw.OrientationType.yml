### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.OrientationType
  commentId: T:DrawnUi.Draw.OrientationType
  id: OrientationType
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.OrientationType.Horizontal
  - DrawnUi.Draw.OrientationType.Vertical
  langs:
  - csharp
  - vb
  name: OrientationType
  nameWithType: OrientationType
  fullName: DrawnUi.Draw.OrientationType
  type: Enum
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/OrientationType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: OrientationType
    path: ../src/Shared/Draw/Internals/Enums/OrientationType.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public enum OrientationType
    content.vb: Public Enum OrientationType
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.OrientationType.Horizontal
  commentId: F:DrawnUi.Draw.OrientationType.Horizontal
  id: Horizontal
  parent: DrawnUi.Draw.OrientationType
  langs:
  - csharp
  - vb
  name: Horizontal
  nameWithType: OrientationType.Horizontal
  fullName: DrawnUi.Draw.OrientationType.Horizontal
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/OrientationType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Horizontal
    path: ../src/Shared/Draw/Internals/Enums/OrientationType.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: Horizontal = 0
    return:
      type: DrawnUi.Draw.OrientationType
- uid: DrawnUi.Draw.OrientationType.Vertical
  commentId: F:DrawnUi.Draw.OrientationType.Vertical
  id: Vertical
  parent: DrawnUi.Draw.OrientationType
  langs:
  - csharp
  - vb
  name: Vertical
  nameWithType: OrientationType.Vertical
  fullName: DrawnUi.Draw.OrientationType.Vertical
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/OrientationType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Vertical
    path: ../src/Shared/Draw/Internals/Enums/OrientationType.cs
    startLine: 5
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: Vertical = 1
    return:
      type: DrawnUi.Draw.OrientationType
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.OrientationType
  commentId: T:DrawnUi.Draw.OrientationType
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.OrientationType.html
  name: OrientationType
  nameWithType: OrientationType
  fullName: DrawnUi.Draw.OrientationType
