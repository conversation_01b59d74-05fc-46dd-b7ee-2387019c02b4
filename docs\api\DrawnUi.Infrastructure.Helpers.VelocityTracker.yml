### YamlMime:ManagedReference
items:
- uid: DrawnUi.Infrastructure.Helpers.VelocityTracker
  commentId: T:DrawnUi.Infrastructure.Helpers.VelocityTracker
  id: VelocityTracker
  parent: DrawnUi.Infrastructure.Helpers
  children:
  - DrawnUi.Infrastructure.Helpers.VelocityTracker.#ctor
  - DrawnUi.Infrastructure.Helpers.VelocityTracker.AddMovement(System.Single,System.Single)
  - DrawnUi.Infrastructure.Helpers.VelocityTracker.Clear
  - DrawnUi.Infrastructure.Helpers.VelocityTracker.ComputeCurrentVelocity(System.Single)
  langs:
  - csharp
  - vb
  name: VelocityTracker
  nameWithType: VelocityTracker
  fullName: DrawnUi.Infrastructure.Helpers.VelocityTracker
  type: Class
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/VelocityTracker.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: VelocityTracker
    path: ../src/Shared/Draw/Internals/Helpers/VelocityTracker.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure.Helpers
  syntax:
    content: public class VelocityTracker
    content.vb: Public Class VelocityTracker
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Infrastructure.Helpers.VelocityTracker.#ctor
  commentId: M:DrawnUi.Infrastructure.Helpers.VelocityTracker.#ctor
  id: '#ctor'
  parent: DrawnUi.Infrastructure.Helpers.VelocityTracker
  langs:
  - csharp
  - vb
  name: VelocityTracker()
  nameWithType: VelocityTracker.VelocityTracker()
  fullName: DrawnUi.Infrastructure.Helpers.VelocityTracker.VelocityTracker()
  type: Constructor
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/VelocityTracker.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Shared/Draw/Internals/Helpers/VelocityTracker.cs
    startLine: 9
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure.Helpers
  syntax:
    content: public VelocityTracker()
    content.vb: Public Sub New()
  overload: DrawnUi.Infrastructure.Helpers.VelocityTracker.#ctor*
  nameWithType.vb: VelocityTracker.New()
  fullName.vb: DrawnUi.Infrastructure.Helpers.VelocityTracker.New()
  name.vb: New()
- uid: DrawnUi.Infrastructure.Helpers.VelocityTracker.AddMovement(System.Single,System.Single)
  commentId: M:DrawnUi.Infrastructure.Helpers.VelocityTracker.AddMovement(System.Single,System.Single)
  id: AddMovement(System.Single,System.Single)
  parent: DrawnUi.Infrastructure.Helpers.VelocityTracker
  langs:
  - csharp
  - vb
  name: AddMovement(float, float)
  nameWithType: VelocityTracker.AddMovement(float, float)
  fullName: DrawnUi.Infrastructure.Helpers.VelocityTracker.AddMovement(float, float)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/VelocityTracker.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: AddMovement
    path: ../src/Shared/Draw/Internals/Helpers/VelocityTracker.cs
    startLine: 15
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure.Helpers
  syntax:
    content: public void AddMovement(float velocityX, float velocityY)
    parameters:
    - id: velocityX
      type: System.Single
    - id: velocityY
      type: System.Single
    content.vb: Public Sub AddMovement(velocityX As Single, velocityY As Single)
  overload: DrawnUi.Infrastructure.Helpers.VelocityTracker.AddMovement*
  nameWithType.vb: VelocityTracker.AddMovement(Single, Single)
  fullName.vb: DrawnUi.Infrastructure.Helpers.VelocityTracker.AddMovement(Single, Single)
  name.vb: AddMovement(Single, Single)
- uid: DrawnUi.Infrastructure.Helpers.VelocityTracker.Clear
  commentId: M:DrawnUi.Infrastructure.Helpers.VelocityTracker.Clear
  id: Clear
  parent: DrawnUi.Infrastructure.Helpers.VelocityTracker
  langs:
  - csharp
  - vb
  name: Clear()
  nameWithType: VelocityTracker.Clear()
  fullName: DrawnUi.Infrastructure.Helpers.VelocityTracker.Clear()
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/VelocityTracker.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Clear
    path: ../src/Shared/Draw/Internals/Helpers/VelocityTracker.cs
    startLine: 26
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure.Helpers
  syntax:
    content: public void Clear()
    content.vb: Public Sub Clear()
  overload: DrawnUi.Infrastructure.Helpers.VelocityTracker.Clear*
- uid: DrawnUi.Infrastructure.Helpers.VelocityTracker.ComputeCurrentVelocity(System.Single)
  commentId: M:DrawnUi.Infrastructure.Helpers.VelocityTracker.ComputeCurrentVelocity(System.Single)
  id: ComputeCurrentVelocity(System.Single)
  parent: DrawnUi.Infrastructure.Helpers.VelocityTracker
  langs:
  - csharp
  - vb
  name: ComputeCurrentVelocity(float)
  nameWithType: VelocityTracker.ComputeCurrentVelocity(float)
  fullName: DrawnUi.Infrastructure.Helpers.VelocityTracker.ComputeCurrentVelocity(float)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Helpers/VelocityTracker.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ComputeCurrentVelocity
    path: ../src/Shared/Draw/Internals/Helpers/VelocityTracker.cs
    startLine: 36
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Infrastructure.Helpers
  syntax:
    content: public Vector2? ComputeCurrentVelocity(float VelocityWeighting = 0.1)
    parameters:
    - id: VelocityWeighting
      type: System.Single
    return:
      type: System.Nullable{System.Numerics.Vector2}
    content.vb: Public Function ComputeCurrentVelocity(VelocityWeighting As Single = 0.1) As Vector2?
  overload: DrawnUi.Infrastructure.Helpers.VelocityTracker.ComputeCurrentVelocity*
  nameWithType.vb: VelocityTracker.ComputeCurrentVelocity(Single)
  fullName.vb: DrawnUi.Infrastructure.Helpers.VelocityTracker.ComputeCurrentVelocity(Single)
  name.vb: ComputeCurrentVelocity(Single)
references:
- uid: DrawnUi.Infrastructure.Helpers
  commentId: N:DrawnUi.Infrastructure.Helpers
  href: DrawnUi.html
  name: DrawnUi.Infrastructure.Helpers
  nameWithType: DrawnUi.Infrastructure.Helpers
  fullName: DrawnUi.Infrastructure.Helpers
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Infrastructure
    name: Infrastructure
    href: DrawnUi.Infrastructure.html
  - name: .
  - uid: DrawnUi.Infrastructure.Helpers
    name: Helpers
    href: DrawnUi.Infrastructure.Helpers.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Infrastructure
    name: Infrastructure
    href: DrawnUi.Infrastructure.html
  - name: .
  - uid: DrawnUi.Infrastructure.Helpers
    name: Helpers
    href: DrawnUi.Infrastructure.Helpers.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Infrastructure.Helpers.VelocityTracker.#ctor*
  commentId: Overload:DrawnUi.Infrastructure.Helpers.VelocityTracker.#ctor
  href: DrawnUi.Infrastructure.Helpers.VelocityTracker.html#DrawnUi_Infrastructure_Helpers_VelocityTracker__ctor
  name: VelocityTracker
  nameWithType: VelocityTracker.VelocityTracker
  fullName: DrawnUi.Infrastructure.Helpers.VelocityTracker.VelocityTracker
  nameWithType.vb: VelocityTracker.New
  fullName.vb: DrawnUi.Infrastructure.Helpers.VelocityTracker.New
  name.vb: New
- uid: DrawnUi.Infrastructure.Helpers.VelocityTracker.AddMovement*
  commentId: Overload:DrawnUi.Infrastructure.Helpers.VelocityTracker.AddMovement
  href: DrawnUi.Infrastructure.Helpers.VelocityTracker.html#DrawnUi_Infrastructure_Helpers_VelocityTracker_AddMovement_System_Single_System_Single_
  name: AddMovement
  nameWithType: VelocityTracker.AddMovement
  fullName: DrawnUi.Infrastructure.Helpers.VelocityTracker.AddMovement
- uid: System.Single
  commentId: T:System.Single
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.single
  name: float
  nameWithType: float
  fullName: float
  nameWithType.vb: Single
  fullName.vb: Single
  name.vb: Single
- uid: DrawnUi.Infrastructure.Helpers.VelocityTracker.Clear*
  commentId: Overload:DrawnUi.Infrastructure.Helpers.VelocityTracker.Clear
  href: DrawnUi.Infrastructure.Helpers.VelocityTracker.html#DrawnUi_Infrastructure_Helpers_VelocityTracker_Clear
  name: Clear
  nameWithType: VelocityTracker.Clear
  fullName: DrawnUi.Infrastructure.Helpers.VelocityTracker.Clear
- uid: DrawnUi.Infrastructure.Helpers.VelocityTracker.ComputeCurrentVelocity*
  commentId: Overload:DrawnUi.Infrastructure.Helpers.VelocityTracker.ComputeCurrentVelocity
  href: DrawnUi.Infrastructure.Helpers.VelocityTracker.html#DrawnUi_Infrastructure_Helpers_VelocityTracker_ComputeCurrentVelocity_System_Single_
  name: ComputeCurrentVelocity
  nameWithType: VelocityTracker.ComputeCurrentVelocity
  fullName: DrawnUi.Infrastructure.Helpers.VelocityTracker.ComputeCurrentVelocity
- uid: System.Nullable{System.Numerics.Vector2}
  commentId: T:System.Nullable{System.Numerics.Vector2}
  parent: System
  definition: System.Nullable`1
  href: https://learn.microsoft.com/dotnet/api/system.numerics.vector2
  name: Vector2?
  nameWithType: Vector2?
  fullName: System.Numerics.Vector2?
  spec.csharp:
  - uid: System.Numerics.Vector2
    name: Vector2
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.numerics.vector2
  - name: '?'
  spec.vb:
  - uid: System.Numerics.Vector2
    name: Vector2
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.numerics.vector2
  - name: '?'
- uid: System.Nullable`1
  commentId: T:System.Nullable`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.nullable-1
  name: Nullable<T>
  nameWithType: Nullable<T>
  fullName: System.Nullable<T>
  nameWithType.vb: Nullable(Of T)
  fullName.vb: System.Nullable(Of T)
  name.vb: Nullable(Of T)
  spec.csharp:
  - uid: System.Nullable`1
    name: Nullable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.nullable-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Nullable`1
    name: Nullable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.nullable-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
