<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <title>Class SvgSpan | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width">
      <meta name="title" content="Class SvgSpan | DrawnUi Documentation ">
    
    
      <link rel="shortcut icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../styles/docfx.vendor.min.css">
      <link rel="stylesheet" href="../styles/docfx.css">
      <link rel="stylesheet" href="../styles/main.css">
      <meta property="docfx:navrel" content="../toc.html">
      <meta property="docfx:tocrel" content="toc.html">
    
    <meta property="docfx:rel" content="../">
    
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first=First data-prev=Previous data-next=Next data-last=Last></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="DrawnUi.Draw.SvgSpan">



  <h1 id="DrawnUi_Draw_SvgSpan" data-uid="DrawnUi.Draw.SvgSpan" class="text-break">Class SvgSpan</h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></div>
    <div class="level1"><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a></div>
    <div class="level2"><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element">Element</a></div>
    <div class="level3"><a class="xref" href="DrawnUi.Draw.TextSpan.html">TextSpan</a></div>
    <div class="level4"><span class="xref">SvgSpan</span></div>
  </div>
  <div class="implements">
    <h5>Implements</h5>
    <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.componentmodel.inotifypropertychanged">INotifyPropertyChanged</a></div>
    <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ielementcontroller">IElementController</a></div>
    <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.ivisualtreeelement">IVisualTreeElement</a></div>
    <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ieffectcontrolprovider">IEffectControlProvider</a></div>
    <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.itooltipelement">IToolTipElement</a></div>
    <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.icontextflyoutelement">IContextFlyoutElement</a></div>
    <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.ielement">IElement</a></div>
    <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.idisposable">IDisposable</a></div>
    <div><a class="xref" href="DrawnUi.Draw.IDrawnTextSpan.html">IDrawnTextSpan</a></div>
  </div>
  <div class="inheritedMembers">
    <h5>Inherited Members</h5>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_TextProperty">TextSpan.TextProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_Text">TextSpan.Text</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_Default">TextSpan.Default</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_DebugString">TextSpan.DebugString</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_RenderingScale">TextSpan.RenderingScale</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_Glyphs">TextSpan.Glyphs</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_Shape">TextSpan.Shape</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_TextFiltered">TextSpan.TextFiltered</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_CheckGlyphsCanBeRendered">TextSpan.CheckGlyphsCanBeRendered()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_SetupPaint_System_Double_SkiaSharp_SKPaint_">TextSpan.SetupPaint(double, SKPaint)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_HasSetFont">TextSpan.HasSetFont</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_HasSetSize">TextSpan.HasSetSize</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_HasSetColor">TextSpan.HasSetColor</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_Paint">TextSpan.Paint</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_TypeFace">TextSpan.TypeFace</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_NeedShape">TextSpan.NeedShape</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_HasDecorations">TextSpan.HasDecorations</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_Underline">TextSpan.Underline</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_UnderlineWidth">TextSpan.UnderlineWidth</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_Strikeout">TextSpan.Strikeout</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_StrikeoutWidth">TextSpan.StrikeoutWidth</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_StrikeoutColor">TextSpan.StrikeoutColor</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_HasTapHandler">TextSpan.HasTapHandler</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_ForceCaptureInput">TextSpan.ForceCaptureInput</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_HitIsInside_System_Single_System_Single_">TextSpan.HitIsInside(float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_FireTap">TextSpan.FireTap()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_DrawingOffset">TextSpan.DrawingOffset</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_Tag">TextSpan.Tag</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_Rects">TextSpan.Rects</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_CommandTapped">TextSpan.CommandTapped</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_Tapped">TextSpan.Tapped</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_ParentControl">TextSpan.ParentControl</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_OnPropertyChanged_System_String_">TextSpan.OnPropertyChanged(string)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan__fontAutoSet">TextSpan._fontAutoSet</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_UpdateFont">TextSpan.UpdateFont()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_FontFamily">TextSpan.FontFamily</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_LineSpacing">TextSpan.LineSpacing</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_LineHeight">TextSpan.LineHeight</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_FontWeight">TextSpan.FontWeight</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_TextColorProperty">TextSpan.TextColorProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_TextColor">TextSpan.TextColor</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_BackgroundColor">TextSpan.BackgroundColor</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_ParagraphColor">TextSpan.ParagraphColor</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_FontSizeProperty">TextSpan.FontSizeProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_FontSize">TextSpan.FontSize</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_IsItalic">TextSpan.IsItalic</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_IsBold">TextSpan.IsBold</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_AutoFindFont">TextSpan.AutoFindFont</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_FontDetectedWith">TextSpan.FontDetectedWith</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.automationidproperty">Element.AutomationIdProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.classidproperty">Element.ClassIdProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.insertlogicalchild">Element.InsertLogicalChild(int, Element)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.addlogicalchild">Element.AddLogicalChild(Element)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.removelogicalchild">Element.RemoveLogicalChild(Element)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.clearlogicalchildren">Element.ClearLogicalChildren()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.findbyname">Element.FindByName(string)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.removedynamicresource">Element.RemoveDynamicResource(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.setdynamicresource">Element.SetDynamicResource(BindableProperty, string)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onbindingcontextchanged">Element.OnBindingContextChanged()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onchildadded">Element.OnChildAdded(Element)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onchildremoved">Element.OnChildRemoved(Element, int)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentset">Element.OnParentSet()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentchanging">Element.OnParentChanging(ParentChangingEventArgs)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentchanged">Element.OnParentChanged()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onhandlerchanging">Element.OnHandlerChanging(HandlerChangingEventArgs)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onhandlerchanged">Element.OnHandlerChanged()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.mapautomationpropertiesisinaccessibletree">Element.MapAutomationPropertiesIsInAccessibleTree(IElementHandler, Element)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.mapautomationpropertiesexcludedwithchildren">Element.MapAutomationPropertiesExcludedWithChildren(IElementHandler, Element)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.automationid">Element.AutomationId</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.classid">Element.ClassId</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.effects">Element.Effects</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.id">Element.Id</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.styleid">Element.StyleId</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.parent">Element.Parent</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.handler">Element.Handler</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.childadded">Element.ChildAdded</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.childremoved">Element.ChildRemoved</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.descendantadded">Element.DescendantAdded</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.descendantremoved">Element.DescendantRemoved</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.parentchanging">Element.ParentChanging</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.parentchanged">Element.ParentChanged</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.handlerchanging">Element.HandlerChanging</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.handlerchanged">Element.HandlerChanged</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.bindingcontextproperty">BindableObject.BindingContextProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindableproperty)">BindableObject.ClearValue(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindablepropertykey)">BindableObject.ClearValue(BindablePropertyKey)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.getvalue">BindableObject.GetValue(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.isset">BindableObject.IsSet(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.removebinding">BindableObject.RemoveBinding(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setbinding">BindableObject.SetBinding(BindableProperty, BindingBase)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.applybindings">BindableObject.ApplyBindings()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.onpropertychanging">BindableObject.OnPropertyChanging(string)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.unapplybindings">BindableObject.UnapplyBindings()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindableproperty-system-object)">BindableObject.SetValue(BindableProperty, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindablepropertykey-system-object)">BindableObject.SetValue(BindablePropertyKey, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindableproperty)">BindableObject.CoerceValue(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindablepropertykey)">BindableObject.CoerceValue(BindablePropertyKey)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.dispatcher">BindableObject.Dispatcher</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.bindingcontext">BindableObject.BindingContext</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.propertychanged">BindableObject.PropertyChanged</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.propertychanging">BindableObject.PropertyChanging</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.bindingcontextchanged">BindableObject.BindingContextChanged</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)">object.Equals(object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)">object.Equals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gethashcode">object.GetHashCode()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gettype">object.GetType()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone">object.MemberwiseClone()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.referenceequals">object.ReferenceEquals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.tostring">object.ToString()</a>
    </div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="DrawnUi.html">DrawnUi</a>.<a class="xref" href="DrawnUi.Draw.html">Draw</a></h6>
  <h6><strong>Assembly</strong>: DrawnUi.Maui.dll</h6>
  <h5 id="DrawnUi_Draw_SvgSpan_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class SvgSpan : TextSpan, INotifyPropertyChanged, IElementController, IVisualTreeElement, IEffectControlProvider, IToolTipElement, IContextFlyoutElement, IElement, IDisposable, IDrawnTextSpan</code></pre>
  </div>
  <h3 id="constructors">Constructors
</h3>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_SvgSpan__ctor.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.SvgSpan.%23ctor%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Text/SvgSpan.cs/#L5">View Source</a>
  </span>
  <a id="DrawnUi_Draw_SvgSpan__ctor_" data-uid="DrawnUi.Draw.SvgSpan.#ctor*"></a>
  <h4 id="DrawnUi_Draw_SvgSpan__ctor" data-uid="DrawnUi.Draw.SvgSpan.#ctor">SvgSpan()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SvgSpan()</code></pre>
  </div>
  <h3 id="fields">Fields
</h3>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_SvgSpan_Control.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.SvgSpan.Control%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Text/SvgSpan.cs/#L96">View Source</a>
  </span>
  <h4 id="DrawnUi_Draw_SvgSpan_Control" data-uid="DrawnUi.Draw.SvgSpan.Control">Control</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected SkiaSvg Control</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="DrawnUi.Draw.SkiaSvg.html">SkiaSvg</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="properties">Properties
</h3>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_SvgSpan_Height.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.SvgSpan.Height%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Text/SvgSpan.cs/#L29">View Source</a>
  </span>
  <a id="DrawnUi_Draw_SvgSpan_Height_" data-uid="DrawnUi.Draw.SvgSpan.Height*"></a>
  <h4 id="DrawnUi_Draw_SvgSpan_Height" data-uid="DrawnUi.Draw.SvgSpan.Height">Height</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double Height { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_SvgSpan_Source.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.SvgSpan.Source%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Text/SvgSpan.cs/#L80">View Source</a>
  </span>
  <a id="DrawnUi_Draw_SvgSpan_Source_" data-uid="DrawnUi.Draw.SvgSpan.Source*"></a>
  <h4 id="DrawnUi_Draw_SvgSpan_Source" data-uid="DrawnUi.Draw.SvgSpan.Source">Source</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public string Source { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_SvgSpan_TintColor.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.SvgSpan.TintColor%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Text/SvgSpan.cs/#L63">View Source</a>
  </span>
  <a id="DrawnUi_Draw_SvgSpan_TintColor_" data-uid="DrawnUi.Draw.SvgSpan.TintColor*"></a>
  <h4 id="DrawnUi_Draw_SvgSpan_TintColor" data-uid="DrawnUi.Draw.SvgSpan.TintColor">TintColor</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Color TintColor { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.color">Color</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_SvgSpan_VerticalAlignement.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.SvgSpan.VerticalAlignement%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Text/SvgSpan.cs/#L12">View Source</a>
  </span>
  <a id="DrawnUi_Draw_SvgSpan_VerticalAlignement_" data-uid="DrawnUi.Draw.SvgSpan.VerticalAlignement*"></a>
  <h4 id="DrawnUi_Draw_SvgSpan_VerticalAlignement" data-uid="DrawnUi.Draw.SvgSpan.VerticalAlignement">VerticalAlignement</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public DrawImageAlignment VerticalAlignement { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="DrawnUi.Draw.DrawImageAlignment.html">DrawImageAlignment</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_SvgSpan_Width.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.SvgSpan.Width%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Text/SvgSpan.cs/#L46">View Source</a>
  </span>
  <a id="DrawnUi_Draw_SvgSpan_Width_" data-uid="DrawnUi.Draw.SvgSpan.Width*"></a>
  <h4 id="DrawnUi_Draw_SvgSpan_Width" data-uid="DrawnUi.Draw.SvgSpan.Width">Width</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double Width { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
</h3>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_SvgSpan_Dispose.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.SvgSpan.Dispose%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Text/SvgSpan.cs/#L103">View Source</a>
  </span>
  <a id="DrawnUi_Draw_SvgSpan_Dispose_" data-uid="DrawnUi.Draw.SvgSpan.Dispose*"></a>
  <h4 id="DrawnUi_Draw_SvgSpan_Dispose" data-uid="DrawnUi.Draw.SvgSpan.Dispose">Dispose()</h4>
  <div class="markdown level1 summary"><p>Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override void Dispose()</code></pre>
  </div>
  <h5 class="overrides">Overrides</h5>
  <div><a class="xref" href="DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_Dispose">TextSpan.Dispose()</a></div>
  <h3 id="implements">Implements</h3>
  <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.componentmodel.inotifypropertychanged">INotifyPropertyChanged</a>
  </div>
  <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ielementcontroller">IElementController</a>
  </div>
  <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.ivisualtreeelement">IVisualTreeElement</a>
  </div>
  <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ieffectcontrolprovider">IEffectControlProvider</a>
  </div>
  <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.itooltipelement">IToolTipElement</a>
  </div>
  <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.icontextflyoutelement">IContextFlyoutElement</a>
  </div>
  <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.ielement">IElement</a>
  </div>
  <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.idisposable">IDisposable</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnTextSpan.html">IDrawnTextSpan</a>
  </div>
  <h3 id="extensionmethods">Extension Methods</h3>
  <div>
      <a class="xref" href="DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_FindParent__1_Microsoft_Maui_Controls_Element_">StaticResourcesExtensions.FindParent&lt;T&gt;(Element)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FindMauiContext_Microsoft_Maui_Controls_Element_System_Boolean_">InternalExtensions.FindMauiContext(Element, bool)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_GetParentsPath_Microsoft_Maui_Controls_Element_">InternalExtensions.GetParentsPath(Element)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_">InternalExtensions.FromPlatform(object)</a>
  </div>

</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_SvgSpan.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.SvgSpan%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A" class="contribution-link">Edit this page</a>
                  </li>
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Text/SvgSpan.cs/#L3" class="contribution-link">View Source</a>
                  </li>
                </ul>
              </div>
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In this article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      
      <span>Generated by <strong>DocFX</strong></span>
          </div>
        </div>
      </footer>
    </div>

    <script type="text/javascript" src="../styles/docfx.vendor.min.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
