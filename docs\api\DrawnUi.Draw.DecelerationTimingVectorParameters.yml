### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.DecelerationTimingVectorParameters
  commentId: T:DrawnUi.Draw.DecelerationTimingVectorParameters
  id: DecelerationTimingVectorParameters
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.DecelerationTimingVectorParameters.#ctor(System.Numerics.Vector2,System.Numerics.Vector2,System.Single,System.Single)
  - DrawnUi.Draw.DecelerationTimingVectorParameters.DecelerationK
  - DrawnUi.Draw.DecelerationTimingVectorParameters.DecelerationRate
  - DrawnUi.Draw.DecelerationTimingVectorParameters.Destination
  - DrawnUi.Draw.DecelerationTimingVectorParameters.DistanceToSegment(System.Numerics.Vector2,System.Numerics.Vector2,System.Numerics.Vector2)
  - DrawnUi.Draw.DecelerationTimingVectorParameters.DurationSecs
  - DrawnUi.Draw.DecelerationTimingVectorParameters.DurationToValue(System.Numerics.Vector2)
  - DrawnUi.Draw.DecelerationTimingVectorParameters.InitialValue
  - DrawnUi.Draw.DecelerationTimingVectorParameters.InitialVelocity
  - DrawnUi.Draw.DecelerationTimingVectorParameters.Threshold
  - DrawnUi.Draw.DecelerationTimingVectorParameters.ValueAt(System.Single)
  - DrawnUi.Draw.DecelerationTimingVectorParameters.VelocityAt(System.Double)
  - DrawnUi.Draw.DecelerationTimingVectorParameters.VelocityTo(System.Numerics.Vector2,System.Numerics.Vector2,System.Double)
  - DrawnUi.Draw.DecelerationTimingVectorParameters.VelocityToZero(System.Numerics.Vector2,System.Numerics.Vector2,System.Single,System.Single)
  langs:
  - csharp
  - vb
  name: DecelerationTimingVectorParameters
  nameWithType: DecelerationTimingVectorParameters
  fullName: DrawnUi.Draw.DecelerationTimingVectorParameters
  type: Class
  source:
    remote:
      path: src/Shared/Features/Animations/Parameters/DecelerationTimingVectorParameters.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: DecelerationTimingVectorParameters
    path: ../src/Shared/Features/Animations/Parameters/DecelerationTimingVectorParameters.cs
    startLine: 7
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: 'public class DecelerationTimingVectorParameters : ITimingVectorParameters'
    content.vb: Public Class DecelerationTimingVectorParameters Implements ITimingVectorParameters
  inheritance:
  - System.Object
  implements:
  - DrawnUi.Draw.ITimingVectorParameters
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.DecelerationTimingVectorParameters.InitialValue
  commentId: P:DrawnUi.Draw.DecelerationTimingVectorParameters.InitialValue
  id: InitialValue
  parent: DrawnUi.Draw.DecelerationTimingVectorParameters
  langs:
  - csharp
  - vb
  name: InitialValue
  nameWithType: DecelerationTimingVectorParameters.InitialValue
  fullName: DrawnUi.Draw.DecelerationTimingVectorParameters.InitialValue
  type: Property
  source:
    remote:
      path: src/Shared/Features/Animations/Parameters/DecelerationTimingVectorParameters.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: InitialValue
    path: ../src/Shared/Features/Animations/Parameters/DecelerationTimingVectorParameters.cs
    startLine: 10
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public Vector2 InitialValue { get; set; }
    parameters: []
    return:
      type: System.Numerics.Vector2
    content.vb: Public Property InitialValue As Vector2
  overload: DrawnUi.Draw.DecelerationTimingVectorParameters.InitialValue*
- uid: DrawnUi.Draw.DecelerationTimingVectorParameters.InitialVelocity
  commentId: P:DrawnUi.Draw.DecelerationTimingVectorParameters.InitialVelocity
  id: InitialVelocity
  parent: DrawnUi.Draw.DecelerationTimingVectorParameters
  langs:
  - csharp
  - vb
  name: InitialVelocity
  nameWithType: DecelerationTimingVectorParameters.InitialVelocity
  fullName: DrawnUi.Draw.DecelerationTimingVectorParameters.InitialVelocity
  type: Property
  source:
    remote:
      path: src/Shared/Features/Animations/Parameters/DecelerationTimingVectorParameters.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: InitialVelocity
    path: ../src/Shared/Features/Animations/Parameters/DecelerationTimingVectorParameters.cs
    startLine: 11
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public Vector2 InitialVelocity { get; set; }
    parameters: []
    return:
      type: System.Numerics.Vector2
    content.vb: Public Property InitialVelocity As Vector2
  overload: DrawnUi.Draw.DecelerationTimingVectorParameters.InitialVelocity*
- uid: DrawnUi.Draw.DecelerationTimingVectorParameters.DecelerationRate
  commentId: P:DrawnUi.Draw.DecelerationTimingVectorParameters.DecelerationRate
  id: DecelerationRate
  parent: DrawnUi.Draw.DecelerationTimingVectorParameters
  langs:
  - csharp
  - vb
  name: DecelerationRate
  nameWithType: DecelerationTimingVectorParameters.DecelerationRate
  fullName: DrawnUi.Draw.DecelerationTimingVectorParameters.DecelerationRate
  type: Property
  source:
    remote:
      path: src/Shared/Features/Animations/Parameters/DecelerationTimingVectorParameters.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: DecelerationRate
    path: ../src/Shared/Features/Animations/Parameters/DecelerationTimingVectorParameters.cs
    startLine: 12
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public float DecelerationRate { get; protected set; }
    parameters: []
    return:
      type: System.Single
    content.vb: Public Property DecelerationRate As Single
  overload: DrawnUi.Draw.DecelerationTimingVectorParameters.DecelerationRate*
- uid: DrawnUi.Draw.DecelerationTimingVectorParameters.DecelerationK
  commentId: P:DrawnUi.Draw.DecelerationTimingVectorParameters.DecelerationK
  id: DecelerationK
  parent: DrawnUi.Draw.DecelerationTimingVectorParameters
  langs:
  - csharp
  - vb
  name: DecelerationK
  nameWithType: DecelerationTimingVectorParameters.DecelerationK
  fullName: DrawnUi.Draw.DecelerationTimingVectorParameters.DecelerationK
  type: Property
  source:
    remote:
      path: src/Shared/Features/Animations/Parameters/DecelerationTimingVectorParameters.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: DecelerationK
    path: ../src/Shared/Features/Animations/Parameters/DecelerationTimingVectorParameters.cs
    startLine: 13
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public float DecelerationK { get; protected set; }
    parameters: []
    return:
      type: System.Single
    content.vb: Public Property DecelerationK As Single
  overload: DrawnUi.Draw.DecelerationTimingVectorParameters.DecelerationK*
- uid: DrawnUi.Draw.DecelerationTimingVectorParameters.Threshold
  commentId: P:DrawnUi.Draw.DecelerationTimingVectorParameters.Threshold
  id: Threshold
  parent: DrawnUi.Draw.DecelerationTimingVectorParameters
  langs:
  - csharp
  - vb
  name: Threshold
  nameWithType: DecelerationTimingVectorParameters.Threshold
  fullName: DrawnUi.Draw.DecelerationTimingVectorParameters.Threshold
  type: Property
  source:
    remote:
      path: src/Shared/Features/Animations/Parameters/DecelerationTimingVectorParameters.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Threshold
    path: ../src/Shared/Features/Animations/Parameters/DecelerationTimingVectorParameters.cs
    startLine: 14
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public float Threshold { get; set; }
    parameters: []
    return:
      type: System.Single
    content.vb: Public Property Threshold As Single
  overload: DrawnUi.Draw.DecelerationTimingVectorParameters.Threshold*
- uid: DrawnUi.Draw.DecelerationTimingVectorParameters.#ctor(System.Numerics.Vector2,System.Numerics.Vector2,System.Single,System.Single)
  commentId: M:DrawnUi.Draw.DecelerationTimingVectorParameters.#ctor(System.Numerics.Vector2,System.Numerics.Vector2,System.Single,System.Single)
  id: '#ctor(System.Numerics.Vector2,System.Numerics.Vector2,System.Single,System.Single)'
  parent: DrawnUi.Draw.DecelerationTimingVectorParameters
  langs:
  - csharp
  - vb
  name: DecelerationTimingVectorParameters(Vector2, Vector2, float, float)
  nameWithType: DecelerationTimingVectorParameters.DecelerationTimingVectorParameters(Vector2, Vector2, float, float)
  fullName: DrawnUi.Draw.DecelerationTimingVectorParameters.DecelerationTimingVectorParameters(System.Numerics.Vector2, System.Numerics.Vector2, float, float)
  type: Constructor
  source:
    remote:
      path: src/Shared/Features/Animations/Parameters/DecelerationTimingVectorParameters.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Shared/Features/Animations/Parameters/DecelerationTimingVectorParameters.cs
    startLine: 16
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public DecelerationTimingVectorParameters(Vector2 initialValue, Vector2 initialVelocity, float decelerationRate, float threshold)
    parameters:
    - id: initialValue
      type: System.Numerics.Vector2
    - id: initialVelocity
      type: System.Numerics.Vector2
    - id: decelerationRate
      type: System.Single
    - id: threshold
      type: System.Single
    content.vb: Public Sub New(initialValue As Vector2, initialVelocity As Vector2, decelerationRate As Single, threshold As Single)
  overload: DrawnUi.Draw.DecelerationTimingVectorParameters.#ctor*
  nameWithType.vb: DecelerationTimingVectorParameters.New(Vector2, Vector2, Single, Single)
  fullName.vb: DrawnUi.Draw.DecelerationTimingVectorParameters.New(System.Numerics.Vector2, System.Numerics.Vector2, Single, Single)
  name.vb: New(Vector2, Vector2, Single, Single)
- uid: DrawnUi.Draw.DecelerationTimingVectorParameters.Destination
  commentId: P:DrawnUi.Draw.DecelerationTimingVectorParameters.Destination
  id: Destination
  parent: DrawnUi.Draw.DecelerationTimingVectorParameters
  langs:
  - csharp
  - vb
  name: Destination
  nameWithType: DecelerationTimingVectorParameters.Destination
  fullName: DrawnUi.Draw.DecelerationTimingVectorParameters.Destination
  type: Property
  source:
    remote:
      path: src/Shared/Features/Animations/Parameters/DecelerationTimingVectorParameters.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Destination
    path: ../src/Shared/Features/Animations/Parameters/DecelerationTimingVectorParameters.cs
    startLine: 31
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public Vector2 Destination { get; }
    parameters: []
    return:
      type: System.Numerics.Vector2
    content.vb: Public ReadOnly Property Destination As Vector2
  overload: DrawnUi.Draw.DecelerationTimingVectorParameters.Destination*
- uid: DrawnUi.Draw.DecelerationTimingVectorParameters.DurationSecs
  commentId: P:DrawnUi.Draw.DecelerationTimingVectorParameters.DurationSecs
  id: DurationSecs
  parent: DrawnUi.Draw.DecelerationTimingVectorParameters
  langs:
  - csharp
  - vb
  name: DurationSecs
  nameWithType: DecelerationTimingVectorParameters.DurationSecs
  fullName: DrawnUi.Draw.DecelerationTimingVectorParameters.DurationSecs
  type: Property
  source:
    remote:
      path: src/Shared/Features/Animations/Parameters/DecelerationTimingVectorParameters.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: DurationSecs
    path: ../src/Shared/Features/Animations/Parameters/DecelerationTimingVectorParameters.cs
    startLine: 39
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  example: []
  syntax:
    content: public float DurationSecs { get; }
    parameters: []
    return:
      type: System.Single
    content.vb: Public ReadOnly Property DurationSecs As Single
  overload: DrawnUi.Draw.DecelerationTimingVectorParameters.DurationSecs*
  implements:
  - DrawnUi.Draw.ITimingVectorParameters.DurationSecs
- uid: DrawnUi.Draw.DecelerationTimingVectorParameters.ValueAt(System.Single)
  commentId: M:DrawnUi.Draw.DecelerationTimingVectorParameters.ValueAt(System.Single)
  id: ValueAt(System.Single)
  parent: DrawnUi.Draw.DecelerationTimingVectorParameters
  langs:
  - csharp
  - vb
  name: ValueAt(float)
  nameWithType: DecelerationTimingVectorParameters.ValueAt(float)
  fullName: DrawnUi.Draw.DecelerationTimingVectorParameters.ValueAt(float)
  type: Method
  source:
    remote:
      path: src/Shared/Features/Animations/Parameters/DecelerationTimingVectorParameters.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ValueAt
    path: ../src/Shared/Features/Animations/Parameters/DecelerationTimingVectorParameters.cs
    startLine: 56
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: time is in seconds
  example: []
  syntax:
    content: public Vector2 ValueAt(float offsetSecs)
    parameters:
    - id: offsetSecs
      type: System.Single
      description: ''
    return:
      type: System.Numerics.Vector2
      description: ''
    content.vb: Public Function ValueAt(offsetSecs As Single) As Vector2
  overload: DrawnUi.Draw.DecelerationTimingVectorParameters.ValueAt*
  implements:
  - DrawnUi.Draw.ITimingVectorParameters.ValueAt(System.Single)
  nameWithType.vb: DecelerationTimingVectorParameters.ValueAt(Single)
  fullName.vb: DrawnUi.Draw.DecelerationTimingVectorParameters.ValueAt(Single)
  name.vb: ValueAt(Single)
- uid: DrawnUi.Draw.DecelerationTimingVectorParameters.VelocityAt(System.Double)
  commentId: M:DrawnUi.Draw.DecelerationTimingVectorParameters.VelocityAt(System.Double)
  id: VelocityAt(System.Double)
  parent: DrawnUi.Draw.DecelerationTimingVectorParameters
  langs:
  - csharp
  - vb
  name: VelocityAt(double)
  nameWithType: DecelerationTimingVectorParameters.VelocityAt(double)
  fullName: DrawnUi.Draw.DecelerationTimingVectorParameters.VelocityAt(double)
  type: Method
  source:
    remote:
      path: src/Shared/Features/Animations/Parameters/DecelerationTimingVectorParameters.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: VelocityAt
    path: ../src/Shared/Features/Animations/Parameters/DecelerationTimingVectorParameters.cs
    startLine: 65
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public Vector2 VelocityAt(double time)
    parameters:
    - id: time
      type: System.Double
    return:
      type: System.Numerics.Vector2
    content.vb: Public Function VelocityAt(time As Double) As Vector2
  overload: DrawnUi.Draw.DecelerationTimingVectorParameters.VelocityAt*
  nameWithType.vb: DecelerationTimingVectorParameters.VelocityAt(Double)
  fullName.vb: DrawnUi.Draw.DecelerationTimingVectorParameters.VelocityAt(Double)
  name.vb: VelocityAt(Double)
- uid: DrawnUi.Draw.DecelerationTimingVectorParameters.DurationToValue(System.Numerics.Vector2)
  commentId: M:DrawnUi.Draw.DecelerationTimingVectorParameters.DurationToValue(System.Numerics.Vector2)
  id: DurationToValue(System.Numerics.Vector2)
  parent: DrawnUi.Draw.DecelerationTimingVectorParameters
  langs:
  - csharp
  - vb
  name: DurationToValue(Vector2)
  nameWithType: DecelerationTimingVectorParameters.DurationToValue(Vector2)
  fullName: DrawnUi.Draw.DecelerationTimingVectorParameters.DurationToValue(System.Numerics.Vector2)
  type: Method
  source:
    remote:
      path: src/Shared/Features/Animations/Parameters/DecelerationTimingVectorParameters.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: DurationToValue
    path: ../src/Shared/Features/Animations/Parameters/DecelerationTimingVectorParameters.cs
    startLine: 72
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public double DurationToValue(Vector2 value)
    parameters:
    - id: value
      type: System.Numerics.Vector2
    return:
      type: System.Double
    content.vb: Public Function DurationToValue(value As Vector2) As Double
  overload: DrawnUi.Draw.DecelerationTimingVectorParameters.DurationToValue*
- uid: DrawnUi.Draw.DecelerationTimingVectorParameters.VelocityTo(System.Numerics.Vector2,System.Numerics.Vector2,System.Double)
  commentId: M:DrawnUi.Draw.DecelerationTimingVectorParameters.VelocityTo(System.Numerics.Vector2,System.Numerics.Vector2,System.Double)
  id: VelocityTo(System.Numerics.Vector2,System.Numerics.Vector2,System.Double)
  parent: DrawnUi.Draw.DecelerationTimingVectorParameters
  langs:
  - csharp
  - vb
  name: VelocityTo(Vector2, Vector2, double)
  nameWithType: DecelerationTimingVectorParameters.VelocityTo(Vector2, Vector2, double)
  fullName: DrawnUi.Draw.DecelerationTimingVectorParameters.VelocityTo(System.Numerics.Vector2, System.Numerics.Vector2, double)
  type: Method
  source:
    remote:
      path: src/Shared/Features/Animations/Parameters/DecelerationTimingVectorParameters.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: VelocityTo
    path: ../src/Shared/Features/Animations/Parameters/DecelerationTimingVectorParameters.cs
    startLine: 81
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public Vector2 VelocityTo(Vector2 startingPoint, Vector2 targetPoint, double time)
    parameters:
    - id: startingPoint
      type: System.Numerics.Vector2
    - id: targetPoint
      type: System.Numerics.Vector2
    - id: time
      type: System.Double
    return:
      type: System.Numerics.Vector2
    content.vb: Public Function VelocityTo(startingPoint As Vector2, targetPoint As Vector2, time As Double) As Vector2
  overload: DrawnUi.Draw.DecelerationTimingVectorParameters.VelocityTo*
  nameWithType.vb: DecelerationTimingVectorParameters.VelocityTo(Vector2, Vector2, Double)
  fullName.vb: DrawnUi.Draw.DecelerationTimingVectorParameters.VelocityTo(System.Numerics.Vector2, System.Numerics.Vector2, Double)
  name.vb: VelocityTo(Vector2, Vector2, Double)
- uid: DrawnUi.Draw.DecelerationTimingVectorParameters.VelocityToZero(System.Numerics.Vector2,System.Numerics.Vector2,System.Single,System.Single)
  commentId: M:DrawnUi.Draw.DecelerationTimingVectorParameters.VelocityToZero(System.Numerics.Vector2,System.Numerics.Vector2,System.Single,System.Single)
  id: VelocityToZero(System.Numerics.Vector2,System.Numerics.Vector2,System.Single,System.Single)
  parent: DrawnUi.Draw.DecelerationTimingVectorParameters
  langs:
  - csharp
  - vb
  name: VelocityToZero(Vector2, Vector2, float, float)
  nameWithType: DecelerationTimingVectorParameters.VelocityToZero(Vector2, Vector2, float, float)
  fullName: DrawnUi.Draw.DecelerationTimingVectorParameters.VelocityToZero(System.Numerics.Vector2, System.Numerics.Vector2, float, float)
  type: Method
  source:
    remote:
      path: src/Shared/Features/Animations/Parameters/DecelerationTimingVectorParameters.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: VelocityToZero
    path: ../src/Shared/Features/Animations/Parameters/DecelerationTimingVectorParameters.cs
    startLine: 89
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public Vector2 VelocityToZero(Vector2 startingPoint, Vector2 targetPoint, float maxTimeSecs = 0, float epsilon = 1E-06)
    parameters:
    - id: startingPoint
      type: System.Numerics.Vector2
    - id: targetPoint
      type: System.Numerics.Vector2
    - id: maxTimeSecs
      type: System.Single
    - id: epsilon
      type: System.Single
    return:
      type: System.Numerics.Vector2
    content.vb: Public Function VelocityToZero(startingPoint As Vector2, targetPoint As Vector2, maxTimeSecs As Single = 0, epsilon As Single = 1E-06) As Vector2
  overload: DrawnUi.Draw.DecelerationTimingVectorParameters.VelocityToZero*
  nameWithType.vb: DecelerationTimingVectorParameters.VelocityToZero(Vector2, Vector2, Single, Single)
  fullName.vb: DrawnUi.Draw.DecelerationTimingVectorParameters.VelocityToZero(System.Numerics.Vector2, System.Numerics.Vector2, Single, Single)
  name.vb: VelocityToZero(Vector2, Vector2, Single, Single)
- uid: DrawnUi.Draw.DecelerationTimingVectorParameters.DistanceToSegment(System.Numerics.Vector2,System.Numerics.Vector2,System.Numerics.Vector2)
  commentId: M:DrawnUi.Draw.DecelerationTimingVectorParameters.DistanceToSegment(System.Numerics.Vector2,System.Numerics.Vector2,System.Numerics.Vector2)
  id: DistanceToSegment(System.Numerics.Vector2,System.Numerics.Vector2,System.Numerics.Vector2)
  parent: DrawnUi.Draw.DecelerationTimingVectorParameters
  langs:
  - csharp
  - vb
  name: DistanceToSegment(Vector2, Vector2, Vector2)
  nameWithType: DecelerationTimingVectorParameters.DistanceToSegment(Vector2, Vector2, Vector2)
  fullName: DrawnUi.Draw.DecelerationTimingVectorParameters.DistanceToSegment(System.Numerics.Vector2, System.Numerics.Vector2, System.Numerics.Vector2)
  type: Method
  source:
    remote:
      path: src/Shared/Features/Animations/Parameters/DecelerationTimingVectorParameters.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: DistanceToSegment
    path: ../src/Shared/Features/Animations/Parameters/DecelerationTimingVectorParameters.cs
    startLine: 108
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static float DistanceToSegment(Vector2 point, Vector2 segmentStart, Vector2 segmentEnd)
    parameters:
    - id: point
      type: System.Numerics.Vector2
    - id: segmentStart
      type: System.Numerics.Vector2
    - id: segmentEnd
      type: System.Numerics.Vector2
    return:
      type: System.Single
    content.vb: Public Shared Function DistanceToSegment(point As Vector2, segmentStart As Vector2, segmentEnd As Vector2) As Single
  overload: DrawnUi.Draw.DecelerationTimingVectorParameters.DistanceToSegment*
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: DrawnUi.Draw.ITimingVectorParameters
  commentId: T:DrawnUi.Draw.ITimingVectorParameters
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ITimingVectorParameters.html
  name: ITimingVectorParameters
  nameWithType: ITimingVectorParameters
  fullName: DrawnUi.Draw.ITimingVectorParameters
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.DecelerationTimingVectorParameters.InitialValue*
  commentId: Overload:DrawnUi.Draw.DecelerationTimingVectorParameters.InitialValue
  href: DrawnUi.Draw.DecelerationTimingVectorParameters.html#DrawnUi_Draw_DecelerationTimingVectorParameters_InitialValue
  name: InitialValue
  nameWithType: DecelerationTimingVectorParameters.InitialValue
  fullName: DrawnUi.Draw.DecelerationTimingVectorParameters.InitialValue
- uid: System.Numerics.Vector2
  commentId: T:System.Numerics.Vector2
  parent: System.Numerics
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.numerics.vector2
  name: Vector2
  nameWithType: Vector2
  fullName: System.Numerics.Vector2
- uid: System.Numerics
  commentId: N:System.Numerics
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Numerics
  nameWithType: System.Numerics
  fullName: System.Numerics
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Numerics
    name: Numerics
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.numerics
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Numerics
    name: Numerics
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.numerics
- uid: DrawnUi.Draw.DecelerationTimingVectorParameters.InitialVelocity*
  commentId: Overload:DrawnUi.Draw.DecelerationTimingVectorParameters.InitialVelocity
  href: DrawnUi.Draw.DecelerationTimingVectorParameters.html#DrawnUi_Draw_DecelerationTimingVectorParameters_InitialVelocity
  name: InitialVelocity
  nameWithType: DecelerationTimingVectorParameters.InitialVelocity
  fullName: DrawnUi.Draw.DecelerationTimingVectorParameters.InitialVelocity
- uid: DrawnUi.Draw.DecelerationTimingVectorParameters.DecelerationRate*
  commentId: Overload:DrawnUi.Draw.DecelerationTimingVectorParameters.DecelerationRate
  href: DrawnUi.Draw.DecelerationTimingVectorParameters.html#DrawnUi_Draw_DecelerationTimingVectorParameters_DecelerationRate
  name: DecelerationRate
  nameWithType: DecelerationTimingVectorParameters.DecelerationRate
  fullName: DrawnUi.Draw.DecelerationTimingVectorParameters.DecelerationRate
- uid: System.Single
  commentId: T:System.Single
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.single
  name: float
  nameWithType: float
  fullName: float
  nameWithType.vb: Single
  fullName.vb: Single
  name.vb: Single
- uid: DrawnUi.Draw.DecelerationTimingVectorParameters.DecelerationK*
  commentId: Overload:DrawnUi.Draw.DecelerationTimingVectorParameters.DecelerationK
  href: DrawnUi.Draw.DecelerationTimingVectorParameters.html#DrawnUi_Draw_DecelerationTimingVectorParameters_DecelerationK
  name: DecelerationK
  nameWithType: DecelerationTimingVectorParameters.DecelerationK
  fullName: DrawnUi.Draw.DecelerationTimingVectorParameters.DecelerationK
- uid: DrawnUi.Draw.DecelerationTimingVectorParameters.Threshold*
  commentId: Overload:DrawnUi.Draw.DecelerationTimingVectorParameters.Threshold
  href: DrawnUi.Draw.DecelerationTimingVectorParameters.html#DrawnUi_Draw_DecelerationTimingVectorParameters_Threshold
  name: Threshold
  nameWithType: DecelerationTimingVectorParameters.Threshold
  fullName: DrawnUi.Draw.DecelerationTimingVectorParameters.Threshold
- uid: DrawnUi.Draw.DecelerationTimingVectorParameters.#ctor*
  commentId: Overload:DrawnUi.Draw.DecelerationTimingVectorParameters.#ctor
  href: DrawnUi.Draw.DecelerationTimingVectorParameters.html#DrawnUi_Draw_DecelerationTimingVectorParameters__ctor_System_Numerics_Vector2_System_Numerics_Vector2_System_Single_System_Single_
  name: DecelerationTimingVectorParameters
  nameWithType: DecelerationTimingVectorParameters.DecelerationTimingVectorParameters
  fullName: DrawnUi.Draw.DecelerationTimingVectorParameters.DecelerationTimingVectorParameters
  nameWithType.vb: DecelerationTimingVectorParameters.New
  fullName.vb: DrawnUi.Draw.DecelerationTimingVectorParameters.New
  name.vb: New
- uid: DrawnUi.Draw.DecelerationTimingVectorParameters.Destination*
  commentId: Overload:DrawnUi.Draw.DecelerationTimingVectorParameters.Destination
  href: DrawnUi.Draw.DecelerationTimingVectorParameters.html#DrawnUi_Draw_DecelerationTimingVectorParameters_Destination
  name: Destination
  nameWithType: DecelerationTimingVectorParameters.Destination
  fullName: DrawnUi.Draw.DecelerationTimingVectorParameters.Destination
- uid: DrawnUi.Draw.DecelerationTimingVectorParameters.DurationSecs*
  commentId: Overload:DrawnUi.Draw.DecelerationTimingVectorParameters.DurationSecs
  href: DrawnUi.Draw.DecelerationTimingVectorParameters.html#DrawnUi_Draw_DecelerationTimingVectorParameters_DurationSecs
  name: DurationSecs
  nameWithType: DecelerationTimingVectorParameters.DurationSecs
  fullName: DrawnUi.Draw.DecelerationTimingVectorParameters.DurationSecs
- uid: DrawnUi.Draw.ITimingVectorParameters.DurationSecs
  commentId: P:DrawnUi.Draw.ITimingVectorParameters.DurationSecs
  parent: DrawnUi.Draw.ITimingVectorParameters
  href: DrawnUi.Draw.ITimingVectorParameters.html#DrawnUi_Draw_ITimingVectorParameters_DurationSecs
  name: DurationSecs
  nameWithType: ITimingVectorParameters.DurationSecs
  fullName: DrawnUi.Draw.ITimingVectorParameters.DurationSecs
- uid: DrawnUi.Draw.DecelerationTimingVectorParameters.ValueAt*
  commentId: Overload:DrawnUi.Draw.DecelerationTimingVectorParameters.ValueAt
  href: DrawnUi.Draw.DecelerationTimingVectorParameters.html#DrawnUi_Draw_DecelerationTimingVectorParameters_ValueAt_System_Single_
  name: ValueAt
  nameWithType: DecelerationTimingVectorParameters.ValueAt
  fullName: DrawnUi.Draw.DecelerationTimingVectorParameters.ValueAt
- uid: DrawnUi.Draw.ITimingVectorParameters.ValueAt(System.Single)
  commentId: M:DrawnUi.Draw.ITimingVectorParameters.ValueAt(System.Single)
  parent: DrawnUi.Draw.ITimingVectorParameters
  isExternal: true
  href: DrawnUi.Draw.ITimingVectorParameters.html#DrawnUi_Draw_ITimingVectorParameters_ValueAt_System_Single_
  name: ValueAt(float)
  nameWithType: ITimingVectorParameters.ValueAt(float)
  fullName: DrawnUi.Draw.ITimingVectorParameters.ValueAt(float)
  nameWithType.vb: ITimingVectorParameters.ValueAt(Single)
  fullName.vb: DrawnUi.Draw.ITimingVectorParameters.ValueAt(Single)
  name.vb: ValueAt(Single)
  spec.csharp:
  - uid: DrawnUi.Draw.ITimingVectorParameters.ValueAt(System.Single)
    name: ValueAt
    href: DrawnUi.Draw.ITimingVectorParameters.html#DrawnUi_Draw_ITimingVectorParameters_ValueAt_System_Single_
  - name: (
  - uid: System.Single
    name: float
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.single
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.ITimingVectorParameters.ValueAt(System.Single)
    name: ValueAt
    href: DrawnUi.Draw.ITimingVectorParameters.html#DrawnUi_Draw_ITimingVectorParameters_ValueAt_System_Single_
  - name: (
  - uid: System.Single
    name: Single
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.single
  - name: )
- uid: DrawnUi.Draw.DecelerationTimingVectorParameters.VelocityAt*
  commentId: Overload:DrawnUi.Draw.DecelerationTimingVectorParameters.VelocityAt
  href: DrawnUi.Draw.DecelerationTimingVectorParameters.html#DrawnUi_Draw_DecelerationTimingVectorParameters_VelocityAt_System_Double_
  name: VelocityAt
  nameWithType: DecelerationTimingVectorParameters.VelocityAt
  fullName: DrawnUi.Draw.DecelerationTimingVectorParameters.VelocityAt
- uid: System.Double
  commentId: T:System.Double
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.double
  name: double
  nameWithType: double
  fullName: double
  nameWithType.vb: Double
  fullName.vb: Double
  name.vb: Double
- uid: DrawnUi.Draw.DecelerationTimingVectorParameters.DurationToValue*
  commentId: Overload:DrawnUi.Draw.DecelerationTimingVectorParameters.DurationToValue
  href: DrawnUi.Draw.DecelerationTimingVectorParameters.html#DrawnUi_Draw_DecelerationTimingVectorParameters_DurationToValue_System_Numerics_Vector2_
  name: DurationToValue
  nameWithType: DecelerationTimingVectorParameters.DurationToValue
  fullName: DrawnUi.Draw.DecelerationTimingVectorParameters.DurationToValue
- uid: DrawnUi.Draw.DecelerationTimingVectorParameters.VelocityTo*
  commentId: Overload:DrawnUi.Draw.DecelerationTimingVectorParameters.VelocityTo
  href: DrawnUi.Draw.DecelerationTimingVectorParameters.html#DrawnUi_Draw_DecelerationTimingVectorParameters_VelocityTo_System_Numerics_Vector2_System_Numerics_Vector2_System_Double_
  name: VelocityTo
  nameWithType: DecelerationTimingVectorParameters.VelocityTo
  fullName: DrawnUi.Draw.DecelerationTimingVectorParameters.VelocityTo
- uid: DrawnUi.Draw.DecelerationTimingVectorParameters.VelocityToZero*
  commentId: Overload:DrawnUi.Draw.DecelerationTimingVectorParameters.VelocityToZero
  href: DrawnUi.Draw.DecelerationTimingVectorParameters.html#DrawnUi_Draw_DecelerationTimingVectorParameters_VelocityToZero_System_Numerics_Vector2_System_Numerics_Vector2_System_Single_System_Single_
  name: VelocityToZero
  nameWithType: DecelerationTimingVectorParameters.VelocityToZero
  fullName: DrawnUi.Draw.DecelerationTimingVectorParameters.VelocityToZero
- uid: DrawnUi.Draw.DecelerationTimingVectorParameters.DistanceToSegment*
  commentId: Overload:DrawnUi.Draw.DecelerationTimingVectorParameters.DistanceToSegment
  href: DrawnUi.Draw.DecelerationTimingVectorParameters.html#DrawnUi_Draw_DecelerationTimingVectorParameters_DistanceToSegment_System_Numerics_Vector2_System_Numerics_Vector2_System_Numerics_Vector2_
  name: DistanceToSegment
  nameWithType: DecelerationTimingVectorParameters.DistanceToSegment
  fullName: DrawnUi.Draw.DecelerationTimingVectorParameters.DistanceToSegment
