### YamlMime:ManagedReference
items:
- uid: DrawnUi.Views.BasePageReloadable
  commentId: T:DrawnUi.Views.BasePageReloadable
  id: BasePageReloadable
  parent: DrawnUi.Views
  children:
  - DrawnUi.Views.BasePageReloadable.#ctor
  - DrawnUi.Views.BasePageReloadable.Build
  - DrawnUi.Views.BasePageReloadable.CountReloads
  - DrawnUi.Views.BasePageReloadable.Dispose
  - DrawnUi.Views.BasePageReloadable.Dispose(System.Boolean)
  - DrawnUi.Views.BasePageReloadable.IsDisposed
  - DrawnUi.Views.BasePageReloadable.ReloadUi(System.Type[])
  langs:
  - csharp
  - vb
  name: BasePageReloadable
  nameWithType: BasePageReloadable
  fullName: DrawnUi.Views.BasePageReloadable
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Views/BasePageReloadable.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: BasePageReloadable
    path: ../src/Maui/DrawnUi/Views/BasePageReloadable.cs
    startLine: 7
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  summary: >-
    Base class for a page with canvas, supports C# HotReload for building UI with code (not XAML).

    Override `Build()`, see examples.
  example: []
  syntax:
    content: 'public class BasePageReloadable : DrawnUiBasePage, INotifyPropertyChanged, IVisualTreeElement, IEffectControlProvider, IToolTipElement, IContextFlyoutElement, IAnimatable, ILayout, IPageController, IVisualElementController, IElementController, IElementConfiguration<Page>, ISafeAreaView, ITitledElement, IToolbarElement, IContentView, IPadding, ICrossPlatformLayout, IHotReloadableView, IView, IElement, ITransform, IReplaceableView, IDisposable'
    content.vb: Public Class BasePageReloadable Inherits DrawnUiBasePage Implements INotifyPropertyChanged, IVisualTreeElement, IEffectControlProvider, IToolTipElement, IContextFlyoutElement, IAnimatable, ILayout, IPageController, IVisualElementController, IElementController, IElementConfiguration(Of Page), ISafeAreaView, ITitledElement, IToolbarElement, IContentView, IPadding, ICrossPlatformLayout, IHotReloadableView, IView, IElement, ITransform, IReplaceableView, IDisposable
  inheritance:
  - System.Object
  - Microsoft.Maui.Controls.BindableObject
  - Microsoft.Maui.Controls.Element
  - Microsoft.Maui.Controls.StyleableElement
  - Microsoft.Maui.Controls.NavigableElement
  - Microsoft.Maui.Controls.VisualElement
  - Microsoft.Maui.Controls.Page
  - Microsoft.Maui.Controls.TemplatedPage
  - Microsoft.Maui.Controls.ContentPage
  - DrawnUi.Views.DrawnUiBasePage
  derivedClasses:
  - DrawnUi.Controls.SkiaShell
  implements:
  - System.ComponentModel.INotifyPropertyChanged
  - Microsoft.Maui.IVisualTreeElement
  - Microsoft.Maui.Controls.IEffectControlProvider
  - Microsoft.Maui.IToolTipElement
  - Microsoft.Maui.IContextFlyoutElement
  - Microsoft.Maui.Controls.IAnimatable
  - Microsoft.Maui.Controls.ILayout
  - Microsoft.Maui.Controls.IPageController
  - Microsoft.Maui.Controls.IVisualElementController
  - Microsoft.Maui.Controls.IElementController
  - Microsoft.Maui.Controls.IElementConfiguration{Microsoft.Maui.Controls.Page}
  - Microsoft.Maui.ISafeAreaView
  - Microsoft.Maui.ITitledElement
  - Microsoft.Maui.IToolbarElement
  - Microsoft.Maui.IContentView
  - Microsoft.Maui.IPadding
  - Microsoft.Maui.ICrossPlatformLayout
  - Microsoft.Maui.HotReload.IHotReloadableView
  - Microsoft.Maui.IView
  - Microsoft.Maui.IElement
  - Microsoft.Maui.ITransform
  - Microsoft.Maui.IReplaceableView
  - System.IDisposable
  inheritedMembers:
  - DrawnUi.Views.DrawnUiBasePage.KeyboardResized(System.Double)
  - DrawnUi.Views.DrawnUiBasePage.OnKeyboardResized(System.Double)
  - Microsoft.Maui.Controls.ContentPage.ContentProperty
  - Microsoft.Maui.Controls.ContentPage.HideSoftInputOnTappedProperty
  - Microsoft.Maui.Controls.ContentPage.OnBindingContextChanged
  - Microsoft.Maui.Controls.ContentPage.MeasureOverride(System.Double,System.Double)
  - Microsoft.Maui.Controls.ContentPage.LayoutChildren(System.Double,System.Double,System.Double,System.Double)
  - Microsoft.Maui.Controls.ContentPage.ArrangeOverride(Microsoft.Maui.Graphics.Rect)
  - Microsoft.Maui.Controls.ContentPage.InvalidateMeasureOverride
  - Microsoft.Maui.Controls.ContentPage.Content
  - Microsoft.Maui.Controls.ContentPage.HideSoftInputOnTapped
  - Microsoft.Maui.Controls.TemplatedPage.ControlTemplateProperty
  - Microsoft.Maui.Controls.TemplatedPage.OnApplyTemplate
  - Microsoft.Maui.Controls.TemplatedPage.OnChildRemoved(Microsoft.Maui.Controls.Element,System.Int32)
  - Microsoft.Maui.Controls.TemplatedPage.GetTemplateChild(System.String)
  - Microsoft.Maui.Controls.TemplatedPage.ControlTemplate
  - Microsoft.Maui.Controls.Page.BusySetSignalName
  - Microsoft.Maui.Controls.Page.AlertSignalName
  - Microsoft.Maui.Controls.Page.PromptSignalName
  - Microsoft.Maui.Controls.Page.ActionSheetSignalName
  - Microsoft.Maui.Controls.Page.BackgroundImageSourceProperty
  - Microsoft.Maui.Controls.Page.IsBusyProperty
  - Microsoft.Maui.Controls.Page.PaddingProperty
  - Microsoft.Maui.Controls.Page.TitleProperty
  - Microsoft.Maui.Controls.Page.IconImageSourceProperty
  - Microsoft.Maui.Controls.Page.DisplayActionSheet(System.String,System.String,System.String,System.String[])
  - Microsoft.Maui.Controls.Page.DisplayActionSheet(System.String,System.String,System.String,Microsoft.Maui.FlowDirection,System.String[])
  - Microsoft.Maui.Controls.Page.DisplayAlert(System.String,System.String,System.String)
  - Microsoft.Maui.Controls.Page.DisplayAlert(System.String,System.String,System.String,System.String)
  - Microsoft.Maui.Controls.Page.DisplayAlert(System.String,System.String,System.String,Microsoft.Maui.FlowDirection)
  - Microsoft.Maui.Controls.Page.DisplayAlert(System.String,System.String,System.String,System.String,Microsoft.Maui.FlowDirection)
  - Microsoft.Maui.Controls.Page.DisplayPromptAsync(System.String,System.String,System.String,System.String,System.String,System.Int32,Microsoft.Maui.Keyboard,System.String)
  - Microsoft.Maui.Controls.Page.ForceLayout
  - Microsoft.Maui.Controls.Page.SendBackButtonPressed
  - Microsoft.Maui.Controls.Page.OnAppearing
  - Microsoft.Maui.Controls.Page.OnBackButtonPressed
  - Microsoft.Maui.Controls.Page.OnChildMeasureInvalidated(System.Object,System.EventArgs)
  - Microsoft.Maui.Controls.Page.OnDisappearing
  - Microsoft.Maui.Controls.Page.OnParentSet
  - Microsoft.Maui.Controls.Page.OnSizeAllocated(System.Double,System.Double)
  - Microsoft.Maui.Controls.Page.UpdateChildrenLayout
  - Microsoft.Maui.Controls.Page.On``1
  - Microsoft.Maui.Controls.Page.OnNavigatedTo(Microsoft.Maui.Controls.NavigatedToEventArgs)
  - Microsoft.Maui.Controls.Page.OnNavigatingFrom(Microsoft.Maui.Controls.NavigatingFromEventArgs)
  - Microsoft.Maui.Controls.Page.OnNavigatedFrom(Microsoft.Maui.Controls.NavigatedFromEventArgs)
  - Microsoft.Maui.Controls.Page.GetParentWindow
  - Microsoft.Maui.Controls.Page.BackgroundImageSource
  - Microsoft.Maui.Controls.Page.IconImageSource
  - Microsoft.Maui.Controls.Page.IsBusy
  - Microsoft.Maui.Controls.Page.Padding
  - Microsoft.Maui.Controls.Page.Title
  - Microsoft.Maui.Controls.Page.ToolbarItems
  - Microsoft.Maui.Controls.Page.MenuBarItems
  - Microsoft.Maui.Controls.Page.LayoutChanged
  - Microsoft.Maui.Controls.Page.Appearing
  - Microsoft.Maui.Controls.Page.Disappearing
  - Microsoft.Maui.Controls.Page.NavigatedTo
  - Microsoft.Maui.Controls.Page.NavigatingFrom
  - Microsoft.Maui.Controls.Page.NavigatedFrom
  - Microsoft.Maui.Controls.VisualElement.NavigationProperty
  - Microsoft.Maui.Controls.VisualElement.StyleProperty
  - Microsoft.Maui.Controls.VisualElement.InputTransparentProperty
  - Microsoft.Maui.Controls.VisualElement.IsEnabledProperty
  - Microsoft.Maui.Controls.VisualElement.XProperty
  - Microsoft.Maui.Controls.VisualElement.YProperty
  - Microsoft.Maui.Controls.VisualElement.AnchorXProperty
  - Microsoft.Maui.Controls.VisualElement.AnchorYProperty
  - Microsoft.Maui.Controls.VisualElement.TranslationXProperty
  - Microsoft.Maui.Controls.VisualElement.TranslationYProperty
  - Microsoft.Maui.Controls.VisualElement.WidthProperty
  - Microsoft.Maui.Controls.VisualElement.HeightProperty
  - Microsoft.Maui.Controls.VisualElement.RotationProperty
  - Microsoft.Maui.Controls.VisualElement.RotationXProperty
  - Microsoft.Maui.Controls.VisualElement.RotationYProperty
  - Microsoft.Maui.Controls.VisualElement.ScaleProperty
  - Microsoft.Maui.Controls.VisualElement.ScaleXProperty
  - Microsoft.Maui.Controls.VisualElement.ScaleYProperty
  - Microsoft.Maui.Controls.VisualElement.ClipProperty
  - Microsoft.Maui.Controls.VisualElement.VisualProperty
  - Microsoft.Maui.Controls.VisualElement.IsVisibleProperty
  - Microsoft.Maui.Controls.VisualElement.OpacityProperty
  - Microsoft.Maui.Controls.VisualElement.BackgroundColorProperty
  - Microsoft.Maui.Controls.VisualElement.BackgroundProperty
  - Microsoft.Maui.Controls.VisualElement.BehaviorsProperty
  - Microsoft.Maui.Controls.VisualElement.TriggersProperty
  - Microsoft.Maui.Controls.VisualElement.WidthRequestProperty
  - Microsoft.Maui.Controls.VisualElement.HeightRequestProperty
  - Microsoft.Maui.Controls.VisualElement.MinimumWidthRequestProperty
  - Microsoft.Maui.Controls.VisualElement.MinimumHeightRequestProperty
  - Microsoft.Maui.Controls.VisualElement.MaximumWidthRequestProperty
  - Microsoft.Maui.Controls.VisualElement.MaximumHeightRequestProperty
  - Microsoft.Maui.Controls.VisualElement.IsFocusedProperty
  - Microsoft.Maui.Controls.VisualElement.FlowDirectionProperty
  - Microsoft.Maui.Controls.VisualElement.WindowProperty
  - Microsoft.Maui.Controls.VisualElement.ShadowProperty
  - Microsoft.Maui.Controls.VisualElement.ZIndexProperty
  - Microsoft.Maui.Controls.VisualElement.BatchBegin
  - Microsoft.Maui.Controls.VisualElement.BatchCommit
  - Microsoft.Maui.Controls.VisualElement.Focus
  - Microsoft.Maui.Controls.VisualElement.Measure(System.Double,System.Double)
  - Microsoft.Maui.Controls.VisualElement.Measure(System.Double,System.Double,Microsoft.Maui.Controls.MeasureFlags)
  - Microsoft.Maui.Controls.VisualElement.Unfocus
  - Microsoft.Maui.Controls.VisualElement.InvalidateMeasure
  - Microsoft.Maui.Controls.VisualElement.OnChildAdded(Microsoft.Maui.Controls.Element)
  - Microsoft.Maui.Controls.VisualElement.OnChildrenReordered
  - Microsoft.Maui.Controls.VisualElement.OnMeasure(System.Double,System.Double)
  - Microsoft.Maui.Controls.VisualElement.SizeAllocated(System.Double,System.Double)
  - Microsoft.Maui.Controls.VisualElement.ChangeVisualState
  - Microsoft.Maui.Controls.VisualElement.RefreshIsEnabledProperty
  - Microsoft.Maui.Controls.VisualElement.Arrange(Microsoft.Maui.Graphics.Rect)
  - Microsoft.Maui.Controls.VisualElement.Layout(Microsoft.Maui.Graphics.Rect)
  - Microsoft.Maui.Controls.VisualElement.MapBackgroundColor(Microsoft.Maui.IViewHandler,Microsoft.Maui.IView)
  - Microsoft.Maui.Controls.VisualElement.MapBackgroundImageSource(Microsoft.Maui.IViewHandler,Microsoft.Maui.IView)
  - Microsoft.Maui.Controls.VisualElement.Visual
  - Microsoft.Maui.Controls.VisualElement.FlowDirection
  - Microsoft.Maui.Controls.VisualElement.Window
  - Microsoft.Maui.Controls.VisualElement.AnchorX
  - Microsoft.Maui.Controls.VisualElement.AnchorY
  - Microsoft.Maui.Controls.VisualElement.BackgroundColor
  - Microsoft.Maui.Controls.VisualElement.Background
  - Microsoft.Maui.Controls.VisualElement.Behaviors
  - Microsoft.Maui.Controls.VisualElement.Bounds
  - Microsoft.Maui.Controls.VisualElement.Height
  - Microsoft.Maui.Controls.VisualElement.HeightRequest
  - Microsoft.Maui.Controls.VisualElement.InputTransparent
  - Microsoft.Maui.Controls.VisualElement.IsEnabled
  - Microsoft.Maui.Controls.VisualElement.IsEnabledCore
  - Microsoft.Maui.Controls.VisualElement.IsFocused
  - Microsoft.Maui.Controls.VisualElement.IsVisible
  - Microsoft.Maui.Controls.VisualElement.MinimumHeightRequest
  - Microsoft.Maui.Controls.VisualElement.MinimumWidthRequest
  - Microsoft.Maui.Controls.VisualElement.MaximumHeightRequest
  - Microsoft.Maui.Controls.VisualElement.MaximumWidthRequest
  - Microsoft.Maui.Controls.VisualElement.Opacity
  - Microsoft.Maui.Controls.VisualElement.Rotation
  - Microsoft.Maui.Controls.VisualElement.RotationX
  - Microsoft.Maui.Controls.VisualElement.RotationY
  - Microsoft.Maui.Controls.VisualElement.Scale
  - Microsoft.Maui.Controls.VisualElement.ScaleX
  - Microsoft.Maui.Controls.VisualElement.ScaleY
  - Microsoft.Maui.Controls.VisualElement.TranslationX
  - Microsoft.Maui.Controls.VisualElement.TranslationY
  - Microsoft.Maui.Controls.VisualElement.Triggers
  - Microsoft.Maui.Controls.VisualElement.Width
  - Microsoft.Maui.Controls.VisualElement.WidthRequest
  - Microsoft.Maui.Controls.VisualElement.X
  - Microsoft.Maui.Controls.VisualElement.Y
  - Microsoft.Maui.Controls.VisualElement.Clip
  - Microsoft.Maui.Controls.VisualElement.Resources
  - Microsoft.Maui.Controls.VisualElement.Frame
  - Microsoft.Maui.Controls.VisualElement.Handler
  - Microsoft.Maui.Controls.VisualElement.Shadow
  - Microsoft.Maui.Controls.VisualElement.ZIndex
  - Microsoft.Maui.Controls.VisualElement.DesiredSize
  - Microsoft.Maui.Controls.VisualElement.IsLoaded
  - Microsoft.Maui.Controls.VisualElement.ChildrenReordered
  - Microsoft.Maui.Controls.VisualElement.Focused
  - Microsoft.Maui.Controls.VisualElement.MeasureInvalidated
  - Microsoft.Maui.Controls.VisualElement.SizeChanged
  - Microsoft.Maui.Controls.VisualElement.Unfocused
  - Microsoft.Maui.Controls.VisualElement.Loaded
  - Microsoft.Maui.Controls.VisualElement.Unloaded
  - Microsoft.Maui.Controls.NavigableElement.Navigation
  - Microsoft.Maui.Controls.StyleableElement.Style
  - Microsoft.Maui.Controls.StyleableElement.StyleClass
  - Microsoft.Maui.Controls.StyleableElement.class
  - Microsoft.Maui.Controls.Element.AutomationIdProperty
  - Microsoft.Maui.Controls.Element.ClassIdProperty
  - Microsoft.Maui.Controls.Element.InsertLogicalChild(System.Int32,Microsoft.Maui.Controls.Element)
  - Microsoft.Maui.Controls.Element.AddLogicalChild(Microsoft.Maui.Controls.Element)
  - Microsoft.Maui.Controls.Element.RemoveLogicalChild(Microsoft.Maui.Controls.Element)
  - Microsoft.Maui.Controls.Element.ClearLogicalChildren
  - Microsoft.Maui.Controls.Element.FindByName(System.String)
  - Microsoft.Maui.Controls.Element.RemoveDynamicResource(Microsoft.Maui.Controls.BindableProperty)
  - Microsoft.Maui.Controls.Element.SetDynamicResource(Microsoft.Maui.Controls.BindableProperty,System.String)
  - Microsoft.Maui.Controls.Element.OnPropertyChanged(System.String)
  - Microsoft.Maui.Controls.Element.OnParentChanging(Microsoft.Maui.Controls.ParentChangingEventArgs)
  - Microsoft.Maui.Controls.Element.OnParentChanged
  - Microsoft.Maui.Controls.Element.OnHandlerChanging(Microsoft.Maui.Controls.HandlerChangingEventArgs)
  - Microsoft.Maui.Controls.Element.OnHandlerChanged
  - Microsoft.Maui.Controls.Element.MapAutomationPropertiesIsInAccessibleTree(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
  - Microsoft.Maui.Controls.Element.MapAutomationPropertiesExcludedWithChildren(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
  - Microsoft.Maui.Controls.Element.AutomationId
  - Microsoft.Maui.Controls.Element.ClassId
  - Microsoft.Maui.Controls.Element.Effects
  - Microsoft.Maui.Controls.Element.Id
  - Microsoft.Maui.Controls.Element.StyleId
  - Microsoft.Maui.Controls.Element.Parent
  - Microsoft.Maui.Controls.Element.ChildAdded
  - Microsoft.Maui.Controls.Element.ChildRemoved
  - Microsoft.Maui.Controls.Element.DescendantAdded
  - Microsoft.Maui.Controls.Element.DescendantRemoved
  - Microsoft.Maui.Controls.Element.ParentChanging
  - Microsoft.Maui.Controls.Element.ParentChanged
  - Microsoft.Maui.Controls.Element.HandlerChanging
  - Microsoft.Maui.Controls.Element.HandlerChanged
  - Microsoft.Maui.Controls.BindableObject.BindingContextProperty
  - Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindableProperty)
  - Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindablePropertyKey)
  - Microsoft.Maui.Controls.BindableObject.GetValue(Microsoft.Maui.Controls.BindableProperty)
  - Microsoft.Maui.Controls.BindableObject.IsSet(Microsoft.Maui.Controls.BindableProperty)
  - Microsoft.Maui.Controls.BindableObject.RemoveBinding(Microsoft.Maui.Controls.BindableProperty)
  - Microsoft.Maui.Controls.BindableObject.SetBinding(Microsoft.Maui.Controls.BindableProperty,Microsoft.Maui.Controls.BindingBase)
  - Microsoft.Maui.Controls.BindableObject.ApplyBindings
  - Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(System.String)
  - Microsoft.Maui.Controls.BindableObject.UnapplyBindings
  - Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty,System.Object)
  - Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey,System.Object)
  - Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindableProperty)
  - Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindablePropertyKey)
  - Microsoft.Maui.Controls.BindableObject.Dispatcher
  - Microsoft.Maui.Controls.BindableObject.BindingContext
  - Microsoft.Maui.Controls.BindableObject.PropertyChanged
  - Microsoft.Maui.Controls.BindableObject.PropertyChanging
  - Microsoft.Maui.Controls.BindableObject.BindingContextChanged
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - DrawnUi.Views.BasePageReloadable.DrawnUi.Draw.FluentExtensions.AssignNative``1(DrawnUi.Views.BasePageReloadable@)
  - Microsoft.Maui.Controls.Element.DrawnUi.Draw.StaticResourcesExtensions.FindParent``1
  - Microsoft.Maui.Controls.Element.DrawnUi.Extensions.InternalExtensions.FindMauiContext(System.Boolean)
  - Microsoft.Maui.Controls.Element.DrawnUi.Extensions.InternalExtensions.GetParentsPath
  - Microsoft.Maui.Controls.VisualElement.DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents
  - Microsoft.Maui.IView.DrawnUi.Extensions.InternalExtensions.DisposeControlAndChildren
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Views.BasePageReloadable.#ctor
  commentId: M:DrawnUi.Views.BasePageReloadable.#ctor
  id: '#ctor'
  parent: DrawnUi.Views.BasePageReloadable
  langs:
  - csharp
  - vb
  name: BasePageReloadable()
  nameWithType: BasePageReloadable.BasePageReloadable()
  fullName: DrawnUi.Views.BasePageReloadable.BasePageReloadable()
  type: Constructor
  source:
    remote:
      path: src/Maui/DrawnUi/Views/BasePageReloadable.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Maui/DrawnUi/Views/BasePageReloadable.cs
    startLine: 10
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: public BasePageReloadable()
    content.vb: Public Sub New()
  overload: DrawnUi.Views.BasePageReloadable.#ctor*
  nameWithType.vb: BasePageReloadable.New()
  fullName.vb: DrawnUi.Views.BasePageReloadable.New()
  name.vb: New()
- uid: DrawnUi.Views.BasePageReloadable.CountReloads
  commentId: P:DrawnUi.Views.BasePageReloadable.CountReloads
  id: CountReloads
  parent: DrawnUi.Views.BasePageReloadable
  langs:
  - csharp
  - vb
  name: CountReloads
  nameWithType: BasePageReloadable.CountReloads
  fullName: DrawnUi.Views.BasePageReloadable.CountReloads
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Views/BasePageReloadable.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: CountReloads
    path: ../src/Maui/DrawnUi/Views/BasePageReloadable.cs
    startLine: 28
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: public int CountReloads { get; protected set; }
    parameters: []
    return:
      type: System.Int32
    content.vb: Public Property CountReloads As Integer
  overload: DrawnUi.Views.BasePageReloadable.CountReloads*
- uid: DrawnUi.Views.BasePageReloadable.ReloadUi(System.Type[])
  commentId: M:DrawnUi.Views.BasePageReloadable.ReloadUi(System.Type[])
  id: ReloadUi(System.Type[])
  parent: DrawnUi.Views.BasePageReloadable
  langs:
  - csharp
  - vb
  name: ReloadUi(Type[])
  nameWithType: BasePageReloadable.ReloadUi(Type[])
  fullName: DrawnUi.Views.BasePageReloadable.ReloadUi(System.Type[])
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/BasePageReloadable.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ReloadUi
    path: ../src/Maui/DrawnUi/Views/BasePageReloadable.cs
    startLine: 34
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  summary: Reload code-behind constructed page
  example: []
  syntax:
    content: protected virtual void ReloadUi(Type[] obj)
    parameters:
    - id: obj
      type: System.Type[]
      description: ''
    content.vb: Protected Overridable Sub ReloadUi(obj As Type())
  overload: DrawnUi.Views.BasePageReloadable.ReloadUi*
  nameWithType.vb: BasePageReloadable.ReloadUi(Type())
  fullName.vb: DrawnUi.Views.BasePageReloadable.ReloadUi(System.Type())
  name.vb: ReloadUi(Type())
- uid: DrawnUi.Views.BasePageReloadable.Build
  commentId: M:DrawnUi.Views.BasePageReloadable.Build
  id: Build
  parent: DrawnUi.Views.BasePageReloadable
  langs:
  - csharp
  - vb
  name: Build()
  nameWithType: BasePageReloadable.Build()
  fullName: DrawnUi.Views.BasePageReloadable.Build()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/BasePageReloadable.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Build
    path: ../src/Maui/DrawnUi/Views/BasePageReloadable.cs
    startLine: 46
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  summary: Build code-behind constructed page
  example: []
  syntax:
    content: public virtual void Build()
    content.vb: Public Overridable Sub Build()
  overload: DrawnUi.Views.BasePageReloadable.Build*
- uid: DrawnUi.Views.BasePageReloadable.Dispose
  commentId: M:DrawnUi.Views.BasePageReloadable.Dispose
  id: Dispose
  parent: DrawnUi.Views.BasePageReloadable
  langs:
  - csharp
  - vb
  name: Dispose()
  nameWithType: BasePageReloadable.Dispose()
  fullName: DrawnUi.Views.BasePageReloadable.Dispose()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/BasePageReloadable.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Dispose
    path: ../src/Maui/DrawnUi/Views/BasePageReloadable.cs
    startLine: 51
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  summary: Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
  example: []
  syntax:
    content: public void Dispose()
    content.vb: Public Sub Dispose()
  overload: DrawnUi.Views.BasePageReloadable.Dispose*
  implements:
  - System.IDisposable.Dispose
- uid: DrawnUi.Views.BasePageReloadable.IsDisposed
  commentId: P:DrawnUi.Views.BasePageReloadable.IsDisposed
  id: IsDisposed
  parent: DrawnUi.Views.BasePageReloadable
  langs:
  - csharp
  - vb
  name: IsDisposed
  nameWithType: BasePageReloadable.IsDisposed
  fullName: DrawnUi.Views.BasePageReloadable.IsDisposed
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Views/BasePageReloadable.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IsDisposed
    path: ../src/Maui/DrawnUi/Views/BasePageReloadable.cs
    startLine: 57
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: public bool IsDisposed { get; protected set; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property IsDisposed As Boolean
  overload: DrawnUi.Views.BasePageReloadable.IsDisposed*
- uid: DrawnUi.Views.BasePageReloadable.Dispose(System.Boolean)
  commentId: M:DrawnUi.Views.BasePageReloadable.Dispose(System.Boolean)
  id: Dispose(System.Boolean)
  parent: DrawnUi.Views.BasePageReloadable
  langs:
  - csharp
  - vb
  name: Dispose(bool)
  nameWithType: BasePageReloadable.Dispose(bool)
  fullName: DrawnUi.Views.BasePageReloadable.Dispose(bool)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/BasePageReloadable.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Dispose
    path: ../src/Maui/DrawnUi/Views/BasePageReloadable.cs
    startLine: 59
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: protected virtual void Dispose(bool isDisposing)
    parameters:
    - id: isDisposing
      type: System.Boolean
    content.vb: Protected Overridable Sub Dispose(isDisposing As Boolean)
  overload: DrawnUi.Views.BasePageReloadable.Dispose*
  nameWithType.vb: BasePageReloadable.Dispose(Boolean)
  fullName.vb: DrawnUi.Views.BasePageReloadable.Dispose(Boolean)
  name.vb: Dispose(Boolean)
references:
- uid: DrawnUi.Views
  commentId: N:DrawnUi.Views
  href: DrawnUi.html
  name: DrawnUi.Views
  nameWithType: DrawnUi.Views
  fullName: DrawnUi.Views
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Views
    name: Views
    href: DrawnUi.Views.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Views
    name: Views
    href: DrawnUi.Views.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: Microsoft.Maui.Controls.BindableObject
  commentId: T:Microsoft.Maui.Controls.BindableObject
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject
  name: BindableObject
  nameWithType: BindableObject
  fullName: Microsoft.Maui.Controls.BindableObject
- uid: Microsoft.Maui.Controls.Element
  commentId: T:Microsoft.Maui.Controls.Element
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  name: Element
  nameWithType: Element
  fullName: Microsoft.Maui.Controls.Element
- uid: Microsoft.Maui.Controls.StyleableElement
  commentId: T:Microsoft.Maui.Controls.StyleableElement
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.styleableelement
  name: StyleableElement
  nameWithType: StyleableElement
  fullName: Microsoft.Maui.Controls.StyleableElement
- uid: Microsoft.Maui.Controls.NavigableElement
  commentId: T:Microsoft.Maui.Controls.NavigableElement
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.navigableelement
  name: NavigableElement
  nameWithType: NavigableElement
  fullName: Microsoft.Maui.Controls.NavigableElement
- uid: Microsoft.Maui.Controls.VisualElement
  commentId: T:Microsoft.Maui.Controls.VisualElement
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement
  name: VisualElement
  nameWithType: VisualElement
  fullName: Microsoft.Maui.Controls.VisualElement
- uid: Microsoft.Maui.Controls.Page
  commentId: T:Microsoft.Maui.Controls.Page
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page
  name: Page
  nameWithType: Page
  fullName: Microsoft.Maui.Controls.Page
- uid: Microsoft.Maui.Controls.TemplatedPage
  commentId: T:Microsoft.Maui.Controls.TemplatedPage
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedpage
  name: TemplatedPage
  nameWithType: TemplatedPage
  fullName: Microsoft.Maui.Controls.TemplatedPage
- uid: Microsoft.Maui.Controls.ContentPage
  commentId: T:Microsoft.Maui.Controls.ContentPage
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.contentpage
  name: ContentPage
  nameWithType: ContentPage
  fullName: Microsoft.Maui.Controls.ContentPage
- uid: DrawnUi.Views.DrawnUiBasePage
  commentId: T:DrawnUi.Views.DrawnUiBasePage
  parent: DrawnUi.Views
  href: DrawnUi.Views.DrawnUiBasePage.html
  name: DrawnUiBasePage
  nameWithType: DrawnUiBasePage
  fullName: DrawnUi.Views.DrawnUiBasePage
- uid: System.ComponentModel.INotifyPropertyChanged
  commentId: T:System.ComponentModel.INotifyPropertyChanged
  parent: System.ComponentModel
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.inotifypropertychanged
  name: INotifyPropertyChanged
  nameWithType: INotifyPropertyChanged
  fullName: System.ComponentModel.INotifyPropertyChanged
- uid: Microsoft.Maui.IVisualTreeElement
  commentId: T:Microsoft.Maui.IVisualTreeElement
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.ivisualtreeelement
  name: IVisualTreeElement
  nameWithType: IVisualTreeElement
  fullName: Microsoft.Maui.IVisualTreeElement
- uid: Microsoft.Maui.Controls.IEffectControlProvider
  commentId: T:Microsoft.Maui.Controls.IEffectControlProvider
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ieffectcontrolprovider
  name: IEffectControlProvider
  nameWithType: IEffectControlProvider
  fullName: Microsoft.Maui.Controls.IEffectControlProvider
- uid: Microsoft.Maui.IToolTipElement
  commentId: T:Microsoft.Maui.IToolTipElement
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.itooltipelement
  name: IToolTipElement
  nameWithType: IToolTipElement
  fullName: Microsoft.Maui.IToolTipElement
- uid: Microsoft.Maui.IContextFlyoutElement
  commentId: T:Microsoft.Maui.IContextFlyoutElement
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.icontextflyoutelement
  name: IContextFlyoutElement
  nameWithType: IContextFlyoutElement
  fullName: Microsoft.Maui.IContextFlyoutElement
- uid: Microsoft.Maui.Controls.IAnimatable
  commentId: T:Microsoft.Maui.Controls.IAnimatable
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ianimatable
  name: IAnimatable
  nameWithType: IAnimatable
  fullName: Microsoft.Maui.Controls.IAnimatable
- uid: Microsoft.Maui.Controls.ILayout
  commentId: T:Microsoft.Maui.Controls.ILayout
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ilayout
  name: ILayout
  nameWithType: ILayout
  fullName: Microsoft.Maui.Controls.ILayout
- uid: Microsoft.Maui.Controls.IPageController
  commentId: T:Microsoft.Maui.Controls.IPageController
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ipagecontroller
  name: IPageController
  nameWithType: IPageController
  fullName: Microsoft.Maui.Controls.IPageController
- uid: Microsoft.Maui.Controls.IVisualElementController
  commentId: T:Microsoft.Maui.Controls.IVisualElementController
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ivisualelementcontroller
  name: IVisualElementController
  nameWithType: IVisualElementController
  fullName: Microsoft.Maui.Controls.IVisualElementController
- uid: Microsoft.Maui.Controls.IElementController
  commentId: T:Microsoft.Maui.Controls.IElementController
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ielementcontroller
  name: IElementController
  nameWithType: IElementController
  fullName: Microsoft.Maui.Controls.IElementController
- uid: Microsoft.Maui.Controls.IElementConfiguration{Microsoft.Maui.Controls.Page}
  commentId: T:Microsoft.Maui.Controls.IElementConfiguration{Microsoft.Maui.Controls.Page}
  parent: Microsoft.Maui.Controls
  definition: Microsoft.Maui.Controls.IElementConfiguration`1
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ielementconfiguration-1
  name: IElementConfiguration<Page>
  nameWithType: IElementConfiguration<Page>
  fullName: Microsoft.Maui.Controls.IElementConfiguration<Microsoft.Maui.Controls.Page>
  nameWithType.vb: IElementConfiguration(Of Page)
  fullName.vb: Microsoft.Maui.Controls.IElementConfiguration(Of Microsoft.Maui.Controls.Page)
  name.vb: IElementConfiguration(Of Page)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.IElementConfiguration`1
    name: IElementConfiguration
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ielementconfiguration-1
  - name: <
  - uid: Microsoft.Maui.Controls.Page
    name: Page
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page
  - name: '>'
  spec.vb:
  - uid: Microsoft.Maui.Controls.IElementConfiguration`1
    name: IElementConfiguration
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ielementconfiguration-1
  - name: (
  - name: Of
  - name: " "
  - uid: Microsoft.Maui.Controls.Page
    name: Page
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page
  - name: )
- uid: Microsoft.Maui.ISafeAreaView
  commentId: T:Microsoft.Maui.ISafeAreaView
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.isafeareaview
  name: ISafeAreaView
  nameWithType: ISafeAreaView
  fullName: Microsoft.Maui.ISafeAreaView
- uid: Microsoft.Maui.ITitledElement
  commentId: T:Microsoft.Maui.ITitledElement
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.ititledelement
  name: ITitledElement
  nameWithType: ITitledElement
  fullName: Microsoft.Maui.ITitledElement
- uid: Microsoft.Maui.IToolbarElement
  commentId: T:Microsoft.Maui.IToolbarElement
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.itoolbarelement
  name: IToolbarElement
  nameWithType: IToolbarElement
  fullName: Microsoft.Maui.IToolbarElement
- uid: Microsoft.Maui.IContentView
  commentId: T:Microsoft.Maui.IContentView
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.icontentview
  name: IContentView
  nameWithType: IContentView
  fullName: Microsoft.Maui.IContentView
- uid: Microsoft.Maui.IPadding
  commentId: T:Microsoft.Maui.IPadding
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.ipadding
  name: IPadding
  nameWithType: IPadding
  fullName: Microsoft.Maui.IPadding
- uid: Microsoft.Maui.ICrossPlatformLayout
  commentId: T:Microsoft.Maui.ICrossPlatformLayout
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.icrossplatformlayout
  name: ICrossPlatformLayout
  nameWithType: ICrossPlatformLayout
  fullName: Microsoft.Maui.ICrossPlatformLayout
- uid: Microsoft.Maui.HotReload.IHotReloadableView
  commentId: T:Microsoft.Maui.HotReload.IHotReloadableView
  parent: Microsoft.Maui.HotReload
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.hotreload.ihotreloadableview
  name: IHotReloadableView
  nameWithType: IHotReloadableView
  fullName: Microsoft.Maui.HotReload.IHotReloadableView
- uid: Microsoft.Maui.IView
  commentId: T:Microsoft.Maui.IView
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iview
  name: IView
  nameWithType: IView
  fullName: Microsoft.Maui.IView
- uid: Microsoft.Maui.IElement
  commentId: T:Microsoft.Maui.IElement
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.ielement
  name: IElement
  nameWithType: IElement
  fullName: Microsoft.Maui.IElement
- uid: Microsoft.Maui.ITransform
  commentId: T:Microsoft.Maui.ITransform
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.itransform
  name: ITransform
  nameWithType: ITransform
  fullName: Microsoft.Maui.ITransform
- uid: Microsoft.Maui.IReplaceableView
  commentId: T:Microsoft.Maui.IReplaceableView
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.ireplaceableview
  name: IReplaceableView
  nameWithType: IReplaceableView
  fullName: Microsoft.Maui.IReplaceableView
- uid: System.IDisposable
  commentId: T:System.IDisposable
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.idisposable
  name: IDisposable
  nameWithType: IDisposable
  fullName: System.IDisposable
- uid: DrawnUi.Views.DrawnUiBasePage.KeyboardResized(System.Double)
  commentId: M:DrawnUi.Views.DrawnUiBasePage.KeyboardResized(System.Double)
  parent: DrawnUi.Views.DrawnUiBasePage
  isExternal: true
  href: DrawnUi.Views.DrawnUiBasePage.html#DrawnUi_Views_DrawnUiBasePage_KeyboardResized_System_Double_
  name: KeyboardResized(double)
  nameWithType: DrawnUiBasePage.KeyboardResized(double)
  fullName: DrawnUi.Views.DrawnUiBasePage.KeyboardResized(double)
  nameWithType.vb: DrawnUiBasePage.KeyboardResized(Double)
  fullName.vb: DrawnUi.Views.DrawnUiBasePage.KeyboardResized(Double)
  name.vb: KeyboardResized(Double)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnUiBasePage.KeyboardResized(System.Double)
    name: KeyboardResized
    href: DrawnUi.Views.DrawnUiBasePage.html#DrawnUi_Views_DrawnUiBasePage_KeyboardResized_System_Double_
  - name: (
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnUiBasePage.KeyboardResized(System.Double)
    name: KeyboardResized
    href: DrawnUi.Views.DrawnUiBasePage.html#DrawnUi_Views_DrawnUiBasePage_KeyboardResized_System_Double_
  - name: (
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
- uid: DrawnUi.Views.DrawnUiBasePage.OnKeyboardResized(System.Double)
  commentId: M:DrawnUi.Views.DrawnUiBasePage.OnKeyboardResized(System.Double)
  parent: DrawnUi.Views.DrawnUiBasePage
  isExternal: true
  href: DrawnUi.Views.DrawnUiBasePage.html#DrawnUi_Views_DrawnUiBasePage_OnKeyboardResized_System_Double_
  name: OnKeyboardResized(double)
  nameWithType: DrawnUiBasePage.OnKeyboardResized(double)
  fullName: DrawnUi.Views.DrawnUiBasePage.OnKeyboardResized(double)
  nameWithType.vb: DrawnUiBasePage.OnKeyboardResized(Double)
  fullName.vb: DrawnUi.Views.DrawnUiBasePage.OnKeyboardResized(Double)
  name.vb: OnKeyboardResized(Double)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnUiBasePage.OnKeyboardResized(System.Double)
    name: OnKeyboardResized
    href: DrawnUi.Views.DrawnUiBasePage.html#DrawnUi_Views_DrawnUiBasePage_OnKeyboardResized_System_Double_
  - name: (
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnUiBasePage.OnKeyboardResized(System.Double)
    name: OnKeyboardResized
    href: DrawnUi.Views.DrawnUiBasePage.html#DrawnUi_Views_DrawnUiBasePage_OnKeyboardResized_System_Double_
  - name: (
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
- uid: Microsoft.Maui.Controls.ContentPage.ContentProperty
  commentId: F:Microsoft.Maui.Controls.ContentPage.ContentProperty
  parent: Microsoft.Maui.Controls.ContentPage
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.contentpage.contentproperty
  name: ContentProperty
  nameWithType: ContentPage.ContentProperty
  fullName: Microsoft.Maui.Controls.ContentPage.ContentProperty
- uid: Microsoft.Maui.Controls.ContentPage.HideSoftInputOnTappedProperty
  commentId: F:Microsoft.Maui.Controls.ContentPage.HideSoftInputOnTappedProperty
  parent: Microsoft.Maui.Controls.ContentPage
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.contentpage.hidesoftinputontappedproperty
  name: HideSoftInputOnTappedProperty
  nameWithType: ContentPage.HideSoftInputOnTappedProperty
  fullName: Microsoft.Maui.Controls.ContentPage.HideSoftInputOnTappedProperty
- uid: Microsoft.Maui.Controls.ContentPage.OnBindingContextChanged
  commentId: M:Microsoft.Maui.Controls.ContentPage.OnBindingContextChanged
  parent: Microsoft.Maui.Controls.ContentPage
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.contentpage.onbindingcontextchanged
  name: OnBindingContextChanged()
  nameWithType: ContentPage.OnBindingContextChanged()
  fullName: Microsoft.Maui.Controls.ContentPage.OnBindingContextChanged()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.ContentPage.OnBindingContextChanged
    name: OnBindingContextChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.contentpage.onbindingcontextchanged
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.ContentPage.OnBindingContextChanged
    name: OnBindingContextChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.contentpage.onbindingcontextchanged
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.ContentPage.MeasureOverride(System.Double,System.Double)
  commentId: M:Microsoft.Maui.Controls.ContentPage.MeasureOverride(System.Double,System.Double)
  parent: Microsoft.Maui.Controls.ContentPage
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.contentpage.measureoverride
  name: MeasureOverride(double, double)
  nameWithType: ContentPage.MeasureOverride(double, double)
  fullName: Microsoft.Maui.Controls.ContentPage.MeasureOverride(double, double)
  nameWithType.vb: ContentPage.MeasureOverride(Double, Double)
  fullName.vb: Microsoft.Maui.Controls.ContentPage.MeasureOverride(Double, Double)
  name.vb: MeasureOverride(Double, Double)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.ContentPage.MeasureOverride(System.Double,System.Double)
    name: MeasureOverride
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.contentpage.measureoverride
  - name: (
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.ContentPage.MeasureOverride(System.Double,System.Double)
    name: MeasureOverride
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.contentpage.measureoverride
  - name: (
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
- uid: Microsoft.Maui.Controls.ContentPage.LayoutChildren(System.Double,System.Double,System.Double,System.Double)
  commentId: M:Microsoft.Maui.Controls.ContentPage.LayoutChildren(System.Double,System.Double,System.Double,System.Double)
  parent: Microsoft.Maui.Controls.ContentPage
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.contentpage.layoutchildren
  name: LayoutChildren(double, double, double, double)
  nameWithType: ContentPage.LayoutChildren(double, double, double, double)
  fullName: Microsoft.Maui.Controls.ContentPage.LayoutChildren(double, double, double, double)
  nameWithType.vb: ContentPage.LayoutChildren(Double, Double, Double, Double)
  fullName.vb: Microsoft.Maui.Controls.ContentPage.LayoutChildren(Double, Double, Double, Double)
  name.vb: LayoutChildren(Double, Double, Double, Double)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.ContentPage.LayoutChildren(System.Double,System.Double,System.Double,System.Double)
    name: LayoutChildren
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.contentpage.layoutchildren
  - name: (
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.ContentPage.LayoutChildren(System.Double,System.Double,System.Double,System.Double)
    name: LayoutChildren
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.contentpage.layoutchildren
  - name: (
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
- uid: Microsoft.Maui.Controls.ContentPage.ArrangeOverride(Microsoft.Maui.Graphics.Rect)
  commentId: M:Microsoft.Maui.Controls.ContentPage.ArrangeOverride(Microsoft.Maui.Graphics.Rect)
  parent: Microsoft.Maui.Controls.ContentPage
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.contentpage.arrangeoverride
  name: ArrangeOverride(Rect)
  nameWithType: ContentPage.ArrangeOverride(Rect)
  fullName: Microsoft.Maui.Controls.ContentPage.ArrangeOverride(Microsoft.Maui.Graphics.Rect)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.ContentPage.ArrangeOverride(Microsoft.Maui.Graphics.Rect)
    name: ArrangeOverride
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.contentpage.arrangeoverride
  - name: (
  - uid: Microsoft.Maui.Graphics.Rect
    name: Rect
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.rect
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.ContentPage.ArrangeOverride(Microsoft.Maui.Graphics.Rect)
    name: ArrangeOverride
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.contentpage.arrangeoverride
  - name: (
  - uid: Microsoft.Maui.Graphics.Rect
    name: Rect
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.rect
  - name: )
- uid: Microsoft.Maui.Controls.ContentPage.InvalidateMeasureOverride
  commentId: M:Microsoft.Maui.Controls.ContentPage.InvalidateMeasureOverride
  parent: Microsoft.Maui.Controls.ContentPage
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.contentpage.invalidatemeasureoverride
  name: InvalidateMeasureOverride()
  nameWithType: ContentPage.InvalidateMeasureOverride()
  fullName: Microsoft.Maui.Controls.ContentPage.InvalidateMeasureOverride()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.ContentPage.InvalidateMeasureOverride
    name: InvalidateMeasureOverride
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.contentpage.invalidatemeasureoverride
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.ContentPage.InvalidateMeasureOverride
    name: InvalidateMeasureOverride
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.contentpage.invalidatemeasureoverride
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.ContentPage.Content
  commentId: P:Microsoft.Maui.Controls.ContentPage.Content
  parent: Microsoft.Maui.Controls.ContentPage
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.contentpage.content
  name: Content
  nameWithType: ContentPage.Content
  fullName: Microsoft.Maui.Controls.ContentPage.Content
- uid: Microsoft.Maui.Controls.ContentPage.HideSoftInputOnTapped
  commentId: P:Microsoft.Maui.Controls.ContentPage.HideSoftInputOnTapped
  parent: Microsoft.Maui.Controls.ContentPage
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.contentpage.hidesoftinputontapped
  name: HideSoftInputOnTapped
  nameWithType: ContentPage.HideSoftInputOnTapped
  fullName: Microsoft.Maui.Controls.ContentPage.HideSoftInputOnTapped
- uid: Microsoft.Maui.Controls.TemplatedPage.ControlTemplateProperty
  commentId: F:Microsoft.Maui.Controls.TemplatedPage.ControlTemplateProperty
  parent: Microsoft.Maui.Controls.TemplatedPage
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedpage.controltemplateproperty
  name: ControlTemplateProperty
  nameWithType: TemplatedPage.ControlTemplateProperty
  fullName: Microsoft.Maui.Controls.TemplatedPage.ControlTemplateProperty
- uid: Microsoft.Maui.Controls.TemplatedPage.OnApplyTemplate
  commentId: M:Microsoft.Maui.Controls.TemplatedPage.OnApplyTemplate
  parent: Microsoft.Maui.Controls.TemplatedPage
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedpage.onapplytemplate
  name: OnApplyTemplate()
  nameWithType: TemplatedPage.OnApplyTemplate()
  fullName: Microsoft.Maui.Controls.TemplatedPage.OnApplyTemplate()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.TemplatedPage.OnApplyTemplate
    name: OnApplyTemplate
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedpage.onapplytemplate
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.TemplatedPage.OnApplyTemplate
    name: OnApplyTemplate
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedpage.onapplytemplate
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.TemplatedPage.OnChildRemoved(Microsoft.Maui.Controls.Element,System.Int32)
  commentId: M:Microsoft.Maui.Controls.TemplatedPage.OnChildRemoved(Microsoft.Maui.Controls.Element,System.Int32)
  parent: Microsoft.Maui.Controls.TemplatedPage
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedpage.onchildremoved
  name: OnChildRemoved(Element, int)
  nameWithType: TemplatedPage.OnChildRemoved(Element, int)
  fullName: Microsoft.Maui.Controls.TemplatedPage.OnChildRemoved(Microsoft.Maui.Controls.Element, int)
  nameWithType.vb: TemplatedPage.OnChildRemoved(Element, Integer)
  fullName.vb: Microsoft.Maui.Controls.TemplatedPage.OnChildRemoved(Microsoft.Maui.Controls.Element, Integer)
  name.vb: OnChildRemoved(Element, Integer)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.TemplatedPage.OnChildRemoved(Microsoft.Maui.Controls.Element,System.Int32)
    name: OnChildRemoved
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedpage.onchildremoved
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: ','
  - name: " "
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.TemplatedPage.OnChildRemoved(Microsoft.Maui.Controls.Element,System.Int32)
    name: OnChildRemoved
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedpage.onchildremoved
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: ','
  - name: " "
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: Microsoft.Maui.Controls.TemplatedPage.GetTemplateChild(System.String)
  commentId: M:Microsoft.Maui.Controls.TemplatedPage.GetTemplateChild(System.String)
  parent: Microsoft.Maui.Controls.TemplatedPage
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedpage.gettemplatechild
  name: GetTemplateChild(string)
  nameWithType: TemplatedPage.GetTemplateChild(string)
  fullName: Microsoft.Maui.Controls.TemplatedPage.GetTemplateChild(string)
  nameWithType.vb: TemplatedPage.GetTemplateChild(String)
  fullName.vb: Microsoft.Maui.Controls.TemplatedPage.GetTemplateChild(String)
  name.vb: GetTemplateChild(String)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.TemplatedPage.GetTemplateChild(System.String)
    name: GetTemplateChild
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedpage.gettemplatechild
  - name: (
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.TemplatedPage.GetTemplateChild(System.String)
    name: GetTemplateChild
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedpage.gettemplatechild
  - name: (
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: Microsoft.Maui.Controls.TemplatedPage.ControlTemplate
  commentId: P:Microsoft.Maui.Controls.TemplatedPage.ControlTemplate
  parent: Microsoft.Maui.Controls.TemplatedPage
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedpage.controltemplate
  name: ControlTemplate
  nameWithType: TemplatedPage.ControlTemplate
  fullName: Microsoft.Maui.Controls.TemplatedPage.ControlTemplate
- uid: Microsoft.Maui.Controls.Page.BusySetSignalName
  commentId: F:Microsoft.Maui.Controls.Page.BusySetSignalName
  parent: Microsoft.Maui.Controls.Page
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.busysetsignalname
  name: BusySetSignalName
  nameWithType: Page.BusySetSignalName
  fullName: Microsoft.Maui.Controls.Page.BusySetSignalName
- uid: Microsoft.Maui.Controls.Page.AlertSignalName
  commentId: F:Microsoft.Maui.Controls.Page.AlertSignalName
  parent: Microsoft.Maui.Controls.Page
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.alertsignalname
  name: AlertSignalName
  nameWithType: Page.AlertSignalName
  fullName: Microsoft.Maui.Controls.Page.AlertSignalName
- uid: Microsoft.Maui.Controls.Page.PromptSignalName
  commentId: F:Microsoft.Maui.Controls.Page.PromptSignalName
  parent: Microsoft.Maui.Controls.Page
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.promptsignalname
  name: PromptSignalName
  nameWithType: Page.PromptSignalName
  fullName: Microsoft.Maui.Controls.Page.PromptSignalName
- uid: Microsoft.Maui.Controls.Page.ActionSheetSignalName
  commentId: F:Microsoft.Maui.Controls.Page.ActionSheetSignalName
  parent: Microsoft.Maui.Controls.Page
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.actionsheetsignalname
  name: ActionSheetSignalName
  nameWithType: Page.ActionSheetSignalName
  fullName: Microsoft.Maui.Controls.Page.ActionSheetSignalName
- uid: Microsoft.Maui.Controls.Page.BackgroundImageSourceProperty
  commentId: F:Microsoft.Maui.Controls.Page.BackgroundImageSourceProperty
  parent: Microsoft.Maui.Controls.Page
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.backgroundimagesourceproperty
  name: BackgroundImageSourceProperty
  nameWithType: Page.BackgroundImageSourceProperty
  fullName: Microsoft.Maui.Controls.Page.BackgroundImageSourceProperty
- uid: Microsoft.Maui.Controls.Page.IsBusyProperty
  commentId: F:Microsoft.Maui.Controls.Page.IsBusyProperty
  parent: Microsoft.Maui.Controls.Page
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.isbusyproperty
  name: IsBusyProperty
  nameWithType: Page.IsBusyProperty
  fullName: Microsoft.Maui.Controls.Page.IsBusyProperty
- uid: Microsoft.Maui.Controls.Page.PaddingProperty
  commentId: F:Microsoft.Maui.Controls.Page.PaddingProperty
  parent: Microsoft.Maui.Controls.Page
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.paddingproperty
  name: PaddingProperty
  nameWithType: Page.PaddingProperty
  fullName: Microsoft.Maui.Controls.Page.PaddingProperty
- uid: Microsoft.Maui.Controls.Page.TitleProperty
  commentId: F:Microsoft.Maui.Controls.Page.TitleProperty
  parent: Microsoft.Maui.Controls.Page
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.titleproperty
  name: TitleProperty
  nameWithType: Page.TitleProperty
  fullName: Microsoft.Maui.Controls.Page.TitleProperty
- uid: Microsoft.Maui.Controls.Page.IconImageSourceProperty
  commentId: F:Microsoft.Maui.Controls.Page.IconImageSourceProperty
  parent: Microsoft.Maui.Controls.Page
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.iconimagesourceproperty
  name: IconImageSourceProperty
  nameWithType: Page.IconImageSourceProperty
  fullName: Microsoft.Maui.Controls.Page.IconImageSourceProperty
- uid: Microsoft.Maui.Controls.Page.DisplayActionSheet(System.String,System.String,System.String,System.String[])
  commentId: M:Microsoft.Maui.Controls.Page.DisplayActionSheet(System.String,System.String,System.String,System.String[])
  parent: Microsoft.Maui.Controls.Page
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.displayactionsheet#microsoft-maui-controls-page-displayactionsheet(system-string-system-string-system-string-system-string())
  name: DisplayActionSheet(string, string, string, params string[])
  nameWithType: Page.DisplayActionSheet(string, string, string, params string[])
  fullName: Microsoft.Maui.Controls.Page.DisplayActionSheet(string, string, string, params string[])
  nameWithType.vb: Page.DisplayActionSheet(String, String, String, ParamArray String())
  fullName.vb: Microsoft.Maui.Controls.Page.DisplayActionSheet(String, String, String, ParamArray String())
  name.vb: DisplayActionSheet(String, String, String, ParamArray String())
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Page.DisplayActionSheet(System.String,System.String,System.String,System.String[])
    name: DisplayActionSheet
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.displayactionsheet#microsoft-maui-controls-page-displayactionsheet(system-string-system-string-system-string-system-string())
  - name: (
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - name: params
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: '['
  - name: ']'
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Page.DisplayActionSheet(System.String,System.String,System.String,System.String[])
    name: DisplayActionSheet
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.displayactionsheet#microsoft-maui-controls-page-displayactionsheet(system-string-system-string-system-string-system-string())
  - name: (
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - name: ParamArray
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: (
  - name: )
  - name: )
- uid: Microsoft.Maui.Controls.Page.DisplayActionSheet(System.String,System.String,System.String,Microsoft.Maui.FlowDirection,System.String[])
  commentId: M:Microsoft.Maui.Controls.Page.DisplayActionSheet(System.String,System.String,System.String,Microsoft.Maui.FlowDirection,System.String[])
  parent: Microsoft.Maui.Controls.Page
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.displayactionsheet#microsoft-maui-controls-page-displayactionsheet(system-string-system-string-system-string-microsoft-maui-flowdirection-system-string())
  name: DisplayActionSheet(string, string, string, FlowDirection, params string[])
  nameWithType: Page.DisplayActionSheet(string, string, string, FlowDirection, params string[])
  fullName: Microsoft.Maui.Controls.Page.DisplayActionSheet(string, string, string, Microsoft.Maui.FlowDirection, params string[])
  nameWithType.vb: Page.DisplayActionSheet(String, String, String, FlowDirection, ParamArray String())
  fullName.vb: Microsoft.Maui.Controls.Page.DisplayActionSheet(String, String, String, Microsoft.Maui.FlowDirection, ParamArray String())
  name.vb: DisplayActionSheet(String, String, String, FlowDirection, ParamArray String())
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Page.DisplayActionSheet(System.String,System.String,System.String,Microsoft.Maui.FlowDirection,System.String[])
    name: DisplayActionSheet
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.displayactionsheet#microsoft-maui-controls-page-displayactionsheet(system-string-system-string-system-string-microsoft-maui-flowdirection-system-string())
  - name: (
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.FlowDirection
    name: FlowDirection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.flowdirection
  - name: ','
  - name: " "
  - name: params
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: '['
  - name: ']'
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Page.DisplayActionSheet(System.String,System.String,System.String,Microsoft.Maui.FlowDirection,System.String[])
    name: DisplayActionSheet
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.displayactionsheet#microsoft-maui-controls-page-displayactionsheet(system-string-system-string-system-string-microsoft-maui-flowdirection-system-string())
  - name: (
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.FlowDirection
    name: FlowDirection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.flowdirection
  - name: ','
  - name: " "
  - name: ParamArray
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: (
  - name: )
  - name: )
- uid: Microsoft.Maui.Controls.Page.DisplayAlert(System.String,System.String,System.String)
  commentId: M:Microsoft.Maui.Controls.Page.DisplayAlert(System.String,System.String,System.String)
  parent: Microsoft.Maui.Controls.Page
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.displayalert#microsoft-maui-controls-page-displayalert(system-string-system-string-system-string)
  name: DisplayAlert(string, string, string)
  nameWithType: Page.DisplayAlert(string, string, string)
  fullName: Microsoft.Maui.Controls.Page.DisplayAlert(string, string, string)
  nameWithType.vb: Page.DisplayAlert(String, String, String)
  fullName.vb: Microsoft.Maui.Controls.Page.DisplayAlert(String, String, String)
  name.vb: DisplayAlert(String, String, String)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Page.DisplayAlert(System.String,System.String,System.String)
    name: DisplayAlert
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.displayalert#microsoft-maui-controls-page-displayalert(system-string-system-string-system-string)
  - name: (
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Page.DisplayAlert(System.String,System.String,System.String)
    name: DisplayAlert
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.displayalert#microsoft-maui-controls-page-displayalert(system-string-system-string-system-string)
  - name: (
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: Microsoft.Maui.Controls.Page.DisplayAlert(System.String,System.String,System.String,System.String)
  commentId: M:Microsoft.Maui.Controls.Page.DisplayAlert(System.String,System.String,System.String,System.String)
  parent: Microsoft.Maui.Controls.Page
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.displayalert#microsoft-maui-controls-page-displayalert(system-string-system-string-system-string-system-string)
  name: DisplayAlert(string, string, string, string)
  nameWithType: Page.DisplayAlert(string, string, string, string)
  fullName: Microsoft.Maui.Controls.Page.DisplayAlert(string, string, string, string)
  nameWithType.vb: Page.DisplayAlert(String, String, String, String)
  fullName.vb: Microsoft.Maui.Controls.Page.DisplayAlert(String, String, String, String)
  name.vb: DisplayAlert(String, String, String, String)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Page.DisplayAlert(System.String,System.String,System.String,System.String)
    name: DisplayAlert
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.displayalert#microsoft-maui-controls-page-displayalert(system-string-system-string-system-string-system-string)
  - name: (
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Page.DisplayAlert(System.String,System.String,System.String,System.String)
    name: DisplayAlert
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.displayalert#microsoft-maui-controls-page-displayalert(system-string-system-string-system-string-system-string)
  - name: (
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: Microsoft.Maui.Controls.Page.DisplayAlert(System.String,System.String,System.String,Microsoft.Maui.FlowDirection)
  commentId: M:Microsoft.Maui.Controls.Page.DisplayAlert(System.String,System.String,System.String,Microsoft.Maui.FlowDirection)
  parent: Microsoft.Maui.Controls.Page
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.displayalert#microsoft-maui-controls-page-displayalert(system-string-system-string-system-string-microsoft-maui-flowdirection)
  name: DisplayAlert(string, string, string, FlowDirection)
  nameWithType: Page.DisplayAlert(string, string, string, FlowDirection)
  fullName: Microsoft.Maui.Controls.Page.DisplayAlert(string, string, string, Microsoft.Maui.FlowDirection)
  nameWithType.vb: Page.DisplayAlert(String, String, String, FlowDirection)
  fullName.vb: Microsoft.Maui.Controls.Page.DisplayAlert(String, String, String, Microsoft.Maui.FlowDirection)
  name.vb: DisplayAlert(String, String, String, FlowDirection)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Page.DisplayAlert(System.String,System.String,System.String,Microsoft.Maui.FlowDirection)
    name: DisplayAlert
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.displayalert#microsoft-maui-controls-page-displayalert(system-string-system-string-system-string-microsoft-maui-flowdirection)
  - name: (
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.FlowDirection
    name: FlowDirection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.flowdirection
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Page.DisplayAlert(System.String,System.String,System.String,Microsoft.Maui.FlowDirection)
    name: DisplayAlert
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.displayalert#microsoft-maui-controls-page-displayalert(system-string-system-string-system-string-microsoft-maui-flowdirection)
  - name: (
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.FlowDirection
    name: FlowDirection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.flowdirection
  - name: )
- uid: Microsoft.Maui.Controls.Page.DisplayAlert(System.String,System.String,System.String,System.String,Microsoft.Maui.FlowDirection)
  commentId: M:Microsoft.Maui.Controls.Page.DisplayAlert(System.String,System.String,System.String,System.String,Microsoft.Maui.FlowDirection)
  parent: Microsoft.Maui.Controls.Page
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.displayalert#microsoft-maui-controls-page-displayalert(system-string-system-string-system-string-system-string-microsoft-maui-flowdirection)
  name: DisplayAlert(string, string, string, string, FlowDirection)
  nameWithType: Page.DisplayAlert(string, string, string, string, FlowDirection)
  fullName: Microsoft.Maui.Controls.Page.DisplayAlert(string, string, string, string, Microsoft.Maui.FlowDirection)
  nameWithType.vb: Page.DisplayAlert(String, String, String, String, FlowDirection)
  fullName.vb: Microsoft.Maui.Controls.Page.DisplayAlert(String, String, String, String, Microsoft.Maui.FlowDirection)
  name.vb: DisplayAlert(String, String, String, String, FlowDirection)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Page.DisplayAlert(System.String,System.String,System.String,System.String,Microsoft.Maui.FlowDirection)
    name: DisplayAlert
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.displayalert#microsoft-maui-controls-page-displayalert(system-string-system-string-system-string-system-string-microsoft-maui-flowdirection)
  - name: (
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.FlowDirection
    name: FlowDirection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.flowdirection
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Page.DisplayAlert(System.String,System.String,System.String,System.String,Microsoft.Maui.FlowDirection)
    name: DisplayAlert
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.displayalert#microsoft-maui-controls-page-displayalert(system-string-system-string-system-string-system-string-microsoft-maui-flowdirection)
  - name: (
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.FlowDirection
    name: FlowDirection
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.flowdirection
  - name: )
- uid: Microsoft.Maui.Controls.Page.DisplayPromptAsync(System.String,System.String,System.String,System.String,System.String,System.Int32,Microsoft.Maui.Keyboard,System.String)
  commentId: M:Microsoft.Maui.Controls.Page.DisplayPromptAsync(System.String,System.String,System.String,System.String,System.String,System.Int32,Microsoft.Maui.Keyboard,System.String)
  parent: Microsoft.Maui.Controls.Page
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.displaypromptasync
  name: DisplayPromptAsync(string, string, string, string, string, int, Keyboard, string)
  nameWithType: Page.DisplayPromptAsync(string, string, string, string, string, int, Keyboard, string)
  fullName: Microsoft.Maui.Controls.Page.DisplayPromptAsync(string, string, string, string, string, int, Microsoft.Maui.Keyboard, string)
  nameWithType.vb: Page.DisplayPromptAsync(String, String, String, String, String, Integer, Keyboard, String)
  fullName.vb: Microsoft.Maui.Controls.Page.DisplayPromptAsync(String, String, String, String, String, Integer, Microsoft.Maui.Keyboard, String)
  name.vb: DisplayPromptAsync(String, String, String, String, String, Integer, Keyboard, String)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Page.DisplayPromptAsync(System.String,System.String,System.String,System.String,System.String,System.Int32,Microsoft.Maui.Keyboard,System.String)
    name: DisplayPromptAsync
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.displaypromptasync
  - name: (
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Keyboard
    name: Keyboard
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.keyboard
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Page.DisplayPromptAsync(System.String,System.String,System.String,System.String,System.String,System.Int32,Microsoft.Maui.Keyboard,System.String)
    name: DisplayPromptAsync
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.displaypromptasync
  - name: (
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Keyboard
    name: Keyboard
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.keyboard
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: Microsoft.Maui.Controls.Page.ForceLayout
  commentId: M:Microsoft.Maui.Controls.Page.ForceLayout
  parent: Microsoft.Maui.Controls.Page
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.forcelayout
  name: ForceLayout()
  nameWithType: Page.ForceLayout()
  fullName: Microsoft.Maui.Controls.Page.ForceLayout()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Page.ForceLayout
    name: ForceLayout
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.forcelayout
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Page.ForceLayout
    name: ForceLayout
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.forcelayout
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.Page.SendBackButtonPressed
  commentId: M:Microsoft.Maui.Controls.Page.SendBackButtonPressed
  parent: Microsoft.Maui.Controls.Page
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.sendbackbuttonpressed
  name: SendBackButtonPressed()
  nameWithType: Page.SendBackButtonPressed()
  fullName: Microsoft.Maui.Controls.Page.SendBackButtonPressed()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Page.SendBackButtonPressed
    name: SendBackButtonPressed
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.sendbackbuttonpressed
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Page.SendBackButtonPressed
    name: SendBackButtonPressed
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.sendbackbuttonpressed
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.Page.OnAppearing
  commentId: M:Microsoft.Maui.Controls.Page.OnAppearing
  parent: Microsoft.Maui.Controls.Page
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.onappearing
  name: OnAppearing()
  nameWithType: Page.OnAppearing()
  fullName: Microsoft.Maui.Controls.Page.OnAppearing()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Page.OnAppearing
    name: OnAppearing
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.onappearing
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Page.OnAppearing
    name: OnAppearing
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.onappearing
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.Page.OnBackButtonPressed
  commentId: M:Microsoft.Maui.Controls.Page.OnBackButtonPressed
  parent: Microsoft.Maui.Controls.Page
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.onbackbuttonpressed
  name: OnBackButtonPressed()
  nameWithType: Page.OnBackButtonPressed()
  fullName: Microsoft.Maui.Controls.Page.OnBackButtonPressed()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Page.OnBackButtonPressed
    name: OnBackButtonPressed
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.onbackbuttonpressed
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Page.OnBackButtonPressed
    name: OnBackButtonPressed
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.onbackbuttonpressed
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.Page.OnChildMeasureInvalidated(System.Object,System.EventArgs)
  commentId: M:Microsoft.Maui.Controls.Page.OnChildMeasureInvalidated(System.Object,System.EventArgs)
  parent: Microsoft.Maui.Controls.Page
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.onchildmeasureinvalidated#microsoft-maui-controls-page-onchildmeasureinvalidated(system-object-system-eventargs)
  name: OnChildMeasureInvalidated(object, EventArgs)
  nameWithType: Page.OnChildMeasureInvalidated(object, EventArgs)
  fullName: Microsoft.Maui.Controls.Page.OnChildMeasureInvalidated(object, System.EventArgs)
  nameWithType.vb: Page.OnChildMeasureInvalidated(Object, EventArgs)
  fullName.vb: Microsoft.Maui.Controls.Page.OnChildMeasureInvalidated(Object, System.EventArgs)
  name.vb: OnChildMeasureInvalidated(Object, EventArgs)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Page.OnChildMeasureInvalidated(System.Object,System.EventArgs)
    name: OnChildMeasureInvalidated
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.onchildmeasureinvalidated#microsoft-maui-controls-page-onchildmeasureinvalidated(system-object-system-eventargs)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.EventArgs
    name: EventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.eventargs
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Page.OnChildMeasureInvalidated(System.Object,System.EventArgs)
    name: OnChildMeasureInvalidated
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.onchildmeasureinvalidated#microsoft-maui-controls-page-onchildmeasureinvalidated(system-object-system-eventargs)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.EventArgs
    name: EventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.eventargs
  - name: )
- uid: Microsoft.Maui.Controls.Page.OnDisappearing
  commentId: M:Microsoft.Maui.Controls.Page.OnDisappearing
  parent: Microsoft.Maui.Controls.Page
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.ondisappearing
  name: OnDisappearing()
  nameWithType: Page.OnDisappearing()
  fullName: Microsoft.Maui.Controls.Page.OnDisappearing()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Page.OnDisappearing
    name: OnDisappearing
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.ondisappearing
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Page.OnDisappearing
    name: OnDisappearing
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.ondisappearing
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.Page.OnParentSet
  commentId: M:Microsoft.Maui.Controls.Page.OnParentSet
  parent: Microsoft.Maui.Controls.Page
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.onparentset
  name: OnParentSet()
  nameWithType: Page.OnParentSet()
  fullName: Microsoft.Maui.Controls.Page.OnParentSet()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Page.OnParentSet
    name: OnParentSet
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.onparentset
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Page.OnParentSet
    name: OnParentSet
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.onparentset
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.Page.OnSizeAllocated(System.Double,System.Double)
  commentId: M:Microsoft.Maui.Controls.Page.OnSizeAllocated(System.Double,System.Double)
  parent: Microsoft.Maui.Controls.Page
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.onsizeallocated
  name: OnSizeAllocated(double, double)
  nameWithType: Page.OnSizeAllocated(double, double)
  fullName: Microsoft.Maui.Controls.Page.OnSizeAllocated(double, double)
  nameWithType.vb: Page.OnSizeAllocated(Double, Double)
  fullName.vb: Microsoft.Maui.Controls.Page.OnSizeAllocated(Double, Double)
  name.vb: OnSizeAllocated(Double, Double)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Page.OnSizeAllocated(System.Double,System.Double)
    name: OnSizeAllocated
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.onsizeallocated
  - name: (
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Page.OnSizeAllocated(System.Double,System.Double)
    name: OnSizeAllocated
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.onsizeallocated
  - name: (
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
- uid: Microsoft.Maui.Controls.Page.UpdateChildrenLayout
  commentId: M:Microsoft.Maui.Controls.Page.UpdateChildrenLayout
  parent: Microsoft.Maui.Controls.Page
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.updatechildrenlayout
  name: UpdateChildrenLayout()
  nameWithType: Page.UpdateChildrenLayout()
  fullName: Microsoft.Maui.Controls.Page.UpdateChildrenLayout()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Page.UpdateChildrenLayout
    name: UpdateChildrenLayout
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.updatechildrenlayout
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Page.UpdateChildrenLayout
    name: UpdateChildrenLayout
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.updatechildrenlayout
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.Page.On``1
  commentId: M:Microsoft.Maui.Controls.Page.On``1
  parent: Microsoft.Maui.Controls.Page
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.on
  name: On<T>()
  nameWithType: Page.On<T>()
  fullName: Microsoft.Maui.Controls.Page.On<T>()
  nameWithType.vb: Page.On(Of T)()
  fullName.vb: Microsoft.Maui.Controls.Page.On(Of T)()
  name.vb: On(Of T)()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Page.On``1
    name: On
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.on
  - name: <
  - name: T
  - name: '>'
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Page.On``1
    name: On
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.on
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.Page.OnNavigatedTo(Microsoft.Maui.Controls.NavigatedToEventArgs)
  commentId: M:Microsoft.Maui.Controls.Page.OnNavigatedTo(Microsoft.Maui.Controls.NavigatedToEventArgs)
  parent: Microsoft.Maui.Controls.Page
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.onnavigatedto
  name: OnNavigatedTo(NavigatedToEventArgs)
  nameWithType: Page.OnNavigatedTo(NavigatedToEventArgs)
  fullName: Microsoft.Maui.Controls.Page.OnNavigatedTo(Microsoft.Maui.Controls.NavigatedToEventArgs)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Page.OnNavigatedTo(Microsoft.Maui.Controls.NavigatedToEventArgs)
    name: OnNavigatedTo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.onnavigatedto
  - name: (
  - uid: Microsoft.Maui.Controls.NavigatedToEventArgs
    name: NavigatedToEventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.navigatedtoeventargs
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Page.OnNavigatedTo(Microsoft.Maui.Controls.NavigatedToEventArgs)
    name: OnNavigatedTo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.onnavigatedto
  - name: (
  - uid: Microsoft.Maui.Controls.NavigatedToEventArgs
    name: NavigatedToEventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.navigatedtoeventargs
  - name: )
- uid: Microsoft.Maui.Controls.Page.OnNavigatingFrom(Microsoft.Maui.Controls.NavigatingFromEventArgs)
  commentId: M:Microsoft.Maui.Controls.Page.OnNavigatingFrom(Microsoft.Maui.Controls.NavigatingFromEventArgs)
  parent: Microsoft.Maui.Controls.Page
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.onnavigatingfrom
  name: OnNavigatingFrom(NavigatingFromEventArgs)
  nameWithType: Page.OnNavigatingFrom(NavigatingFromEventArgs)
  fullName: Microsoft.Maui.Controls.Page.OnNavigatingFrom(Microsoft.Maui.Controls.NavigatingFromEventArgs)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Page.OnNavigatingFrom(Microsoft.Maui.Controls.NavigatingFromEventArgs)
    name: OnNavigatingFrom
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.onnavigatingfrom
  - name: (
  - uid: Microsoft.Maui.Controls.NavigatingFromEventArgs
    name: NavigatingFromEventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.navigatingfromeventargs
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Page.OnNavigatingFrom(Microsoft.Maui.Controls.NavigatingFromEventArgs)
    name: OnNavigatingFrom
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.onnavigatingfrom
  - name: (
  - uid: Microsoft.Maui.Controls.NavigatingFromEventArgs
    name: NavigatingFromEventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.navigatingfromeventargs
  - name: )
- uid: Microsoft.Maui.Controls.Page.OnNavigatedFrom(Microsoft.Maui.Controls.NavigatedFromEventArgs)
  commentId: M:Microsoft.Maui.Controls.Page.OnNavigatedFrom(Microsoft.Maui.Controls.NavigatedFromEventArgs)
  parent: Microsoft.Maui.Controls.Page
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.onnavigatedfrom
  name: OnNavigatedFrom(NavigatedFromEventArgs)
  nameWithType: Page.OnNavigatedFrom(NavigatedFromEventArgs)
  fullName: Microsoft.Maui.Controls.Page.OnNavigatedFrom(Microsoft.Maui.Controls.NavigatedFromEventArgs)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Page.OnNavigatedFrom(Microsoft.Maui.Controls.NavigatedFromEventArgs)
    name: OnNavigatedFrom
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.onnavigatedfrom
  - name: (
  - uid: Microsoft.Maui.Controls.NavigatedFromEventArgs
    name: NavigatedFromEventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.navigatedfromeventargs
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Page.OnNavigatedFrom(Microsoft.Maui.Controls.NavigatedFromEventArgs)
    name: OnNavigatedFrom
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.onnavigatedfrom
  - name: (
  - uid: Microsoft.Maui.Controls.NavigatedFromEventArgs
    name: NavigatedFromEventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.navigatedfromeventargs
  - name: )
- uid: Microsoft.Maui.Controls.Page.GetParentWindow
  commentId: M:Microsoft.Maui.Controls.Page.GetParentWindow
  parent: Microsoft.Maui.Controls.Page
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.getparentwindow
  name: GetParentWindow()
  nameWithType: Page.GetParentWindow()
  fullName: Microsoft.Maui.Controls.Page.GetParentWindow()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Page.GetParentWindow
    name: GetParentWindow
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.getparentwindow
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Page.GetParentWindow
    name: GetParentWindow
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.getparentwindow
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.Page.BackgroundImageSource
  commentId: P:Microsoft.Maui.Controls.Page.BackgroundImageSource
  parent: Microsoft.Maui.Controls.Page
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.backgroundimagesource
  name: BackgroundImageSource
  nameWithType: Page.BackgroundImageSource
  fullName: Microsoft.Maui.Controls.Page.BackgroundImageSource
- uid: Microsoft.Maui.Controls.Page.IconImageSource
  commentId: P:Microsoft.Maui.Controls.Page.IconImageSource
  parent: Microsoft.Maui.Controls.Page
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.iconimagesource
  name: IconImageSource
  nameWithType: Page.IconImageSource
  fullName: Microsoft.Maui.Controls.Page.IconImageSource
- uid: Microsoft.Maui.Controls.Page.IsBusy
  commentId: P:Microsoft.Maui.Controls.Page.IsBusy
  parent: Microsoft.Maui.Controls.Page
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.isbusy
  name: IsBusy
  nameWithType: Page.IsBusy
  fullName: Microsoft.Maui.Controls.Page.IsBusy
- uid: Microsoft.Maui.Controls.Page.Padding
  commentId: P:Microsoft.Maui.Controls.Page.Padding
  parent: Microsoft.Maui.Controls.Page
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.padding
  name: Padding
  nameWithType: Page.Padding
  fullName: Microsoft.Maui.Controls.Page.Padding
- uid: Microsoft.Maui.Controls.Page.Title
  commentId: P:Microsoft.Maui.Controls.Page.Title
  parent: Microsoft.Maui.Controls.Page
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.title
  name: Title
  nameWithType: Page.Title
  fullName: Microsoft.Maui.Controls.Page.Title
- uid: Microsoft.Maui.Controls.Page.ToolbarItems
  commentId: P:Microsoft.Maui.Controls.Page.ToolbarItems
  parent: Microsoft.Maui.Controls.Page
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.toolbaritems
  name: ToolbarItems
  nameWithType: Page.ToolbarItems
  fullName: Microsoft.Maui.Controls.Page.ToolbarItems
- uid: Microsoft.Maui.Controls.Page.MenuBarItems
  commentId: P:Microsoft.Maui.Controls.Page.MenuBarItems
  parent: Microsoft.Maui.Controls.Page
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.menubaritems
  name: MenuBarItems
  nameWithType: Page.MenuBarItems
  fullName: Microsoft.Maui.Controls.Page.MenuBarItems
- uid: Microsoft.Maui.Controls.Page.LayoutChanged
  commentId: E:Microsoft.Maui.Controls.Page.LayoutChanged
  parent: Microsoft.Maui.Controls.Page
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.layoutchanged
  name: LayoutChanged
  nameWithType: Page.LayoutChanged
  fullName: Microsoft.Maui.Controls.Page.LayoutChanged
- uid: Microsoft.Maui.Controls.Page.Appearing
  commentId: E:Microsoft.Maui.Controls.Page.Appearing
  parent: Microsoft.Maui.Controls.Page
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.appearing
  name: Appearing
  nameWithType: Page.Appearing
  fullName: Microsoft.Maui.Controls.Page.Appearing
- uid: Microsoft.Maui.Controls.Page.Disappearing
  commentId: E:Microsoft.Maui.Controls.Page.Disappearing
  parent: Microsoft.Maui.Controls.Page
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.disappearing
  name: Disappearing
  nameWithType: Page.Disappearing
  fullName: Microsoft.Maui.Controls.Page.Disappearing
- uid: Microsoft.Maui.Controls.Page.NavigatedTo
  commentId: E:Microsoft.Maui.Controls.Page.NavigatedTo
  parent: Microsoft.Maui.Controls.Page
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.navigatedto
  name: NavigatedTo
  nameWithType: Page.NavigatedTo
  fullName: Microsoft.Maui.Controls.Page.NavigatedTo
- uid: Microsoft.Maui.Controls.Page.NavigatingFrom
  commentId: E:Microsoft.Maui.Controls.Page.NavigatingFrom
  parent: Microsoft.Maui.Controls.Page
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.navigatingfrom
  name: NavigatingFrom
  nameWithType: Page.NavigatingFrom
  fullName: Microsoft.Maui.Controls.Page.NavigatingFrom
- uid: Microsoft.Maui.Controls.Page.NavigatedFrom
  commentId: E:Microsoft.Maui.Controls.Page.NavigatedFrom
  parent: Microsoft.Maui.Controls.Page
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.page.navigatedfrom
  name: NavigatedFrom
  nameWithType: Page.NavigatedFrom
  fullName: Microsoft.Maui.Controls.Page.NavigatedFrom
- uid: Microsoft.Maui.Controls.VisualElement.NavigationProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.NavigationProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.navigationproperty
  name: NavigationProperty
  nameWithType: VisualElement.NavigationProperty
  fullName: Microsoft.Maui.Controls.VisualElement.NavigationProperty
- uid: Microsoft.Maui.Controls.VisualElement.StyleProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.StyleProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.styleproperty
  name: StyleProperty
  nameWithType: VisualElement.StyleProperty
  fullName: Microsoft.Maui.Controls.VisualElement.StyleProperty
- uid: Microsoft.Maui.Controls.VisualElement.InputTransparentProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.InputTransparentProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.inputtransparentproperty
  name: InputTransparentProperty
  nameWithType: VisualElement.InputTransparentProperty
  fullName: Microsoft.Maui.Controls.VisualElement.InputTransparentProperty
- uid: Microsoft.Maui.Controls.VisualElement.IsEnabledProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.IsEnabledProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isenabledproperty
  name: IsEnabledProperty
  nameWithType: VisualElement.IsEnabledProperty
  fullName: Microsoft.Maui.Controls.VisualElement.IsEnabledProperty
- uid: Microsoft.Maui.Controls.VisualElement.XProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.XProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.xproperty
  name: XProperty
  nameWithType: VisualElement.XProperty
  fullName: Microsoft.Maui.Controls.VisualElement.XProperty
- uid: Microsoft.Maui.Controls.VisualElement.YProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.YProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.yproperty
  name: YProperty
  nameWithType: VisualElement.YProperty
  fullName: Microsoft.Maui.Controls.VisualElement.YProperty
- uid: Microsoft.Maui.Controls.VisualElement.AnchorXProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.AnchorXProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.anchorxproperty
  name: AnchorXProperty
  nameWithType: VisualElement.AnchorXProperty
  fullName: Microsoft.Maui.Controls.VisualElement.AnchorXProperty
- uid: Microsoft.Maui.Controls.VisualElement.AnchorYProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.AnchorYProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.anchoryproperty
  name: AnchorYProperty
  nameWithType: VisualElement.AnchorYProperty
  fullName: Microsoft.Maui.Controls.VisualElement.AnchorYProperty
- uid: Microsoft.Maui.Controls.VisualElement.TranslationXProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.TranslationXProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.translationxproperty
  name: TranslationXProperty
  nameWithType: VisualElement.TranslationXProperty
  fullName: Microsoft.Maui.Controls.VisualElement.TranslationXProperty
- uid: Microsoft.Maui.Controls.VisualElement.TranslationYProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.TranslationYProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.translationyproperty
  name: TranslationYProperty
  nameWithType: VisualElement.TranslationYProperty
  fullName: Microsoft.Maui.Controls.VisualElement.TranslationYProperty
- uid: Microsoft.Maui.Controls.VisualElement.WidthProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.WidthProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.widthproperty
  name: WidthProperty
  nameWithType: VisualElement.WidthProperty
  fullName: Microsoft.Maui.Controls.VisualElement.WidthProperty
- uid: Microsoft.Maui.Controls.VisualElement.HeightProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.HeightProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.heightproperty
  name: HeightProperty
  nameWithType: VisualElement.HeightProperty
  fullName: Microsoft.Maui.Controls.VisualElement.HeightProperty
- uid: Microsoft.Maui.Controls.VisualElement.RotationProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.RotationProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotationproperty
  name: RotationProperty
  nameWithType: VisualElement.RotationProperty
  fullName: Microsoft.Maui.Controls.VisualElement.RotationProperty
- uid: Microsoft.Maui.Controls.VisualElement.RotationXProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.RotationXProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotationxproperty
  name: RotationXProperty
  nameWithType: VisualElement.RotationXProperty
  fullName: Microsoft.Maui.Controls.VisualElement.RotationXProperty
- uid: Microsoft.Maui.Controls.VisualElement.RotationYProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.RotationYProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotationyproperty
  name: RotationYProperty
  nameWithType: VisualElement.RotationYProperty
  fullName: Microsoft.Maui.Controls.VisualElement.RotationYProperty
- uid: Microsoft.Maui.Controls.VisualElement.ScaleProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.ScaleProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.scaleproperty
  name: ScaleProperty
  nameWithType: VisualElement.ScaleProperty
  fullName: Microsoft.Maui.Controls.VisualElement.ScaleProperty
- uid: Microsoft.Maui.Controls.VisualElement.ScaleXProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.ScaleXProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.scalexproperty
  name: ScaleXProperty
  nameWithType: VisualElement.ScaleXProperty
  fullName: Microsoft.Maui.Controls.VisualElement.ScaleXProperty
- uid: Microsoft.Maui.Controls.VisualElement.ScaleYProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.ScaleYProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.scaleyproperty
  name: ScaleYProperty
  nameWithType: VisualElement.ScaleYProperty
  fullName: Microsoft.Maui.Controls.VisualElement.ScaleYProperty
- uid: Microsoft.Maui.Controls.VisualElement.ClipProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.ClipProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.clipproperty
  name: ClipProperty
  nameWithType: VisualElement.ClipProperty
  fullName: Microsoft.Maui.Controls.VisualElement.ClipProperty
- uid: Microsoft.Maui.Controls.VisualElement.VisualProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.VisualProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.visualproperty
  name: VisualProperty
  nameWithType: VisualElement.VisualProperty
  fullName: Microsoft.Maui.Controls.VisualElement.VisualProperty
- uid: Microsoft.Maui.Controls.VisualElement.IsVisibleProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.IsVisibleProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isvisibleproperty
  name: IsVisibleProperty
  nameWithType: VisualElement.IsVisibleProperty
  fullName: Microsoft.Maui.Controls.VisualElement.IsVisibleProperty
- uid: Microsoft.Maui.Controls.VisualElement.OpacityProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.OpacityProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.opacityproperty
  name: OpacityProperty
  nameWithType: VisualElement.OpacityProperty
  fullName: Microsoft.Maui.Controls.VisualElement.OpacityProperty
- uid: Microsoft.Maui.Controls.VisualElement.BackgroundColorProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.BackgroundColorProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.backgroundcolorproperty
  name: BackgroundColorProperty
  nameWithType: VisualElement.BackgroundColorProperty
  fullName: Microsoft.Maui.Controls.VisualElement.BackgroundColorProperty
- uid: Microsoft.Maui.Controls.VisualElement.BackgroundProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.BackgroundProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.backgroundproperty
  name: BackgroundProperty
  nameWithType: VisualElement.BackgroundProperty
  fullName: Microsoft.Maui.Controls.VisualElement.BackgroundProperty
- uid: Microsoft.Maui.Controls.VisualElement.BehaviorsProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.BehaviorsProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.behaviorsproperty
  name: BehaviorsProperty
  nameWithType: VisualElement.BehaviorsProperty
  fullName: Microsoft.Maui.Controls.VisualElement.BehaviorsProperty
- uid: Microsoft.Maui.Controls.VisualElement.TriggersProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.TriggersProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.triggersproperty
  name: TriggersProperty
  nameWithType: VisualElement.TriggersProperty
  fullName: Microsoft.Maui.Controls.VisualElement.TriggersProperty
- uid: Microsoft.Maui.Controls.VisualElement.WidthRequestProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.WidthRequestProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.widthrequestproperty
  name: WidthRequestProperty
  nameWithType: VisualElement.WidthRequestProperty
  fullName: Microsoft.Maui.Controls.VisualElement.WidthRequestProperty
- uid: Microsoft.Maui.Controls.VisualElement.HeightRequestProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.HeightRequestProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.heightrequestproperty
  name: HeightRequestProperty
  nameWithType: VisualElement.HeightRequestProperty
  fullName: Microsoft.Maui.Controls.VisualElement.HeightRequestProperty
- uid: Microsoft.Maui.Controls.VisualElement.MinimumWidthRequestProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.MinimumWidthRequestProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.minimumwidthrequestproperty
  name: MinimumWidthRequestProperty
  nameWithType: VisualElement.MinimumWidthRequestProperty
  fullName: Microsoft.Maui.Controls.VisualElement.MinimumWidthRequestProperty
- uid: Microsoft.Maui.Controls.VisualElement.MinimumHeightRequestProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.MinimumHeightRequestProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.minimumheightrequestproperty
  name: MinimumHeightRequestProperty
  nameWithType: VisualElement.MinimumHeightRequestProperty
  fullName: Microsoft.Maui.Controls.VisualElement.MinimumHeightRequestProperty
- uid: Microsoft.Maui.Controls.VisualElement.MaximumWidthRequestProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.MaximumWidthRequestProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.maximumwidthrequestproperty
  name: MaximumWidthRequestProperty
  nameWithType: VisualElement.MaximumWidthRequestProperty
  fullName: Microsoft.Maui.Controls.VisualElement.MaximumWidthRequestProperty
- uid: Microsoft.Maui.Controls.VisualElement.MaximumHeightRequestProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.MaximumHeightRequestProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.maximumheightrequestproperty
  name: MaximumHeightRequestProperty
  nameWithType: VisualElement.MaximumHeightRequestProperty
  fullName: Microsoft.Maui.Controls.VisualElement.MaximumHeightRequestProperty
- uid: Microsoft.Maui.Controls.VisualElement.IsFocusedProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.IsFocusedProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isfocusedproperty
  name: IsFocusedProperty
  nameWithType: VisualElement.IsFocusedProperty
  fullName: Microsoft.Maui.Controls.VisualElement.IsFocusedProperty
- uid: Microsoft.Maui.Controls.VisualElement.FlowDirectionProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.FlowDirectionProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.flowdirectionproperty
  name: FlowDirectionProperty
  nameWithType: VisualElement.FlowDirectionProperty
  fullName: Microsoft.Maui.Controls.VisualElement.FlowDirectionProperty
- uid: Microsoft.Maui.Controls.VisualElement.WindowProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.WindowProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.windowproperty
  name: WindowProperty
  nameWithType: VisualElement.WindowProperty
  fullName: Microsoft.Maui.Controls.VisualElement.WindowProperty
- uid: Microsoft.Maui.Controls.VisualElement.ShadowProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.ShadowProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.shadowproperty
  name: ShadowProperty
  nameWithType: VisualElement.ShadowProperty
  fullName: Microsoft.Maui.Controls.VisualElement.ShadowProperty
- uid: Microsoft.Maui.Controls.VisualElement.ZIndexProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.ZIndexProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.zindexproperty
  name: ZIndexProperty
  nameWithType: VisualElement.ZIndexProperty
  fullName: Microsoft.Maui.Controls.VisualElement.ZIndexProperty
- uid: Microsoft.Maui.Controls.VisualElement.BatchBegin
  commentId: M:Microsoft.Maui.Controls.VisualElement.BatchBegin
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.batchbegin
  name: BatchBegin()
  nameWithType: VisualElement.BatchBegin()
  fullName: Microsoft.Maui.Controls.VisualElement.BatchBegin()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.BatchBegin
    name: BatchBegin
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.batchbegin
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.BatchBegin
    name: BatchBegin
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.batchbegin
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.BatchCommit
  commentId: M:Microsoft.Maui.Controls.VisualElement.BatchCommit
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.batchcommit
  name: BatchCommit()
  nameWithType: VisualElement.BatchCommit()
  fullName: Microsoft.Maui.Controls.VisualElement.BatchCommit()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.BatchCommit
    name: BatchCommit
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.batchcommit
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.BatchCommit
    name: BatchCommit
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.batchcommit
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.Focus
  commentId: M:Microsoft.Maui.Controls.VisualElement.Focus
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.focus
  name: Focus()
  nameWithType: VisualElement.Focus()
  fullName: Microsoft.Maui.Controls.VisualElement.Focus()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.Focus
    name: Focus
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.focus
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.Focus
    name: Focus
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.focus
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.Measure(System.Double,System.Double)
  commentId: M:Microsoft.Maui.Controls.VisualElement.Measure(System.Double,System.Double)
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.measure#microsoft-maui-controls-visualelement-measure(system-double-system-double)
  name: Measure(double, double)
  nameWithType: VisualElement.Measure(double, double)
  fullName: Microsoft.Maui.Controls.VisualElement.Measure(double, double)
  nameWithType.vb: VisualElement.Measure(Double, Double)
  fullName.vb: Microsoft.Maui.Controls.VisualElement.Measure(Double, Double)
  name.vb: Measure(Double, Double)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.Measure(System.Double,System.Double)
    name: Measure
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.measure#microsoft-maui-controls-visualelement-measure(system-double-system-double)
  - name: (
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.Measure(System.Double,System.Double)
    name: Measure
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.measure#microsoft-maui-controls-visualelement-measure(system-double-system-double)
  - name: (
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.Measure(System.Double,System.Double,Microsoft.Maui.Controls.MeasureFlags)
  commentId: M:Microsoft.Maui.Controls.VisualElement.Measure(System.Double,System.Double,Microsoft.Maui.Controls.MeasureFlags)
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.measure#microsoft-maui-controls-visualelement-measure(system-double-system-double-microsoft-maui-controls-measureflags)
  name: Measure(double, double, MeasureFlags)
  nameWithType: VisualElement.Measure(double, double, MeasureFlags)
  fullName: Microsoft.Maui.Controls.VisualElement.Measure(double, double, Microsoft.Maui.Controls.MeasureFlags)
  nameWithType.vb: VisualElement.Measure(Double, Double, MeasureFlags)
  fullName.vb: Microsoft.Maui.Controls.VisualElement.Measure(Double, Double, Microsoft.Maui.Controls.MeasureFlags)
  name.vb: Measure(Double, Double, MeasureFlags)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.Measure(System.Double,System.Double,Microsoft.Maui.Controls.MeasureFlags)
    name: Measure
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.measure#microsoft-maui-controls-visualelement-measure(system-double-system-double-microsoft-maui-controls-measureflags)
  - name: (
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.MeasureFlags
    name: MeasureFlags
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.measureflags
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.Measure(System.Double,System.Double,Microsoft.Maui.Controls.MeasureFlags)
    name: Measure
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.measure#microsoft-maui-controls-visualelement-measure(system-double-system-double-microsoft-maui-controls-measureflags)
  - name: (
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.MeasureFlags
    name: MeasureFlags
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.measureflags
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.Unfocus
  commentId: M:Microsoft.Maui.Controls.VisualElement.Unfocus
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.unfocus
  name: Unfocus()
  nameWithType: VisualElement.Unfocus()
  fullName: Microsoft.Maui.Controls.VisualElement.Unfocus()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.Unfocus
    name: Unfocus
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.unfocus
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.Unfocus
    name: Unfocus
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.unfocus
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.InvalidateMeasure
  commentId: M:Microsoft.Maui.Controls.VisualElement.InvalidateMeasure
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.invalidatemeasure
  name: InvalidateMeasure()
  nameWithType: VisualElement.InvalidateMeasure()
  fullName: Microsoft.Maui.Controls.VisualElement.InvalidateMeasure()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.InvalidateMeasure
    name: InvalidateMeasure
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.invalidatemeasure
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.InvalidateMeasure
    name: InvalidateMeasure
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.invalidatemeasure
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.OnChildAdded(Microsoft.Maui.Controls.Element)
  commentId: M:Microsoft.Maui.Controls.VisualElement.OnChildAdded(Microsoft.Maui.Controls.Element)
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onchildadded
  name: OnChildAdded(Element)
  nameWithType: VisualElement.OnChildAdded(Element)
  fullName: Microsoft.Maui.Controls.VisualElement.OnChildAdded(Microsoft.Maui.Controls.Element)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.OnChildAdded(Microsoft.Maui.Controls.Element)
    name: OnChildAdded
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onchildadded
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.OnChildAdded(Microsoft.Maui.Controls.Element)
    name: OnChildAdded
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onchildadded
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.OnChildrenReordered
  commentId: M:Microsoft.Maui.Controls.VisualElement.OnChildrenReordered
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onchildrenreordered
  name: OnChildrenReordered()
  nameWithType: VisualElement.OnChildrenReordered()
  fullName: Microsoft.Maui.Controls.VisualElement.OnChildrenReordered()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.OnChildrenReordered
    name: OnChildrenReordered
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onchildrenreordered
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.OnChildrenReordered
    name: OnChildrenReordered
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onchildrenreordered
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.OnMeasure(System.Double,System.Double)
  commentId: M:Microsoft.Maui.Controls.VisualElement.OnMeasure(System.Double,System.Double)
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onmeasure
  name: OnMeasure(double, double)
  nameWithType: VisualElement.OnMeasure(double, double)
  fullName: Microsoft.Maui.Controls.VisualElement.OnMeasure(double, double)
  nameWithType.vb: VisualElement.OnMeasure(Double, Double)
  fullName.vb: Microsoft.Maui.Controls.VisualElement.OnMeasure(Double, Double)
  name.vb: OnMeasure(Double, Double)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.OnMeasure(System.Double,System.Double)
    name: OnMeasure
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onmeasure
  - name: (
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.OnMeasure(System.Double,System.Double)
    name: OnMeasure
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onmeasure
  - name: (
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.SizeAllocated(System.Double,System.Double)
  commentId: M:Microsoft.Maui.Controls.VisualElement.SizeAllocated(System.Double,System.Double)
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.sizeallocated
  name: SizeAllocated(double, double)
  nameWithType: VisualElement.SizeAllocated(double, double)
  fullName: Microsoft.Maui.Controls.VisualElement.SizeAllocated(double, double)
  nameWithType.vb: VisualElement.SizeAllocated(Double, Double)
  fullName.vb: Microsoft.Maui.Controls.VisualElement.SizeAllocated(Double, Double)
  name.vb: SizeAllocated(Double, Double)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.SizeAllocated(System.Double,System.Double)
    name: SizeAllocated
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.sizeallocated
  - name: (
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.SizeAllocated(System.Double,System.Double)
    name: SizeAllocated
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.sizeallocated
  - name: (
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.ChangeVisualState
  commentId: M:Microsoft.Maui.Controls.VisualElement.ChangeVisualState
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.changevisualstate
  name: ChangeVisualState()
  nameWithType: VisualElement.ChangeVisualState()
  fullName: Microsoft.Maui.Controls.VisualElement.ChangeVisualState()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.ChangeVisualState
    name: ChangeVisualState
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.changevisualstate
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.ChangeVisualState
    name: ChangeVisualState
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.changevisualstate
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.RefreshIsEnabledProperty
  commentId: M:Microsoft.Maui.Controls.VisualElement.RefreshIsEnabledProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.refreshisenabledproperty
  name: RefreshIsEnabledProperty()
  nameWithType: VisualElement.RefreshIsEnabledProperty()
  fullName: Microsoft.Maui.Controls.VisualElement.RefreshIsEnabledProperty()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.RefreshIsEnabledProperty
    name: RefreshIsEnabledProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.refreshisenabledproperty
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.RefreshIsEnabledProperty
    name: RefreshIsEnabledProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.refreshisenabledproperty
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.Arrange(Microsoft.Maui.Graphics.Rect)
  commentId: M:Microsoft.Maui.Controls.VisualElement.Arrange(Microsoft.Maui.Graphics.Rect)
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.arrange
  name: Arrange(Rect)
  nameWithType: VisualElement.Arrange(Rect)
  fullName: Microsoft.Maui.Controls.VisualElement.Arrange(Microsoft.Maui.Graphics.Rect)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.Arrange(Microsoft.Maui.Graphics.Rect)
    name: Arrange
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.arrange
  - name: (
  - uid: Microsoft.Maui.Graphics.Rect
    name: Rect
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.rect
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.Arrange(Microsoft.Maui.Graphics.Rect)
    name: Arrange
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.arrange
  - name: (
  - uid: Microsoft.Maui.Graphics.Rect
    name: Rect
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.rect
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.Layout(Microsoft.Maui.Graphics.Rect)
  commentId: M:Microsoft.Maui.Controls.VisualElement.Layout(Microsoft.Maui.Graphics.Rect)
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.layout
  name: Layout(Rect)
  nameWithType: VisualElement.Layout(Rect)
  fullName: Microsoft.Maui.Controls.VisualElement.Layout(Microsoft.Maui.Graphics.Rect)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.Layout(Microsoft.Maui.Graphics.Rect)
    name: Layout
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.layout
  - name: (
  - uid: Microsoft.Maui.Graphics.Rect
    name: Rect
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.rect
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.Layout(Microsoft.Maui.Graphics.Rect)
    name: Layout
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.layout
  - name: (
  - uid: Microsoft.Maui.Graphics.Rect
    name: Rect
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.rect
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.MapBackgroundColor(Microsoft.Maui.IViewHandler,Microsoft.Maui.IView)
  commentId: M:Microsoft.Maui.Controls.VisualElement.MapBackgroundColor(Microsoft.Maui.IViewHandler,Microsoft.Maui.IView)
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.mapbackgroundcolor
  name: MapBackgroundColor(IViewHandler, IView)
  nameWithType: VisualElement.MapBackgroundColor(IViewHandler, IView)
  fullName: Microsoft.Maui.Controls.VisualElement.MapBackgroundColor(Microsoft.Maui.IViewHandler, Microsoft.Maui.IView)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.MapBackgroundColor(Microsoft.Maui.IViewHandler,Microsoft.Maui.IView)
    name: MapBackgroundColor
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.mapbackgroundcolor
  - name: (
  - uid: Microsoft.Maui.IViewHandler
    name: IViewHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iviewhandler
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.IView
    name: IView
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iview
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.MapBackgroundColor(Microsoft.Maui.IViewHandler,Microsoft.Maui.IView)
    name: MapBackgroundColor
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.mapbackgroundcolor
  - name: (
  - uid: Microsoft.Maui.IViewHandler
    name: IViewHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iviewhandler
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.IView
    name: IView
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iview
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.MapBackgroundImageSource(Microsoft.Maui.IViewHandler,Microsoft.Maui.IView)
  commentId: M:Microsoft.Maui.Controls.VisualElement.MapBackgroundImageSource(Microsoft.Maui.IViewHandler,Microsoft.Maui.IView)
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.mapbackgroundimagesource
  name: MapBackgroundImageSource(IViewHandler, IView)
  nameWithType: VisualElement.MapBackgroundImageSource(IViewHandler, IView)
  fullName: Microsoft.Maui.Controls.VisualElement.MapBackgroundImageSource(Microsoft.Maui.IViewHandler, Microsoft.Maui.IView)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.MapBackgroundImageSource(Microsoft.Maui.IViewHandler,Microsoft.Maui.IView)
    name: MapBackgroundImageSource
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.mapbackgroundimagesource
  - name: (
  - uid: Microsoft.Maui.IViewHandler
    name: IViewHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iviewhandler
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.IView
    name: IView
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iview
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.MapBackgroundImageSource(Microsoft.Maui.IViewHandler,Microsoft.Maui.IView)
    name: MapBackgroundImageSource
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.mapbackgroundimagesource
  - name: (
  - uid: Microsoft.Maui.IViewHandler
    name: IViewHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iviewhandler
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.IView
    name: IView
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iview
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.Visual
  commentId: P:Microsoft.Maui.Controls.VisualElement.Visual
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.visual
  name: Visual
  nameWithType: VisualElement.Visual
  fullName: Microsoft.Maui.Controls.VisualElement.Visual
- uid: Microsoft.Maui.Controls.VisualElement.FlowDirection
  commentId: P:Microsoft.Maui.Controls.VisualElement.FlowDirection
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.flowdirection
  name: FlowDirection
  nameWithType: VisualElement.FlowDirection
  fullName: Microsoft.Maui.Controls.VisualElement.FlowDirection
- uid: Microsoft.Maui.Controls.VisualElement.Window
  commentId: P:Microsoft.Maui.Controls.VisualElement.Window
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.window
  name: Window
  nameWithType: VisualElement.Window
  fullName: Microsoft.Maui.Controls.VisualElement.Window
- uid: Microsoft.Maui.Controls.VisualElement.AnchorX
  commentId: P:Microsoft.Maui.Controls.VisualElement.AnchorX
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.anchorx
  name: AnchorX
  nameWithType: VisualElement.AnchorX
  fullName: Microsoft.Maui.Controls.VisualElement.AnchorX
- uid: Microsoft.Maui.Controls.VisualElement.AnchorY
  commentId: P:Microsoft.Maui.Controls.VisualElement.AnchorY
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.anchory
  name: AnchorY
  nameWithType: VisualElement.AnchorY
  fullName: Microsoft.Maui.Controls.VisualElement.AnchorY
- uid: Microsoft.Maui.Controls.VisualElement.BackgroundColor
  commentId: P:Microsoft.Maui.Controls.VisualElement.BackgroundColor
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.backgroundcolor
  name: BackgroundColor
  nameWithType: VisualElement.BackgroundColor
  fullName: Microsoft.Maui.Controls.VisualElement.BackgroundColor
- uid: Microsoft.Maui.Controls.VisualElement.Background
  commentId: P:Microsoft.Maui.Controls.VisualElement.Background
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.background
  name: Background
  nameWithType: VisualElement.Background
  fullName: Microsoft.Maui.Controls.VisualElement.Background
- uid: Microsoft.Maui.Controls.VisualElement.Behaviors
  commentId: P:Microsoft.Maui.Controls.VisualElement.Behaviors
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.behaviors
  name: Behaviors
  nameWithType: VisualElement.Behaviors
  fullName: Microsoft.Maui.Controls.VisualElement.Behaviors
- uid: Microsoft.Maui.Controls.VisualElement.Bounds
  commentId: P:Microsoft.Maui.Controls.VisualElement.Bounds
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.bounds
  name: Bounds
  nameWithType: VisualElement.Bounds
  fullName: Microsoft.Maui.Controls.VisualElement.Bounds
- uid: Microsoft.Maui.Controls.VisualElement.Height
  commentId: P:Microsoft.Maui.Controls.VisualElement.Height
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.height
  name: Height
  nameWithType: VisualElement.Height
  fullName: Microsoft.Maui.Controls.VisualElement.Height
- uid: Microsoft.Maui.Controls.VisualElement.HeightRequest
  commentId: P:Microsoft.Maui.Controls.VisualElement.HeightRequest
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.heightrequest
  name: HeightRequest
  nameWithType: VisualElement.HeightRequest
  fullName: Microsoft.Maui.Controls.VisualElement.HeightRequest
- uid: Microsoft.Maui.Controls.VisualElement.InputTransparent
  commentId: P:Microsoft.Maui.Controls.VisualElement.InputTransparent
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.inputtransparent
  name: InputTransparent
  nameWithType: VisualElement.InputTransparent
  fullName: Microsoft.Maui.Controls.VisualElement.InputTransparent
- uid: Microsoft.Maui.Controls.VisualElement.IsEnabled
  commentId: P:Microsoft.Maui.Controls.VisualElement.IsEnabled
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isenabled
  name: IsEnabled
  nameWithType: VisualElement.IsEnabled
  fullName: Microsoft.Maui.Controls.VisualElement.IsEnabled
- uid: Microsoft.Maui.Controls.VisualElement.IsEnabledCore
  commentId: P:Microsoft.Maui.Controls.VisualElement.IsEnabledCore
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isenabledcore
  name: IsEnabledCore
  nameWithType: VisualElement.IsEnabledCore
  fullName: Microsoft.Maui.Controls.VisualElement.IsEnabledCore
- uid: Microsoft.Maui.Controls.VisualElement.IsFocused
  commentId: P:Microsoft.Maui.Controls.VisualElement.IsFocused
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isfocused
  name: IsFocused
  nameWithType: VisualElement.IsFocused
  fullName: Microsoft.Maui.Controls.VisualElement.IsFocused
- uid: Microsoft.Maui.Controls.VisualElement.IsVisible
  commentId: P:Microsoft.Maui.Controls.VisualElement.IsVisible
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isvisible
  name: IsVisible
  nameWithType: VisualElement.IsVisible
  fullName: Microsoft.Maui.Controls.VisualElement.IsVisible
- uid: Microsoft.Maui.Controls.VisualElement.MinimumHeightRequest
  commentId: P:Microsoft.Maui.Controls.VisualElement.MinimumHeightRequest
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.minimumheightrequest
  name: MinimumHeightRequest
  nameWithType: VisualElement.MinimumHeightRequest
  fullName: Microsoft.Maui.Controls.VisualElement.MinimumHeightRequest
- uid: Microsoft.Maui.Controls.VisualElement.MinimumWidthRequest
  commentId: P:Microsoft.Maui.Controls.VisualElement.MinimumWidthRequest
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.minimumwidthrequest
  name: MinimumWidthRequest
  nameWithType: VisualElement.MinimumWidthRequest
  fullName: Microsoft.Maui.Controls.VisualElement.MinimumWidthRequest
- uid: Microsoft.Maui.Controls.VisualElement.MaximumHeightRequest
  commentId: P:Microsoft.Maui.Controls.VisualElement.MaximumHeightRequest
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.maximumheightrequest
  name: MaximumHeightRequest
  nameWithType: VisualElement.MaximumHeightRequest
  fullName: Microsoft.Maui.Controls.VisualElement.MaximumHeightRequest
- uid: Microsoft.Maui.Controls.VisualElement.MaximumWidthRequest
  commentId: P:Microsoft.Maui.Controls.VisualElement.MaximumWidthRequest
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.maximumwidthrequest
  name: MaximumWidthRequest
  nameWithType: VisualElement.MaximumWidthRequest
  fullName: Microsoft.Maui.Controls.VisualElement.MaximumWidthRequest
- uid: Microsoft.Maui.Controls.VisualElement.Opacity
  commentId: P:Microsoft.Maui.Controls.VisualElement.Opacity
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.opacity
  name: Opacity
  nameWithType: VisualElement.Opacity
  fullName: Microsoft.Maui.Controls.VisualElement.Opacity
- uid: Microsoft.Maui.Controls.VisualElement.Rotation
  commentId: P:Microsoft.Maui.Controls.VisualElement.Rotation
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotation
  name: Rotation
  nameWithType: VisualElement.Rotation
  fullName: Microsoft.Maui.Controls.VisualElement.Rotation
- uid: Microsoft.Maui.Controls.VisualElement.RotationX
  commentId: P:Microsoft.Maui.Controls.VisualElement.RotationX
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotationx
  name: RotationX
  nameWithType: VisualElement.RotationX
  fullName: Microsoft.Maui.Controls.VisualElement.RotationX
- uid: Microsoft.Maui.Controls.VisualElement.RotationY
  commentId: P:Microsoft.Maui.Controls.VisualElement.RotationY
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotationy
  name: RotationY
  nameWithType: VisualElement.RotationY
  fullName: Microsoft.Maui.Controls.VisualElement.RotationY
- uid: Microsoft.Maui.Controls.VisualElement.Scale
  commentId: P:Microsoft.Maui.Controls.VisualElement.Scale
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.scale
  name: Scale
  nameWithType: VisualElement.Scale
  fullName: Microsoft.Maui.Controls.VisualElement.Scale
- uid: Microsoft.Maui.Controls.VisualElement.ScaleX
  commentId: P:Microsoft.Maui.Controls.VisualElement.ScaleX
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.scalex
  name: ScaleX
  nameWithType: VisualElement.ScaleX
  fullName: Microsoft.Maui.Controls.VisualElement.ScaleX
- uid: Microsoft.Maui.Controls.VisualElement.ScaleY
  commentId: P:Microsoft.Maui.Controls.VisualElement.ScaleY
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.scaley
  name: ScaleY
  nameWithType: VisualElement.ScaleY
  fullName: Microsoft.Maui.Controls.VisualElement.ScaleY
- uid: Microsoft.Maui.Controls.VisualElement.TranslationX
  commentId: P:Microsoft.Maui.Controls.VisualElement.TranslationX
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.translationx
  name: TranslationX
  nameWithType: VisualElement.TranslationX
  fullName: Microsoft.Maui.Controls.VisualElement.TranslationX
- uid: Microsoft.Maui.Controls.VisualElement.TranslationY
  commentId: P:Microsoft.Maui.Controls.VisualElement.TranslationY
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.translationy
  name: TranslationY
  nameWithType: VisualElement.TranslationY
  fullName: Microsoft.Maui.Controls.VisualElement.TranslationY
- uid: Microsoft.Maui.Controls.VisualElement.Triggers
  commentId: P:Microsoft.Maui.Controls.VisualElement.Triggers
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.triggers
  name: Triggers
  nameWithType: VisualElement.Triggers
  fullName: Microsoft.Maui.Controls.VisualElement.Triggers
- uid: Microsoft.Maui.Controls.VisualElement.Width
  commentId: P:Microsoft.Maui.Controls.VisualElement.Width
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.width
  name: Width
  nameWithType: VisualElement.Width
  fullName: Microsoft.Maui.Controls.VisualElement.Width
- uid: Microsoft.Maui.Controls.VisualElement.WidthRequest
  commentId: P:Microsoft.Maui.Controls.VisualElement.WidthRequest
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.widthrequest
  name: WidthRequest
  nameWithType: VisualElement.WidthRequest
  fullName: Microsoft.Maui.Controls.VisualElement.WidthRequest
- uid: Microsoft.Maui.Controls.VisualElement.X
  commentId: P:Microsoft.Maui.Controls.VisualElement.X
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.x
  name: X
  nameWithType: VisualElement.X
  fullName: Microsoft.Maui.Controls.VisualElement.X
- uid: Microsoft.Maui.Controls.VisualElement.Y
  commentId: P:Microsoft.Maui.Controls.VisualElement.Y
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.y
  name: Y
  nameWithType: VisualElement.Y
  fullName: Microsoft.Maui.Controls.VisualElement.Y
- uid: Microsoft.Maui.Controls.VisualElement.Clip
  commentId: P:Microsoft.Maui.Controls.VisualElement.Clip
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.clip
  name: Clip
  nameWithType: VisualElement.Clip
  fullName: Microsoft.Maui.Controls.VisualElement.Clip
- uid: Microsoft.Maui.Controls.VisualElement.Resources
  commentId: P:Microsoft.Maui.Controls.VisualElement.Resources
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.resources
  name: Resources
  nameWithType: VisualElement.Resources
  fullName: Microsoft.Maui.Controls.VisualElement.Resources
- uid: Microsoft.Maui.Controls.VisualElement.Frame
  commentId: P:Microsoft.Maui.Controls.VisualElement.Frame
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.frame
  name: Frame
  nameWithType: VisualElement.Frame
  fullName: Microsoft.Maui.Controls.VisualElement.Frame
- uid: Microsoft.Maui.Controls.VisualElement.Handler
  commentId: P:Microsoft.Maui.Controls.VisualElement.Handler
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.handler
  name: Handler
  nameWithType: VisualElement.Handler
  fullName: Microsoft.Maui.Controls.VisualElement.Handler
- uid: Microsoft.Maui.Controls.VisualElement.Shadow
  commentId: P:Microsoft.Maui.Controls.VisualElement.Shadow
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.shadow
  name: Shadow
  nameWithType: VisualElement.Shadow
  fullName: Microsoft.Maui.Controls.VisualElement.Shadow
- uid: Microsoft.Maui.Controls.VisualElement.ZIndex
  commentId: P:Microsoft.Maui.Controls.VisualElement.ZIndex
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.zindex
  name: ZIndex
  nameWithType: VisualElement.ZIndex
  fullName: Microsoft.Maui.Controls.VisualElement.ZIndex
- uid: Microsoft.Maui.Controls.VisualElement.DesiredSize
  commentId: P:Microsoft.Maui.Controls.VisualElement.DesiredSize
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.desiredsize
  name: DesiredSize
  nameWithType: VisualElement.DesiredSize
  fullName: Microsoft.Maui.Controls.VisualElement.DesiredSize
- uid: Microsoft.Maui.Controls.VisualElement.IsLoaded
  commentId: P:Microsoft.Maui.Controls.VisualElement.IsLoaded
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isloaded
  name: IsLoaded
  nameWithType: VisualElement.IsLoaded
  fullName: Microsoft.Maui.Controls.VisualElement.IsLoaded
- uid: Microsoft.Maui.Controls.VisualElement.ChildrenReordered
  commentId: E:Microsoft.Maui.Controls.VisualElement.ChildrenReordered
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.childrenreordered
  name: ChildrenReordered
  nameWithType: VisualElement.ChildrenReordered
  fullName: Microsoft.Maui.Controls.VisualElement.ChildrenReordered
- uid: Microsoft.Maui.Controls.VisualElement.Focused
  commentId: E:Microsoft.Maui.Controls.VisualElement.Focused
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.focused
  name: Focused
  nameWithType: VisualElement.Focused
  fullName: Microsoft.Maui.Controls.VisualElement.Focused
- uid: Microsoft.Maui.Controls.VisualElement.MeasureInvalidated
  commentId: E:Microsoft.Maui.Controls.VisualElement.MeasureInvalidated
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.measureinvalidated
  name: MeasureInvalidated
  nameWithType: VisualElement.MeasureInvalidated
  fullName: Microsoft.Maui.Controls.VisualElement.MeasureInvalidated
- uid: Microsoft.Maui.Controls.VisualElement.SizeChanged
  commentId: E:Microsoft.Maui.Controls.VisualElement.SizeChanged
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.sizechanged
  name: SizeChanged
  nameWithType: VisualElement.SizeChanged
  fullName: Microsoft.Maui.Controls.VisualElement.SizeChanged
- uid: Microsoft.Maui.Controls.VisualElement.Unfocused
  commentId: E:Microsoft.Maui.Controls.VisualElement.Unfocused
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.unfocused
  name: Unfocused
  nameWithType: VisualElement.Unfocused
  fullName: Microsoft.Maui.Controls.VisualElement.Unfocused
- uid: Microsoft.Maui.Controls.VisualElement.Loaded
  commentId: E:Microsoft.Maui.Controls.VisualElement.Loaded
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.loaded
  name: Loaded
  nameWithType: VisualElement.Loaded
  fullName: Microsoft.Maui.Controls.VisualElement.Loaded
- uid: Microsoft.Maui.Controls.VisualElement.Unloaded
  commentId: E:Microsoft.Maui.Controls.VisualElement.Unloaded
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.unloaded
  name: Unloaded
  nameWithType: VisualElement.Unloaded
  fullName: Microsoft.Maui.Controls.VisualElement.Unloaded
- uid: Microsoft.Maui.Controls.NavigableElement.Navigation
  commentId: P:Microsoft.Maui.Controls.NavigableElement.Navigation
  parent: Microsoft.Maui.Controls.NavigableElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.navigableelement.navigation
  name: Navigation
  nameWithType: NavigableElement.Navigation
  fullName: Microsoft.Maui.Controls.NavigableElement.Navigation
- uid: Microsoft.Maui.Controls.StyleableElement.Style
  commentId: P:Microsoft.Maui.Controls.StyleableElement.Style
  parent: Microsoft.Maui.Controls.StyleableElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.styleableelement.style
  name: Style
  nameWithType: StyleableElement.Style
  fullName: Microsoft.Maui.Controls.StyleableElement.Style
- uid: Microsoft.Maui.Controls.StyleableElement.StyleClass
  commentId: P:Microsoft.Maui.Controls.StyleableElement.StyleClass
  parent: Microsoft.Maui.Controls.StyleableElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.styleableelement.styleclass
  name: StyleClass
  nameWithType: StyleableElement.StyleClass
  fullName: Microsoft.Maui.Controls.StyleableElement.StyleClass
- uid: Microsoft.Maui.Controls.StyleableElement.class
  commentId: P:Microsoft.Maui.Controls.StyleableElement.class
  parent: Microsoft.Maui.Controls.StyleableElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.styleableelement.class
  name: class
  nameWithType: StyleableElement.class
  fullName: Microsoft.Maui.Controls.StyleableElement.class
- uid: Microsoft.Maui.Controls.Element.AutomationIdProperty
  commentId: F:Microsoft.Maui.Controls.Element.AutomationIdProperty
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.automationidproperty
  name: AutomationIdProperty
  nameWithType: Element.AutomationIdProperty
  fullName: Microsoft.Maui.Controls.Element.AutomationIdProperty
- uid: Microsoft.Maui.Controls.Element.ClassIdProperty
  commentId: F:Microsoft.Maui.Controls.Element.ClassIdProperty
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.classidproperty
  name: ClassIdProperty
  nameWithType: Element.ClassIdProperty
  fullName: Microsoft.Maui.Controls.Element.ClassIdProperty
- uid: Microsoft.Maui.Controls.Element.InsertLogicalChild(System.Int32,Microsoft.Maui.Controls.Element)
  commentId: M:Microsoft.Maui.Controls.Element.InsertLogicalChild(System.Int32,Microsoft.Maui.Controls.Element)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.insertlogicalchild
  name: InsertLogicalChild(int, Element)
  nameWithType: Element.InsertLogicalChild(int, Element)
  fullName: Microsoft.Maui.Controls.Element.InsertLogicalChild(int, Microsoft.Maui.Controls.Element)
  nameWithType.vb: Element.InsertLogicalChild(Integer, Element)
  fullName.vb: Microsoft.Maui.Controls.Element.InsertLogicalChild(Integer, Microsoft.Maui.Controls.Element)
  name.vb: InsertLogicalChild(Integer, Element)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.InsertLogicalChild(System.Int32,Microsoft.Maui.Controls.Element)
    name: InsertLogicalChild
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.insertlogicalchild
  - name: (
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.InsertLogicalChild(System.Int32,Microsoft.Maui.Controls.Element)
    name: InsertLogicalChild
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.insertlogicalchild
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: Microsoft.Maui.Controls.Element.AddLogicalChild(Microsoft.Maui.Controls.Element)
  commentId: M:Microsoft.Maui.Controls.Element.AddLogicalChild(Microsoft.Maui.Controls.Element)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.addlogicalchild
  name: AddLogicalChild(Element)
  nameWithType: Element.AddLogicalChild(Element)
  fullName: Microsoft.Maui.Controls.Element.AddLogicalChild(Microsoft.Maui.Controls.Element)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.AddLogicalChild(Microsoft.Maui.Controls.Element)
    name: AddLogicalChild
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.addlogicalchild
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.AddLogicalChild(Microsoft.Maui.Controls.Element)
    name: AddLogicalChild
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.addlogicalchild
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: Microsoft.Maui.Controls.Element.RemoveLogicalChild(Microsoft.Maui.Controls.Element)
  commentId: M:Microsoft.Maui.Controls.Element.RemoveLogicalChild(Microsoft.Maui.Controls.Element)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.removelogicalchild
  name: RemoveLogicalChild(Element)
  nameWithType: Element.RemoveLogicalChild(Element)
  fullName: Microsoft.Maui.Controls.Element.RemoveLogicalChild(Microsoft.Maui.Controls.Element)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.RemoveLogicalChild(Microsoft.Maui.Controls.Element)
    name: RemoveLogicalChild
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.removelogicalchild
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.RemoveLogicalChild(Microsoft.Maui.Controls.Element)
    name: RemoveLogicalChild
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.removelogicalchild
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: Microsoft.Maui.Controls.Element.ClearLogicalChildren
  commentId: M:Microsoft.Maui.Controls.Element.ClearLogicalChildren
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.clearlogicalchildren
  name: ClearLogicalChildren()
  nameWithType: Element.ClearLogicalChildren()
  fullName: Microsoft.Maui.Controls.Element.ClearLogicalChildren()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.ClearLogicalChildren
    name: ClearLogicalChildren
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.clearlogicalchildren
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.ClearLogicalChildren
    name: ClearLogicalChildren
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.clearlogicalchildren
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.Element.FindByName(System.String)
  commentId: M:Microsoft.Maui.Controls.Element.FindByName(System.String)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.findbyname
  name: FindByName(string)
  nameWithType: Element.FindByName(string)
  fullName: Microsoft.Maui.Controls.Element.FindByName(string)
  nameWithType.vb: Element.FindByName(String)
  fullName.vb: Microsoft.Maui.Controls.Element.FindByName(String)
  name.vb: FindByName(String)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.FindByName(System.String)
    name: FindByName
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.findbyname
  - name: (
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.FindByName(System.String)
    name: FindByName
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.findbyname
  - name: (
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: Microsoft.Maui.Controls.Element.RemoveDynamicResource(Microsoft.Maui.Controls.BindableProperty)
  commentId: M:Microsoft.Maui.Controls.Element.RemoveDynamicResource(Microsoft.Maui.Controls.BindableProperty)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.removedynamicresource
  name: RemoveDynamicResource(BindableProperty)
  nameWithType: Element.RemoveDynamicResource(BindableProperty)
  fullName: Microsoft.Maui.Controls.Element.RemoveDynamicResource(Microsoft.Maui.Controls.BindableProperty)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.RemoveDynamicResource(Microsoft.Maui.Controls.BindableProperty)
    name: RemoveDynamicResource
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.removedynamicresource
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.RemoveDynamicResource(Microsoft.Maui.Controls.BindableProperty)
    name: RemoveDynamicResource
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.removedynamicresource
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
- uid: Microsoft.Maui.Controls.Element.SetDynamicResource(Microsoft.Maui.Controls.BindableProperty,System.String)
  commentId: M:Microsoft.Maui.Controls.Element.SetDynamicResource(Microsoft.Maui.Controls.BindableProperty,System.String)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.setdynamicresource
  name: SetDynamicResource(BindableProperty, string)
  nameWithType: Element.SetDynamicResource(BindableProperty, string)
  fullName: Microsoft.Maui.Controls.Element.SetDynamicResource(Microsoft.Maui.Controls.BindableProperty, string)
  nameWithType.vb: Element.SetDynamicResource(BindableProperty, String)
  fullName.vb: Microsoft.Maui.Controls.Element.SetDynamicResource(Microsoft.Maui.Controls.BindableProperty, String)
  name.vb: SetDynamicResource(BindableProperty, String)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.SetDynamicResource(Microsoft.Maui.Controls.BindableProperty,System.String)
    name: SetDynamicResource
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.setdynamicresource
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.SetDynamicResource(Microsoft.Maui.Controls.BindableProperty,System.String)
    name: SetDynamicResource
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.setdynamicresource
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: Microsoft.Maui.Controls.Element.OnPropertyChanged(System.String)
  commentId: M:Microsoft.Maui.Controls.Element.OnPropertyChanged(System.String)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onpropertychanged
  name: OnPropertyChanged(string)
  nameWithType: Element.OnPropertyChanged(string)
  fullName: Microsoft.Maui.Controls.Element.OnPropertyChanged(string)
  nameWithType.vb: Element.OnPropertyChanged(String)
  fullName.vb: Microsoft.Maui.Controls.Element.OnPropertyChanged(String)
  name.vb: OnPropertyChanged(String)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.OnPropertyChanged(System.String)
    name: OnPropertyChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onpropertychanged
  - name: (
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.OnPropertyChanged(System.String)
    name: OnPropertyChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onpropertychanged
  - name: (
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: Microsoft.Maui.Controls.Element.OnParentChanging(Microsoft.Maui.Controls.ParentChangingEventArgs)
  commentId: M:Microsoft.Maui.Controls.Element.OnParentChanging(Microsoft.Maui.Controls.ParentChangingEventArgs)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentchanging
  name: OnParentChanging(ParentChangingEventArgs)
  nameWithType: Element.OnParentChanging(ParentChangingEventArgs)
  fullName: Microsoft.Maui.Controls.Element.OnParentChanging(Microsoft.Maui.Controls.ParentChangingEventArgs)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.OnParentChanging(Microsoft.Maui.Controls.ParentChangingEventArgs)
    name: OnParentChanging
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentchanging
  - name: (
  - uid: Microsoft.Maui.Controls.ParentChangingEventArgs
    name: ParentChangingEventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.parentchangingeventargs
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.OnParentChanging(Microsoft.Maui.Controls.ParentChangingEventArgs)
    name: OnParentChanging
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentchanging
  - name: (
  - uid: Microsoft.Maui.Controls.ParentChangingEventArgs
    name: ParentChangingEventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.parentchangingeventargs
  - name: )
- uid: Microsoft.Maui.Controls.Element.OnParentChanged
  commentId: M:Microsoft.Maui.Controls.Element.OnParentChanged
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentchanged
  name: OnParentChanged()
  nameWithType: Element.OnParentChanged()
  fullName: Microsoft.Maui.Controls.Element.OnParentChanged()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.OnParentChanged
    name: OnParentChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentchanged
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.OnParentChanged
    name: OnParentChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentchanged
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.Element.OnHandlerChanging(Microsoft.Maui.Controls.HandlerChangingEventArgs)
  commentId: M:Microsoft.Maui.Controls.Element.OnHandlerChanging(Microsoft.Maui.Controls.HandlerChangingEventArgs)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onhandlerchanging
  name: OnHandlerChanging(HandlerChangingEventArgs)
  nameWithType: Element.OnHandlerChanging(HandlerChangingEventArgs)
  fullName: Microsoft.Maui.Controls.Element.OnHandlerChanging(Microsoft.Maui.Controls.HandlerChangingEventArgs)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.OnHandlerChanging(Microsoft.Maui.Controls.HandlerChangingEventArgs)
    name: OnHandlerChanging
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onhandlerchanging
  - name: (
  - uid: Microsoft.Maui.Controls.HandlerChangingEventArgs
    name: HandlerChangingEventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.handlerchangingeventargs
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.OnHandlerChanging(Microsoft.Maui.Controls.HandlerChangingEventArgs)
    name: OnHandlerChanging
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onhandlerchanging
  - name: (
  - uid: Microsoft.Maui.Controls.HandlerChangingEventArgs
    name: HandlerChangingEventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.handlerchangingeventargs
  - name: )
- uid: Microsoft.Maui.Controls.Element.OnHandlerChanged
  commentId: M:Microsoft.Maui.Controls.Element.OnHandlerChanged
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onhandlerchanged
  name: OnHandlerChanged()
  nameWithType: Element.OnHandlerChanged()
  fullName: Microsoft.Maui.Controls.Element.OnHandlerChanged()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.OnHandlerChanged
    name: OnHandlerChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onhandlerchanged
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.OnHandlerChanged
    name: OnHandlerChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onhandlerchanged
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.Element.MapAutomationPropertiesIsInAccessibleTree(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
  commentId: M:Microsoft.Maui.Controls.Element.MapAutomationPropertiesIsInAccessibleTree(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.mapautomationpropertiesisinaccessibletree
  name: MapAutomationPropertiesIsInAccessibleTree(IElementHandler, Element)
  nameWithType: Element.MapAutomationPropertiesIsInAccessibleTree(IElementHandler, Element)
  fullName: Microsoft.Maui.Controls.Element.MapAutomationPropertiesIsInAccessibleTree(Microsoft.Maui.IElementHandler, Microsoft.Maui.Controls.Element)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.MapAutomationPropertiesIsInAccessibleTree(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
    name: MapAutomationPropertiesIsInAccessibleTree
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.mapautomationpropertiesisinaccessibletree
  - name: (
  - uid: Microsoft.Maui.IElementHandler
    name: IElementHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.ielementhandler
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.MapAutomationPropertiesIsInAccessibleTree(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
    name: MapAutomationPropertiesIsInAccessibleTree
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.mapautomationpropertiesisinaccessibletree
  - name: (
  - uid: Microsoft.Maui.IElementHandler
    name: IElementHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.ielementhandler
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: Microsoft.Maui.Controls.Element.MapAutomationPropertiesExcludedWithChildren(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
  commentId: M:Microsoft.Maui.Controls.Element.MapAutomationPropertiesExcludedWithChildren(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.mapautomationpropertiesexcludedwithchildren
  name: MapAutomationPropertiesExcludedWithChildren(IElementHandler, Element)
  nameWithType: Element.MapAutomationPropertiesExcludedWithChildren(IElementHandler, Element)
  fullName: Microsoft.Maui.Controls.Element.MapAutomationPropertiesExcludedWithChildren(Microsoft.Maui.IElementHandler, Microsoft.Maui.Controls.Element)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.MapAutomationPropertiesExcludedWithChildren(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
    name: MapAutomationPropertiesExcludedWithChildren
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.mapautomationpropertiesexcludedwithchildren
  - name: (
  - uid: Microsoft.Maui.IElementHandler
    name: IElementHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.ielementhandler
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.MapAutomationPropertiesExcludedWithChildren(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
    name: MapAutomationPropertiesExcludedWithChildren
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.mapautomationpropertiesexcludedwithchildren
  - name: (
  - uid: Microsoft.Maui.IElementHandler
    name: IElementHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.ielementhandler
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: Microsoft.Maui.Controls.Element.AutomationId
  commentId: P:Microsoft.Maui.Controls.Element.AutomationId
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.automationid
  name: AutomationId
  nameWithType: Element.AutomationId
  fullName: Microsoft.Maui.Controls.Element.AutomationId
- uid: Microsoft.Maui.Controls.Element.ClassId
  commentId: P:Microsoft.Maui.Controls.Element.ClassId
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.classid
  name: ClassId
  nameWithType: Element.ClassId
  fullName: Microsoft.Maui.Controls.Element.ClassId
- uid: Microsoft.Maui.Controls.Element.Effects
  commentId: P:Microsoft.Maui.Controls.Element.Effects
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.effects
  name: Effects
  nameWithType: Element.Effects
  fullName: Microsoft.Maui.Controls.Element.Effects
- uid: Microsoft.Maui.Controls.Element.Id
  commentId: P:Microsoft.Maui.Controls.Element.Id
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.id
  name: Id
  nameWithType: Element.Id
  fullName: Microsoft.Maui.Controls.Element.Id
- uid: Microsoft.Maui.Controls.Element.StyleId
  commentId: P:Microsoft.Maui.Controls.Element.StyleId
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.styleid
  name: StyleId
  nameWithType: Element.StyleId
  fullName: Microsoft.Maui.Controls.Element.StyleId
- uid: Microsoft.Maui.Controls.Element.Parent
  commentId: P:Microsoft.Maui.Controls.Element.Parent
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.parent
  name: Parent
  nameWithType: Element.Parent
  fullName: Microsoft.Maui.Controls.Element.Parent
- uid: Microsoft.Maui.Controls.Element.ChildAdded
  commentId: E:Microsoft.Maui.Controls.Element.ChildAdded
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.childadded
  name: ChildAdded
  nameWithType: Element.ChildAdded
  fullName: Microsoft.Maui.Controls.Element.ChildAdded
- uid: Microsoft.Maui.Controls.Element.ChildRemoved
  commentId: E:Microsoft.Maui.Controls.Element.ChildRemoved
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.childremoved
  name: ChildRemoved
  nameWithType: Element.ChildRemoved
  fullName: Microsoft.Maui.Controls.Element.ChildRemoved
- uid: Microsoft.Maui.Controls.Element.DescendantAdded
  commentId: E:Microsoft.Maui.Controls.Element.DescendantAdded
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.descendantadded
  name: DescendantAdded
  nameWithType: Element.DescendantAdded
  fullName: Microsoft.Maui.Controls.Element.DescendantAdded
- uid: Microsoft.Maui.Controls.Element.DescendantRemoved
  commentId: E:Microsoft.Maui.Controls.Element.DescendantRemoved
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.descendantremoved
  name: DescendantRemoved
  nameWithType: Element.DescendantRemoved
  fullName: Microsoft.Maui.Controls.Element.DescendantRemoved
- uid: Microsoft.Maui.Controls.Element.ParentChanging
  commentId: E:Microsoft.Maui.Controls.Element.ParentChanging
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.parentchanging
  name: ParentChanging
  nameWithType: Element.ParentChanging
  fullName: Microsoft.Maui.Controls.Element.ParentChanging
- uid: Microsoft.Maui.Controls.Element.ParentChanged
  commentId: E:Microsoft.Maui.Controls.Element.ParentChanged
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.parentchanged
  name: ParentChanged
  nameWithType: Element.ParentChanged
  fullName: Microsoft.Maui.Controls.Element.ParentChanged
- uid: Microsoft.Maui.Controls.Element.HandlerChanging
  commentId: E:Microsoft.Maui.Controls.Element.HandlerChanging
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.handlerchanging
  name: HandlerChanging
  nameWithType: Element.HandlerChanging
  fullName: Microsoft.Maui.Controls.Element.HandlerChanging
- uid: Microsoft.Maui.Controls.Element.HandlerChanged
  commentId: E:Microsoft.Maui.Controls.Element.HandlerChanged
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.handlerchanged
  name: HandlerChanged
  nameWithType: Element.HandlerChanged
  fullName: Microsoft.Maui.Controls.Element.HandlerChanged
- uid: Microsoft.Maui.Controls.BindableObject.BindingContextProperty
  commentId: F:Microsoft.Maui.Controls.BindableObject.BindingContextProperty
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.bindingcontextproperty
  name: BindingContextProperty
  nameWithType: BindableObject.BindingContextProperty
  fullName: Microsoft.Maui.Controls.BindableObject.BindingContextProperty
- uid: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindableProperty)
  commentId: M:Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindableProperty)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindableproperty)
  name: ClearValue(BindableProperty)
  nameWithType: BindableObject.ClearValue(BindableProperty)
  fullName: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindableProperty)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindableProperty)
    name: ClearValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindableproperty)
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindableProperty)
    name: ClearValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindableproperty)
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindablePropertyKey)
  commentId: M:Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindablePropertyKey)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindablepropertykey)
  name: ClearValue(BindablePropertyKey)
  nameWithType: BindableObject.ClearValue(BindablePropertyKey)
  fullName: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindablePropertyKey)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindablePropertyKey)
    name: ClearValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindablepropertykey)
  - name: (
  - uid: Microsoft.Maui.Controls.BindablePropertyKey
    name: BindablePropertyKey
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindablepropertykey
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindablePropertyKey)
    name: ClearValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindablepropertykey)
  - name: (
  - uid: Microsoft.Maui.Controls.BindablePropertyKey
    name: BindablePropertyKey
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindablepropertykey
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.GetValue(Microsoft.Maui.Controls.BindableProperty)
  commentId: M:Microsoft.Maui.Controls.BindableObject.GetValue(Microsoft.Maui.Controls.BindableProperty)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.getvalue
  name: GetValue(BindableProperty)
  nameWithType: BindableObject.GetValue(BindableProperty)
  fullName: Microsoft.Maui.Controls.BindableObject.GetValue(Microsoft.Maui.Controls.BindableProperty)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.GetValue(Microsoft.Maui.Controls.BindableProperty)
    name: GetValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.getvalue
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.GetValue(Microsoft.Maui.Controls.BindableProperty)
    name: GetValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.getvalue
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.IsSet(Microsoft.Maui.Controls.BindableProperty)
  commentId: M:Microsoft.Maui.Controls.BindableObject.IsSet(Microsoft.Maui.Controls.BindableProperty)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.isset
  name: IsSet(BindableProperty)
  nameWithType: BindableObject.IsSet(BindableProperty)
  fullName: Microsoft.Maui.Controls.BindableObject.IsSet(Microsoft.Maui.Controls.BindableProperty)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.IsSet(Microsoft.Maui.Controls.BindableProperty)
    name: IsSet
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.isset
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.IsSet(Microsoft.Maui.Controls.BindableProperty)
    name: IsSet
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.isset
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.RemoveBinding(Microsoft.Maui.Controls.BindableProperty)
  commentId: M:Microsoft.Maui.Controls.BindableObject.RemoveBinding(Microsoft.Maui.Controls.BindableProperty)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.removebinding
  name: RemoveBinding(BindableProperty)
  nameWithType: BindableObject.RemoveBinding(BindableProperty)
  fullName: Microsoft.Maui.Controls.BindableObject.RemoveBinding(Microsoft.Maui.Controls.BindableProperty)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.RemoveBinding(Microsoft.Maui.Controls.BindableProperty)
    name: RemoveBinding
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.removebinding
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.RemoveBinding(Microsoft.Maui.Controls.BindableProperty)
    name: RemoveBinding
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.removebinding
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.SetBinding(Microsoft.Maui.Controls.BindableProperty,Microsoft.Maui.Controls.BindingBase)
  commentId: M:Microsoft.Maui.Controls.BindableObject.SetBinding(Microsoft.Maui.Controls.BindableProperty,Microsoft.Maui.Controls.BindingBase)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setbinding
  name: SetBinding(BindableProperty, BindingBase)
  nameWithType: BindableObject.SetBinding(BindableProperty, BindingBase)
  fullName: Microsoft.Maui.Controls.BindableObject.SetBinding(Microsoft.Maui.Controls.BindableProperty, Microsoft.Maui.Controls.BindingBase)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.SetBinding(Microsoft.Maui.Controls.BindableProperty,Microsoft.Maui.Controls.BindingBase)
    name: SetBinding
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setbinding
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.BindingBase
    name: BindingBase
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindingbase
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.SetBinding(Microsoft.Maui.Controls.BindableProperty,Microsoft.Maui.Controls.BindingBase)
    name: SetBinding
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setbinding
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.BindingBase
    name: BindingBase
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindingbase
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.ApplyBindings
  commentId: M:Microsoft.Maui.Controls.BindableObject.ApplyBindings
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.applybindings
  name: ApplyBindings()
  nameWithType: BindableObject.ApplyBindings()
  fullName: Microsoft.Maui.Controls.BindableObject.ApplyBindings()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.ApplyBindings
    name: ApplyBindings
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.applybindings
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.ApplyBindings
    name: ApplyBindings
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.applybindings
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(System.String)
  commentId: M:Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(System.String)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.onpropertychanging
  name: OnPropertyChanging(string)
  nameWithType: BindableObject.OnPropertyChanging(string)
  fullName: Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(string)
  nameWithType.vb: BindableObject.OnPropertyChanging(String)
  fullName.vb: Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(String)
  name.vb: OnPropertyChanging(String)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(System.String)
    name: OnPropertyChanging
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.onpropertychanging
  - name: (
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(System.String)
    name: OnPropertyChanging
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.onpropertychanging
  - name: (
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.UnapplyBindings
  commentId: M:Microsoft.Maui.Controls.BindableObject.UnapplyBindings
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.unapplybindings
  name: UnapplyBindings()
  nameWithType: BindableObject.UnapplyBindings()
  fullName: Microsoft.Maui.Controls.BindableObject.UnapplyBindings()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.UnapplyBindings
    name: UnapplyBindings
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.unapplybindings
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.UnapplyBindings
    name: UnapplyBindings
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.unapplybindings
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty,System.Object)
  commentId: M:Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty,System.Object)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindableproperty-system-object)
  name: SetValue(BindableProperty, object)
  nameWithType: BindableObject.SetValue(BindableProperty, object)
  fullName: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty, object)
  nameWithType.vb: BindableObject.SetValue(BindableProperty, Object)
  fullName.vb: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty, Object)
  name.vb: SetValue(BindableProperty, Object)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty,System.Object)
    name: SetValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindableproperty-system-object)
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty,System.Object)
    name: SetValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindableproperty-system-object)
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey,System.Object)
  commentId: M:Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey,System.Object)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindablepropertykey-system-object)
  name: SetValue(BindablePropertyKey, object)
  nameWithType: BindableObject.SetValue(BindablePropertyKey, object)
  fullName: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey, object)
  nameWithType.vb: BindableObject.SetValue(BindablePropertyKey, Object)
  fullName.vb: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey, Object)
  name.vb: SetValue(BindablePropertyKey, Object)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey,System.Object)
    name: SetValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindablepropertykey-system-object)
  - name: (
  - uid: Microsoft.Maui.Controls.BindablePropertyKey
    name: BindablePropertyKey
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindablepropertykey
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey,System.Object)
    name: SetValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindablepropertykey-system-object)
  - name: (
  - uid: Microsoft.Maui.Controls.BindablePropertyKey
    name: BindablePropertyKey
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindablepropertykey
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindableProperty)
  commentId: M:Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindableProperty)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindableproperty)
  name: CoerceValue(BindableProperty)
  nameWithType: BindableObject.CoerceValue(BindableProperty)
  fullName: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindableProperty)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindableProperty)
    name: CoerceValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindableproperty)
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindableProperty)
    name: CoerceValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindableproperty)
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindablePropertyKey)
  commentId: M:Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindablePropertyKey)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindablepropertykey)
  name: CoerceValue(BindablePropertyKey)
  nameWithType: BindableObject.CoerceValue(BindablePropertyKey)
  fullName: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindablePropertyKey)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindablePropertyKey)
    name: CoerceValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindablepropertykey)
  - name: (
  - uid: Microsoft.Maui.Controls.BindablePropertyKey
    name: BindablePropertyKey
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindablepropertykey
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindablePropertyKey)
    name: CoerceValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindablepropertykey)
  - name: (
  - uid: Microsoft.Maui.Controls.BindablePropertyKey
    name: BindablePropertyKey
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindablepropertykey
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.Dispatcher
  commentId: P:Microsoft.Maui.Controls.BindableObject.Dispatcher
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.dispatcher
  name: Dispatcher
  nameWithType: BindableObject.Dispatcher
  fullName: Microsoft.Maui.Controls.BindableObject.Dispatcher
- uid: Microsoft.Maui.Controls.BindableObject.BindingContext
  commentId: P:Microsoft.Maui.Controls.BindableObject.BindingContext
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.bindingcontext
  name: BindingContext
  nameWithType: BindableObject.BindingContext
  fullName: Microsoft.Maui.Controls.BindableObject.BindingContext
- uid: Microsoft.Maui.Controls.BindableObject.PropertyChanged
  commentId: E:Microsoft.Maui.Controls.BindableObject.PropertyChanged
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.propertychanged
  name: PropertyChanged
  nameWithType: BindableObject.PropertyChanged
  fullName: Microsoft.Maui.Controls.BindableObject.PropertyChanged
- uid: Microsoft.Maui.Controls.BindableObject.PropertyChanging
  commentId: E:Microsoft.Maui.Controls.BindableObject.PropertyChanging
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.propertychanging
  name: PropertyChanging
  nameWithType: BindableObject.PropertyChanging
  fullName: Microsoft.Maui.Controls.BindableObject.PropertyChanging
- uid: Microsoft.Maui.Controls.BindableObject.BindingContextChanged
  commentId: E:Microsoft.Maui.Controls.BindableObject.BindingContextChanged
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.bindingcontextchanged
  name: BindingContextChanged
  nameWithType: BindableObject.BindingContextChanged
  fullName: Microsoft.Maui.Controls.BindableObject.BindingContextChanged
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: DrawnUi.Views.BasePageReloadable.DrawnUi.Draw.FluentExtensions.AssignNative``1(DrawnUi.Views.BasePageReloadable@)
  commentId: M:DrawnUi.Draw.FluentExtensions.AssignNative``1(``0,``0@)
  parent: DrawnUi.Draw.FluentExtensions
  definition: DrawnUi.Draw.FluentExtensions.AssignNative``1(``0,``0@)
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_AssignNative__1___0___0__
  name: AssignNative<BasePageReloadable>(BasePageReloadable, out BasePageReloadable)
  nameWithType: FluentExtensions.AssignNative<BasePageReloadable>(BasePageReloadable, out BasePageReloadable)
  fullName: DrawnUi.Draw.FluentExtensions.AssignNative<DrawnUi.Views.BasePageReloadable>(DrawnUi.Views.BasePageReloadable, out DrawnUi.Views.BasePageReloadable)
  nameWithType.vb: FluentExtensions.AssignNative(Of BasePageReloadable)(BasePageReloadable, BasePageReloadable)
  fullName.vb: DrawnUi.Draw.FluentExtensions.AssignNative(Of DrawnUi.Views.BasePageReloadable)(DrawnUi.Views.BasePageReloadable, DrawnUi.Views.BasePageReloadable)
  name.vb: AssignNative(Of BasePageReloadable)(BasePageReloadable, BasePageReloadable)
  spec.csharp:
  - uid: DrawnUi.Draw.FluentExtensions.AssignNative``1(DrawnUi.Views.BasePageReloadable,DrawnUi.Views.BasePageReloadable@)
    name: AssignNative
    href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_AssignNative__1___0___0__
  - name: <
  - uid: DrawnUi.Views.BasePageReloadable
    name: BasePageReloadable
    href: DrawnUi.Views.BasePageReloadable.html
  - name: '>'
  - name: (
  - uid: DrawnUi.Views.BasePageReloadable
    name: BasePageReloadable
    href: DrawnUi.Views.BasePageReloadable.html
  - name: ','
  - name: " "
  - name: out
  - name: " "
  - uid: DrawnUi.Views.BasePageReloadable
    name: BasePageReloadable
    href: DrawnUi.Views.BasePageReloadable.html
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.FluentExtensions.AssignNative``1(DrawnUi.Views.BasePageReloadable,DrawnUi.Views.BasePageReloadable@)
    name: AssignNative
    href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_AssignNative__1___0___0__
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Views.BasePageReloadable
    name: BasePageReloadable
    href: DrawnUi.Views.BasePageReloadable.html
  - name: )
  - name: (
  - uid: DrawnUi.Views.BasePageReloadable
    name: BasePageReloadable
    href: DrawnUi.Views.BasePageReloadable.html
  - name: ','
  - name: " "
  - uid: DrawnUi.Views.BasePageReloadable
    name: BasePageReloadable
    href: DrawnUi.Views.BasePageReloadable.html
  - name: )
- uid: Microsoft.Maui.Controls.Element.DrawnUi.Draw.StaticResourcesExtensions.FindParent``1
  commentId: M:DrawnUi.Draw.StaticResourcesExtensions.FindParent``1(Microsoft.Maui.Controls.Element)
  parent: DrawnUi.Draw.StaticResourcesExtensions
  definition: DrawnUi.Draw.StaticResourcesExtensions.FindParent``1(Microsoft.Maui.Controls.Element)
  href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_FindParent__1_Microsoft_Maui_Controls_Element_
  name: FindParent<T>(Element)
  nameWithType: StaticResourcesExtensions.FindParent<T>(Element)
  fullName: DrawnUi.Draw.StaticResourcesExtensions.FindParent<T>(Microsoft.Maui.Controls.Element)
  nameWithType.vb: StaticResourcesExtensions.FindParent(Of T)(Element)
  fullName.vb: DrawnUi.Draw.StaticResourcesExtensions.FindParent(Of T)(Microsoft.Maui.Controls.Element)
  name.vb: FindParent(Of T)(Element)
  spec.csharp:
  - uid: DrawnUi.Draw.StaticResourcesExtensions.FindParent``1(Microsoft.Maui.Controls.Element)
    name: FindParent
    href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_FindParent__1_Microsoft_Maui_Controls_Element_
  - name: <
  - name: T
  - name: '>'
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.StaticResourcesExtensions.FindParent``1(Microsoft.Maui.Controls.Element)
    name: FindParent
    href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_FindParent__1_Microsoft_Maui_Controls_Element_
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: Microsoft.Maui.Controls.Element.DrawnUi.Extensions.InternalExtensions.FindMauiContext(System.Boolean)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element,System.Boolean)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element,System.Boolean)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FindMauiContext_Microsoft_Maui_Controls_Element_System_Boolean_
  name: FindMauiContext(Element, bool)
  nameWithType: InternalExtensions.FindMauiContext(Element, bool)
  fullName: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element, bool)
  nameWithType.vb: InternalExtensions.FindMauiContext(Element, Boolean)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element, Boolean)
  name.vb: FindMauiContext(Element, Boolean)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element,System.Boolean)
    name: FindMauiContext
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FindMauiContext_Microsoft_Maui_Controls_Element_System_Boolean_
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: bool
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element,System.Boolean)
    name: FindMauiContext
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FindMauiContext_Microsoft_Maui_Controls_Element_System_Boolean_
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: Boolean
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
- uid: Microsoft.Maui.Controls.Element.DrawnUi.Extensions.InternalExtensions.GetParentsPath
  commentId: M:DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_GetParentsPath_Microsoft_Maui_Controls_Element_
  name: GetParentsPath(Element)
  nameWithType: InternalExtensions.GetParentsPath(Element)
  fullName: DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
    name: GetParentsPath
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_GetParentsPath_Microsoft_Maui_Controls_Element_
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
    name: GetParentsPath
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_GetParentsPath_Microsoft_Maui_Controls_Element_
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents
  commentId: M:DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents(Microsoft.Maui.Controls.VisualElement)
  parent: DrawnUi.Draw.StaticResourcesExtensions
  definition: DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents(Microsoft.Maui.Controls.VisualElement)
  href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_GetAllWithMyselfParents_Microsoft_Maui_Controls_VisualElement_
  name: GetAllWithMyselfParents(VisualElement)
  nameWithType: StaticResourcesExtensions.GetAllWithMyselfParents(VisualElement)
  fullName: DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents(Microsoft.Maui.Controls.VisualElement)
  spec.csharp:
  - uid: DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents(Microsoft.Maui.Controls.VisualElement)
    name: GetAllWithMyselfParents
    href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_GetAllWithMyselfParents_Microsoft_Maui_Controls_VisualElement_
  - name: (
  - uid: Microsoft.Maui.Controls.VisualElement
    name: VisualElement
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents(Microsoft.Maui.Controls.VisualElement)
    name: GetAllWithMyselfParents
    href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_GetAllWithMyselfParents_Microsoft_Maui_Controls_VisualElement_
  - name: (
  - uid: Microsoft.Maui.Controls.VisualElement
    name: VisualElement
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement
  - name: )
- uid: Microsoft.Maui.IView.DrawnUi.Extensions.InternalExtensions.DisposeControlAndChildren
  commentId: M:DrawnUi.Extensions.InternalExtensions.DisposeControlAndChildren(Microsoft.Maui.IView)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.DisposeControlAndChildren(Microsoft.Maui.IView)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_DisposeControlAndChildren_Microsoft_Maui_IView_
  name: DisposeControlAndChildren(IView)
  nameWithType: InternalExtensions.DisposeControlAndChildren(IView)
  fullName: DrawnUi.Extensions.InternalExtensions.DisposeControlAndChildren(Microsoft.Maui.IView)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.DisposeControlAndChildren(Microsoft.Maui.IView)
    name: DisposeControlAndChildren
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_DisposeControlAndChildren_Microsoft_Maui_IView_
  - name: (
  - uid: Microsoft.Maui.IView
    name: IView
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iview
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.DisposeControlAndChildren(Microsoft.Maui.IView)
    name: DisposeControlAndChildren
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_DisposeControlAndChildren_Microsoft_Maui_IView_
  - name: (
  - uid: Microsoft.Maui.IView
    name: IView
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iview
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: Microsoft.Maui.Controls
  commentId: N:Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft
  name: Microsoft.Maui.Controls
  nameWithType: Microsoft.Maui.Controls
  fullName: Microsoft.Maui.Controls
  spec.csharp:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Controls
    name: Controls
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls
  spec.vb:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Controls
    name: Controls
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls
- uid: System.ComponentModel
  commentId: N:System.ComponentModel
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.ComponentModel
  nameWithType: System.ComponentModel
  fullName: System.ComponentModel
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.ComponentModel
    name: ComponentModel
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.ComponentModel
    name: ComponentModel
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel
- uid: Microsoft.Maui
  commentId: N:Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft
  name: Microsoft.Maui
  nameWithType: Microsoft.Maui
  fullName: Microsoft.Maui
  spec.csharp:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  spec.vb:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
- uid: Microsoft.Maui.Controls.IElementConfiguration`1
  commentId: T:Microsoft.Maui.Controls.IElementConfiguration`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ielementconfiguration-1
  name: IElementConfiguration<TElement>
  nameWithType: IElementConfiguration<TElement>
  fullName: Microsoft.Maui.Controls.IElementConfiguration<TElement>
  nameWithType.vb: IElementConfiguration(Of TElement)
  fullName.vb: Microsoft.Maui.Controls.IElementConfiguration(Of TElement)
  name.vb: IElementConfiguration(Of TElement)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.IElementConfiguration`1
    name: IElementConfiguration
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ielementconfiguration-1
  - name: <
  - name: TElement
  - name: '>'
  spec.vb:
  - uid: Microsoft.Maui.Controls.IElementConfiguration`1
    name: IElementConfiguration
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ielementconfiguration-1
  - name: (
  - name: Of
  - name: " "
  - name: TElement
  - name: )
- uid: Microsoft.Maui.HotReload
  commentId: N:Microsoft.Maui.HotReload
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft
  name: Microsoft.Maui.HotReload
  nameWithType: Microsoft.Maui.HotReload
  fullName: Microsoft.Maui.HotReload
  spec.csharp:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.HotReload
    name: HotReload
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.hotreload
  spec.vb:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.HotReload
    name: HotReload
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.hotreload
- uid: DrawnUi.Draw.FluentExtensions.AssignNative``1(``0,``0@)
  commentId: M:DrawnUi.Draw.FluentExtensions.AssignNative``1(``0,``0@)
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_AssignNative__1___0___0__
  name: AssignNative<T>(T, out T)
  nameWithType: FluentExtensions.AssignNative<T>(T, out T)
  fullName: DrawnUi.Draw.FluentExtensions.AssignNative<T>(T, out T)
  nameWithType.vb: FluentExtensions.AssignNative(Of T)(T, T)
  fullName.vb: DrawnUi.Draw.FluentExtensions.AssignNative(Of T)(T, T)
  name.vb: AssignNative(Of T)(T, T)
  spec.csharp:
  - uid: DrawnUi.Draw.FluentExtensions.AssignNative``1(``0,``0@)
    name: AssignNative
    href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_AssignNative__1___0___0__
  - name: <
  - name: T
  - name: '>'
  - name: (
  - name: T
  - name: ','
  - name: " "
  - name: out
  - name: " "
  - name: T
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.FluentExtensions.AssignNative``1(``0,``0@)
    name: AssignNative
    href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_AssignNative__1___0___0__
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
  - name: (
  - name: T
  - name: ','
  - name: " "
  - name: T
  - name: )
- uid: DrawnUi.Draw.FluentExtensions
  commentId: T:DrawnUi.Draw.FluentExtensions
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.FluentExtensions.html
  name: FluentExtensions
  nameWithType: FluentExtensions
  fullName: DrawnUi.Draw.FluentExtensions
- uid: DrawnUi.Draw.StaticResourcesExtensions.FindParent``1(Microsoft.Maui.Controls.Element)
  commentId: M:DrawnUi.Draw.StaticResourcesExtensions.FindParent``1(Microsoft.Maui.Controls.Element)
  isExternal: true
  href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_FindParent__1_Microsoft_Maui_Controls_Element_
  name: FindParent<T>(Element)
  nameWithType: StaticResourcesExtensions.FindParent<T>(Element)
  fullName: DrawnUi.Draw.StaticResourcesExtensions.FindParent<T>(Microsoft.Maui.Controls.Element)
  nameWithType.vb: StaticResourcesExtensions.FindParent(Of T)(Element)
  fullName.vb: DrawnUi.Draw.StaticResourcesExtensions.FindParent(Of T)(Microsoft.Maui.Controls.Element)
  name.vb: FindParent(Of T)(Element)
  spec.csharp:
  - uid: DrawnUi.Draw.StaticResourcesExtensions.FindParent``1(Microsoft.Maui.Controls.Element)
    name: FindParent
    href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_FindParent__1_Microsoft_Maui_Controls_Element_
  - name: <
  - name: T
  - name: '>'
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.StaticResourcesExtensions.FindParent``1(Microsoft.Maui.Controls.Element)
    name: FindParent
    href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_FindParent__1_Microsoft_Maui_Controls_Element_
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: DrawnUi.Draw.StaticResourcesExtensions
  commentId: T:DrawnUi.Draw.StaticResourcesExtensions
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.StaticResourcesExtensions.html
  name: StaticResourcesExtensions
  nameWithType: StaticResourcesExtensions
  fullName: DrawnUi.Draw.StaticResourcesExtensions
- uid: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element,System.Boolean)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element,System.Boolean)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FindMauiContext_Microsoft_Maui_Controls_Element_System_Boolean_
  name: FindMauiContext(Element, bool)
  nameWithType: InternalExtensions.FindMauiContext(Element, bool)
  fullName: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element, bool)
  nameWithType.vb: InternalExtensions.FindMauiContext(Element, Boolean)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element, Boolean)
  name.vb: FindMauiContext(Element, Boolean)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element,System.Boolean)
    name: FindMauiContext
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FindMauiContext_Microsoft_Maui_Controls_Element_System_Boolean_
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: bool
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element,System.Boolean)
    name: FindMauiContext
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FindMauiContext_Microsoft_Maui_Controls_Element_System_Boolean_
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: Boolean
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
  commentId: M:DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_GetParentsPath_Microsoft_Maui_Controls_Element_
  name: GetParentsPath(Element)
  nameWithType: InternalExtensions.GetParentsPath(Element)
  fullName: DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
    name: GetParentsPath
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_GetParentsPath_Microsoft_Maui_Controls_Element_
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
    name: GetParentsPath
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_GetParentsPath_Microsoft_Maui_Controls_Element_
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents(Microsoft.Maui.Controls.VisualElement)
  commentId: M:DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents(Microsoft.Maui.Controls.VisualElement)
  isExternal: true
  href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_GetAllWithMyselfParents_Microsoft_Maui_Controls_VisualElement_
  name: GetAllWithMyselfParents(VisualElement)
  nameWithType: StaticResourcesExtensions.GetAllWithMyselfParents(VisualElement)
  fullName: DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents(Microsoft.Maui.Controls.VisualElement)
  spec.csharp:
  - uid: DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents(Microsoft.Maui.Controls.VisualElement)
    name: GetAllWithMyselfParents
    href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_GetAllWithMyselfParents_Microsoft_Maui_Controls_VisualElement_
  - name: (
  - uid: Microsoft.Maui.Controls.VisualElement
    name: VisualElement
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents(Microsoft.Maui.Controls.VisualElement)
    name: GetAllWithMyselfParents
    href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_GetAllWithMyselfParents_Microsoft_Maui_Controls_VisualElement_
  - name: (
  - uid: Microsoft.Maui.Controls.VisualElement
    name: VisualElement
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.DisposeControlAndChildren(Microsoft.Maui.IView)
  commentId: M:DrawnUi.Extensions.InternalExtensions.DisposeControlAndChildren(Microsoft.Maui.IView)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_DisposeControlAndChildren_Microsoft_Maui_IView_
  name: DisposeControlAndChildren(IView)
  nameWithType: InternalExtensions.DisposeControlAndChildren(IView)
  fullName: DrawnUi.Extensions.InternalExtensions.DisposeControlAndChildren(Microsoft.Maui.IView)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.DisposeControlAndChildren(Microsoft.Maui.IView)
    name: DisposeControlAndChildren
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_DisposeControlAndChildren_Microsoft_Maui_IView_
  - name: (
  - uid: Microsoft.Maui.IView
    name: IView
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iview
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.DisposeControlAndChildren(Microsoft.Maui.IView)
    name: DisposeControlAndChildren
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_DisposeControlAndChildren_Microsoft_Maui_IView_
  - name: (
  - uid: Microsoft.Maui.IView
    name: IView
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iview
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Views.BasePageReloadable.#ctor*
  commentId: Overload:DrawnUi.Views.BasePageReloadable.#ctor
  href: DrawnUi.Views.BasePageReloadable.html#DrawnUi_Views_BasePageReloadable__ctor
  name: BasePageReloadable
  nameWithType: BasePageReloadable.BasePageReloadable
  fullName: DrawnUi.Views.BasePageReloadable.BasePageReloadable
  nameWithType.vb: BasePageReloadable.New
  fullName.vb: DrawnUi.Views.BasePageReloadable.New
  name.vb: New
- uid: DrawnUi.Views.BasePageReloadable.CountReloads*
  commentId: Overload:DrawnUi.Views.BasePageReloadable.CountReloads
  href: DrawnUi.Views.BasePageReloadable.html#DrawnUi_Views_BasePageReloadable_CountReloads
  name: CountReloads
  nameWithType: BasePageReloadable.CountReloads
  fullName: DrawnUi.Views.BasePageReloadable.CountReloads
- uid: System.Int32
  commentId: T:System.Int32
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.int32
  name: int
  nameWithType: int
  fullName: int
  nameWithType.vb: Integer
  fullName.vb: Integer
  name.vb: Integer
- uid: DrawnUi.Views.BasePageReloadable.ReloadUi*
  commentId: Overload:DrawnUi.Views.BasePageReloadable.ReloadUi
  href: DrawnUi.Views.BasePageReloadable.html#DrawnUi_Views_BasePageReloadable_ReloadUi_System_Type___
  name: ReloadUi
  nameWithType: BasePageReloadable.ReloadUi
  fullName: DrawnUi.Views.BasePageReloadable.ReloadUi
- uid: System.Type[]
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.type
  name: Type[]
  nameWithType: Type[]
  fullName: System.Type[]
  nameWithType.vb: Type()
  fullName.vb: System.Type()
  name.vb: Type()
  spec.csharp:
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: '['
  - name: ']'
  spec.vb:
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: (
  - name: )
- uid: DrawnUi.Views.BasePageReloadable.Build*
  commentId: Overload:DrawnUi.Views.BasePageReloadable.Build
  href: DrawnUi.Views.BasePageReloadable.html#DrawnUi_Views_BasePageReloadable_Build
  name: Build
  nameWithType: BasePageReloadable.Build
  fullName: DrawnUi.Views.BasePageReloadable.Build
- uid: DrawnUi.Views.BasePageReloadable.Dispose*
  commentId: Overload:DrawnUi.Views.BasePageReloadable.Dispose
  href: DrawnUi.Views.BasePageReloadable.html#DrawnUi_Views_BasePageReloadable_Dispose
  name: Dispose
  nameWithType: BasePageReloadable.Dispose
  fullName: DrawnUi.Views.BasePageReloadable.Dispose
- uid: System.IDisposable.Dispose
  commentId: M:System.IDisposable.Dispose
  parent: System.IDisposable
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.idisposable.dispose
  name: Dispose()
  nameWithType: IDisposable.Dispose()
  fullName: System.IDisposable.Dispose()
  spec.csharp:
  - uid: System.IDisposable.Dispose
    name: Dispose
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.idisposable.dispose
  - name: (
  - name: )
  spec.vb:
  - uid: System.IDisposable.Dispose
    name: Dispose
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.idisposable.dispose
  - name: (
  - name: )
- uid: DrawnUi.Views.BasePageReloadable.IsDisposed*
  commentId: Overload:DrawnUi.Views.BasePageReloadable.IsDisposed
  href: DrawnUi.Views.BasePageReloadable.html#DrawnUi_Views_BasePageReloadable_IsDisposed
  name: IsDisposed
  nameWithType: BasePageReloadable.IsDisposed
  fullName: DrawnUi.Views.BasePageReloadable.IsDisposed
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
