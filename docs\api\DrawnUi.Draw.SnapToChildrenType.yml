### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.SnapToChildrenType
  commentId: T:DrawnUi.Draw.SnapToChildrenType
  id: SnapToChildrenType
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.SnapToChildrenType.Center
  - DrawnUi.Draw.SnapToChildrenType.Disabled
  - DrawnUi.Draw.SnapToChildrenType.Side
  langs:
  - csharp
  - vb
  name: SnapToChildrenType
  nameWithType: SnapToChildrenType
  fullName: DrawnUi.Draw.SnapToChildrenType
  type: Enum
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/SnapToChildrenType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: SnapToChildrenType
    path: ../src/Shared/Draw/Internals/Enums/SnapToChildrenType.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public enum SnapToChildrenType
    content.vb: Public Enum SnapToChildrenType
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.SnapToChildrenType.Disabled
  commentId: F:DrawnUi.Draw.SnapToChildrenType.Disabled
  id: Disabled
  parent: DrawnUi.Draw.SnapToChildrenType
  langs:
  - csharp
  - vb
  name: Disabled
  nameWithType: SnapToChildrenType.Disabled
  fullName: DrawnUi.Draw.SnapToChildrenType.Disabled
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/SnapToChildrenType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Disabled
    path: ../src/Shared/Draw/Internals/Enums/SnapToChildrenType.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: Disabled = 0
    return:
      type: DrawnUi.Draw.SnapToChildrenType
- uid: DrawnUi.Draw.SnapToChildrenType.Side
  commentId: F:DrawnUi.Draw.SnapToChildrenType.Side
  id: Side
  parent: DrawnUi.Draw.SnapToChildrenType
  langs:
  - csharp
  - vb
  name: Side
  nameWithType: SnapToChildrenType.Side
  fullName: DrawnUi.Draw.SnapToChildrenType.Side
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/SnapToChildrenType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Side
    path: ../src/Shared/Draw/Internals/Enums/SnapToChildrenType.cs
    startLine: 5
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: Side = 1
    return:
      type: DrawnUi.Draw.SnapToChildrenType
- uid: DrawnUi.Draw.SnapToChildrenType.Center
  commentId: F:DrawnUi.Draw.SnapToChildrenType.Center
  id: Center
  parent: DrawnUi.Draw.SnapToChildrenType
  langs:
  - csharp
  - vb
  name: Center
  nameWithType: SnapToChildrenType.Center
  fullName: DrawnUi.Draw.SnapToChildrenType.Center
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/SnapToChildrenType.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Center
    path: ../src/Shared/Draw/Internals/Enums/SnapToChildrenType.cs
    startLine: 6
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: Center = 2
    return:
      type: DrawnUi.Draw.SnapToChildrenType
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.SnapToChildrenType
  commentId: T:DrawnUi.Draw.SnapToChildrenType
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SnapToChildrenType.html
  name: SnapToChildrenType
  nameWithType: SnapToChildrenType
  fullName: DrawnUi.Draw.SnapToChildrenType
