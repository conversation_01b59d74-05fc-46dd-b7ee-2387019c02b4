### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.IVisibilityAware
  commentId: T:DrawnUi.Draw.IVisibilityAware
  id: IVisibilityAware
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.IVisibilityAware.OnAppeared
  - DrawnUi.Draw.IVisibilityAware.OnAppearing
  - DrawnUi.Draw.IVisibilityAware.OnDisappeared
  - DrawnUi.Draw.IVisibilityAware.OnDisappearing
  langs:
  - csharp
  - vb
  name: IVisibilityAware
  nameWithType: IVisibilityAware
  fullName: DrawnUi.Draw.IVisibilityAware
  type: Interface
  source:
    remote:
      path: src/Shared/Draw/Internals/Interfaces/IVisibilityAware.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IVisibilityAware
    path: ../src/Shared/Draw/Internals/Interfaces/IVisibilityAware.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public interface IVisibilityAware
    content.vb: Public Interface IVisibilityAware
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.IVisibilityAware.OnAppearing
  commentId: M:DrawnUi.Draw.IVisibilityAware.OnAppearing
  id: OnAppearing
  parent: DrawnUi.Draw.IVisibilityAware
  langs:
  - csharp
  - vb
  name: OnAppearing()
  nameWithType: IVisibilityAware.OnAppearing()
  fullName: DrawnUi.Draw.IVisibilityAware.OnAppearing()
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Interfaces/IVisibilityAware.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: OnAppearing
    path: ../src/Shared/Draw/Internals/Interfaces/IVisibilityAware.cs
    startLine: 7
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: This can sometimes be omitted,
  example: []
  syntax:
    content: void OnAppearing()
    content.vb: Sub OnAppearing()
  overload: DrawnUi.Draw.IVisibilityAware.OnAppearing*
- uid: DrawnUi.Draw.IVisibilityAware.OnAppeared
  commentId: M:DrawnUi.Draw.IVisibilityAware.OnAppeared
  id: OnAppeared
  parent: DrawnUi.Draw.IVisibilityAware
  langs:
  - csharp
  - vb
  name: OnAppeared()
  nameWithType: IVisibilityAware.OnAppeared()
  fullName: DrawnUi.Draw.IVisibilityAware.OnAppeared()
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Interfaces/IVisibilityAware.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: OnAppeared
    path: ../src/Shared/Draw/Internals/Interfaces/IVisibilityAware.cs
    startLine: 12
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: This event can sometimes be called without prior OnAppearing
  example: []
  syntax:
    content: void OnAppeared()
    content.vb: Sub OnAppeared()
  overload: DrawnUi.Draw.IVisibilityAware.OnAppeared*
- uid: DrawnUi.Draw.IVisibilityAware.OnDisappeared
  commentId: M:DrawnUi.Draw.IVisibilityAware.OnDisappeared
  id: OnDisappeared
  parent: DrawnUi.Draw.IVisibilityAware
  langs:
  - csharp
  - vb
  name: OnDisappeared()
  nameWithType: IVisibilityAware.OnDisappeared()
  fullName: DrawnUi.Draw.IVisibilityAware.OnDisappeared()
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Interfaces/IVisibilityAware.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: OnDisappeared
    path: ../src/Shared/Draw/Internals/Interfaces/IVisibilityAware.cs
    startLine: 14
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: void OnDisappeared()
    content.vb: Sub OnDisappeared()
  overload: DrawnUi.Draw.IVisibilityAware.OnDisappeared*
- uid: DrawnUi.Draw.IVisibilityAware.OnDisappearing
  commentId: M:DrawnUi.Draw.IVisibilityAware.OnDisappearing
  id: OnDisappearing
  parent: DrawnUi.Draw.IVisibilityAware
  langs:
  - csharp
  - vb
  name: OnDisappearing()
  nameWithType: IVisibilityAware.OnDisappearing()
  fullName: DrawnUi.Draw.IVisibilityAware.OnDisappearing()
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Interfaces/IVisibilityAware.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: OnDisappearing
    path: ../src/Shared/Draw/Internals/Interfaces/IVisibilityAware.cs
    startLine: 16
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: void OnDisappearing()
    content.vb: Sub OnDisappearing()
  overload: DrawnUi.Draw.IVisibilityAware.OnDisappearing*
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.IVisibilityAware.OnAppearing*
  commentId: Overload:DrawnUi.Draw.IVisibilityAware.OnAppearing
  href: DrawnUi.Draw.IVisibilityAware.html#DrawnUi_Draw_IVisibilityAware_OnAppearing
  name: OnAppearing
  nameWithType: IVisibilityAware.OnAppearing
  fullName: DrawnUi.Draw.IVisibilityAware.OnAppearing
- uid: DrawnUi.Draw.IVisibilityAware.OnAppeared*
  commentId: Overload:DrawnUi.Draw.IVisibilityAware.OnAppeared
  href: DrawnUi.Draw.IVisibilityAware.html#DrawnUi_Draw_IVisibilityAware_OnAppeared
  name: OnAppeared
  nameWithType: IVisibilityAware.OnAppeared
  fullName: DrawnUi.Draw.IVisibilityAware.OnAppeared
- uid: DrawnUi.Draw.IVisibilityAware.OnDisappeared*
  commentId: Overload:DrawnUi.Draw.IVisibilityAware.OnDisappeared
  href: DrawnUi.Draw.IVisibilityAware.html#DrawnUi_Draw_IVisibilityAware_OnDisappeared
  name: OnDisappeared
  nameWithType: IVisibilityAware.OnDisappeared
  fullName: DrawnUi.Draw.IVisibilityAware.OnDisappeared
- uid: DrawnUi.Draw.IVisibilityAware.OnDisappearing*
  commentId: Overload:DrawnUi.Draw.IVisibilityAware.OnDisappearing
  href: DrawnUi.Draw.IVisibilityAware.html#DrawnUi_Draw_IVisibilityAware_OnDisappearing
  name: OnDisappearing
  nameWithType: IVisibilityAware.OnDisappearing
  fullName: DrawnUi.Draw.IVisibilityAware.OnDisappearing
