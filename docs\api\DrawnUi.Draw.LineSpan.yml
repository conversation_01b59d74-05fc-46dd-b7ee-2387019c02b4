### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.LineSpan
  commentId: T:DrawnUi.Draw.LineSpan
  id: LineSpan
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.LineSpan.#ctor
  - DrawnUi.Draw.LineSpan.Default
  - DrawnUi.Draw.LineSpan.Glyphs
  - DrawnUi.Draw.LineSpan.NeedsShaping
  - DrawnUi.Draw.LineSpan.Size
  - DrawnUi.Draw.LineSpan.Span
  - DrawnUi.Draw.LineSpan.Text
  langs:
  - csharp
  - vb
  name: LineSpan
  nameWithType: LineSpan
  fullName: DrawnUi.Draw.LineSpan
  type: Struct
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/LineSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: LineSpan
    path: ../src/Maui/DrawnUi/Draw/Text/LineSpan.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public struct LineSpan
    content.vb: Public Structure LineSpan
  inheritedMembers:
  - System.ValueType.Equals(System.Object)
  - System.ValueType.GetHashCode
  - System.ValueType.ToString
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetType
  - System.Object.ReferenceEquals(System.Object,System.Object)
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.LineSpan.#ctor
  commentId: M:DrawnUi.Draw.LineSpan.#ctor
  id: '#ctor'
  parent: DrawnUi.Draw.LineSpan
  langs:
  - csharp
  - vb
  name: LineSpan()
  nameWithType: LineSpan.LineSpan()
  fullName: DrawnUi.Draw.LineSpan.LineSpan()
  type: Constructor
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/LineSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Maui/DrawnUi/Draw/Text/LineSpan.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public LineSpan()
    content.vb: Public Sub New()
  overload: DrawnUi.Draw.LineSpan.#ctor*
  nameWithType.vb: LineSpan.New()
  fullName.vb: DrawnUi.Draw.LineSpan.New()
  name.vb: New()
- uid: DrawnUi.Draw.LineSpan.Text
  commentId: P:DrawnUi.Draw.LineSpan.Text
  id: Text
  parent: DrawnUi.Draw.LineSpan
  langs:
  - csharp
  - vb
  name: Text
  nameWithType: LineSpan.Text
  fullName: DrawnUi.Draw.LineSpan.Text
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/LineSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Text
    path: ../src/Maui/DrawnUi/Draw/Text/LineSpan.cs
    startLine: 9
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public string Text { readonly get; set; }
    parameters: []
    return:
      type: System.String
    content.vb: Public Property Text As String
  overload: DrawnUi.Draw.LineSpan.Text*
- uid: DrawnUi.Draw.LineSpan.Span
  commentId: P:DrawnUi.Draw.LineSpan.Span
  id: Span
  parent: DrawnUi.Draw.LineSpan
  langs:
  - csharp
  - vb
  name: Span
  nameWithType: LineSpan.Span
  fullName: DrawnUi.Draw.LineSpan.Span
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/LineSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Span
    path: ../src/Maui/DrawnUi/Draw/Text/LineSpan.cs
    startLine: 11
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public TextSpan Span { readonly get; set; }
    parameters: []
    return:
      type: DrawnUi.Draw.TextSpan
    content.vb: Public Property Span As TextSpan
  overload: DrawnUi.Draw.LineSpan.Span*
- uid: DrawnUi.Draw.LineSpan.Glyphs
  commentId: P:DrawnUi.Draw.LineSpan.Glyphs
  id: Glyphs
  parent: DrawnUi.Draw.LineSpan
  langs:
  - csharp
  - vb
  name: Glyphs
  nameWithType: LineSpan.Glyphs
  fullName: DrawnUi.Draw.LineSpan.Glyphs
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/LineSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Glyphs
    path: ../src/Maui/DrawnUi/Draw/Text/LineSpan.cs
    startLine: 13
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public LineGlyph[] Glyphs { readonly get; set; }
    parameters: []
    return:
      type: DrawnUi.Draw.LineGlyph[]
    content.vb: Public Property Glyphs As LineGlyph()
  overload: DrawnUi.Draw.LineSpan.Glyphs*
- uid: DrawnUi.Draw.LineSpan.NeedsShaping
  commentId: P:DrawnUi.Draw.LineSpan.NeedsShaping
  id: NeedsShaping
  parent: DrawnUi.Draw.LineSpan
  langs:
  - csharp
  - vb
  name: NeedsShaping
  nameWithType: LineSpan.NeedsShaping
  fullName: DrawnUi.Draw.LineSpan.NeedsShaping
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/LineSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: NeedsShaping
    path: ../src/Maui/DrawnUi/Draw/Text/LineSpan.cs
    startLine: 15
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public bool NeedsShaping { readonly get; set; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property NeedsShaping As Boolean
  overload: DrawnUi.Draw.LineSpan.NeedsShaping*
- uid: DrawnUi.Draw.LineSpan.Size
  commentId: P:DrawnUi.Draw.LineSpan.Size
  id: Size
  parent: DrawnUi.Draw.LineSpan
  langs:
  - csharp
  - vb
  name: Size
  nameWithType: LineSpan.Size
  fullName: DrawnUi.Draw.LineSpan.Size
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/LineSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Size
    path: ../src/Maui/DrawnUi/Draw/Text/LineSpan.cs
    startLine: 17
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public SKSize Size { readonly get; set; }
    parameters: []
    return:
      type: SkiaSharp.SKSize
    content.vb: Public Property Size As SKSize
  overload: DrawnUi.Draw.LineSpan.Size*
- uid: DrawnUi.Draw.LineSpan.Default
  commentId: P:DrawnUi.Draw.LineSpan.Default
  id: Default
  parent: DrawnUi.Draw.LineSpan
  langs:
  - csharp
  - vb
  name: Default
  nameWithType: LineSpan.Default
  fullName: DrawnUi.Draw.LineSpan.Default
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/LineSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Default
    path: ../src/Maui/DrawnUi/Draw/Text/LineSpan.cs
    startLine: 21
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static LineSpan Default { get; }
    parameters: []
    return:
      type: DrawnUi.Draw.LineSpan
    content.vb: Public Shared ReadOnly Property [Default] As LineSpan
  overload: DrawnUi.Draw.LineSpan.Default*
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.ValueType.Equals(System.Object)
  commentId: M:System.ValueType.Equals(System.Object)
  parent: System.ValueType
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype.equals
  name: Equals(object)
  nameWithType: ValueType.Equals(object)
  fullName: System.ValueType.Equals(object)
  nameWithType.vb: ValueType.Equals(Object)
  fullName.vb: System.ValueType.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.ValueType.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.equals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.ValueType.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.equals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.ValueType.GetHashCode
  commentId: M:System.ValueType.GetHashCode
  parent: System.ValueType
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype.gethashcode
  name: GetHashCode()
  nameWithType: ValueType.GetHashCode()
  fullName: System.ValueType.GetHashCode()
  spec.csharp:
  - uid: System.ValueType.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.ValueType.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.gethashcode
  - name: (
  - name: )
- uid: System.ValueType.ToString
  commentId: M:System.ValueType.ToString
  parent: System.ValueType
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype.tostring
  name: ToString()
  nameWithType: ValueType.ToString()
  fullName: System.ValueType.ToString()
  spec.csharp:
  - uid: System.ValueType.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.ValueType.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.tostring
  - name: (
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.ValueType
  commentId: T:System.ValueType
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype
  name: ValueType
  nameWithType: ValueType
  fullName: System.ValueType
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.LineSpan.#ctor*
  commentId: Overload:DrawnUi.Draw.LineSpan.#ctor
  href: DrawnUi.Draw.LineSpan.html#DrawnUi_Draw_LineSpan__ctor
  name: LineSpan
  nameWithType: LineSpan.LineSpan
  fullName: DrawnUi.Draw.LineSpan.LineSpan
  nameWithType.vb: LineSpan.New
  fullName.vb: DrawnUi.Draw.LineSpan.New
  name.vb: New
- uid: DrawnUi.Draw.LineSpan.Text*
  commentId: Overload:DrawnUi.Draw.LineSpan.Text
  href: DrawnUi.Draw.LineSpan.html#DrawnUi_Draw_LineSpan_Text
  name: Text
  nameWithType: LineSpan.Text
  fullName: DrawnUi.Draw.LineSpan.Text
- uid: System.String
  commentId: T:System.String
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.string
  name: string
  nameWithType: string
  fullName: string
  nameWithType.vb: String
  fullName.vb: String
  name.vb: String
- uid: DrawnUi.Draw.LineSpan.Span*
  commentId: Overload:DrawnUi.Draw.LineSpan.Span
  href: DrawnUi.Draw.LineSpan.html#DrawnUi_Draw_LineSpan_Span
  name: Span
  nameWithType: LineSpan.Span
  fullName: DrawnUi.Draw.LineSpan.Span
- uid: DrawnUi.Draw.TextSpan
  commentId: T:DrawnUi.Draw.TextSpan
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.TextSpan.html
  name: TextSpan
  nameWithType: TextSpan
  fullName: DrawnUi.Draw.TextSpan
- uid: DrawnUi.Draw.LineSpan.Glyphs*
  commentId: Overload:DrawnUi.Draw.LineSpan.Glyphs
  href: DrawnUi.Draw.LineSpan.html#DrawnUi_Draw_LineSpan_Glyphs
  name: Glyphs
  nameWithType: LineSpan.Glyphs
  fullName: DrawnUi.Draw.LineSpan.Glyphs
- uid: DrawnUi.Draw.LineGlyph[]
  isExternal: true
  href: DrawnUi.Draw.LineGlyph.html
  name: LineGlyph[]
  nameWithType: LineGlyph[]
  fullName: DrawnUi.Draw.LineGlyph[]
  nameWithType.vb: LineGlyph()
  fullName.vb: DrawnUi.Draw.LineGlyph()
  name.vb: LineGlyph()
  spec.csharp:
  - uid: DrawnUi.Draw.LineGlyph
    name: LineGlyph
    href: DrawnUi.Draw.LineGlyph.html
  - name: '['
  - name: ']'
  spec.vb:
  - uid: DrawnUi.Draw.LineGlyph
    name: LineGlyph
    href: DrawnUi.Draw.LineGlyph.html
  - name: (
  - name: )
- uid: DrawnUi.Draw.LineSpan.NeedsShaping*
  commentId: Overload:DrawnUi.Draw.LineSpan.NeedsShaping
  href: DrawnUi.Draw.LineSpan.html#DrawnUi_Draw_LineSpan_NeedsShaping
  name: NeedsShaping
  nameWithType: LineSpan.NeedsShaping
  fullName: DrawnUi.Draw.LineSpan.NeedsShaping
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: DrawnUi.Draw.LineSpan.Size*
  commentId: Overload:DrawnUi.Draw.LineSpan.Size
  href: DrawnUi.Draw.LineSpan.html#DrawnUi_Draw_LineSpan_Size
  name: Size
  nameWithType: LineSpan.Size
  fullName: DrawnUi.Draw.LineSpan.Size
- uid: SkiaSharp.SKSize
  commentId: T:SkiaSharp.SKSize
  parent: SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.sksize
  name: SKSize
  nameWithType: SKSize
  fullName: SkiaSharp.SKSize
- uid: SkiaSharp
  commentId: N:SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp
  name: SkiaSharp
  nameWithType: SkiaSharp
  fullName: SkiaSharp
- uid: DrawnUi.Draw.LineSpan.Default*
  commentId: Overload:DrawnUi.Draw.LineSpan.Default
  href: DrawnUi.Draw.LineSpan.html#DrawnUi_Draw_LineSpan_Default
  name: Default
  nameWithType: LineSpan.Default
  fullName: DrawnUi.Draw.LineSpan.Default
- uid: DrawnUi.Draw.LineSpan
  commentId: T:DrawnUi.Draw.LineSpan
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.LineSpan.html
  name: LineSpan
  nameWithType: LineSpan
  fullName: DrawnUi.Draw.LineSpan
