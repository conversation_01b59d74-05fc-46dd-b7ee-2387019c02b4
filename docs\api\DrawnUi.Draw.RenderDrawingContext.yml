### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.RenderDrawingContext
  commentId: T:DrawnUi.Draw.RenderDrawingContext
  id: RenderDrawingContext
  parent: DrawnUi.Draw
  children: []
  langs:
  - csharp
  - vb
  name: RenderDrawingContext
  nameWithType: RenderDrawingContext
  fullName: DrawnUi.Draw.RenderDrawingContext
  type: Class
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/DrawingContext.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: RenderDrawingContext
    path: ../src/Shared/Draw/Internals/Models/DrawingContext.cs
    startLine: 155
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: 'public class RenderDrawingContext : SkiaDrawingContext'
    content.vb: Public Class RenderDrawingContext Inherits SkiaDrawingContext
  inheritance:
  - System.Object
  - DrawnUi.Draw.SkiaDrawingContext
  inheritedMembers:
  - DrawnUi.Draw.SkiaDrawingContext.Canvas
  - DrawnUi.Draw.SkiaDrawingContext.Surface
  - DrawnUi.Draw.SkiaDrawingContext.Width
  - DrawnUi.Draw.SkiaDrawingContext.Height
  - DrawnUi.Draw.SkiaDrawingContext.FrameTimeNanos
  - DrawnUi.Draw.SkiaDrawingContext.Superview
  - DrawnUi.Draw.SkiaDrawingContext.IsVirtual
  - DrawnUi.Draw.SkiaDrawingContext.IsRecycled
  - DrawnUi.Draw.SkiaDrawingContext.Clone
  - DrawnUi.Draw.SkiaDrawingContext.CreateForRecordingImage(SkiaSharp.SKSurface,SkiaSharp.SKSize)
  - DrawnUi.Draw.SkiaDrawingContext.CreateForRecordingOperations(SkiaSharp.SKPictureRecorder,SkiaSharp.SKRect)
  - DrawnUi.Draw.SkiaDrawingContext.DeviceDensity
  - DrawnUi.Draw.SkiaDrawingContext.Services
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: DrawnUi.Draw.SkiaDrawingContext
  commentId: T:DrawnUi.Draw.SkiaDrawingContext
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaDrawingContext.html
  name: SkiaDrawingContext
  nameWithType: SkiaDrawingContext
  fullName: DrawnUi.Draw.SkiaDrawingContext
- uid: DrawnUi.Draw.SkiaDrawingContext.Canvas
  commentId: P:DrawnUi.Draw.SkiaDrawingContext.Canvas
  parent: DrawnUi.Draw.SkiaDrawingContext
  href: DrawnUi.Draw.SkiaDrawingContext.html#DrawnUi_Draw_SkiaDrawingContext_Canvas
  name: Canvas
  nameWithType: SkiaDrawingContext.Canvas
  fullName: DrawnUi.Draw.SkiaDrawingContext.Canvas
- uid: DrawnUi.Draw.SkiaDrawingContext.Surface
  commentId: P:DrawnUi.Draw.SkiaDrawingContext.Surface
  parent: DrawnUi.Draw.SkiaDrawingContext
  href: DrawnUi.Draw.SkiaDrawingContext.html#DrawnUi_Draw_SkiaDrawingContext_Surface
  name: Surface
  nameWithType: SkiaDrawingContext.Surface
  fullName: DrawnUi.Draw.SkiaDrawingContext.Surface
- uid: DrawnUi.Draw.SkiaDrawingContext.Width
  commentId: P:DrawnUi.Draw.SkiaDrawingContext.Width
  parent: DrawnUi.Draw.SkiaDrawingContext
  href: DrawnUi.Draw.SkiaDrawingContext.html#DrawnUi_Draw_SkiaDrawingContext_Width
  name: Width
  nameWithType: SkiaDrawingContext.Width
  fullName: DrawnUi.Draw.SkiaDrawingContext.Width
- uid: DrawnUi.Draw.SkiaDrawingContext.Height
  commentId: P:DrawnUi.Draw.SkiaDrawingContext.Height
  parent: DrawnUi.Draw.SkiaDrawingContext
  href: DrawnUi.Draw.SkiaDrawingContext.html#DrawnUi_Draw_SkiaDrawingContext_Height
  name: Height
  nameWithType: SkiaDrawingContext.Height
  fullName: DrawnUi.Draw.SkiaDrawingContext.Height
- uid: DrawnUi.Draw.SkiaDrawingContext.FrameTimeNanos
  commentId: P:DrawnUi.Draw.SkiaDrawingContext.FrameTimeNanos
  parent: DrawnUi.Draw.SkiaDrawingContext
  href: DrawnUi.Draw.SkiaDrawingContext.html#DrawnUi_Draw_SkiaDrawingContext_FrameTimeNanos
  name: FrameTimeNanos
  nameWithType: SkiaDrawingContext.FrameTimeNanos
  fullName: DrawnUi.Draw.SkiaDrawingContext.FrameTimeNanos
- uid: DrawnUi.Draw.SkiaDrawingContext.Superview
  commentId: P:DrawnUi.Draw.SkiaDrawingContext.Superview
  parent: DrawnUi.Draw.SkiaDrawingContext
  href: DrawnUi.Draw.SkiaDrawingContext.html#DrawnUi_Draw_SkiaDrawingContext_Superview
  name: Superview
  nameWithType: SkiaDrawingContext.Superview
  fullName: DrawnUi.Draw.SkiaDrawingContext.Superview
- uid: DrawnUi.Draw.SkiaDrawingContext.IsVirtual
  commentId: P:DrawnUi.Draw.SkiaDrawingContext.IsVirtual
  parent: DrawnUi.Draw.SkiaDrawingContext
  href: DrawnUi.Draw.SkiaDrawingContext.html#DrawnUi_Draw_SkiaDrawingContext_IsVirtual
  name: IsVirtual
  nameWithType: SkiaDrawingContext.IsVirtual
  fullName: DrawnUi.Draw.SkiaDrawingContext.IsVirtual
- uid: DrawnUi.Draw.SkiaDrawingContext.IsRecycled
  commentId: P:DrawnUi.Draw.SkiaDrawingContext.IsRecycled
  parent: DrawnUi.Draw.SkiaDrawingContext
  href: DrawnUi.Draw.SkiaDrawingContext.html#DrawnUi_Draw_SkiaDrawingContext_IsRecycled
  name: IsRecycled
  nameWithType: SkiaDrawingContext.IsRecycled
  fullName: DrawnUi.Draw.SkiaDrawingContext.IsRecycled
- uid: DrawnUi.Draw.SkiaDrawingContext.Clone
  commentId: M:DrawnUi.Draw.SkiaDrawingContext.Clone
  parent: DrawnUi.Draw.SkiaDrawingContext
  href: DrawnUi.Draw.SkiaDrawingContext.html#DrawnUi_Draw_SkiaDrawingContext_Clone
  name: Clone()
  nameWithType: SkiaDrawingContext.Clone()
  fullName: DrawnUi.Draw.SkiaDrawingContext.Clone()
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaDrawingContext.Clone
    name: Clone
    href: DrawnUi.Draw.SkiaDrawingContext.html#DrawnUi_Draw_SkiaDrawingContext_Clone
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SkiaDrawingContext.Clone
    name: Clone
    href: DrawnUi.Draw.SkiaDrawingContext.html#DrawnUi_Draw_SkiaDrawingContext_Clone
  - name: (
  - name: )
- uid: DrawnUi.Draw.SkiaDrawingContext.CreateForRecordingImage(SkiaSharp.SKSurface,SkiaSharp.SKSize)
  commentId: M:DrawnUi.Draw.SkiaDrawingContext.CreateForRecordingImage(SkiaSharp.SKSurface,SkiaSharp.SKSize)
  parent: DrawnUi.Draw.SkiaDrawingContext
  isExternal: true
  href: DrawnUi.Draw.SkiaDrawingContext.html#DrawnUi_Draw_SkiaDrawingContext_CreateForRecordingImage_SkiaSharp_SKSurface_SkiaSharp_SKSize_
  name: CreateForRecordingImage(SKSurface, SKSize)
  nameWithType: SkiaDrawingContext.CreateForRecordingImage(SKSurface, SKSize)
  fullName: DrawnUi.Draw.SkiaDrawingContext.CreateForRecordingImage(SkiaSharp.SKSurface, SkiaSharp.SKSize)
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaDrawingContext.CreateForRecordingImage(SkiaSharp.SKSurface,SkiaSharp.SKSize)
    name: CreateForRecordingImage
    href: DrawnUi.Draw.SkiaDrawingContext.html#DrawnUi_Draw_SkiaDrawingContext_CreateForRecordingImage_SkiaSharp_SKSurface_SkiaSharp_SKSize_
  - name: (
  - uid: SkiaSharp.SKSurface
    name: SKSurface
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.sksurface
  - name: ','
  - name: " "
  - uid: SkiaSharp.SKSize
    name: SKSize
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.sksize
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SkiaDrawingContext.CreateForRecordingImage(SkiaSharp.SKSurface,SkiaSharp.SKSize)
    name: CreateForRecordingImage
    href: DrawnUi.Draw.SkiaDrawingContext.html#DrawnUi_Draw_SkiaDrawingContext_CreateForRecordingImage_SkiaSharp_SKSurface_SkiaSharp_SKSize_
  - name: (
  - uid: SkiaSharp.SKSurface
    name: SKSurface
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.sksurface
  - name: ','
  - name: " "
  - uid: SkiaSharp.SKSize
    name: SKSize
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.sksize
  - name: )
- uid: DrawnUi.Draw.SkiaDrawingContext.CreateForRecordingOperations(SkiaSharp.SKPictureRecorder,SkiaSharp.SKRect)
  commentId: M:DrawnUi.Draw.SkiaDrawingContext.CreateForRecordingOperations(SkiaSharp.SKPictureRecorder,SkiaSharp.SKRect)
  parent: DrawnUi.Draw.SkiaDrawingContext
  isExternal: true
  href: DrawnUi.Draw.SkiaDrawingContext.html#DrawnUi_Draw_SkiaDrawingContext_CreateForRecordingOperations_SkiaSharp_SKPictureRecorder_SkiaSharp_SKRect_
  name: CreateForRecordingOperations(SKPictureRecorder, SKRect)
  nameWithType: SkiaDrawingContext.CreateForRecordingOperations(SKPictureRecorder, SKRect)
  fullName: DrawnUi.Draw.SkiaDrawingContext.CreateForRecordingOperations(SkiaSharp.SKPictureRecorder, SkiaSharp.SKRect)
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaDrawingContext.CreateForRecordingOperations(SkiaSharp.SKPictureRecorder,SkiaSharp.SKRect)
    name: CreateForRecordingOperations
    href: DrawnUi.Draw.SkiaDrawingContext.html#DrawnUi_Draw_SkiaDrawingContext_CreateForRecordingOperations_SkiaSharp_SKPictureRecorder_SkiaSharp_SKRect_
  - name: (
  - uid: SkiaSharp.SKPictureRecorder
    name: SKPictureRecorder
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skpicturerecorder
  - name: ','
  - name: " "
  - uid: SkiaSharp.SKRect
    name: SKRect
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skrect
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SkiaDrawingContext.CreateForRecordingOperations(SkiaSharp.SKPictureRecorder,SkiaSharp.SKRect)
    name: CreateForRecordingOperations
    href: DrawnUi.Draw.SkiaDrawingContext.html#DrawnUi_Draw_SkiaDrawingContext_CreateForRecordingOperations_SkiaSharp_SKPictureRecorder_SkiaSharp_SKRect_
  - name: (
  - uid: SkiaSharp.SKPictureRecorder
    name: SKPictureRecorder
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skpicturerecorder
  - name: ','
  - name: " "
  - uid: SkiaSharp.SKRect
    name: SKRect
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skrect
  - name: )
- uid: DrawnUi.Draw.SkiaDrawingContext.DeviceDensity
  commentId: P:DrawnUi.Draw.SkiaDrawingContext.DeviceDensity
  parent: DrawnUi.Draw.SkiaDrawingContext
  href: DrawnUi.Draw.SkiaDrawingContext.html#DrawnUi_Draw_SkiaDrawingContext_DeviceDensity
  name: DeviceDensity
  nameWithType: SkiaDrawingContext.DeviceDensity
  fullName: DrawnUi.Draw.SkiaDrawingContext.DeviceDensity
- uid: DrawnUi.Draw.SkiaDrawingContext.Services
  commentId: P:DrawnUi.Draw.SkiaDrawingContext.Services
  parent: DrawnUi.Draw.SkiaDrawingContext
  href: DrawnUi.Draw.SkiaDrawingContext.html#DrawnUi_Draw_SkiaDrawingContext_Services
  name: Services
  nameWithType: SkiaDrawingContext.Services
  fullName: DrawnUi.Draw.SkiaDrawingContext.Services
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
