### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.ScrollInteractionState
  commentId: T:DrawnUi.Draw.ScrollInteractionState
  id: ScrollInteractionState
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.ScrollInteractionState.Dragging
  - DrawnUi.Draw.ScrollInteractionState.None
  - DrawnUi.Draw.ScrollInteractionState.Scrolling
  - DrawnUi.Draw.ScrollInteractionState.Zooming
  langs:
  - csharp
  - vb
  name: ScrollInteractionState
  nameWithType: ScrollInteractionState
  fullName: DrawnUi.Draw.ScrollInteractionState
  type: Enum
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/ScrollInteractionState.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ScrollInteractionState
    path: ../src/Shared/Draw/Internals/Enums/ScrollInteractionState.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public enum ScrollInteractionState
    content.vb: Public Enum ScrollInteractionState
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.ScrollInteractionState.None
  commentId: F:DrawnUi.Draw.ScrollInteractionState.None
  id: None
  parent: DrawnUi.Draw.ScrollInteractionState
  langs:
  - csharp
  - vb
  name: None
  nameWithType: ScrollInteractionState.None
  fullName: DrawnUi.Draw.ScrollInteractionState.None
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/ScrollInteractionState.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: None
    path: ../src/Shared/Draw/Internals/Enums/ScrollInteractionState.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: None = 0
    return:
      type: DrawnUi.Draw.ScrollInteractionState
- uid: DrawnUi.Draw.ScrollInteractionState.Dragging
  commentId: F:DrawnUi.Draw.ScrollInteractionState.Dragging
  id: Dragging
  parent: DrawnUi.Draw.ScrollInteractionState
  langs:
  - csharp
  - vb
  name: Dragging
  nameWithType: ScrollInteractionState.Dragging
  fullName: DrawnUi.Draw.ScrollInteractionState.Dragging
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/ScrollInteractionState.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Dragging
    path: ../src/Shared/Draw/Internals/Enums/ScrollInteractionState.cs
    startLine: 5
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: Dragging = 1
    return:
      type: DrawnUi.Draw.ScrollInteractionState
- uid: DrawnUi.Draw.ScrollInteractionState.Scrolling
  commentId: F:DrawnUi.Draw.ScrollInteractionState.Scrolling
  id: Scrolling
  parent: DrawnUi.Draw.ScrollInteractionState
  langs:
  - csharp
  - vb
  name: Scrolling
  nameWithType: ScrollInteractionState.Scrolling
  fullName: DrawnUi.Draw.ScrollInteractionState.Scrolling
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/ScrollInteractionState.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Scrolling
    path: ../src/Shared/Draw/Internals/Enums/ScrollInteractionState.cs
    startLine: 6
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: Scrolling = 2
    return:
      type: DrawnUi.Draw.ScrollInteractionState
- uid: DrawnUi.Draw.ScrollInteractionState.Zooming
  commentId: F:DrawnUi.Draw.ScrollInteractionState.Zooming
  id: Zooming
  parent: DrawnUi.Draw.ScrollInteractionState
  langs:
  - csharp
  - vb
  name: Zooming
  nameWithType: ScrollInteractionState.Zooming
  fullName: DrawnUi.Draw.ScrollInteractionState.Zooming
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/ScrollInteractionState.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Zooming
    path: ../src/Shared/Draw/Internals/Enums/ScrollInteractionState.cs
    startLine: 7
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: Zooming = 3
    return:
      type: DrawnUi.Draw.ScrollInteractionState
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.ScrollInteractionState
  commentId: T:DrawnUi.Draw.ScrollInteractionState
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ScrollInteractionState.html
  name: ScrollInteractionState
  nameWithType: ScrollInteractionState
  fullName: DrawnUi.Draw.ScrollInteractionState
