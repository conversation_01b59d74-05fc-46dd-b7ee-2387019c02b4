### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.UsedGlyph
  commentId: T:DrawnUi.Draw.UsedGlyph
  id: UsedGlyph
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.UsedGlyph.GetGlyphText
  - DrawnUi.Draw.UsedGlyph.Id
  - DrawnUi.Draw.UsedGlyph.IsAvailable
  - DrawnUi.Draw.UsedGlyph.IsNumber
  - DrawnUi.Draw.UsedGlyph.Length
  - DrawnUi.Draw.UsedGlyph.Source
  - DrawnUi.Draw.UsedGlyph.StartIndex
  - DrawnUi.Draw.UsedGlyph.Symbol
  langs:
  - csharp
  - vb
  name: UsedGlyph
  nameWithType: UsedGlyph
  fullName: DrawnUi.Draw.UsedGlyph
  type: Struct
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/UsedGlyph.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: UsedGlyph
    path: ../src/Maui/DrawnUi/Draw/Text/UsedGlyph.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public struct UsedGlyph
    content.vb: Public Structure UsedGlyph
  inheritedMembers:
  - System.ValueType.Equals(System.Object)
  - System.ValueType.GetHashCode
  - System.ValueType.ToString
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetType
  - System.Object.ReferenceEquals(System.Object,System.Object)
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.UsedGlyph.Id
  commentId: P:DrawnUi.Draw.UsedGlyph.Id
  id: Id
  parent: DrawnUi.Draw.UsedGlyph
  langs:
  - csharp
  - vb
  name: Id
  nameWithType: UsedGlyph.Id
  fullName: DrawnUi.Draw.UsedGlyph.Id
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/UsedGlyph.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Id
    path: ../src/Maui/DrawnUi/Draw/Text/UsedGlyph.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public ushort Id { readonly get; set; }
    parameters: []
    return:
      type: System.UInt16
    content.vb: Public Property Id As UShort
  overload: DrawnUi.Draw.UsedGlyph.Id*
- uid: DrawnUi.Draw.UsedGlyph.Source
  commentId: P:DrawnUi.Draw.UsedGlyph.Source
  id: Source
  parent: DrawnUi.Draw.UsedGlyph
  langs:
  - csharp
  - vb
  name: Source
  nameWithType: UsedGlyph.Source
  fullName: DrawnUi.Draw.UsedGlyph.Source
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/UsedGlyph.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Source
    path: ../src/Maui/DrawnUi/Draw/Text/UsedGlyph.cs
    startLine: 6
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public string Source { readonly get; set; }
    parameters: []
    return:
      type: System.String
    content.vb: Public Property Source As String
  overload: DrawnUi.Draw.UsedGlyph.Source*
- uid: DrawnUi.Draw.UsedGlyph.Symbol
  commentId: P:DrawnUi.Draw.UsedGlyph.Symbol
  id: Symbol
  parent: DrawnUi.Draw.UsedGlyph
  langs:
  - csharp
  - vb
  name: Symbol
  nameWithType: UsedGlyph.Symbol
  fullName: DrawnUi.Draw.UsedGlyph.Symbol
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/UsedGlyph.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Symbol
    path: ../src/Maui/DrawnUi/Draw/Text/UsedGlyph.cs
    startLine: 8
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public int Symbol { readonly get; set; }
    parameters: []
    return:
      type: System.Int32
    content.vb: Public Property Symbol As Integer
  overload: DrawnUi.Draw.UsedGlyph.Symbol*
- uid: DrawnUi.Draw.UsedGlyph.IsAvailable
  commentId: P:DrawnUi.Draw.UsedGlyph.IsAvailable
  id: IsAvailable
  parent: DrawnUi.Draw.UsedGlyph
  langs:
  - csharp
  - vb
  name: IsAvailable
  nameWithType: UsedGlyph.IsAvailable
  fullName: DrawnUi.Draw.UsedGlyph.IsAvailable
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/UsedGlyph.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IsAvailable
    path: ../src/Maui/DrawnUi/Draw/Text/UsedGlyph.cs
    startLine: 10
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public bool IsAvailable { readonly get; set; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property IsAvailable As Boolean
  overload: DrawnUi.Draw.UsedGlyph.IsAvailable*
- uid: DrawnUi.Draw.UsedGlyph.StartIndex
  commentId: P:DrawnUi.Draw.UsedGlyph.StartIndex
  id: StartIndex
  parent: DrawnUi.Draw.UsedGlyph
  langs:
  - csharp
  - vb
  name: StartIndex
  nameWithType: UsedGlyph.StartIndex
  fullName: DrawnUi.Draw.UsedGlyph.StartIndex
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/UsedGlyph.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: StartIndex
    path: ../src/Maui/DrawnUi/Draw/Text/UsedGlyph.cs
    startLine: 15
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Position inside existing span
  example: []
  syntax:
    content: public int StartIndex { readonly get; set; }
    parameters: []
    return:
      type: System.Int32
    content.vb: Public Property StartIndex As Integer
  overload: DrawnUi.Draw.UsedGlyph.StartIndex*
- uid: DrawnUi.Draw.UsedGlyph.Length
  commentId: P:DrawnUi.Draw.UsedGlyph.Length
  id: Length
  parent: DrawnUi.Draw.UsedGlyph
  langs:
  - csharp
  - vb
  name: Length
  nameWithType: UsedGlyph.Length
  fullName: DrawnUi.Draw.UsedGlyph.Length
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/UsedGlyph.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Length
    path: ../src/Maui/DrawnUi/Draw/Text/UsedGlyph.cs
    startLine: 20
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Length inside existing span
  example: []
  syntax:
    content: public int Length { readonly get; set; }
    parameters: []
    return:
      type: System.Int32
    content.vb: Public Property Length As Integer
  overload: DrawnUi.Draw.UsedGlyph.Length*
- uid: DrawnUi.Draw.UsedGlyph.IsNumber
  commentId: M:DrawnUi.Draw.UsedGlyph.IsNumber
  id: IsNumber
  parent: DrawnUi.Draw.UsedGlyph
  langs:
  - csharp
  - vb
  name: IsNumber()
  nameWithType: UsedGlyph.IsNumber()
  fullName: DrawnUi.Draw.UsedGlyph.IsNumber()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/UsedGlyph.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IsNumber
    path: ../src/Maui/DrawnUi/Draw/Text/UsedGlyph.cs
    startLine: 22
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public bool IsNumber()
    return:
      type: System.Boolean
    content.vb: Public Function IsNumber() As Boolean
  overload: DrawnUi.Draw.UsedGlyph.IsNumber*
- uid: DrawnUi.Draw.UsedGlyph.GetGlyphText
  commentId: M:DrawnUi.Draw.UsedGlyph.GetGlyphText
  id: GetGlyphText
  parent: DrawnUi.Draw.UsedGlyph
  langs:
  - csharp
  - vb
  name: GetGlyphText()
  nameWithType: UsedGlyph.GetGlyphText()
  fullName: DrawnUi.Draw.UsedGlyph.GetGlyphText()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/UsedGlyph.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: GetGlyphText
    path: ../src/Maui/DrawnUi/Draw/Text/UsedGlyph.cs
    startLine: 28
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public ReadOnlySpan<char> GetGlyphText()
    return:
      type: System.ReadOnlySpan{System.Char}
    content.vb: Public Function GetGlyphText() As ReadOnlySpan(Of Char)
  overload: DrawnUi.Draw.UsedGlyph.GetGlyphText*
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.ValueType.Equals(System.Object)
  commentId: M:System.ValueType.Equals(System.Object)
  parent: System.ValueType
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype.equals
  name: Equals(object)
  nameWithType: ValueType.Equals(object)
  fullName: System.ValueType.Equals(object)
  nameWithType.vb: ValueType.Equals(Object)
  fullName.vb: System.ValueType.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.ValueType.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.equals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.ValueType.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.equals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.ValueType.GetHashCode
  commentId: M:System.ValueType.GetHashCode
  parent: System.ValueType
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype.gethashcode
  name: GetHashCode()
  nameWithType: ValueType.GetHashCode()
  fullName: System.ValueType.GetHashCode()
  spec.csharp:
  - uid: System.ValueType.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.ValueType.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.gethashcode
  - name: (
  - name: )
- uid: System.ValueType.ToString
  commentId: M:System.ValueType.ToString
  parent: System.ValueType
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype.tostring
  name: ToString()
  nameWithType: ValueType.ToString()
  fullName: System.ValueType.ToString()
  spec.csharp:
  - uid: System.ValueType.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.ValueType.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.tostring
  - name: (
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.ValueType
  commentId: T:System.ValueType
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype
  name: ValueType
  nameWithType: ValueType
  fullName: System.ValueType
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.UsedGlyph.Id*
  commentId: Overload:DrawnUi.Draw.UsedGlyph.Id
  href: DrawnUi.Draw.UsedGlyph.html#DrawnUi_Draw_UsedGlyph_Id
  name: Id
  nameWithType: UsedGlyph.Id
  fullName: DrawnUi.Draw.UsedGlyph.Id
- uid: System.UInt16
  commentId: T:System.UInt16
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.uint16
  name: ushort
  nameWithType: ushort
  fullName: ushort
  nameWithType.vb: UShort
  fullName.vb: UShort
  name.vb: UShort
- uid: DrawnUi.Draw.UsedGlyph.Source*
  commentId: Overload:DrawnUi.Draw.UsedGlyph.Source
  href: DrawnUi.Draw.UsedGlyph.html#DrawnUi_Draw_UsedGlyph_Source
  name: Source
  nameWithType: UsedGlyph.Source
  fullName: DrawnUi.Draw.UsedGlyph.Source
- uid: System.String
  commentId: T:System.String
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.string
  name: string
  nameWithType: string
  fullName: string
  nameWithType.vb: String
  fullName.vb: String
  name.vb: String
- uid: DrawnUi.Draw.UsedGlyph.Symbol*
  commentId: Overload:DrawnUi.Draw.UsedGlyph.Symbol
  href: DrawnUi.Draw.UsedGlyph.html#DrawnUi_Draw_UsedGlyph_Symbol
  name: Symbol
  nameWithType: UsedGlyph.Symbol
  fullName: DrawnUi.Draw.UsedGlyph.Symbol
- uid: System.Int32
  commentId: T:System.Int32
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.int32
  name: int
  nameWithType: int
  fullName: int
  nameWithType.vb: Integer
  fullName.vb: Integer
  name.vb: Integer
- uid: DrawnUi.Draw.UsedGlyph.IsAvailable*
  commentId: Overload:DrawnUi.Draw.UsedGlyph.IsAvailable
  href: DrawnUi.Draw.UsedGlyph.html#DrawnUi_Draw_UsedGlyph_IsAvailable
  name: IsAvailable
  nameWithType: UsedGlyph.IsAvailable
  fullName: DrawnUi.Draw.UsedGlyph.IsAvailable
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: DrawnUi.Draw.UsedGlyph.StartIndex*
  commentId: Overload:DrawnUi.Draw.UsedGlyph.StartIndex
  href: DrawnUi.Draw.UsedGlyph.html#DrawnUi_Draw_UsedGlyph_StartIndex
  name: StartIndex
  nameWithType: UsedGlyph.StartIndex
  fullName: DrawnUi.Draw.UsedGlyph.StartIndex
- uid: DrawnUi.Draw.UsedGlyph.Length*
  commentId: Overload:DrawnUi.Draw.UsedGlyph.Length
  href: DrawnUi.Draw.UsedGlyph.html#DrawnUi_Draw_UsedGlyph_Length
  name: Length
  nameWithType: UsedGlyph.Length
  fullName: DrawnUi.Draw.UsedGlyph.Length
- uid: DrawnUi.Draw.UsedGlyph.IsNumber*
  commentId: Overload:DrawnUi.Draw.UsedGlyph.IsNumber
  href: DrawnUi.Draw.UsedGlyph.html#DrawnUi_Draw_UsedGlyph_IsNumber
  name: IsNumber
  nameWithType: UsedGlyph.IsNumber
  fullName: DrawnUi.Draw.UsedGlyph.IsNumber
- uid: DrawnUi.Draw.UsedGlyph.GetGlyphText*
  commentId: Overload:DrawnUi.Draw.UsedGlyph.GetGlyphText
  href: DrawnUi.Draw.UsedGlyph.html#DrawnUi_Draw_UsedGlyph_GetGlyphText
  name: GetGlyphText
  nameWithType: UsedGlyph.GetGlyphText
  fullName: DrawnUi.Draw.UsedGlyph.GetGlyphText
- uid: System.ReadOnlySpan{System.Char}
  commentId: T:System.ReadOnlySpan{System.Char}
  parent: System
  definition: System.ReadOnlySpan`1
  href: https://learn.microsoft.com/dotnet/api/system.readonlyspan-1
  name: ReadOnlySpan<char>
  nameWithType: ReadOnlySpan<char>
  fullName: System.ReadOnlySpan<char>
  nameWithType.vb: ReadOnlySpan(Of Char)
  fullName.vb: System.ReadOnlySpan(Of Char)
  name.vb: ReadOnlySpan(Of Char)
  spec.csharp:
  - uid: System.ReadOnlySpan`1
    name: ReadOnlySpan
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.readonlyspan-1
  - name: <
  - uid: System.Char
    name: char
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.char
  - name: '>'
  spec.vb:
  - uid: System.ReadOnlySpan`1
    name: ReadOnlySpan
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.readonlyspan-1
  - name: (
  - name: Of
  - name: " "
  - uid: System.Char
    name: Char
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.char
  - name: )
- uid: System.ReadOnlySpan`1
  commentId: T:System.ReadOnlySpan`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.readonlyspan-1
  name: ReadOnlySpan<T>
  nameWithType: ReadOnlySpan<T>
  fullName: System.ReadOnlySpan<T>
  nameWithType.vb: ReadOnlySpan(Of T)
  fullName.vb: System.ReadOnlySpan(Of T)
  name.vb: ReadOnlySpan(Of T)
  spec.csharp:
  - uid: System.ReadOnlySpan`1
    name: ReadOnlySpan
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.readonlyspan-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.ReadOnlySpan`1
    name: ReadOnlySpan
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.readonlyspan-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
